import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ConfirmationService, MenuItem } from 'primeng/api';
import { MenuService } from '../service/menu.service';
import { LayoutService } from './service/app.layout.service';
import { Router } from '@angular/router';
import {environment} from '../../../environments/environment';
@Component({
  selector: 'app-menu',
  templateUrl: './app.menu.component.html',
  styles: [
    `
      .user-logo-section {
        position: relative;
        padding-top: 1rem;
      }

      .user-logo {
        display: flex;
        align-items: center;
        padding-left: 1rem; 
      }

      .user-profile-section { 
        padding-top: 0px;
        padding-bottom: 0px;
        position: relative;
      }

      .user-profile {
        display: flex;
        align-items: center;
        padding-left: 1rem;
      }

      .user-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 1rem;
      }

      .user-name {
        font-weight: bold;
      }
     
.layout-menu li ul li:has(a.active-route) {
  background: #e6edfa !important;
}
::ng-deep .p-divider
{
  margin:10px 0px;
}
    `,
  ],
  providers: [ConfirmationService]
})
export class AppMenuComponent implements OnInit {

  model: any[] = [];
  selectedLabel: any;
  dropdownOpen = false;
  userName: string = '';
  userItems: any = [];
  items: MenuItem[] | undefined;
    version: string = environment.version;
  constructor(
    public layoutService: LayoutService,
    public menuService: MenuService,
    public sanitizer: DomSanitizer,
    public router: Router,
    public confirmationService: ConfirmationService
  ) {
    this.selectedLabel = localStorage.getItem('selectedmenu')
      ? localStorage.getItem('selectedmenu')
      : 'Dashboard';

    this.items = [
      {
        label: '',
        items: [
          {
            label: 'Profile',
            icon: 'pi pi-user-edit',
            command: () => {
              this.router.navigate(['/profile'])
            }
          },
          {
            label: 'Change Password',
            icon: 'pi pi-key',
            command: () => {
              this.router.navigate(['/profile/resetPassword'])
            }
          },
          {
            label: 'Logout',
            icon: 'pi pi-sign-out',
            command: () => {
              this.logout()
            }
          }
        ]
      }
    ];
  }

  save(severity: string) {

  }

  update() {

  }

  delete() {

  }

  ngOnInit() {
    this.menuService.currentMessage.subscribe((message) =>
      this.setActiveLink(message)
    );
    let userRole = JSON.parse(localStorage.getItem('user')!);
    console.log(userRole);
    this.userName = userRole?.firstName + ' ' + userRole?.lastName;
    if (userRole && userRole.roleType == 1) {
      this.model = [
        {
          items: [
            {
              label: 'Inventory',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1 1H3.27962C3.98844 1 4.34285 1 4.60715 1.19431C4.87144 1.38862 4.97715 1.7269 5.18858 2.40345L6 5"  stroke-linecap="round"/>
<path d="M20 19H7.39186C6.40403 19 5.91011 19 5.62067 18.7252C5.54364 18.652 5.47876 18.5671 5.42846 18.4735C5.23947 18.122 5.36942 17.6454 5.62934 16.6924V16.6924C5.94789 15.5244 6.10716 14.9404 6.50589 14.5575C6.61459 14.4531 6.73474 14.3613 6.86405 14.2839C7.3384 14 7.94373 14 9.15439 14H15.9091"  stroke-linecap="round" stroke-linejoin="round"/>
<path d="M16.3874 14H10.5995C9.33781 14 8.70696 14 8.20992 13.677C7.71288 13.354 7.45667 12.7775 6.94425 11.6246L5.87485 9.21842C5.02947 7.31631 4.60678 6.36526 5.0504 5.68263C5.49403 5 6.53478 5 8.61629 5H18.735C21.0712 5 22.2392 5 22.6708 5.75739C23.1024 6.51478 22.5069 7.51965 21.3159 9.52941L19.8286 12.0392C19.2612 12.9967 18.9775 13.4754 18.5172 13.7377C18.0569 14 17.5004 14 16.3874 14Z"  stroke-linecap="round"/>
<circle cx="19" cy="22" r="1" />
<circle cx="8" cy="22" r="1" />
</svg>
`),
              routerLink: ['/admin/inventory'],
            },
            {
              label: 'Admin',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg  height="24" width="25" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 474.565 474.565" xml:space="preserve">
<g>
	<path d="M255.204,102.3c-0.606-11.321-12.176-9.395-23.465-9.395C240.078,95.126,247.967,98.216,255.204,102.3z"/>
	<path d="M134.524,73.928c-43.825,0-63.997,55.471-28.963,83.37c11.943-31.89,35.718-54.788,66.886-63.826
		C163.921,81.685,150.146,73.928,134.524,73.928z"/>
	<path d="M43.987,148.617c1.786,5.731,4.1,11.229,6.849,16.438L36.44,179.459c-3.866,3.866-3.866,10.141,0,14.015l25.375,25.383
		c1.848,1.848,4.38,2.888,7.019,2.888c2.61,0,5.125-1.04,7.005-2.888l14.38-14.404c2.158,1.142,4.55,1.842,6.785,2.827
		c0-0.164-0.016-0.334-0.016-0.498c0-11.771,1.352-22.875,3.759-33.302c-17.362-11.174-28.947-30.57-28.947-52.715
		c0-34.592,28.139-62.739,62.723-62.739c23.418,0,43.637,13.037,54.43,32.084c11.523-1.429,22.347-1.429,35.376,1.033
		c-1.676-5.07-3.648-10.032-6.118-14.683l14.396-14.411c1.878-1.856,2.918-4.38,2.918-7.004c0-2.625-1.04-5.148-2.918-7.004
		l-25.361-25.367c-1.94-1.941-4.472-2.904-7.003-2.904c-2.532,0-5.063,0.963-6.989,2.904l-14.442,14.411
		c-5.217-2.764-10.699-5.078-16.444-6.825V9.9c0-5.466-4.411-9.9-9.893-9.9h-35.888c-5.451,0-9.909,4.434-9.909,9.9v20.359
		c-5.73,1.747-11.213,4.061-16.446,6.825L75.839,22.689c-1.942-1.941-4.473-2.904-7.005-2.904c-2.531,0-5.077,0.963-7.003,2.896
		L36.44,48.048c-1.848,1.864-2.888,4.379-2.888,7.012c0,2.632,1.04,5.148,2.888,7.004l14.396,14.403
		c-2.75,5.218-5.063,10.708-6.817,16.438H23.675c-5.482,0-9.909,4.441-9.909,9.915v35.889c0,5.458,4.427,9.908,9.909,9.908H43.987z"
		/>
	<path d="M354.871,340.654c15.872-8.705,26.773-25.367,26.773-44.703c0-28.217-22.967-51.168-51.184-51.168
		c-9.923,0-19.118,2.966-26.975,7.873c-4.705,18.728-12.113,36.642-21.803,52.202C309.152,310.022,334.357,322.531,354.871,340.654z
		"/>
	<path d="M460.782,276.588c0-5.909-4.799-10.693-10.685-10.693H428.14c-1.896-6.189-4.411-12.121-7.393-17.75l15.544-15.544
		c2.02-2.004,3.137-4.721,3.137-7.555c0-2.835-1.118-5.553-3.137-7.563l-27.363-27.371c-2.08-2.09-4.829-3.138-7.561-3.138
		c-2.734,0-5.467,1.048-7.547,3.138l-15.576,15.552c-5.623-2.982-11.539-5.481-17.751-7.369v-21.958
		c0-5.901-4.768-10.685-10.669-10.685H311.11c-2.594,0-4.877,1.04-6.739,2.578c3.26,11.895,5.046,24.793,5.046,38.552
		c0,8.735-0.682,17.604-1.956,26.423c7.205-2.656,14.876-4.324,22.999-4.324c36.99,0,67.086,30.089,67.086,67.07
		c0,23.637-12.345,44.353-30.872,56.303c13.48,14.784,24.195,32.324,31.168,51.976c1.148,0.396,2.344,0.684,3.54,0.684
		c2.733,0,5.467-1.04,7.563-3.13l27.379-27.371c2.004-2.004,3.106-4.721,3.106-7.555s-1.102-5.551-3.106-7.563l-15.576-15.552
		c2.982-5.621,5.497-11.555,7.393-17.75h21.957c2.826,0,5.575-1.118,7.563-3.138c2.004-1.996,3.138-4.72,3.138-7.555
		L460.782,276.588z"/>
	<path d="M376.038,413.906c-16.602-48.848-60.471-82.445-111.113-87.018c-16.958,17.958-37.954,29.351-61.731,29.351
		c-23.759,0-44.771-11.392-61.713-29.351c-50.672,4.573-94.543,38.17-111.145,87.026l-9.177,27.013
		c-2.625,7.773-1.368,16.338,3.416,23.007c4.783,6.671,12.486,10.631,20.685,10.631h315.853c8.215,0,15.918-3.96,20.702-10.631
		c4.767-6.669,6.041-15.234,3.4-23.007L376.038,413.906z"/>
	<path d="M120.842,206.782c0,60.589,36.883,125.603,82.352,125.603c45.487,0,82.368-65.014,82.368-125.603
		C285.563,81.188,120.842,80.939,120.842,206.782z"/>
</g>
</svg>
`),
              routerLink: ['/admin/admins'],
            },
            {
              label: 'Care Managers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/admin/casemanagers'],
            },
            {
              label: 'Physicians',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.9024 22.9324C21.8064 22.0132 21.7824 21.0868 21.6912 20.1652C21.5544 18.7852 21.3576 17.4124 20.9496 16.0804C20.7288 15.3676 20.3928 14.7484 19.7232 14.3548C19.0824 13.978 18.384 13.7548 17.6832 13.5364C17.4384 13.4596 17.4816 13.6324 17.496 13.7572C17.5608 14.3596 17.5056 14.9548 17.3808 15.5428C17.34 15.7324 17.4048 15.8188 17.568 15.9124C18.2592 16.2988 18.6528 16.8892 18.696 17.6884C18.7392 18.5116 18.5712 19.3204 18.5208 20.1364C18.4872 20.6908 18.4272 21.2476 18.1944 21.7636C17.9136 22.3876 17.4288 22.6348 16.7664 22.5076C16.5144 22.4596 16.3488 22.3444 16.3896 22.066C16.4304 21.7924 16.62 21.6964 16.872 21.7468C17.1888 21.8092 17.3736 21.6604 17.5032 21.4012C17.592 21.2212 17.64 21.0244 17.6592 20.8252C17.7528 19.906 17.8536 18.9868 17.928 18.0652C17.9952 17.2324 17.5488 16.6012 16.8096 16.4332C16.0464 16.2604 15.3888 16.606 15.0744 17.3836C14.8224 18.0052 14.616 18.646 14.376 19.2748C14.2392 19.63 14.1216 19.9876 14.0496 20.3596C13.956 20.842 14.052 21.0196 14.4984 21.2212C14.712 21.3172 14.8152 21.4684 14.7408 21.6964C14.6664 21.9268 14.4984 21.9916 14.268 21.9484C13.6104 21.8236 13.2288 21.3556 13.2408 20.6812C13.2456 20.4052 13.2864 20.1388 13.3632 19.8748C13.6464 18.8956 14.0184 17.9476 14.3976 17.002C14.7384 16.1524 15.4176 15.6844 16.3416 15.6196C16.5216 15.6052 16.5984 15.5548 16.632 15.3796C16.7448 14.7964 16.7616 14.2132 16.7088 13.6228C16.6848 13.3444 16.5768 13.1932 16.2864 13.1476C16.0512 13.1116 15.816 13.0324 15.5952 12.9412C15.3744 12.8524 15.2784 12.8788 15.2016 13.1284C14.9304 14.0356 14.5512 14.902 14.112 15.742C13.9008 16.1476 13.656 16.5364 13.3848 17.0068C13.2768 16.4116 13.1952 15.9052 13.0896 15.4012C13.0416 15.1756 13.0464 14.9716 13.1448 14.758C13.2 14.6356 13.3464 14.4916 13.2 14.3596C12.9672 14.1484 12.7128 13.9636 12.4656 13.7692C12.4584 13.762 12.4248 13.7788 12.4104 13.7908C12.1704 13.9972 11.8968 14.1748 11.7096 14.4196C11.5968 14.566 11.8152 14.758 11.8632 14.9356C11.8728 14.9764 11.8944 15.0196 11.8896 15.058C11.7888 15.6772 11.6832 16.294 11.5704 16.9708C11.4912 16.8772 11.4552 16.8412 11.4312 16.8004C10.7976 15.778 10.2816 14.7028 9.89039 13.5652C9.81119 13.3324 9.81839 12.9724 9.61919 12.898C9.43199 12.8284 9.15119 13.0132 8.91119 13.0804C8.78639 13.1164 8.67599 13.15 8.62799 13.2964C8.53679 13.5796 8.40719 13.8484 8.33999 14.1316C7.89839 15.9748 8.00879 17.7988 8.57759 19.6012C8.66399 19.8748 8.76719 20.0236 9.07919 20.0836C9.68159 20.2036 10.0056 20.8636 9.79919 21.4348C9.58319 22.0372 8.93279 22.3204 8.36639 22.0612C7.79519 21.802 7.55039 21.0988 7.88159 20.5756C8.00159 20.386 7.98719 20.2396 7.92239 20.0524C7.31279 18.3028 7.14959 16.5124 7.43039 14.6788C7.49519 14.2564 7.63679 13.8532 7.73279 13.4068C6.89519 13.6516 6.09119 13.8556 5.36639 14.278C4.83359 14.5876 4.42079 14.9932 4.18799 15.5836C3.90959 16.2916 3.74159 17.026 3.59999 17.77C3.27359 19.498 3.17279 21.2524 3.04079 23.002C3.00479 23.4796 3.21839 23.7748 3.59759 23.998H21.3312C21.7824 23.7844 21.9552 23.4436 21.9024 22.9324ZM8.49119 8.17237C8.65439 8.21557 8.73119 8.29477 8.79359 8.44357C9.16079 9.32197 9.61439 10.1476 10.2888 10.834C10.8216 11.3788 11.4384 11.7868 12.2208 11.866C13.2048 11.9668 13.9728 11.5276 14.6376 10.8628C15.3144 10.1836 15.7848 9.36757 16.152 8.48437C16.2024 8.36437 16.2192 8.21317 16.4016 8.19637C17.0568 8.13397 17.568 7.09717 17.46 6.46597C17.4288 6.28597 17.3616 6.12037 17.1648 6.10837C16.92 6.09157 16.9656 5.96197 17.0016 5.81077C17.1312 5.26837 17.1864 4.71397 17.2128 4.23877C17.2296 3.75637 17.184 3.35557 17.088 2.97157C16.9416 2.39077 16.6416 1.89157 16.0968 1.61317C15.7752 1.44757 15.528 1.26517 15.42 0.907567C15.3792 0.768367 15.2424 0.677167 15.1296 0.583567C14.712 0.237967 14.2176 0.086767 13.704 0.036367C12.1656 -0.114833 10.7304 0.213967 9.46079 1.10917C8.58479 1.72357 7.98239 2.53477 7.85999 3.63877C7.77839 4.36597 7.81919 5.08597 7.95599 5.80357C7.98479 5.95717 8.04719 6.11797 7.77599 6.11317C7.65359 6.11077 7.57439 6.21397 7.53359 6.33397C7.30799 6.97717 7.82879 7.99717 8.49119 8.17237Z" />
</svg>
`),
              routerLink: ['/admin/physicians'],
            },
            {
              label: 'Caregivers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/admin/caregivers'],
            },
            {
              label: 'Alerts',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.66322 21.4942H21.3464C22.5824 21.4942 23.3792 20.191 22.82 19.0894L13.9784 3.42221C13.3688 2.20301 11.624 2.20301 11.0144 3.42221L2.17282 19.0894C1.63042 20.1886 2.42722 21.4942 3.66322 21.4942ZM13.892 17.851C13.892 18.6646 13.2992 19.291 12.452 19.291C11.6048 19.291 11.012 18.6646 11.012 17.851V17.8174C11.012 17.0038 11.6048 16.3774 12.452 16.3774C13.2992 16.3774 13.892 17.0038 13.892 17.8174V17.851ZM11.6744 7.26701H13.3352C13.7912 7.26701 14.0624 7.65581 14.012 8.14781L13.2824 14.515C13.232 14.9734 12.9272 15.259 12.5024 15.259C12.08 15.259 11.7728 14.971 11.7224 14.515L10.9928 8.14781C10.9472 7.65581 11.2184 7.26701 11.6744 7.26701Z" />
</svg>
`),
              routerLink: ['/admin/alerts'],
            },

            {
              label: 'Medical Devices',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.1644 9.03112C19.0468 8.75032 19.2652 8.29432 19.7452 7.81192L20.9068 6.65032C21.0724 6.48472 21.1516 6.29752 21.0844 6.23032L20.9644 6.11032L17.8852 3.03352L17.7652 2.91352C17.7652 2.91352 17.5108 2.92792 17.3452 3.09352L16.1836 4.25512C15.6508 4.78552 15.2956 4.86712 15.13 4.86712C15.0076 4.86712 14.9116 4.82632 14.8252 4.73992C14.71 4.62232 14.5132 4.31512 14.5132 3.56392V3.54232C14.5132 3.53032 14.5108 3.33112 14.5108 3.09592V1.91992C14.5108 1.68712 14.3212 1.49512 14.086 1.49512H9.90765C9.67485 1.49512 9.48285 1.68472 9.48285 1.91992V3.56632C9.48285 3.87112 9.43485 4.86472 8.86605 4.86472C8.70045 4.86472 8.34525 4.78552 7.81485 4.25272L6.59565 3.03352L6.47565 2.91352C6.47565 2.91352 6.30045 2.84872 6.23565 2.91352L3.03645 6.11272L2.91645 6.23272C2.91645 6.23272 2.84925 6.40792 2.91645 6.47272L3.03645 6.59272L4.25565 7.81432C4.73565 8.29432 4.95405 8.74792 4.83645 9.03352C4.71885 9.31432 4.24365 9.48472 3.56685 9.48472H1.92285C1.68765 9.48232 1.49805 9.67432 1.49805 9.90712V14.0927C1.49805 14.3255 1.68765 14.5175 1.92285 14.5175H3.56445C4.24365 14.5175 4.71645 14.6855 4.83405 14.9663C4.95165 15.2495 4.73325 15.7031 4.25325 16.1855L3.03405 17.4047L2.91405 17.5247C2.91405 17.5247 2.84685 17.6999 2.91405 17.7647L6.11325 20.9663L6.23325 21.0863C6.23325 21.0863 6.40845 21.1535 6.47325 21.0863L6.59325 20.9663L7.81245 19.7447C8.34285 19.2143 8.70045 19.1327 8.86365 19.1327C9.43245 19.1327 9.48045 20.1263 9.48045 20.4311V22.0775C9.48045 22.3103 9.67005 22.5023 9.90525 22.5023H14.0884C14.3212 22.5023 14.5132 22.3127 14.5132 22.0775V20.9015C14.5132 20.6687 14.5156 20.4695 14.5156 20.4551V20.4335C14.5132 19.6823 14.71 19.3751 14.8276 19.2575C14.914 19.1711 15.01 19.1303 15.1324 19.1303C15.2956 19.1303 15.6532 19.2095 16.186 19.7423L17.4052 20.9639L17.5252 21.0839C17.5252 21.0839 17.7004 21.1511 17.7652 21.0839L20.9668 17.8847L21.0868 17.7647C21.0868 17.7647 21.1516 17.5895 21.0868 17.5247L19.7452 16.1855C19.2652 15.7055 19.0468 15.2495 19.1644 14.9663C19.282 14.6831 19.7572 14.5175 20.4364 14.5175H22.0804C22.3132 14.5175 22.5052 14.3279 22.5052 14.0927V9.90472C22.5052 9.67192 22.3156 9.47992 22.0804 9.47992H20.4412C19.7572 9.48232 19.282 9.31432 19.1644 9.03112ZM16.7428 11.9999C16.7428 14.6135 14.6164 16.7423 12.0004 16.7423C9.38445 16.7423 7.25805 14.6135 7.25805 11.9999C7.25805 9.38632 9.38685 7.25752 12.0004 7.25752C14.614 7.25752 16.7428 9.38392 16.7428 11.9999Z"/>
</svg>
`),
              routerLink: ['/admin/medical-devices'],
            },
            {
              label: 'Connectivity',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg height="24" width="25" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 172.874 172.874" xml:space="preserve">
<g>
	<path  d="M100.806,81.731c0-8.415-6.822-15.237-15.237-15.237c-8.415,0-15.237,6.822-15.237,15.237
		c0,5.667,3.101,10.602,7.692,13.227l0.122,51.174c0.01,4.136,3.366,7.482,7.5,7.482c0.006,0,0.012,0,0.018,0
		c4.143-0.01,7.492-3.376,7.482-7.518l-0.122-51.083C97.665,92.401,100.806,87.436,100.806,81.731z"/>
	<path  d="M30.421,30.496L20.077,19.261c-28.291,37.061-26.359,88.93,3.752,123.863l10.225-11.106
		C10.165,103.108,8.449,61.261,30.421,30.496z"/>
	<path  d="M45.2,46.549L34.711,35.156c-19.371,28.138-17.718,65.87,3.585,92.254l10.29-11.177
		C33.411,95.99,31.993,68.224,45.2,46.549z"/>
	<path  d="M42,79.171c0,11.66,4.081,22.926,11.369,31.867l10.302-11.19C59.376,93.87,57,86.634,57,79.171
		c0-5.53,1.288-10.919,3.708-15.778L50.067,51.834C44.832,59.933,42,69.396,42,79.171z"/>
	<path  d="M138.821,132.018l10.225,11.106c30.111-34.933,32.043-86.802,3.752-123.863l-10.344,11.236
		C164.425,61.261,162.71,103.108,138.821,132.018z"/>
	<path  d="M138.163,35.156l-10.489,11.393c13.207,21.676,11.789,49.441-3.386,69.685l10.29,11.177
		C155.881,101.026,157.535,63.294,138.163,35.156z"/>
	<path  d="M130.875,79.171c0-9.775-2.833-19.238-8.067-27.337l-10.642,11.559
		c2.42,4.859,3.708,10.248,3.708,15.778c0,7.463-2.376,14.699-6.671,20.677l10.302,11.19
		C126.794,102.097,130.875,90.831,130.875,79.171z"/>
</g>
</svg>
`),
              routerLink: ['/admin/connectivity'],
            },
            {
              label: 'Billing Reports',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.9424 3.8784H19.3304V18.9648C19.3304 19.9656 18.5144 20.784 17.5112 20.784H6.34164V21.3432C6.34164 21.984 6.86244 22.5024 7.50084 22.5024H19.94C20.5808 22.5024 21.0992 21.984 21.0992 21.3432V5.0352C21.0992 4.3968 20.5832 3.876 19.9424 3.8784ZM17.5088 20.1216C18.1496 20.1216 18.668 19.6032 18.668 18.9624V2.6592C18.668 2.0184 18.1472 1.5 17.5088 1.5H9.22164V1.884C9.22644 1.9248 9.22884 1.9656 9.22884 2.0064V5.184C9.22884 6.084 8.49684 6.8184 7.59444 6.8184H4.41684C4.38324 6.8184 4.34964 6.816 4.31604 6.8136H3.91284V18.9624C3.91284 19.6032 4.43364 20.1216 5.07204 20.1216H17.5088ZM7.46964 9.3144H15.5816C15.884 9.3144 16.1288 9.5592 16.1288 9.8616C16.1288 10.164 15.884 10.4088 15.5816 10.4088H7.46964C7.16724 10.4088 6.92244 10.164 6.92244 9.8616C6.92244 9.5592 7.16724 9.3144 7.46964 9.3144ZM7.46964 12.2856H15.5816C15.884 12.2856 16.1288 12.5304 16.1288 12.8328C16.1288 13.1352 15.884 13.38 15.5816 13.38H7.46964C7.16724 13.38 6.92244 13.1352 6.92244 12.8328C6.92244 12.5304 7.16724 12.2856 7.46964 12.2856ZM6.92004 15.6312C6.92004 15.3288 7.16484 15.084 7.46724 15.084H11.5232C11.8256 15.084 12.0704 15.3288 12.0704 15.6312C12.0704 15.9336 11.8232 16.1784 11.5208 16.1784H7.46484C7.16724 16.1784 6.92004 15.9336 6.92004 15.6312ZM4.41684 6.1608H7.60164C8.13204 6.156 8.56164 5.7264 8.56644 5.196V2.0064C8.56644 1.7136 8.32404 1.5168 8.07444 1.5168C7.95444 1.5168 7.83204 1.56 7.73124 1.6608L4.06884 5.3256C3.76164 5.6328 3.98004 6.1608 4.41684 6.1608Z" />
</svg>
`),
              routerLink: ['/admin/billing-reports'],
            },
          ],
        },
      ];
    }
    else if (userRole && userRole.roleType == 2) {
      this.model = [
        {
          items: [
            {
              label: 'Billing Reports',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.9424 3.8784H19.3304V18.9648C19.3304 19.9656 18.5144 20.784 17.5112 20.784H6.34164V21.3432C6.34164 21.984 6.86244 22.5024 7.50084 22.5024H19.94C20.5808 22.5024 21.0992 21.984 21.0992 21.3432V5.0352C21.0992 4.3968 20.5832 3.876 19.9424 3.8784ZM17.5088 20.1216C18.1496 20.1216 18.668 19.6032 18.668 18.9624V2.6592C18.668 2.0184 18.1472 1.5 17.5088 1.5H9.22164V1.884C9.22644 1.9248 9.22884 1.9656 9.22884 2.0064V5.184C9.22884 6.084 8.49684 6.8184 7.59444 6.8184H4.41684C4.38324 6.8184 4.34964 6.816 4.31604 6.8136H3.91284V18.9624C3.91284 19.6032 4.43364 20.1216 5.07204 20.1216H17.5088ZM7.46964 9.3144H15.5816C15.884 9.3144 16.1288 9.5592 16.1288 9.8616C16.1288 10.164 15.884 10.4088 15.5816 10.4088H7.46964C7.16724 10.4088 6.92244 10.164 6.92244 9.8616C6.92244 9.5592 7.16724 9.3144 7.46964 9.3144ZM7.46964 12.2856H15.5816C15.884 12.2856 16.1288 12.5304 16.1288 12.8328C16.1288 13.1352 15.884 13.38 15.5816 13.38H7.46964C7.16724 13.38 6.92244 13.1352 6.92244 12.8328C6.92244 12.5304 7.16724 12.2856 7.46964 12.2856ZM6.92004 15.6312C6.92004 15.3288 7.16484 15.084 7.46724 15.084H11.5232C11.8256 15.084 12.0704 15.3288 12.0704 15.6312C12.0704 15.9336 11.8232 16.1784 11.5208 16.1784H7.46484C7.16724 16.1784 6.92004 15.9336 6.92004 15.6312ZM4.41684 6.1608H7.60164C8.13204 6.156 8.56164 5.7264 8.56644 5.196V2.0064C8.56644 1.7136 8.32404 1.5168 8.07444 1.5168C7.95444 1.5168 7.83204 1.56 7.73124 1.6608L4.06884 5.3256C3.76164 5.6328 3.98004 6.1608 4.41684 6.1608Z" />
</svg>
`),
              routerLink: ['/admin/billing-reports'],
            },
            {
              label: 'Care Managers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/admin/casemanagers'],
            },
            {
              label: 'Physicians',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.9024 22.9324C21.8064 22.0132 21.7824 21.0868 21.6912 20.1652C21.5544 18.7852 21.3576 17.4124 20.9496 16.0804C20.7288 15.3676 20.3928 14.7484 19.7232 14.3548C19.0824 13.978 18.384 13.7548 17.6832 13.5364C17.4384 13.4596 17.4816 13.6324 17.496 13.7572C17.5608 14.3596 17.5056 14.9548 17.3808 15.5428C17.34 15.7324 17.4048 15.8188 17.568 15.9124C18.2592 16.2988 18.6528 16.8892 18.696 17.6884C18.7392 18.5116 18.5712 19.3204 18.5208 20.1364C18.4872 20.6908 18.4272 21.2476 18.1944 21.7636C17.9136 22.3876 17.4288 22.6348 16.7664 22.5076C16.5144 22.4596 16.3488 22.3444 16.3896 22.066C16.4304 21.7924 16.62 21.6964 16.872 21.7468C17.1888 21.8092 17.3736 21.6604 17.5032 21.4012C17.592 21.2212 17.64 21.0244 17.6592 20.8252C17.7528 19.906 17.8536 18.9868 17.928 18.0652C17.9952 17.2324 17.5488 16.6012 16.8096 16.4332C16.0464 16.2604 15.3888 16.606 15.0744 17.3836C14.8224 18.0052 14.616 18.646 14.376 19.2748C14.2392 19.63 14.1216 19.9876 14.0496 20.3596C13.956 20.842 14.052 21.0196 14.4984 21.2212C14.712 21.3172 14.8152 21.4684 14.7408 21.6964C14.6664 21.9268 14.4984 21.9916 14.268 21.9484C13.6104 21.8236 13.2288 21.3556 13.2408 20.6812C13.2456 20.4052 13.2864 20.1388 13.3632 19.8748C13.6464 18.8956 14.0184 17.9476 14.3976 17.002C14.7384 16.1524 15.4176 15.6844 16.3416 15.6196C16.5216 15.6052 16.5984 15.5548 16.632 15.3796C16.7448 14.7964 16.7616 14.2132 16.7088 13.6228C16.6848 13.3444 16.5768 13.1932 16.2864 13.1476C16.0512 13.1116 15.816 13.0324 15.5952 12.9412C15.3744 12.8524 15.2784 12.8788 15.2016 13.1284C14.9304 14.0356 14.5512 14.902 14.112 15.742C13.9008 16.1476 13.656 16.5364 13.3848 17.0068C13.2768 16.4116 13.1952 15.9052 13.0896 15.4012C13.0416 15.1756 13.0464 14.9716 13.1448 14.758C13.2 14.6356 13.3464 14.4916 13.2 14.3596C12.9672 14.1484 12.7128 13.9636 12.4656 13.7692C12.4584 13.762 12.4248 13.7788 12.4104 13.7908C12.1704 13.9972 11.8968 14.1748 11.7096 14.4196C11.5968 14.566 11.8152 14.758 11.8632 14.9356C11.8728 14.9764 11.8944 15.0196 11.8896 15.058C11.7888 15.6772 11.6832 16.294 11.5704 16.9708C11.4912 16.8772 11.4552 16.8412 11.4312 16.8004C10.7976 15.778 10.2816 14.7028 9.89039 13.5652C9.81119 13.3324 9.81839 12.9724 9.61919 12.898C9.43199 12.8284 9.15119 13.0132 8.91119 13.0804C8.78639 13.1164 8.67599 13.15 8.62799 13.2964C8.53679 13.5796 8.40719 13.8484 8.33999 14.1316C7.89839 15.9748 8.00879 17.7988 8.57759 19.6012C8.66399 19.8748 8.76719 20.0236 9.07919 20.0836C9.68159 20.2036 10.0056 20.8636 9.79919 21.4348C9.58319 22.0372 8.93279 22.3204 8.36639 22.0612C7.79519 21.802 7.55039 21.0988 7.88159 20.5756C8.00159 20.386 7.98719 20.2396 7.92239 20.0524C7.31279 18.3028 7.14959 16.5124 7.43039 14.6788C7.49519 14.2564 7.63679 13.8532 7.73279 13.4068C6.89519 13.6516 6.09119 13.8556 5.36639 14.278C4.83359 14.5876 4.42079 14.9932 4.18799 15.5836C3.90959 16.2916 3.74159 17.026 3.59999 17.77C3.27359 19.498 3.17279 21.2524 3.04079 23.002C3.00479 23.4796 3.21839 23.7748 3.59759 23.998H21.3312C21.7824 23.7844 21.9552 23.4436 21.9024 22.9324ZM8.49119 8.17237C8.65439 8.21557 8.73119 8.29477 8.79359 8.44357C9.16079 9.32197 9.61439 10.1476 10.2888 10.834C10.8216 11.3788 11.4384 11.7868 12.2208 11.866C13.2048 11.9668 13.9728 11.5276 14.6376 10.8628C15.3144 10.1836 15.7848 9.36757 16.152 8.48437C16.2024 8.36437 16.2192 8.21317 16.4016 8.19637C17.0568 8.13397 17.568 7.09717 17.46 6.46597C17.4288 6.28597 17.3616 6.12037 17.1648 6.10837C16.92 6.09157 16.9656 5.96197 17.0016 5.81077C17.1312 5.26837 17.1864 4.71397 17.2128 4.23877C17.2296 3.75637 17.184 3.35557 17.088 2.97157C16.9416 2.39077 16.6416 1.89157 16.0968 1.61317C15.7752 1.44757 15.528 1.26517 15.42 0.907567C15.3792 0.768367 15.2424 0.677167 15.1296 0.583567C14.712 0.237967 14.2176 0.086767 13.704 0.036367C12.1656 -0.114833 10.7304 0.213967 9.46079 1.10917C8.58479 1.72357 7.98239 2.53477 7.85999 3.63877C7.77839 4.36597 7.81919 5.08597 7.95599 5.80357C7.98479 5.95717 8.04719 6.11797 7.77599 6.11317C7.65359 6.11077 7.57439 6.21397 7.53359 6.33397C7.30799 6.97717 7.82879 7.99717 8.49119 8.17237Z" />
</svg>
`),
              routerLink: ['/admin/physicians'],
            },
            {
              label: 'Caregivers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/admin/caregivers'],
            },
            {
              label: 'Patients',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`
<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none"><g clip-path="url(#clip0_859_55773)"><path d="M8.31683 6.69381C8.47763 7.02261 8.65043 7.31062 8.71763 7.65381C8.90483 8.60662 9.33443 9.46342 9.97763 10.1714C10.3856 10.6202 10.3616 11.081 10.2224 11.5442C10.1024 11.945 10.1384 12.2378 10.4048 12.545C10.76 12.9578 11.1152 13.3682 11.6072 13.6298C12.176 13.9346 12.9728 13.8818 13.616 13.3802C14.1416 12.9698 14.5352 12.4442 14.9 11.8922C14.9648 11.7962 14.9384 11.7122 14.9192 11.621C14.8952 11.5082 14.8736 11.3954 14.8496 11.285C14.7512 10.8314 14.8856 10.4714 15.1952 10.1162C15.8144 9.40341 16.2272 8.57302 16.4192 7.64422C16.4888 7.30822 16.6664 7.02981 16.8536 6.73221C16.8488 6.84741 16.8536 6.96501 16.8368 7.08021C16.6592 8.26581 16.7936 9.43702 17.0048 10.5986C17.0936 11.0762 17.2496 11.5466 17.4416 12.0002C17.5544 12.2642 17.8232 12.2594 18.0296 12.3434C18.776 12.641 19.5008 12.977 20.1872 13.3994C20.8088 13.781 21.1232 14.3618 21.344 15.017C21.6896 16.0394 21.992 17.0738 22.1984 18.137C22.424 19.3034 22.5128 20.4746 22.4456 21.6602C22.4144 22.1786 22.088 22.4882 21.6536 22.6514C20.624 23.0378 19.5632 23.3378 18.4832 23.5586C17.8472 23.6882 17.2088 23.7866 16.5752 23.909C16.0952 24.0026 15.6248 23.9906 15.1472 23.993C13.8896 24.0026 12.6296 23.9762 11.372 24.0026C10.1624 24.0266 8.97443 23.8994 7.78403 23.6762C6.55763 23.4482 5.34803 23.1746 4.16723 22.7834C3.83363 22.673 3.50483 22.5482 3.18083 22.4114C2.67923 22.2002 2.60003 21.749 2.56403 21.2978C2.50883 20.5946 2.63603 19.8986 2.71043 19.205C2.81123 18.2714 2.97203 17.345 3.20243 16.433C3.41843 15.5762 3.68003 14.7314 4.15043 13.9802C4.52723 13.3778 5.13683 13.0466 5.79203 12.8066C6.33683 12.6098 6.88403 12.4202 7.42883 12.221C7.55363 12.1754 7.64243 12.0962 7.70003 11.9522C8.02643 11.1146 8.21363 10.253 8.29523 9.35542C8.36723 8.57782 8.40083 7.80742 8.28563 7.03222C8.27363 6.93382 8.25203 6.82821 8.31683 6.69381ZM11.8424 20.5706C11.8424 20.7914 11.852 20.9738 11.84 21.1562C11.828 21.3386 11.8856 21.4178 12.08 21.4082C12.368 21.3962 12.656 21.3962 12.944 21.4082C13.1408 21.4178 13.1912 21.3314 13.1864 21.1538C13.1768 20.8082 13.1936 20.4626 13.1792 20.117C13.172 19.9106 13.232 19.8218 13.4552 19.8314C13.8008 19.8458 14.1464 19.8266 14.492 19.8386C14.6816 19.8458 14.7512 19.769 14.7488 19.5866C14.744 19.2986 14.7368 19.0106 14.7512 18.725C14.7632 18.521 14.6648 18.4826 14.492 18.485C14.1656 18.4922 13.8392 18.4874 13.5128 18.4922C13.2752 18.497 13.1624 18.4058 13.1768 18.149C13.196 17.8154 13.1792 17.477 13.1864 17.141C13.1888 16.9826 13.136 16.9178 12.968 16.9226C12.6704 16.9322 12.3728 16.9298 12.0752 16.9226C11.9072 16.9202 11.8328 16.9754 11.8376 17.1554C11.8472 17.501 11.828 17.8466 11.8448 18.1922C11.8544 18.413 11.7536 18.4826 11.5568 18.4826C11.3072 18.4826 11.0576 18.485 10.808 18.4874C10.2728 18.4946 10.2728 18.497 10.2752 19.0418C10.2776 19.8362 10.2776 19.8314 11.0672 19.8386C11.3144 19.841 11.6288 19.7306 11.7872 19.8914C11.9504 20.0474 11.816 20.3642 11.8424 20.5706ZM9.32723 5.60421C9.00083 5.60421 8.67443 5.59701 8.34803 5.60661C8.12963 5.61381 8.04083 5.53221 8.03363 5.30661C8.01443 4.74981 7.97603 4.18821 8.05283 3.63861C8.23763 2.33061 8.92883 0.866614 10.652 0.341014C11.4272 0.103414 12.2072 -0.0741859 13.0064 0.0290141C14.5088 0.221014 15.8312 0.780214 16.5656 2.21301C17.0384 3.13221 17.036 4.15221 16.9952 5.16261C16.9928 5.25381 17.0072 5.34741 16.8344 5.31381C16.7 5.28741 16.6784 5.46261 16.6568 5.56341C16.6232 5.72181 16.5776 5.74581 16.4264 5.66661C15.7712 5.32341 15.3416 4.78821 15.0776 4.11381C15.0056 3.92901 14.9744 3.89541 14.7896 4.02501C13.592 4.86021 12.26 5.32821 10.8128 5.52021C10.3184 5.58741 9.82403 5.60901 9.32723 5.60421Z"/></g><defs><clipPath id="clip0_859_55773"><rect width="24" height="24" transform="translate(0.5)"/></clipPath></defs></svg>
`),
              routerLink: ['/admin/patients'],
            },
            {
              label: 'Access Control',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.66322 21.4942H21.3464C22.5824 21.4942 23.3792 20.191 22.82 19.0894L13.9784 3.42221C13.3688 2.20301 11.624 2.20301 11.0144 3.42221L2.17282 19.0894C1.63042 20.1886 2.42722 21.4942 3.66322 21.4942ZM13.892 17.851C13.892 18.6646 13.2992 19.291 12.452 19.291C11.6048 19.291 11.012 18.6646 11.012 17.851V17.8174C11.012 17.0038 11.6048 16.3774 12.452 16.3774C13.2992 16.3774 13.892 17.0038 13.892 17.8174V17.851ZM11.6744 7.26701H13.3352C13.7912 7.26701 14.0624 7.65581 14.012 8.14781L13.2824 14.515C13.232 14.9734 12.9272 15.259 12.5024 15.259C12.08 15.259 11.7728 14.971 11.7224 14.515L10.9928 8.14781C10.9472 7.65581 11.2184 7.26701 11.6744 7.26701Z" />
</svg>
`),
              routerLink: ['/admin/access-control'],
            },
            {
              label: 'Device Management',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.1644 9.03112C19.0468 8.75032 19.2652 8.29432 19.7452 7.81192L20.9068 6.65032C21.0724 6.48472 21.1516 6.29752 21.0844 6.23032L20.9644 6.11032L17.8852 3.03352L17.7652 2.91352C17.7652 2.91352 17.5108 2.92792 17.3452 3.09352L16.1836 4.25512C15.6508 4.78552 15.2956 4.86712 15.13 4.86712C15.0076 4.86712 14.9116 4.82632 14.8252 4.73992C14.71 4.62232 14.5132 4.31512 14.5132 3.56392V3.54232C14.5132 3.53032 14.5108 3.33112 14.5108 3.09592V1.91992C14.5108 1.68712 14.3212 1.49512 14.086 1.49512H9.90765C9.67485 1.49512 9.48285 1.68472 9.48285 1.91992V3.56632C9.48285 3.87112 9.43485 4.86472 8.86605 4.86472C8.70045 4.86472 8.34525 4.78552 7.81485 4.25272L6.59565 3.03352L6.47565 2.91352C6.47565 2.91352 6.30045 2.84872 6.23565 2.91352L3.03645 6.11272L2.91645 6.23272C2.91645 6.23272 2.84925 6.40792 2.91645 6.47272L3.03645 6.59272L4.25565 7.81432C4.73565 8.29432 4.95405 8.74792 4.83645 9.03352C4.71885 9.31432 4.24365 9.48472 3.56685 9.48472H1.92285C1.68765 9.48232 1.49805 9.67432 1.49805 9.90712V14.0927C1.49805 14.3255 1.68765 14.5175 1.92285 14.5175H3.56445C4.24365 14.5175 4.71645 14.6855 4.83405 14.9663C4.95165 15.2495 4.73325 15.7031 4.25325 16.1855L3.03405 17.4047L2.91405 17.5247C2.91405 17.5247 2.84685 17.6999 2.91405 17.7647L6.11325 20.9663L6.23325 21.0863C6.23325 21.0863 6.40845 21.1535 6.47325 21.0863L6.59325 20.9663L7.81245 19.7447C8.34285 19.2143 8.70045 19.1327 8.86365 19.1327C9.43245 19.1327 9.48045 20.1263 9.48045 20.4311V22.0775C9.48045 22.3103 9.67005 22.5023 9.90525 22.5023H14.0884C14.3212 22.5023 14.5132 22.3127 14.5132 22.0775V20.9015C14.5132 20.6687 14.5156 20.4695 14.5156 20.4551V20.4335C14.5132 19.6823 14.71 19.3751 14.8276 19.2575C14.914 19.1711 15.01 19.1303 15.1324 19.1303C15.2956 19.1303 15.6532 19.2095 16.186 19.7423L17.4052 20.9639L17.5252 21.0839C17.5252 21.0839 17.7004 21.1511 17.7652 21.0839L20.9668 17.8847L21.0868 17.7647C21.0868 17.7647 21.1516 17.5895 21.0868 17.5247L19.7452 16.1855C19.2652 15.7055 19.0468 15.2495 19.1644 14.9663C19.282 14.6831 19.7572 14.5175 20.4364 14.5175H22.0804C22.3132 14.5175 22.5052 14.3279 22.5052 14.0927V9.90472C22.5052 9.67192 22.3156 9.47992 22.0804 9.47992H20.4412C19.7572 9.48232 19.282 9.31432 19.1644 9.03112ZM16.7428 11.9999C16.7428 14.6135 14.6164 16.7423 12.0004 16.7423C9.38445 16.7423 7.25805 14.6135 7.25805 11.9999C7.25805 9.38632 9.38685 7.25752 12.0004 7.25752C14.614 7.25752 16.7428 9.38392 16.7428 11.9999Z"/>
</svg>
`),
              routerLink: ['/admin/medical-devices'],
            },
            {
              label: 'Connectivity',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg height="24" width="25" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 172.874 172.874" xml:space="preserve">
<g>
	<path  d="M100.806,81.731c0-8.415-6.822-15.237-15.237-15.237c-8.415,0-15.237,6.822-15.237,15.237
		c0,5.667,3.101,10.602,7.692,13.227l0.122,51.174c0.01,4.136,3.366,7.482,7.5,7.482c0.006,0,0.012,0,0.018,0
		c4.143-0.01,7.492-3.376,7.482-7.518l-0.122-51.083C97.665,92.401,100.806,87.436,100.806,81.731z"/>
	<path  d="M30.421,30.496L20.077,19.261c-28.291,37.061-26.359,88.93,3.752,123.863l10.225-11.106
		C10.165,103.108,8.449,61.261,30.421,30.496z"/>
	<path  d="M45.2,46.549L34.711,35.156c-19.371,28.138-17.718,65.87,3.585,92.254l10.29-11.177
		C33.411,95.99,31.993,68.224,45.2,46.549z"/>
	<path  d="M42,79.171c0,11.66,4.081,22.926,11.369,31.867l10.302-11.19C59.376,93.87,57,86.634,57,79.171
		c0-5.53,1.288-10.919,3.708-15.778L50.067,51.834C44.832,59.933,42,69.396,42,79.171z"/>
	<path  d="M138.821,132.018l10.225,11.106c30.111-34.933,32.043-86.802,3.752-123.863l-10.344,11.236
		C164.425,61.261,162.71,103.108,138.821,132.018z"/>
	<path  d="M138.163,35.156l-10.489,11.393c13.207,21.676,11.789,49.441-3.386,69.685l10.29,11.177
		C155.881,101.026,157.535,63.294,138.163,35.156z"/>
	<path  d="M130.875,79.171c0-9.775-2.833-19.238-8.067-27.337l-10.642,11.559
		c2.42,4.859,3.708,10.248,3.708,15.778c0,7.463-2.376,14.699-6.671,20.677l10.302,11.19
		C126.794,102.097,130.875,90.831,130.875,79.171z"/>
</g>
</svg>
`),
              routerLink: ['/admin/connectivity'],
            },

            {
              label: 'Templates',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.8505 1.49756H4.6401C4.4889 1.49756 4.3641 1.62236 4.3641 1.77356V3.96236C4.3641 4.11356 4.4889 4.23836 4.6401 4.23836H6.8217C6.9729 4.23836 7.0977 4.36316 7.0977 4.51436V6.72956C7.0977 6.88076 6.9729 7.00556 6.8217 7.00556H4.6377C4.4865 7.00556 4.3617 7.13036 4.3617 7.28156V8.36156C4.3617 8.51276 4.4865 8.63756 4.6377 8.63756H6.8265C6.9777 8.63756 7.1025 8.76236 7.1025 8.91356V11.1264C7.1025 11.2776 6.9777 11.4024 6.8265 11.4024H4.6377C4.4865 11.4024 4.3617 11.5272 4.3617 11.6784V12.7536C4.3617 12.9048 4.4865 13.0296 4.6377 13.0296H6.8313C6.9825 13.0296 7.1073 13.1544 7.1073 13.3056V15.5208C7.1073 15.672 6.9825 15.7968 6.8313 15.7968H4.6449C4.4937 15.7968 4.3689 15.9216 4.3689 16.0728V17.16C4.3689 17.3112 4.4937 17.436 4.6449 17.436H6.8289C6.9801 17.436 7.1049 17.5608 7.1049 17.712V19.9248C7.1049 20.076 6.9801 20.2008 6.8289 20.2008H4.6377C4.4865 20.2008 4.3617 20.3256 4.3617 20.4768V22.2336C4.3617 22.3848 4.4865 22.5096 4.6377 22.5096H21.8505C22.0017 22.5096 22.1265 22.3848 22.1265 22.2336V1.77356C22.1265 1.62236 22.0017 1.49756 21.8505 1.49756ZM3.1497 10.5744H6.0297C6.1809 10.5744 6.3057 10.4496 6.3057 10.2984V9.71756C6.3057 9.56636 6.1809 9.44156 6.0297 9.44156H3.1497C2.9985 9.44156 2.8737 9.56636 2.8737 9.71756V10.2984C2.8737 10.4496 2.9985 10.5744 3.1497 10.5744ZM3.1473 14.9688H6.0345C6.1857 14.9688 6.3105 14.844 6.3105 14.6928V14.1216C6.3105 13.9704 6.1857 13.8456 6.0345 13.8456H3.1473C2.9961 13.8456 2.8713 13.9704 2.8713 14.1216V14.6928C2.8713 14.8464 2.9961 14.9688 3.1473 14.9688ZM3.1521 6.15356H6.0369C6.1881 6.15356 6.3129 6.02876 6.3129 5.87756V5.32556C6.3129 5.17436 6.1881 5.04956 6.0369 5.04956H3.1521C3.0009 5.04956 2.8761 5.17436 2.8761 5.32556V5.87756C2.8785 6.03116 2.9985 6.15356 3.1521 6.15356ZM6.3057 19.0896V18.54C6.3057 18.3888 6.1809 18.264 6.0297 18.264H3.1449C2.9937 18.264 2.8689 18.3888 2.8689 18.54V19.0896C2.8689 19.2408 2.9937 19.3656 3.1449 19.3656H6.0297C6.1809 19.3656 6.3057 19.2408 6.3057 19.0896Z"/>
</svg>

`),
              routerLink: ['/admin/templates'],
            },
          ],
        },
      ];
    }
    else if (userRole && userRole.roleType == 3) {
      this.model = [
        {
          items: [
            {
              label: 'Dashboard',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.3288 3.30939C6.1784 3.30939 2 7.48539 2 12.6382C2 17.791 6.176 21.9694 11.3288 21.9694C16.4816 21.9694 20.6576 17.7934 20.6576 12.6382H11.3288V3.30939ZM23 11.5702C23 8.99499 21.956 6.66219 20.2688 4.97499L13.6712 11.5702H23ZM12.5096 2.02539V11.3518L19.1048 4.75659C17.2832 2.93499 14.8952 2.02539 12.5096 2.02539Z" />
              </svg>`),
              routerLink: ['/dashboard'],
            },
            {
              label: 'Patients',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<?xml version="1.0"?>
<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none"><g clip-path="url(#clip0_859_55773)"><path d="M8.31683 6.69381C8.47763 7.02261 8.65043 7.31062 8.71763 7.65381C8.90483 8.60662 9.33443 9.46342 9.97763 10.1714C10.3856 10.6202 10.3616 11.081 10.2224 11.5442C10.1024 11.945 10.1384 12.2378 10.4048 12.545C10.76 12.9578 11.1152 13.3682 11.6072 13.6298C12.176 13.9346 12.9728 13.8818 13.616 13.3802C14.1416 12.9698 14.5352 12.4442 14.9 11.8922C14.9648 11.7962 14.9384 11.7122 14.9192 11.621C14.8952 11.5082 14.8736 11.3954 14.8496 11.285C14.7512 10.8314 14.8856 10.4714 15.1952 10.1162C15.8144 9.40341 16.2272 8.57302 16.4192 7.64422C16.4888 7.30822 16.6664 7.02981 16.8536 6.73221C16.8488 6.84741 16.8536 6.96501 16.8368 7.08021C16.6592 8.26581 16.7936 9.43702 17.0048 10.5986C17.0936 11.0762 17.2496 11.5466 17.4416 12.0002C17.5544 12.2642 17.8232 12.2594 18.0296 12.3434C18.776 12.641 19.5008 12.977 20.1872 13.3994C20.8088 13.781 21.1232 14.3618 21.344 15.017C21.6896 16.0394 21.992 17.0738 22.1984 18.137C22.424 19.3034 22.5128 20.4746 22.4456 21.6602C22.4144 22.1786 22.088 22.4882 21.6536 22.6514C20.624 23.0378 19.5632 23.3378 18.4832 23.5586C17.8472 23.6882 17.2088 23.7866 16.5752 23.909C16.0952 24.0026 15.6248 23.9906 15.1472 23.993C13.8896 24.0026 12.6296 23.9762 11.372 24.0026C10.1624 24.0266 8.97443 23.8994 7.78403 23.6762C6.55763 23.4482 5.34803 23.1746 4.16723 22.7834C3.83363 22.673 3.50483 22.5482 3.18083 22.4114C2.67923 22.2002 2.60003 21.749 2.56403 21.2978C2.50883 20.5946 2.63603 19.8986 2.71043 19.205C2.81123 18.2714 2.97203 17.345 3.20243 16.433C3.41843 15.5762 3.68003 14.7314 4.15043 13.9802C4.52723 13.3778 5.13683 13.0466 5.79203 12.8066C6.33683 12.6098 6.88403 12.4202 7.42883 12.221C7.55363 12.1754 7.64243 12.0962 7.70003 11.9522C8.02643 11.1146 8.21363 10.253 8.29523 9.35542C8.36723 8.57782 8.40083 7.80742 8.28563 7.03222C8.27363 6.93382 8.25203 6.82821 8.31683 6.69381ZM11.8424 20.5706C11.8424 20.7914 11.852 20.9738 11.84 21.1562C11.828 21.3386 11.8856 21.4178 12.08 21.4082C12.368 21.3962 12.656 21.3962 12.944 21.4082C13.1408 21.4178 13.1912 21.3314 13.1864 21.1538C13.1768 20.8082 13.1936 20.4626 13.1792 20.117C13.172 19.9106 13.232 19.8218 13.4552 19.8314C13.8008 19.8458 14.1464 19.8266 14.492 19.8386C14.6816 19.8458 14.7512 19.769 14.7488 19.5866C14.744 19.2986 14.7368 19.0106 14.7512 18.725C14.7632 18.521 14.6648 18.4826 14.492 18.485C14.1656 18.4922 13.8392 18.4874 13.5128 18.4922C13.2752 18.497 13.1624 18.4058 13.1768 18.149C13.196 17.8154 13.1792 17.477 13.1864 17.141C13.1888 16.9826 13.136 16.9178 12.968 16.9226C12.6704 16.9322 12.3728 16.9298 12.0752 16.9226C11.9072 16.9202 11.8328 16.9754 11.8376 17.1554C11.8472 17.501 11.828 17.8466 11.8448 18.1922C11.8544 18.413 11.7536 18.4826 11.5568 18.4826C11.3072 18.4826 11.0576 18.485 10.808 18.4874C10.2728 18.4946 10.2728 18.497 10.2752 19.0418C10.2776 19.8362 10.2776 19.8314 11.0672 19.8386C11.3144 19.841 11.6288 19.7306 11.7872 19.8914C11.9504 20.0474 11.816 20.3642 11.8424 20.5706ZM9.32723 5.60421C9.00083 5.60421 8.67443 5.59701 8.34803 5.60661C8.12963 5.61381 8.04083 5.53221 8.03363 5.30661C8.01443 4.74981 7.97603 4.18821 8.05283 3.63861C8.23763 2.33061 8.92883 0.866614 10.652 0.341014C11.4272 0.103414 12.2072 -0.0741859 13.0064 0.0290141C14.5088 0.221014 15.8312 0.780214 16.5656 2.21301C17.0384 3.13221 17.036 4.15221 16.9952 5.16261C16.9928 5.25381 17.0072 5.34741 16.8344 5.31381C16.7 5.28741 16.6784 5.46261 16.6568 5.56341C16.6232 5.72181 16.5776 5.74581 16.4264 5.66661C15.7712 5.32341 15.3416 4.78821 15.0776 4.11381C15.0056 3.92901 14.9744 3.89541 14.7896 4.02501C13.592 4.86021 12.26 5.32821 10.8128 5.52021C10.3184 5.58741 9.82403 5.60901 9.32723 5.60421Z"/></g><defs><clipPath id="clip0_859_55773"><rect width="24" height="24" transform="translate(0.5)"/></clipPath></defs></svg>
`),
              routerLink: ['/patients'],
            },
            {
              label: 'Care Managers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/care-managers'],
            },

            {
              label: 'Alerts',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.66322 21.4942H21.3464C22.5824 21.4942 23.3792 20.191 22.82 19.0894L13.9784 3.42221C13.3688 2.20301 11.624 2.20301 11.0144 3.42221L2.17282 19.0894C1.63042 20.1886 2.42722 21.4942 3.66322 21.4942ZM13.892 17.851C13.892 18.6646 13.2992 19.291 12.452 19.291C11.6048 19.291 11.012 18.6646 11.012 17.851V17.8174C11.012 17.0038 11.6048 16.3774 12.452 16.3774C13.2992 16.3774 13.892 17.0038 13.892 17.8174V17.851ZM11.6744 7.26701H13.3352C13.7912 7.26701 14.0624 7.65581 14.012 8.14781L13.2824 14.515C13.232 14.9734 12.9272 15.259 12.5024 15.259C12.08 15.259 11.7728 14.971 11.7224 14.515L10.9928 8.14781C10.9472 7.65581 11.2184 7.26701 11.6744 7.26701Z" />
</svg>
`),
              routerLink: ['/patient-alerts'],
            },
            {
              label: 'Reports',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.9424 3.8784H19.3304V18.9648C19.3304 19.9656 18.5144 20.784 17.5112 20.784H6.34164V21.3432C6.34164 21.984 6.86244 22.5024 7.50084 22.5024H19.94C20.5808 22.5024 21.0992 21.984 21.0992 21.3432V5.0352C21.0992 4.3968 20.5832 3.876 19.9424 3.8784ZM17.5088 20.1216C18.1496 20.1216 18.668 19.6032 18.668 18.9624V2.6592C18.668 2.0184 18.1472 1.5 17.5088 1.5H9.22164V1.884C9.22644 1.9248 9.22884 1.9656 9.22884 2.0064V5.184C9.22884 6.084 8.49684 6.8184 7.59444 6.8184H4.41684C4.38324 6.8184 4.34964 6.816 4.31604 6.8136H3.91284V18.9624C3.91284 19.6032 4.43364 20.1216 5.07204 20.1216H17.5088ZM7.46964 9.3144H15.5816C15.884 9.3144 16.1288 9.5592 16.1288 9.8616C16.1288 10.164 15.884 10.4088 15.5816 10.4088H7.46964C7.16724 10.4088 6.92244 10.164 6.92244 9.8616C6.92244 9.5592 7.16724 9.3144 7.46964 9.3144ZM7.46964 12.2856H15.5816C15.884 12.2856 16.1288 12.5304 16.1288 12.8328C16.1288 13.1352 15.884 13.38 15.5816 13.38H7.46964C7.16724 13.38 6.92244 13.1352 6.92244 12.8328C6.92244 12.5304 7.16724 12.2856 7.46964 12.2856ZM6.92004 15.6312C6.92004 15.3288 7.16484 15.084 7.46724 15.084H11.5232C11.8256 15.084 12.0704 15.3288 12.0704 15.6312C12.0704 15.9336 11.8232 16.1784 11.5208 16.1784H7.46484C7.16724 16.1784 6.92004 15.9336 6.92004 15.6312ZM4.41684 6.1608H7.60164C8.13204 6.156 8.56164 5.7264 8.56644 5.196V2.0064C8.56644 1.7136 8.32404 1.5168 8.07444 1.5168C7.95444 1.5168 7.83204 1.56 7.73124 1.6608L4.06884 5.3256C3.76164 5.6328 3.98004 6.1608 4.41684 6.1608Z" />
</svg>
`),
              routerLink: ['/reports'],
            },

            {
              label: 'Task Board',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="icon / icofont / web / action / calendar">
<path id="Vector" d="M11.8177 2.45411C11.8177 2.24771 11.8209 2.05411 11.8177 1.85731C11.8145 1.48131 11.5761 1.16611 11.2081 1.04611C10.8465 0.927713 10.4545 1.04931 10.2257 1.34531C10.0929 1.51491 10.0369 1.71011 10.0369 1.92291V2.44451C10.0369 2.52771 9.96814 2.59491 9.88494 2.59491H8.94254C8.85774 2.59491 8.79054 2.52771 8.79054 2.44451C8.79054 2.26531 8.79214 2.09251 8.79054 1.91811C8.78414 1.50851 8.56974 1.18851 8.21614 1.05731C7.63534 0.842913 7.02254 1.24771 7.00814 1.86211C7.00334 2.05411 7.00494 2.24771 7.00654 2.44131C7.00654 2.52451 6.93774 2.59171 6.85454 2.59171H5.88654C5.80174 2.59171 5.73454 2.52451 5.73454 2.44131C5.74094 2.19651 5.75214 1.95811 5.72654 1.72451C5.68974 1.38531 5.46894 1.15811 5.14414 1.04771C4.82414 0.940513 4.52814 1.00131 4.26894 1.22051C4.03854 1.41411 3.96014 1.67011 3.96334 1.95811C3.96494 2.12131 3.96494 2.28451 3.96494 2.45571C3.96494 2.53891 3.89614 2.60611 3.81294 2.60611H1.17934C1.09454 2.60611 1.02734 2.67331 1.02734 2.75651V14.8477C1.02734 14.9309 1.09614 14.9981 1.17934 14.9981H14.8177C14.9025 14.9981 14.9697 14.9309 14.9697 14.8477V2.75491C14.9697 2.67171 14.9009 2.60451 14.8177 2.60451H11.9697C11.8849 2.60451 11.8161 2.53731 11.8177 2.45411ZM5.20814 14.4029H1.78734C1.70254 14.4029 1.63534 14.3357 1.63534 14.2525V11.8605C1.63534 11.7773 1.70414 11.7101 1.78734 11.7101H5.20814C5.29294 11.7101 5.36014 11.7773 5.36014 11.8605V14.2509C5.36014 14.3357 5.29294 14.4029 5.20814 14.4029ZM5.20974 11.0957H1.78734C1.70254 11.0957 1.63534 11.0285 1.63534 10.9453V8.41731C1.63534 8.33411 1.70414 8.26691 1.78734 8.26691H5.20974C5.29454 8.26691 5.36174 8.33411 5.36174 8.41731V10.9453C5.36174 11.0285 5.29294 11.0957 5.20974 11.0957ZM5.21134 7.65571H1.78734C1.70254 7.65571 1.63534 7.58851 1.63534 7.50531V5.07811C1.63534 4.99491 1.70414 4.92771 1.78734 4.92771H5.21134C5.29614 4.92771 5.36334 4.99491 5.36334 5.07811V7.50531C5.36334 7.58851 5.29614 7.65571 5.21134 7.65571ZM9.76814 14.4045H6.14894C6.06414 14.4045 5.99694 14.3373 5.99694 14.2541V11.8685C5.99694 11.7853 6.06574 11.7181 6.14894 11.7181H9.76814C9.85294 11.7181 9.92014 11.7853 9.92014 11.8685V14.2557C9.92174 14.3373 9.85294 14.4045 9.76814 14.4045ZM9.92814 11.0141C9.92494 11.0605 9.85614 11.0973 9.77134 11.0973H6.14894C6.06414 11.0973 5.99694 11.0301 5.99694 10.9469V8.41731C5.99694 8.33411 6.03694 8.26531 6.08334 8.26211L6.16974 8.25731C7.36174 8.25731 8.55214 8.26051 9.74254 8.25411C9.88654 8.25411 9.93614 8.28611 9.93454 8.43811C9.92814 9.26851 9.93134 10.1021 9.92974 10.9325C9.93134 10.9597 9.92974 10.9853 9.92814 11.0141ZM9.93294 7.56611C9.93294 7.61251 9.86414 7.65091 9.78094 7.65091H6.14574C6.06094 7.65091 5.99374 7.58371 5.99374 7.50051V5.06851C5.99374 4.98531 6.03374 4.91811 6.08334 4.91811H6.17294C7.36494 4.91811 8.55534 4.91971 9.74574 4.91491C9.88974 4.91331 9.93774 4.94851 9.93454 5.09891C9.92814 5.89411 9.93134 6.68611 9.93134 7.48131V7.56611H9.93294ZM10.6977 8.27171H14.2033C14.2881 8.27171 14.3553 8.33891 14.3553 8.42211V10.9485C14.3553 11.0317 14.2865 11.0989 14.2033 11.0989H10.6977C10.6129 11.0989 10.5457 11.0317 10.5457 10.9485V8.42051C10.5457 8.33891 10.6145 8.27171 10.6977 8.27171ZM14.2049 14.4029H10.6977C10.6129 14.4029 10.5457 14.3357 10.5457 14.2525V11.8653C10.5457 11.7821 10.6145 11.7149 10.6977 11.7149H14.2049C14.2897 11.7149 14.3569 11.7821 14.3569 11.8653V14.2525C14.3601 14.3357 14.2913 14.4029 14.2049 14.4029ZM14.2049 7.65091H10.6977C10.6129 7.65091 10.5457 7.58371 10.5457 7.50051V5.07971C10.5457 4.99651 10.6145 4.92931 10.6977 4.92931H14.2049C14.2897 4.92931 14.3569 4.99651 14.3569 5.07971V7.50051C14.3601 7.58371 14.2913 7.65091 14.2049 7.65091Z"/>
</g>
</svg>
`),
              routerLink: ['/taskboard'],
            },
            {
              label: 'Billing Reports',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.9424 3.8784H19.3304V18.9648C19.3304 19.9656 18.5144 20.784 17.5112 20.784H6.34164V21.3432C6.34164 21.984 6.86244 22.5024 7.50084 22.5024H19.94C20.5808 22.5024 21.0992 21.984 21.0992 21.3432V5.0352C21.0992 4.3968 20.5832 3.876 19.9424 3.8784ZM17.5088 20.1216C18.1496 20.1216 18.668 19.6032 18.668 18.9624V2.6592C18.668 2.0184 18.1472 1.5 17.5088 1.5H9.22164V1.884C9.22644 1.9248 9.22884 1.9656 9.22884 2.0064V5.184C9.22884 6.084 8.49684 6.8184 7.59444 6.8184H4.41684C4.38324 6.8184 4.34964 6.816 4.31604 6.8136H3.91284V18.9624C3.91284 19.6032 4.43364 20.1216 5.07204 20.1216H17.5088ZM7.46964 9.3144H15.5816C15.884 9.3144 16.1288 9.5592 16.1288 9.8616C16.1288 10.164 15.884 10.4088 15.5816 10.4088H7.46964C7.16724 10.4088 6.92244 10.164 6.92244 9.8616C6.92244 9.5592 7.16724 9.3144 7.46964 9.3144ZM7.46964 12.2856H15.5816C15.884 12.2856 16.1288 12.5304 16.1288 12.8328C16.1288 13.1352 15.884 13.38 15.5816 13.38H7.46964C7.16724 13.38 6.92244 13.1352 6.92244 12.8328C6.92244 12.5304 7.16724 12.2856 7.46964 12.2856ZM6.92004 15.6312C6.92004 15.3288 7.16484 15.084 7.46724 15.084H11.5232C11.8256 15.084 12.0704 15.3288 12.0704 15.6312C12.0704 15.9336 11.8232 16.1784 11.5208 16.1784H7.46484C7.16724 16.1784 6.92004 15.9336 6.92004 15.6312ZM4.41684 6.1608H7.60164C8.13204 6.156 8.56164 5.7264 8.56644 5.196V2.0064C8.56644 1.7136 8.32404 1.5168 8.07444 1.5168C7.95444 1.5168 7.83204 1.56 7.73124 1.6608L4.06884 5.3256C3.76164 5.6328 3.98004 6.1608 4.41684 6.1608Z" />
</svg>
`),
              routerLink: ['/admin/billing-reports'],
            },
          ],
        },
      ];
    }
    else {
      this.model = [
        {
          items: [
            {
              label: 'Dashboard',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11.3288 3.30939C6.1784 3.30939 2 7.48539 2 12.6382C2 17.791 6.176 21.9694 11.3288 21.9694C16.4816 21.9694 20.6576 17.7934 20.6576 12.6382H11.3288V3.30939ZM23 11.5702C23 8.99499 21.956 6.66219 20.2688 4.97499L13.6712 11.5702H23ZM12.5096 2.02539V11.3518L19.1048 4.75659C17.2832 2.93499 14.8952 2.02539 12.5096 2.02539Z" />
              </svg>`),
              routerLink: ['/dashboard'],
            },
            {
              label: 'Patients',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<?xml version="1.0"?>
<svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none"><g clip-path="url(#clip0_859_55773)"><path d="M8.31683 6.69381C8.47763 7.02261 8.65043 7.31062 8.71763 7.65381C8.90483 8.60662 9.33443 9.46342 9.97763 10.1714C10.3856 10.6202 10.3616 11.081 10.2224 11.5442C10.1024 11.945 10.1384 12.2378 10.4048 12.545C10.76 12.9578 11.1152 13.3682 11.6072 13.6298C12.176 13.9346 12.9728 13.8818 13.616 13.3802C14.1416 12.9698 14.5352 12.4442 14.9 11.8922C14.9648 11.7962 14.9384 11.7122 14.9192 11.621C14.8952 11.5082 14.8736 11.3954 14.8496 11.285C14.7512 10.8314 14.8856 10.4714 15.1952 10.1162C15.8144 9.40341 16.2272 8.57302 16.4192 7.64422C16.4888 7.30822 16.6664 7.02981 16.8536 6.73221C16.8488 6.84741 16.8536 6.96501 16.8368 7.08021C16.6592 8.26581 16.7936 9.43702 17.0048 10.5986C17.0936 11.0762 17.2496 11.5466 17.4416 12.0002C17.5544 12.2642 17.8232 12.2594 18.0296 12.3434C18.776 12.641 19.5008 12.977 20.1872 13.3994C20.8088 13.781 21.1232 14.3618 21.344 15.017C21.6896 16.0394 21.992 17.0738 22.1984 18.137C22.424 19.3034 22.5128 20.4746 22.4456 21.6602C22.4144 22.1786 22.088 22.4882 21.6536 22.6514C20.624 23.0378 19.5632 23.3378 18.4832 23.5586C17.8472 23.6882 17.2088 23.7866 16.5752 23.909C16.0952 24.0026 15.6248 23.9906 15.1472 23.993C13.8896 24.0026 12.6296 23.9762 11.372 24.0026C10.1624 24.0266 8.97443 23.8994 7.78403 23.6762C6.55763 23.4482 5.34803 23.1746 4.16723 22.7834C3.83363 22.673 3.50483 22.5482 3.18083 22.4114C2.67923 22.2002 2.60003 21.749 2.56403 21.2978C2.50883 20.5946 2.63603 19.8986 2.71043 19.205C2.81123 18.2714 2.97203 17.345 3.20243 16.433C3.41843 15.5762 3.68003 14.7314 4.15043 13.9802C4.52723 13.3778 5.13683 13.0466 5.79203 12.8066C6.33683 12.6098 6.88403 12.4202 7.42883 12.221C7.55363 12.1754 7.64243 12.0962 7.70003 11.9522C8.02643 11.1146 8.21363 10.253 8.29523 9.35542C8.36723 8.57782 8.40083 7.80742 8.28563 7.03222C8.27363 6.93382 8.25203 6.82821 8.31683 6.69381ZM11.8424 20.5706C11.8424 20.7914 11.852 20.9738 11.84 21.1562C11.828 21.3386 11.8856 21.4178 12.08 21.4082C12.368 21.3962 12.656 21.3962 12.944 21.4082C13.1408 21.4178 13.1912 21.3314 13.1864 21.1538C13.1768 20.8082 13.1936 20.4626 13.1792 20.117C13.172 19.9106 13.232 19.8218 13.4552 19.8314C13.8008 19.8458 14.1464 19.8266 14.492 19.8386C14.6816 19.8458 14.7512 19.769 14.7488 19.5866C14.744 19.2986 14.7368 19.0106 14.7512 18.725C14.7632 18.521 14.6648 18.4826 14.492 18.485C14.1656 18.4922 13.8392 18.4874 13.5128 18.4922C13.2752 18.497 13.1624 18.4058 13.1768 18.149C13.196 17.8154 13.1792 17.477 13.1864 17.141C13.1888 16.9826 13.136 16.9178 12.968 16.9226C12.6704 16.9322 12.3728 16.9298 12.0752 16.9226C11.9072 16.9202 11.8328 16.9754 11.8376 17.1554C11.8472 17.501 11.828 17.8466 11.8448 18.1922C11.8544 18.413 11.7536 18.4826 11.5568 18.4826C11.3072 18.4826 11.0576 18.485 10.808 18.4874C10.2728 18.4946 10.2728 18.497 10.2752 19.0418C10.2776 19.8362 10.2776 19.8314 11.0672 19.8386C11.3144 19.841 11.6288 19.7306 11.7872 19.8914C11.9504 20.0474 11.816 20.3642 11.8424 20.5706ZM9.32723 5.60421C9.00083 5.60421 8.67443 5.59701 8.34803 5.60661C8.12963 5.61381 8.04083 5.53221 8.03363 5.30661C8.01443 4.74981 7.97603 4.18821 8.05283 3.63861C8.23763 2.33061 8.92883 0.866614 10.652 0.341014C11.4272 0.103414 12.2072 -0.0741859 13.0064 0.0290141C14.5088 0.221014 15.8312 0.780214 16.5656 2.21301C17.0384 3.13221 17.036 4.15221 16.9952 5.16261C16.9928 5.25381 17.0072 5.34741 16.8344 5.31381C16.7 5.28741 16.6784 5.46261 16.6568 5.56341C16.6232 5.72181 16.5776 5.74581 16.4264 5.66661C15.7712 5.32341 15.3416 4.78821 15.0776 4.11381C15.0056 3.92901 14.9744 3.89541 14.7896 4.02501C13.592 4.86021 12.26 5.32821 10.8128 5.52021C10.3184 5.58741 9.82403 5.60901 9.32723 5.60421Z"/></g><defs><clipPath id="clip0_859_55773"><rect width="24" height="24" transform="translate(0.5)"/></clipPath></defs></svg>
`),
              routerLink: ['/patients'],
            },
            {
              label: 'Care Managers',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M11.936 0H12.6848C12.7832 0.0599998 12.872 0.1512 12.9776 0.1776C14.7512 0.607198 16.5224 1.0512 18.3056 1.4352C18.8216 1.5456 18.9776 1.7616 18.9032 2.25599C18.7808 3.08399 18.7088 3.92159 18.584 4.74959C18.344 6.33838 18.056 7.89358 18.8096 9.47038C19.2848 10.4664 19.472 11.5944 19.8584 12.6384C20.0504 13.1544 19.952 13.3968 19.4384 13.5216C18.536 13.74 17.6384 13.968 16.7384 14.1864C16.2128 14.3136 15.6848 14.4264 15.6728 15.1368C15.6584 16.0272 15.4208 17.1792 16.304 17.5944C17.336 18.0792 18.2288 18.8232 19.3448 19.1472C20.4128 19.4568 21.4448 19.896 22.5008 20.2487C23.4704 20.5727 24.0296 21.1607 23.9432 22.2431C23.8952 22.8383 23.996 23.4239 24.116 24.0023H0.872009C1.00641 23.3903 1.09041 22.7711 1.04721 22.1447C0.982409 21.1847 1.47921 20.6135 2.32161 20.3063C4.21041 19.6175 6.14241 19.0656 7.88961 17.9856C9.06321 17.2608 9.46881 16.7688 9.34641 15.2904C9.29841 14.7096 9.06801 14.3688 8.48961 14.2464C7.64001 14.0688 6.79761 13.8528 5.94561 13.6752C5.14641 13.5096 4.84881 13.1808 5.23041 12.324C5.85201 10.9272 6.37761 9.45598 6.30801 7.91998C6.23121 6.18718 6.02001 4.44239 5.66721 2.74559C5.46081 1.7496 5.80641 1.5 6.63441 1.3104C8.40801 0.904798 10.2104 0.609598 11.936 0ZM12.2288 6.94078C13.7888 6.94078 15.3464 6.93118 16.904 6.94798C17.3408 6.95278 17.552 6.87358 17.6024 6.35998C17.72 5.18879 17.9312 4.02719 18.116 2.86319C18.2 2.33279 18.0296 2.12159 17.4608 2.00879C15.9464 1.704 14.4488 1.3056 12.9488 0.933598C12.4928 0.820798 12.0584 0.808798 11.5904 0.928798C10.0928 1.3152 8.59281 1.692 7.07841 2.00879C6.55041 2.11919 6.34401 2.22479 6.45681 2.83679C6.66561 3.96479 6.76401 5.11199 6.88161 6.25438C6.93681 6.80158 7.19361 6.96718 7.73841 6.95278C9.23601 6.91678 10.7312 6.94078 12.2288 6.94078ZM14.1632 3.81359C14.2184 4.25519 14.1632 4.56479 13.6544 4.49519C13.0856 4.41599 12.7592 4.57919 12.8648 5.23439C12.9344 5.66639 12.6704 5.75759 12.3008 5.74799C11.9552 5.74079 11.6456 5.69999 11.696 5.25599C11.7656 4.63199 11.4992 4.40639 10.8992 4.49279C10.4336 4.55999 10.412 4.25519 10.4216 3.91439C10.4312 3.59999 10.3784 3.24719 10.8584 3.27599C11.4152 3.30959 11.7704 3.16559 11.6912 2.50559C11.636 2.03759 11.9672 2.04959 12.296 2.03999C12.656 2.03039 12.9272 2.09279 12.8624 2.53679C12.776 3.11759 13.0304 3.32879 13.5944 3.27359C14.0144 3.23279 14.2784 3.38159 14.1632 3.81359Z"/>
</svg>
`),
              routerLink: ['/care-managers'],
            },
            {
              label: 'Physicians',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M21.9024 22.9324C21.8064 22.0132 21.7824 21.0868 21.6912 20.1652C21.5544 18.7852 21.3576 17.4124 20.9496 16.0804C20.7288 15.3676 20.3928 14.7484 19.7232 14.3548C19.0824 13.978 18.384 13.7548 17.6832 13.5364C17.4384 13.4596 17.4816 13.6324 17.496 13.7572C17.5608 14.3596 17.5056 14.9548 17.3808 15.5428C17.34 15.7324 17.4048 15.8188 17.568 15.9124C18.2592 16.2988 18.6528 16.8892 18.696 17.6884C18.7392 18.5116 18.5712 19.3204 18.5208 20.1364C18.4872 20.6908 18.4272 21.2476 18.1944 21.7636C17.9136 22.3876 17.4288 22.6348 16.7664 22.5076C16.5144 22.4596 16.3488 22.3444 16.3896 22.066C16.4304 21.7924 16.62 21.6964 16.872 21.7468C17.1888 21.8092 17.3736 21.6604 17.5032 21.4012C17.592 21.2212 17.64 21.0244 17.6592 20.8252C17.7528 19.906 17.8536 18.9868 17.928 18.0652C17.9952 17.2324 17.5488 16.6012 16.8096 16.4332C16.0464 16.2604 15.3888 16.606 15.0744 17.3836C14.8224 18.0052 14.616 18.646 14.376 19.2748C14.2392 19.63 14.1216 19.9876 14.0496 20.3596C13.956 20.842 14.052 21.0196 14.4984 21.2212C14.712 21.3172 14.8152 21.4684 14.7408 21.6964C14.6664 21.9268 14.4984 21.9916 14.268 21.9484C13.6104 21.8236 13.2288 21.3556 13.2408 20.6812C13.2456 20.4052 13.2864 20.1388 13.3632 19.8748C13.6464 18.8956 14.0184 17.9476 14.3976 17.002C14.7384 16.1524 15.4176 15.6844 16.3416 15.6196C16.5216 15.6052 16.5984 15.5548 16.632 15.3796C16.7448 14.7964 16.7616 14.2132 16.7088 13.6228C16.6848 13.3444 16.5768 13.1932 16.2864 13.1476C16.0512 13.1116 15.816 13.0324 15.5952 12.9412C15.3744 12.8524 15.2784 12.8788 15.2016 13.1284C14.9304 14.0356 14.5512 14.902 14.112 15.742C13.9008 16.1476 13.656 16.5364 13.3848 17.0068C13.2768 16.4116 13.1952 15.9052 13.0896 15.4012C13.0416 15.1756 13.0464 14.9716 13.1448 14.758C13.2 14.6356 13.3464 14.4916 13.2 14.3596C12.9672 14.1484 12.7128 13.9636 12.4656 13.7692C12.4584 13.762 12.4248 13.7788 12.4104 13.7908C12.1704 13.9972 11.8968 14.1748 11.7096 14.4196C11.5968 14.566 11.8152 14.758 11.8632 14.9356C11.8728 14.9764 11.8944 15.0196 11.8896 15.058C11.7888 15.6772 11.6832 16.294 11.5704 16.9708C11.4912 16.8772 11.4552 16.8412 11.4312 16.8004C10.7976 15.778 10.2816 14.7028 9.89039 13.5652C9.81119 13.3324 9.81839 12.9724 9.61919 12.898C9.43199 12.8284 9.15119 13.0132 8.91119 13.0804C8.78639 13.1164 8.67599 13.15 8.62799 13.2964C8.53679 13.5796 8.40719 13.8484 8.33999 14.1316C7.89839 15.9748 8.00879 17.7988 8.57759 19.6012C8.66399 19.8748 8.76719 20.0236 9.07919 20.0836C9.68159 20.2036 10.0056 20.8636 9.79919 21.4348C9.58319 22.0372 8.93279 22.3204 8.36639 22.0612C7.79519 21.802 7.55039 21.0988 7.88159 20.5756C8.00159 20.386 7.98719 20.2396 7.92239 20.0524C7.31279 18.3028 7.14959 16.5124 7.43039 14.6788C7.49519 14.2564 7.63679 13.8532 7.73279 13.4068C6.89519 13.6516 6.09119 13.8556 5.36639 14.278C4.83359 14.5876 4.42079 14.9932 4.18799 15.5836C3.90959 16.2916 3.74159 17.026 3.59999 17.77C3.27359 19.498 3.17279 21.2524 3.04079 23.002C3.00479 23.4796 3.21839 23.7748 3.59759 23.998H21.3312C21.7824 23.7844 21.9552 23.4436 21.9024 22.9324ZM8.49119 8.17237C8.65439 8.21557 8.73119 8.29477 8.79359 8.44357C9.16079 9.32197 9.61439 10.1476 10.2888 10.834C10.8216 11.3788 11.4384 11.7868 12.2208 11.866C13.2048 11.9668 13.9728 11.5276 14.6376 10.8628C15.3144 10.1836 15.7848 9.36757 16.152 8.48437C16.2024 8.36437 16.2192 8.21317 16.4016 8.19637C17.0568 8.13397 17.568 7.09717 17.46 6.46597C17.4288 6.28597 17.3616 6.12037 17.1648 6.10837C16.92 6.09157 16.9656 5.96197 17.0016 5.81077C17.1312 5.26837 17.1864 4.71397 17.2128 4.23877C17.2296 3.75637 17.184 3.35557 17.088 2.97157C16.9416 2.39077 16.6416 1.89157 16.0968 1.61317C15.7752 1.44757 15.528 1.26517 15.42 0.907567C15.3792 0.768367 15.2424 0.677167 15.1296 0.583567C14.712 0.237967 14.2176 0.086767 13.704 0.036367C12.1656 -0.114833 10.7304 0.213967 9.46079 1.10917C8.58479 1.72357 7.98239 2.53477 7.85999 3.63877C7.77839 4.36597 7.81919 5.08597 7.95599 5.80357C7.98479 5.95717 8.04719 6.11797 7.77599 6.11317C7.65359 6.11077 7.57439 6.21397 7.53359 6.33397C7.30799 6.97717 7.82879 7.99717 8.49119 8.17237Z" />
</svg>
`),
              routerLink: ['/physcians'],
            },
            {
              label: 'Alerts',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.66322 21.4942H21.3464C22.5824 21.4942 23.3792 20.191 22.82 19.0894L13.9784 3.42221C13.3688 2.20301 11.624 2.20301 11.0144 3.42221L2.17282 19.0894C1.63042 20.1886 2.42722 21.4942 3.66322 21.4942ZM13.892 17.851C13.892 18.6646 13.2992 19.291 12.452 19.291C11.6048 19.291 11.012 18.6646 11.012 17.851V17.8174C11.012 17.0038 11.6048 16.3774 12.452 16.3774C13.2992 16.3774 13.892 17.0038 13.892 17.8174V17.851ZM11.6744 7.26701H13.3352C13.7912 7.26701 14.0624 7.65581 14.012 8.14781L13.2824 14.515C13.232 14.9734 12.9272 15.259 12.5024 15.259C12.08 15.259 11.7728 14.971 11.7224 14.515L10.9928 8.14781C10.9472 7.65581 11.2184 7.26701 11.6744 7.26701Z" />
</svg>
`),
              routerLink: ['/patient-alerts'],
            },
            {
              label: 'Reports',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.9424 3.8784H19.3304V18.9648C19.3304 19.9656 18.5144 20.784 17.5112 20.784H6.34164V21.3432C6.34164 21.984 6.86244 22.5024 7.50084 22.5024H19.94C20.5808 22.5024 21.0992 21.984 21.0992 21.3432V5.0352C21.0992 4.3968 20.5832 3.876 19.9424 3.8784ZM17.5088 20.1216C18.1496 20.1216 18.668 19.6032 18.668 18.9624V2.6592C18.668 2.0184 18.1472 1.5 17.5088 1.5H9.22164V1.884C9.22644 1.9248 9.22884 1.9656 9.22884 2.0064V5.184C9.22884 6.084 8.49684 6.8184 7.59444 6.8184H4.41684C4.38324 6.8184 4.34964 6.816 4.31604 6.8136H3.91284V18.9624C3.91284 19.6032 4.43364 20.1216 5.07204 20.1216H17.5088ZM7.46964 9.3144H15.5816C15.884 9.3144 16.1288 9.5592 16.1288 9.8616C16.1288 10.164 15.884 10.4088 15.5816 10.4088H7.46964C7.16724 10.4088 6.92244 10.164 6.92244 9.8616C6.92244 9.5592 7.16724 9.3144 7.46964 9.3144ZM7.46964 12.2856H15.5816C15.884 12.2856 16.1288 12.5304 16.1288 12.8328C16.1288 13.1352 15.884 13.38 15.5816 13.38H7.46964C7.16724 13.38 6.92244 13.1352 6.92244 12.8328C6.92244 12.5304 7.16724 12.2856 7.46964 12.2856ZM6.92004 15.6312C6.92004 15.3288 7.16484 15.084 7.46724 15.084H11.5232C11.8256 15.084 12.0704 15.3288 12.0704 15.6312C12.0704 15.9336 11.8232 16.1784 11.5208 16.1784H7.46484C7.16724 16.1784 6.92004 15.9336 6.92004 15.6312ZM4.41684 6.1608H7.60164C8.13204 6.156 8.56164 5.7264 8.56644 5.196V2.0064C8.56644 1.7136 8.32404 1.5168 8.07444 1.5168C7.95444 1.5168 7.83204 1.56 7.73124 1.6608L4.06884 5.3256C3.76164 5.6328 3.98004 6.1608 4.41684 6.1608Z" />
</svg>
`),
              routerLink: ['/reports'],
            },
            {
              label: 'Device Management',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M19.1644 9.03112C19.0468 8.75032 19.2652 8.29432 19.7452 7.81192L20.9068 6.65032C21.0724 6.48472 21.1516 6.29752 21.0844 6.23032L20.9644 6.11032L17.8852 3.03352L17.7652 2.91352C17.7652 2.91352 17.5108 2.92792 17.3452 3.09352L16.1836 4.25512C15.6508 4.78552 15.2956 4.86712 15.13 4.86712C15.0076 4.86712 14.9116 4.82632 14.8252 4.73992C14.71 4.62232 14.5132 4.31512 14.5132 3.56392V3.54232C14.5132 3.53032 14.5108 3.33112 14.5108 3.09592V1.91992C14.5108 1.68712 14.3212 1.49512 14.086 1.49512H9.90765C9.67485 1.49512 9.48285 1.68472 9.48285 1.91992V3.56632C9.48285 3.87112 9.43485 4.86472 8.86605 4.86472C8.70045 4.86472 8.34525 4.78552 7.81485 4.25272L6.59565 3.03352L6.47565 2.91352C6.47565 2.91352 6.30045 2.84872 6.23565 2.91352L3.03645 6.11272L2.91645 6.23272C2.91645 6.23272 2.84925 6.40792 2.91645 6.47272L3.03645 6.59272L4.25565 7.81432C4.73565 8.29432 4.95405 8.74792 4.83645 9.03352C4.71885 9.31432 4.24365 9.48472 3.56685 9.48472H1.92285C1.68765 9.48232 1.49805 9.67432 1.49805 9.90712V14.0927C1.49805 14.3255 1.68765 14.5175 1.92285 14.5175H3.56445C4.24365 14.5175 4.71645 14.6855 4.83405 14.9663C4.95165 15.2495 4.73325 15.7031 4.25325 16.1855L3.03405 17.4047L2.91405 17.5247C2.91405 17.5247 2.84685 17.6999 2.91405 17.7647L6.11325 20.9663L6.23325 21.0863C6.23325 21.0863 6.40845 21.1535 6.47325 21.0863L6.59325 20.9663L7.81245 19.7447C8.34285 19.2143 8.70045 19.1327 8.86365 19.1327C9.43245 19.1327 9.48045 20.1263 9.48045 20.4311V22.0775C9.48045 22.3103 9.67005 22.5023 9.90525 22.5023H14.0884C14.3212 22.5023 14.5132 22.3127 14.5132 22.0775V20.9015C14.5132 20.6687 14.5156 20.4695 14.5156 20.4551V20.4335C14.5132 19.6823 14.71 19.3751 14.8276 19.2575C14.914 19.1711 15.01 19.1303 15.1324 19.1303C15.2956 19.1303 15.6532 19.2095 16.186 19.7423L17.4052 20.9639L17.5252 21.0839C17.5252 21.0839 17.7004 21.1511 17.7652 21.0839L20.9668 17.8847L21.0868 17.7647C21.0868 17.7647 21.1516 17.5895 21.0868 17.5247L19.7452 16.1855C19.2652 15.7055 19.0468 15.2495 19.1644 14.9663C19.282 14.6831 19.7572 14.5175 20.4364 14.5175H22.0804C22.3132 14.5175 22.5052 14.3279 22.5052 14.0927V9.90472C22.5052 9.67192 22.3156 9.47992 22.0804 9.47992H20.4412C19.7572 9.48232 19.282 9.31432 19.1644 9.03112ZM16.7428 11.9999C16.7428 14.6135 14.6164 16.7423 12.0004 16.7423C9.38445 16.7423 7.25805 14.6135 7.25805 11.9999C7.25805 9.38632 9.38685 7.25752 12.0004 7.25752C14.614 7.25752 16.7428 9.38392 16.7428 11.9999Z"/>
</svg>
`),
              routerLink: ['/medical-devices'],
            },
            {
              label: 'Dialog',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="icon / icofont / web / action / chat">
<path id="Vector" d="M14.9968 3.47725C14.9968 3.29165 14.9872 3.10285 14.9456 2.92205C14.7248 1.94285 13.92 1.31725 12.8864 1.31565C9.62075 1.31405 6.35675 1.31405 3.09115 1.31565C1.88795 1.31565 0.999952 2.20525 0.999952 3.40845C0.998352 5.53165 0.998352 7.65645 1.00155 9.77965C1.00155 9.95085 1.01595 10.1253 1.05435 10.2917C1.27835 11.2405 2.05915 11.8661 3.03835 11.8869C3.43835 11.8949 3.83835 11.8885 4.28315 11.8885C4.21755 12.1893 4.17435 12.4629 4.09755 12.7285C3.93915 13.2821 3.73115 13.8117 3.36635 14.2693C3.20475 14.4725 3.27835 14.6549 3.53755 14.6757C3.71835 14.6901 3.92475 14.6885 4.08315 14.6165C4.71355 14.3285 5.34075 14.0309 5.94715 13.6965C6.66555 13.2997 7.12955 12.6213 7.67515 12.0357C7.76955 11.9333 7.86075 11.8853 8.00475 11.8869C9.64795 11.8917 11.2912 11.8981 12.9344 11.8869C14.128 11.8773 14.9984 10.9797 15 9.78605C15 7.68205 15 5.57965 14.9968 3.47725Z"/>
</g>
</svg>
`),
              routerLink: ['/dialog'],
            },
            {
              label: 'Task Board',
              icon: this.sanitizer
                .bypassSecurityTrustHtml(`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="icon / icofont / web / action / calendar">
<path id="Vector" d="M11.8177 2.45411C11.8177 2.24771 11.8209 2.05411 11.8177 1.85731C11.8145 1.48131 11.5761 1.16611 11.2081 1.04611C10.8465 0.927713 10.4545 1.04931 10.2257 1.34531C10.0929 1.51491 10.0369 1.71011 10.0369 1.92291V2.44451C10.0369 2.52771 9.96814 2.59491 9.88494 2.59491H8.94254C8.85774 2.59491 8.79054 2.52771 8.79054 2.44451C8.79054 2.26531 8.79214 2.09251 8.79054 1.91811C8.78414 1.50851 8.56974 1.18851 8.21614 1.05731C7.63534 0.842913 7.02254 1.24771 7.00814 1.86211C7.00334 2.05411 7.00494 2.24771 7.00654 2.44131C7.00654 2.52451 6.93774 2.59171 6.85454 2.59171H5.88654C5.80174 2.59171 5.73454 2.52451 5.73454 2.44131C5.74094 2.19651 5.75214 1.95811 5.72654 1.72451C5.68974 1.38531 5.46894 1.15811 5.14414 1.04771C4.82414 0.940513 4.52814 1.00131 4.26894 1.22051C4.03854 1.41411 3.96014 1.67011 3.96334 1.95811C3.96494 2.12131 3.96494 2.28451 3.96494 2.45571C3.96494 2.53891 3.89614 2.60611 3.81294 2.60611H1.17934C1.09454 2.60611 1.02734 2.67331 1.02734 2.75651V14.8477C1.02734 14.9309 1.09614 14.9981 1.17934 14.9981H14.8177C14.9025 14.9981 14.9697 14.9309 14.9697 14.8477V2.75491C14.9697 2.67171 14.9009 2.60451 14.8177 2.60451H11.9697C11.8849 2.60451 11.8161 2.53731 11.8177 2.45411ZM5.20814 14.4029H1.78734C1.70254 14.4029 1.63534 14.3357 1.63534 14.2525V11.8605C1.63534 11.7773 1.70414 11.7101 1.78734 11.7101H5.20814C5.29294 11.7101 5.36014 11.7773 5.36014 11.8605V14.2509C5.36014 14.3357 5.29294 14.4029 5.20814 14.4029ZM5.20974 11.0957H1.78734C1.70254 11.0957 1.63534 11.0285 1.63534 10.9453V8.41731C1.63534 8.33411 1.70414 8.26691 1.78734 8.26691H5.20974C5.29454 8.26691 5.36174 8.33411 5.36174 8.41731V10.9453C5.36174 11.0285 5.29294 11.0957 5.20974 11.0957ZM5.21134 7.65571H1.78734C1.70254 7.65571 1.63534 7.58851 1.63534 7.50531V5.07811C1.63534 4.99491 1.70414 4.92771 1.78734 4.92771H5.21134C5.29614 4.92771 5.36334 4.99491 5.36334 5.07811V7.50531C5.36334 7.58851 5.29614 7.65571 5.21134 7.65571ZM9.76814 14.4045H6.14894C6.06414 14.4045 5.99694 14.3373 5.99694 14.2541V11.8685C5.99694 11.7853 6.06574 11.7181 6.14894 11.7181H9.76814C9.85294 11.7181 9.92014 11.7853 9.92014 11.8685V14.2557C9.92174 14.3373 9.85294 14.4045 9.76814 14.4045ZM9.92814 11.0141C9.92494 11.0605 9.85614 11.0973 9.77134 11.0973H6.14894C6.06414 11.0973 5.99694 11.0301 5.99694 10.9469V8.41731C5.99694 8.33411 6.03694 8.26531 6.08334 8.26211L6.16974 8.25731C7.36174 8.25731 8.55214 8.26051 9.74254 8.25411C9.88654 8.25411 9.93614 8.28611 9.93454 8.43811C9.92814 9.26851 9.93134 10.1021 9.92974 10.9325C9.93134 10.9597 9.92974 10.9853 9.92814 11.0141ZM9.93294 7.56611C9.93294 7.61251 9.86414 7.65091 9.78094 7.65091H6.14574C6.06094 7.65091 5.99374 7.58371 5.99374 7.50051V5.06851C5.99374 4.98531 6.03374 4.91811 6.08334 4.91811H6.17294C7.36494 4.91811 8.55534 4.91971 9.74574 4.91491C9.88974 4.91331 9.93774 4.94851 9.93454 5.09891C9.92814 5.89411 9.93134 6.68611 9.93134 7.48131V7.56611H9.93294ZM10.6977 8.27171H14.2033C14.2881 8.27171 14.3553 8.33891 14.3553 8.42211V10.9485C14.3553 11.0317 14.2865 11.0989 14.2033 11.0989H10.6977C10.6129 11.0989 10.5457 11.0317 10.5457 10.9485V8.42051C10.5457 8.33891 10.6145 8.27171 10.6977 8.27171ZM14.2049 14.4029H10.6977C10.6129 14.4029 10.5457 14.3357 10.5457 14.2525V11.8653C10.5457 11.7821 10.6145 11.7149 10.6977 11.7149H14.2049C14.2897 11.7149 14.3569 11.7821 14.3569 11.8653V14.2525C14.3601 14.3357 14.2913 14.4029 14.2049 14.4029ZM14.2049 7.65091H10.6977C10.6129 7.65091 10.5457 7.58371 10.5457 7.50051V5.07971C10.5457 4.99651 10.6145 4.92931 10.6977 4.92931H14.2049C14.2897 4.92931 14.3569 4.99651 14.3569 5.07971V7.50051C14.3601 7.58371 14.2913 7.65091 14.2049 7.65091Z"/>
</g>
</svg>
`),
              routerLink: ['/taskboard'],
            },
          ],
        },
      ];
    }

    this.userItems = [
      {
        label: 'Options',
        items: [
          {
            label: 'Refresh',
            icon: 'pi pi-refresh',
          },
          {
            label: 'Export',
            icon: 'pi pi-upload',
          },
        ],
      },
    ];
  }
  setActiveLink(selectedMenu: string, event?: any) {
    event?.preventDefault();
    if (selectedMenu == 'Medical Devices') {
      selectedMenu = 'Device Management'
    }
    if (selectedMenu == 'Case Managers') {
      selectedMenu = 'Care Managers'
    }
    this.selectedLabel = selectedMenu;
    localStorage.setItem('selectedmenu', this.selectedLabel);
  }

  toggleDropdown(event: Event): void {
    this.dropdownOpen = !this.dropdownOpen;
    event.stopPropagation();
  }
  changePassword(): void {
    // Logic for changing password
    console.log('Change Password clicked');
  }

  openSettings(): void {
    // Logic for opening settings
    console.log('Settings clicked');
  }
  logout() {
    this.confirmationService.confirm({
      message: 'Are you sure that you want to logout?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      accept: () => {
        this.menuService.doLogout().subscribe({
          next: (res) => {
            console.log('Logout successful', res);
            if (document.cookie && document.cookie !== '') {
              const cookies = document.cookie.split(';');
              for (let cookie of cookies) {
                const eqPos = cookie.indexOf('=');
                const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
              }
            }
            // Unregister all service workers
            if ('serviceWorker' in navigator) {
              navigator.serviceWorker.getRegistrations().then(function (registrations) {
                for (let registration of registrations) {
                  registration.unregister();
                }
              })
            }
            setTimeout(() => {
              console.log('Logout successful');
              localStorage.clear();
              //window.location.href = window.location.origin + '?reload=' + new Date().getTime();
            }, 1000)
            this.router.navigate(['/login']);
          }, error: (err) => {
            setTimeout(() => {
              console.log('Logout successful');
              localStorage.clear();
              window.location.href = window.location.origin + '?reload=' + new Date().getTime();
            }, 1000)
            this.router.navigate(['/login']);
          }
        })
      }
    })

  }
}
