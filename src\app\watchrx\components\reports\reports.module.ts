import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { DropdownModule } from 'primeng/dropdown';
import { InputMaskModule } from 'primeng/inputmask';
import { SkeletonModule } from 'primeng/skeleton';
import { TooltipModule } from 'primeng/tooltip';
import { ReportsRoutingModule } from './reports-routing.module';
import { ReportsComponent } from './reports.component';
import { ToastModule } from 'primeng/toast';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
@NgModule({
  declarations: [ReportsComponent],
  imports: [
    CommonModule,
    ReportsRoutingModule,
    ButtonModule,
    FormsModule,
    AutoCompleteModule,
    DropdownModule,
    AutoCompleteModule,
    InputMaskModule,
    ButtonModule,
    ChartModule,
    CardModule,
    TooltipModule,
    SkeletonModule,
    ToastModule,
    NgxSliderModule
  ],
})
export class ReportsModule {}
