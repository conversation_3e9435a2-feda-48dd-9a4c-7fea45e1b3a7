import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, LazyLoadEvent, MessageService } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { debounceTime, Subject } from 'rxjs';
import { PatientInfo, Program, voice } from '../../../api/allPatients';
import { MenuService } from '../../../service/menu.service';
import { AllPatientsService } from './service/all-patients.service';
import { DatePipe, formatDate } from '@angular/common';

//import { Device } from '../../../../twilio/twilio.min.js';

import { KJUR } from 'jsrsasign';

import { Device } from '@twilio/voice-sdk';
import uitoolkit from '@zoom/videosdk-ui-toolkit';
import { DateService } from '../../dashboard/service/date-service.service';
import { DashboardService } from '../../dashboard/service/dashboard.service';
import { ScheduleTextMessageFormComponent } from '../../shared/schedule-text-message-form/schedule-text-message-form.component';
import { FileUpload } from 'primeng/fileupload';
import { LoaderService } from '../../../loader/loader/loader.service';
import { TextMessageService } from '../edit-patient-tab-view/components/encounter-tab-view/text-messages/service/text-message.service';
import { ProfileService } from '../../profile/services/profile.service';
import { PatientEditProfileService } from '../edit-patient-tab-view/components/profile/service/patientEditProfile.service';
@Component({
  selector: 'app-patients-view',
  standalone: false,
  templateUrl: './patients-view.component.html',
  styleUrl: './patients-view.component.scss',
  providers: [MessageService, ConfirmationService]
})
export class PatientsViewComponent {
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: string = '';
  patientName: string = '';
  selectedProgram: any = '';
  sessionContainer: any;
  authEndpoint = '';
  inSession: boolean = false;
  config = {
    videoSDKJWT: '',
    sessionName: '',
    userName: '',
    sessionPasscode: '',
    features: [
      'preview',
      'video',
      'audio',
      'settings',
      'users',
      'chat',
      'share',
    ],
    options: { init: {}, audio: {}, video: {}, share: {} },
    virtualBackground: {
      allowVirtualBackground: true,
      allowVirtualBackgroundUpload: true,
      virtualBackgrounds: [
        'https://images.unsplash.com/photo-1715490187538-30a365fa05bd?q=80&w=1945&auto=format&fit=crop',
      ],
    },
  };
  role = 1;
  selectedPatientInfo: any;
  patientCount: any;
  messageSidebarVisible: boolean = false;
  programList: any;
  @ViewChild(ScheduleTextMessageFormComponent)
  textMessageForm!: ScheduleTextMessageFormComponent;
  encounterLoader: boolean = false;
  openAlertsDilog: boolean = false;
  alerts: any[] = [];
  openVideoDlg: boolean=false;
  providerList:any[]=[];
casemanagerList:any[]=[];
selectedProvider:any=null;
  selectedCaseManager:any=null;
  constructor(
    public menuService: MenuService,
    private router: Router,
    private allPatientService: AllPatientsService,
    private dateService: DateService,
    private dashboardService: DashboardService,
    private messageService: MessageService,
    public loaderService: LoaderService,
    private textMessageService: TextMessageService,
    public profileService:PatientEditProfileService,
    public cdr:ChangeDetectorRef
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      this.debouncedValue = value;
      console.log('Search Value:', this.debouncedValue);
      this.onSearch(this.debouncedValue);
    });
  }

  @ViewChild('dt')
  table!: Table;

  itemsPerPage: number = 15;
  itemsPerPage1:number=15
  patientList: PatientInfo[] = [];
  totalActivePatients: number = 0;
  loader: boolean = false;
  activeTabIndex = 0;
  inActivePatientList: PatientInfo[] = [];
  inActiveTotalPatients: number = 0;
  inActiveLoader: boolean = false;
  voiceData!: voice;
  openVoiceVideoDlg: boolean = false;
  activeIndex: number = 0;
  programs: Program[] = [];
  startDate: Date = this.dateService.getStartDateOfMonth();
  endDate: Date = this.dateService.getCurrentDate();
  @ViewChild('fileUpload') fileUpload!: FileUpload;
  userRole: any = null;
  ngOnInit() {
    this.menuService.changeMenu('Patients');
    this.dashboardCount(this.startDate, this.endDate);
    this.userRole = JSON.parse(localStorage.getItem('user')!);
    this.getAllProvider()
  }

  editPatient(patientId: string, patientName: string, type: string) {
    localStorage.setItem('selectedPatientName', patientName);
    if (type == 'inactive')
      localStorage.setItem('mainTab', '1');
    else
      localStorage.setItem('mainTab', '0');
    localStorage.removeItem('subTab');
    this.router.navigate(['/editpatient'], { queryParams: { id: patientId } });
  }

  dashboardCount(startDate: Date, endDate: Date) {
    let fromToDetails =
      formatDate(startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(endDate, 'yyyy-MM-dd', 'en-US');
    this.allPatientService.setPatientCount(fromToDetails).subscribe((res) => {
      if (res.status) {
        this.patientCount = res;

      }
    });
  }

  loadActivePatients($event: LazyLoadEvent | TableLazyLoadEvent) {
    if (this.activeTabIndex === 0) {
      this.loader = true;

      let pageSize = $event.rows || 15;
      let first = $event.first || 0;
      let pageNo = first / pageSize;
      let selectedProvider = this.selectedProvider?.id||0;
      let selectedCaseManager = this.selectedCaseManager?.id||0;
      let url = pageNo + '/' + pageSize +'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' + 'Y';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        this.loader = false;
        if (res.success) {
          this.patientList = res.patientsInfo!;
          this.totalActivePatients = res.count!;
          this.itemsPerPage=$event.rows || 15;
        }
      },err=>{
        this.loader = false;
      });
    }
  }

  loadInActivePatients($event: LazyLoadEvent | TableLazyLoadEvent) {
    if (this.activeTabIndex === 1) {
      this.inActiveLoader = true;
      let pageSize = $event.rows || 15;
      let first = $event.first || 0;
      let pageNo = first / pageSize;
      let selectedProvider = this.selectedProvider?.id||0;
      let selectedCaseManager = this.selectedCaseManager?.id||0;
      let url = pageNo + '/' + pageSize +'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' + 'N';
      //let url = pageNo + '/' + pageSize + '/0/0/0'+'/N';

      this.allPatientService.setActivePatientList(url).subscribe((res) => {
         this.inActiveLoader = false;
        if (res.success) {
          this.inActivePatientList = res.patientsInfo!;
          this.inActiveTotalPatients = res.count!;
          this.itemsPerPage1=$event.rows || 15;
        }
      },err=>{
         this.inActiveLoader = false;
      });
    }
  }
  searchText: string = '';
  tabChange() {
    this.cdr.detectChanges();
    this.searchText = '';
    this.itemsPerPage = 15;
    this.itemsPerPage1=15;
    this.first=0;
    this.first1=0
    this.selectedProvider = null;
    this.selectedCaseManager = null;
    let selectedProvider = this.selectedProvider?.id||0;
      let selectedCaseManager = this.selectedCaseManager?.id||0;
    if (this.activeTabIndex === 0) {
      this.loader = true;
      let url = '0' + '/' + '15' +'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' + 'Y';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        console.log('Tab1:', res);
        if (res.success) {
          this.patientList = res.patientsInfo!;
          this.totalActivePatients = res.count!;
          this.itemsPerPage =res.patientsInfo.length!;
          this.loader = false;
           this.cdr.detectChanges();
        }
      });
    } else if (this.activeTabIndex === 1) {
      this.inActiveLoader = true;
      let url = '0/15'+'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' +'N';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        if (res.success) {
          this.inActivePatientList = res.patientsInfo!;
          this.inActiveTotalPatients = res.count!;
          this.inActiveLoader = false;
            this.itemsPerPage1 =res.patientsInfo.length!;
           this.cdr.detectChanges();
        }
      });
    }
  }

  onInput(event: any): void {
    // this.inputSubject.next(event.target.value);
    this.debouncedValue = event.target.value;
    this.onSearch(this.debouncedValue)
  }

  onSearch(searchTerm: string): void {
    this.selectedCaseManager = null;
    this.selectedProvider = null;
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      let stats = this.activeTabIndex === 0 ? 'Y' : 'N';
      let url = searchTerm + '?ptstatus=' + stats;
      if (stats === 'Y') {
        this.loader = true;
      } else {
        this.inActiveLoader = true;
      }
      this.allPatientService.setInActivePatientList(url).subscribe((res) => {
        if (res.success) {
          if (this.activeTabIndex === 0) {
            if (res.success) {
              this.patientList = res.patientsInfo!;
              this.totalActivePatients = res.count!;
              this.itemsPerPage = res.count!;
              this.loader = false;
            }
          } else {
            if (res.success) {
              this.inActivePatientList = res.patientsInfo!;
              this.inActiveTotalPatients = res.count!;
              this.inActiveLoader = false;
              this.itemsPerPage1 = res.count!;
            }
          }
        }
      });
    } else {
      // Fix: Explicitly clear searchText and reset paginator options before tabChange
      if (searchTerm !== undefined && searchTerm.length === 0) {
        this.searchText = '';
        this.itemsPerPage = 15;
        this.itemsPerPage1 = 15;
        this.first = 0;
        this.first1 = 0;
        this.cdr.detectChanges();
        this.tabChange();
      }
    }
  }

  onProviderChange(event: any) {
    this.selectedProvider = event.value;
    this.searchText = '';
    this.itemsPerPage = 15;
    this.itemsPerPage1=15;
    this.first=0;
    this.first1=0;
     let selectedProvider = this.selectedProvider?.id||0;
      let selectedCaseManager = this.selectedCaseManager?.id||0;
    if (this.activeTabIndex === 0) {
      this.loader = true;
      let url = '0' + '/' + '15' +'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' + 'Y';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
       this.loader = false;
        if (res.success) {
          this.patientList = res.patientsInfo!;
          this.totalActivePatients = res.count!;
        }
      });
    } else if (this.activeTabIndex === 1) {
      this.inActiveLoader = true;
      let url = '0/15'+'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' +'N';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        this.inActiveLoader = false;
        if (res.success) {
          this.inActivePatientList = res.patientsInfo!;
          this.inActiveTotalPatients = res.count!;
        }
      });
    }
  }

  onCaseManagerChange(event: any) {
    this.selectedCaseManager = event.value;
    this.searchText = '';
    this.itemsPerPage = 15;
    this.itemsPerPage1=15;
    this.first=0;
    this.first1=0;
     let selectedProvider = this.selectedProvider?.id||0;
      let selectedCaseManager = this.selectedCaseManager?.id||0;
    if (this.activeTabIndex === 0) {
      let url = '0' + '/' + '15' +'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' + 'Y';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        if (res.success) {
          this.patientList = res.patientsInfo!;
          this.totalActivePatients = res.count!;
        }
      });
    } else if (this.activeTabIndex === 1) {
      let url = '0/15'+'/'+selectedCaseManager+'/'+selectedProvider+'/'+0+'/' +'N';
      this.allPatientService.setActivePatientList(url).subscribe((res) => {
        if (res.success) {
          this.inActivePatientList = res.patientsInfo!;
          this.inActiveTotalPatients = res.count!;
        }
      });
    }
  }
  getNames(item: any): string {
    if (item === undefined || item === null) {
      return '';
    }
    return item
      .map((obj: { chronicConditionName: any }) => obj.chronicConditionName)
      .join(', ');
  }

  makeCall(patientId: string, patientName: string, patientInfo: any) {
    this.selectedPatientInfo = patientInfo;
    // this.patientName = patientName;
    // this.getAudioDevices();
    this.openVoiceVideoDlg = true;
    // this.voiceToken(Number(patientId));
    // this.config.videoSDKJWT = this.generateSignature(
    //   'dCQGBVj35xjP3KZL8lDPAN7MooAQ91Jc1Z26',
    //   '8Lk5gzjCwfm6dU3QCOTB7JdUiQE2PbqOtiLp',
    //   this.patientName,
    //   1,
    //   patientId,
    //   this.patientName
    // );
    // this.config.sessionPasscode = patientId;
    // this.config.userName = this.patientName;
    // this.config.sessionName = this.patientName;
  }

  makeVideoCall(patientId: string, patientName: string, patientInfo: any)
  {
    this.selectedPatientInfo = patientInfo;
    this.openVideoDlg = true;
  }
  // onTextSubmit() {}

  closeModalPopup() { }

  disconnect() { }

  private voiceToken(id: number) {
    this.allPatientService.voiceToken(id).subscribe((token) => {
      this.voiceData = token;
      (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
        true;
      const callStatus = document.getElementById(
        'callStatus'
      ) as HTMLTextAreaElement;
      const hangUpButton = document.getElementById(
        'button-hangup-outgoing'
      ) as HTMLTextAreaElement;
      (document.getElementById('phonenumber') as HTMLTextAreaElement).value =
        this.voiceData.phoneNumber;
      (document.getElementById('button-call') as HTMLTextAreaElement).disabled =
        true;
      (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
        true;
      (
        document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
      ).disabled = true;
      (
        document.getElementById('cancel-voice-call') as HTMLTextAreaElement
      ).disabled = false;
      //createRadioButton(response.data.programs, 'voice');
      this.programs = this.voiceData.programs;

      callStatus.innerHTML =
        '<h5>Connecting to: ' + this.voiceData.phoneNumber + '</h5>';
      this.resetVolumerController();

      let device = new Device(this.voiceData.token, {
        logLevel: 1,
        // codecPreferences: ["opus", "pcmu"]
      });
      device.register();
      device.on('ready', function (device: any) {
        console.log('Twilio.Device is now ready for connections');
      });

      device.on('registered', function () {
        callStatus.innerHTML = '<h5>Now your device ready to make a call.</h5>';
        (
          document.getElementById('button-call') as HTMLTextAreaElement
        ).disabled = false;
      });

      device.on('error', function (error: any) {
        console.log('Twilio.Device Error: ' + error.message);
        device.disconnectAll();
        device.destroy();
        //getAudioDevices();
        // $('#voiceCalls').modal('toggle');
        //  FlashService.Error("Server communication Error, Please try again...", true);
      });

      (document.getElementById('button-call') as HTMLTextAreaElement).onclick =
        () => {
          /* var selectedRadioButton = (document.querySelector('input[name="program"]:checked')as HTMLTextAreaElement);
          
          var selectedReview = null;
          if (selectedRadioButton) {
selectedReview = selectedRadioButton.value; 
} else {
FlashService.Error("Please select a program before making a call.", true);
return;     
} */

          let ph = (
            document.getElementById('phonenumber') as HTMLTextAreaElement
          ).value;
          if (ph) {
            (
              document.getElementById('button-call') as HTMLTextAreaElement
            ).disabled = true;
            var params = {};
            if (localStorage.getItem('patientId') == null) {
              params = {
                To: (
                  document.getElementById('phonenumber') as HTMLTextAreaElement
                ).value,
                patientId: 574,
                review: 'RPM',
              };
            } else {
              params = {
                To: (
                  document.getElementById('phonenumber') as HTMLTextAreaElement
                ).value,
                patientId: localStorage.getItem('patientId'),
                review: 'RPM',
              };
            }
            console.log(params);
            this.makeCallToNumber(device, params);
          }
        };

      (
        document.getElementById('cancel-voice-call') as HTMLTextAreaElement
      ).onclick = () => {
        var selectedRadioButton = document.querySelector(
          'input[name="program"]:checked'
        ) as HTMLTextAreaElement;
        if (selectedRadioButton) {
          var selectedReview = selectedRadioButton.value;
          if (selectedReview === undefined || selectedReview === null) {
            selectedReview = 'rpm';
          }
        }
        this.openVoiceVideoDlg = false;
        device.disconnectAll();
      };
    }); //
  }

  private async getAudioDevices() {
    await navigator.mediaDevices.getUserMedia({
      audio: true,
    });
  }
  private resetVolumerController() {
    (
      document.getElementById('input-volume') as HTMLTextAreaElement
    ).style.width = '';
    (
      document.getElementById('input-volume') as HTMLTextAreaElement
    ).style.background = '';
    (
      document.getElementById('output-volume') as HTMLTextAreaElement
    ).style.width = '';
    (
      document.getElementById('output-volume') as HTMLTextAreaElement
    ).style.background = '';
  }

  private async makeCallToNumber(device: any, params: any) {
    const callStatus = document.getElementById(
      'callStatus'
    ) as HTMLTextAreaElement;

    const call = await device.connect({
      params,
    });
    (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
      false;
    call.on('accept', function () {
      callStatus.innerHTML = '<h5>Connected to :' + params.To + '</h5>';
      // document.getElementById("input-volume-controller").classList.remove("hide");
      // document.getElementById("output-volume-controller").classList.remove("hide");
      (
        document.getElementById('animation') as HTMLTextAreaElement
      ).classList.remove('hide');
      // bindVolumeIndicators(call);
      (
        document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
      ).disabled = false;
      (
        document.getElementById('cancel-voice-call') as HTMLTextAreaElement
      ).disabled = true;
    });
    call.on('disconnect', function () {
      call.disconnect();
      device.disconnectAll();
      callStatus.innerHTML = '<h5>Call Disconnected.</h5>>';
      //$('#voiceCalls').modal('toggle');
    });
    call.on('cancel', function () {
      call.disconnect();
      device.disconnectAll();
      callStatus.innerHTML = '<h5>Call Disconnected.</h5>>';
      //$('#voiceCalls').modal('toggle');
    });

    (
      document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
    ).onclick = () => {
      //$('#voiceCalls').modal('toggle');
      if (call) {
        call.disconnect();
      }
    };
    call.on('reject', function () {
      call.disconnect();
      device.disconnectAll();
      callStatus.innerHTML = '<h5>Call Disconnected.</h5>>';
      // $('#voiceCalls').modal('toggle');
    });

    let muteUnmute = document.getElementById(
      'muteUnmute'
    ) as HTMLTextAreaElement;
    muteUnmute.onclick = () => {
      if (call.isMuted()) {
        call.mute(false);
        //$('#muteUnmute').find('i').toggleClass('fa fa-microphone-slash fa-2x').toggleClass('fa fa-microphone fa-2x');
        (
          document.getElementById('palypauseanim') as HTMLTextAreaElement
        ).classList.remove('call-animation-pause');
        (
          document.getElementById('palypauseanim') as HTMLTextAreaElement
        ).classList.add('call-animation-play');
      } else {
        call.mute(true);
        //$('#muteUnmute').find('i').toggleClass('fa fa-microphone fa-2x').toggleClass('fa fa-microphone-slash fa-2x');
        (
          document.getElementById('palypauseanim') as HTMLTextAreaElement
        ).classList.remove('call-animation-play');
        (
          document.getElementById('palypauseanim') as HTMLTextAreaElement
        ).classList.add('call-animation-pause');
      }
    };
  }

  generateSignature(
    sdkKey: string,
    sdkSecret: string,
    sessionName: string,
    role: number,
    sessionKey: string,
    userIdentity: string
  ) {
    const iat = Math.round(new Date().getTime() / 1000) - 30;
    const exp = iat + 60 * 60 * 2;
    const oHeader = { alg: 'HS256', typ: 'JWT' };

    const oPayload = {
      app_key: sdkKey,
      tpc: sessionName,
      role_type: role,
      session_key: sessionKey,
      user_identity: userIdentity,
      version: 1,
      iat: iat,
      exp: exp,
    };

    const sHeader = JSON.stringify(oHeader);
    const sPayload = JSON.stringify(oPayload);
    const sdkJWT = KJUR.jws.JWS.sign('HS256', sHeader, sPayload, sdkSecret);
    return sdkJWT;
  }

  getVideoSDKJWT() {
    console.log('getVideoSDKJWT');
    this.sessionContainer = document.getElementById(
      'sessionContainer'
    ) as HTMLTextAreaElement;

    console.log('getVideoSDKJWT', this.config.videoSDKJWT);
    this.joinSession();
  }

  joinSession() {
    this.inSession = true;
    uitoolkit.joinSession(this.sessionContainer, this.config);

    uitoolkit.onSessionClosed(this.sessionClosed);
  }

  sessionClosed = () => {
    console.log('session closed');
    uitoolkit.closeSession(this.sessionContainer);
    this.inSession = false;
  };

  handleClose() {
    this.openVoiceVideoDlg = false;
    this.sessionClosed();
    this.inSession = false;
  }

  private fromToDate() {
    return (
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US')
    );
  }

  makeMessage(patientId: string, patientName: string, patientInfo: any) {
    this.messageSidebarVisible = true;
    this.selectedPatientInfo = patientInfo
    let fromToDetails = patientInfo.patientId + '/0/5/' + this.fromToDate();
    this.dashboardService
      .getPatientEncounter(fromToDetails)
      .subscribe((res) => {
        if (res.success) {
          this.programList = res.programs;
        }
      });
      setTimeout(() => { 
        this.textMessageForm.resetFileds()
       },100)
  }

  onTextSubmit() {
    this.encounterLoader = true;
    let selectedProgram = this.textMessageForm.selectedProgram;
    if (!selectedProgram) {
      this.generateErrorMessage('One program should be selected');
      this.encounterLoader = false;
      return;
    }
    let messageDescription = this.textMessageForm.messageDescription;
    if (!messageDescription) {
      this.generateErrorMessage('Please enter message');
      this.encounterLoader = false;
      return;
    }
    let messageType = this.textMessageForm.messageType;
    let ansSelected = this.textMessageForm.isChecked;
    let frequencyType = this.textMessageForm?.frequencyType;

    let opt1 = this.textMessageForm.option1;
    let opt2 = this.textMessageForm.option2;
    let opt3 = this.textMessageForm.option3;
    let phoneNumber = this.textMessageForm.phoneNumber;
    let isEncounterNeeded = this.textMessageForm.isEnconterNeeded;

    let ans: any[] = [];

    if (ansSelected) {
      if (opt1 && opt1 !== '') {
        ans.push(opt1);
      } else {
        this.generateErrorMessage('Option 1 needed');
        return;
      }

      if (opt2 && opt2 !== '') {
        ans.push(opt2);
      } else {
        this.generateErrorMessage('Option 2 needed');
        return;
      }

      if (opt3 && opt3 !== '') {
        ans.push(opt3);
      }
    }
    let userName = '';
    let logUser = localStorage.getItem('user');
    if (logUser) {
      userName = JSON.parse(logUser)['firstName'];
    }

    let req = {}
    if ((messageType && messageType === 'Now') || messageType === 'SMS') {
      if (messageType == 'Now') {
        req = {
          patientIds: [this.selectedPatientInfo?.patientId],
          deliveryTime: '',
          question: messageDescription,
          senderName: userName,
          timeZone: '',
          nowOrSchedule: 'Now',
          answers: ans,
          phoneNumber: phoneNumber,
          review: selectedProgram,
        };
      }
      else {
        req = {
          patientIds: [this.selectedPatientInfo?.patientId],
          deliveryTime: '',
          question: messageDescription,
          senderName: userName,
          timeZone: '',
          nowOrSchedule: 'Now',
          answers: ans,
          phoneNumber: phoneNumber,
          review: selectedProgram,
          sms:
            messageType === 'SMS'
              ? '**DO NOT REPLY To this message, For emergency please dial 911'
              : '',
        };
      }
      this.dashboardService.sendMessage(req).subscribe((res) => {
        this.encounterLoader = false;
        this.messageSidebarVisible = false;
        if (res.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Message sent successfully.',
            life: 3000,
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'Failed to send message',
            life: 3000,
          });
          return;
        }
      });
    }
    else if (messageType === 'Schedule') {
      if (frequencyType === 'One Day') {
        let dayWeek = 'Su|Mo|Tu|We|Th|Fr|Sa';
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.oneDayStartDate) {
          startDate = formatDate(
            this.textMessageForm?.oneDayStartDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
          endDate = formatDate(
            this.textMessageForm?.oneDayStartDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.oneDayStartTime) {
          timeSlot = formatDate(
            this.textMessageForm?.oneDayStartTime,
            'HH:mm',
            'en-US'
          );
        }
        req = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          patientId: this.selectedPatientInfo?.patientId,
          question: messageDescription,
          senderName: userName,
          answer: ans,
          timeSlots: timeSlot,
          deliveryTime: startDate,
          timeZone: '',
          startDate: startDate,
          endDate: endDate,
          dayOfWeek: dayWeek,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(req)
          .subscribe((res) => {
            this.encounterLoader = false;
            this.messageSidebarVisible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      } else if (frequencyType === 'Daily') {
        let dayWeek = 'Su|Mo|Tu|We|Th|Fr|Sa';
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.startDate) {
          startDate = formatDate(
            this.textMessageForm?.startDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
        }
        if (this.textMessageForm?.endDate) {
          endDate = formatDate(
            this.textMessageForm?.endDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.time) {
          timeSlot = formatDate(this.textMessageForm?.time, 'HH:mm', 'en-US');
        }
        req = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          patientId: this.selectedPatientInfo?.patientId,
          question: messageDescription,
          senderName: userName,
          answer: ans,
          timeSlots: timeSlot,
          deliveryTime: startDate,
          timeZone: '',
          startDate: startDate,
          endDate: endDate,
          dayOfWeek: dayWeek,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(req)
          .subscribe((res) => {
            this.loader = false;
            this.encounterLoader = false;
            this.messageSidebarVisible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      } else {
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.startDate) {
          startDate = formatDate(
            this.textMessageForm?.startDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
        }
        if (this.textMessageForm?.endDate) {
          endDate = formatDate(
            this.textMessageForm?.endDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.time) {
          timeSlot = formatDate(this.textMessageForm?.time, 'HH:mm', 'en-US');
        }
        let dayofweek: any[] = [];
        let days = this.textMessageForm?.days;
        console.log(days, 'days......');
        if (days?.length === 0) {
          this.generateErrorMessage('Please select atleast one day');
        }

        let dayAbbreviations: { [key: string]: string } = {
          Sunday: 'Su',
          Monday: 'Mo',
          Tuesday: 'Tu',
          Wednesday: 'We',
          Thursday: 'Th',
          Friday: 'Fr',
          Saturday: 'Sa',
        };

        let daysString = days.map((day) => dayAbbreviations[day]).join('|');

        req = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          question: messageDescription,
          answer: ans,
          senderName: userName,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          dayOfWeek: daysString,
          timeSlots: timeSlot,
          patientId: this.selectedPatientInfo?.patientId,
          startDate: startDate,
          endDate: endDate,
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(req)
          .subscribe((res) => {
            this.loader = false;
            this.encounterLoader = false;
            this.messageSidebarVisible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      }
    }

  }
  generateErrorMessage(text: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: text,
      life: 3000,
    });
  }
  viewAlerts(patientId: any, patientInfo: any) {
    this.allPatientService.viewPatientAlerts(patientId).subscribe((res) => {
      this.openAlertsDilog = true;
      this.alerts = res.alerts;
    })

  }
  handleAlertClose() {
    this.openAlertsDilog = false;
  }

  uploadPatients(event: any) {
    this.loaderService.show();
    let medImage = event.files[0]
    if (medImage) {
      const formData = new FormData();
      formData.append('file', medImage);
      this.allPatientService
        .patientBulkUpload(formData)
        .subscribe((res) => {
          this.loaderService.hide();
          this.loadActivePatients({ rows: 25, first: 0 });
          this.fileUpload.clear();
          this.dashboardCount(this.startDate, this.endDate);
          if (res.status) {
            this.messageService.add({
              severity: 'success',
              summary: 'Confirmed',
              detail: 'Patients list uploaded successfully.',
              life: 3000,
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Failed to upload patients list',
              life: 3000,
            });
            return;
          }

        }, err => {
          this.loaderService.hide();
        });
    }
  }
  checkStatus(x: any) {
    if (x == 'N') {
      return "orange"
    }
    else {
      return "red"
    }
  }
  getClass(x: any) {
    if (x == 'N') {
      return "Not Taken"
    }
    else {
      return "Declined"
    }
  }
  downloadFile() {
    const fileUrl = `assets/pdf/Patients.xlsx`; // Update with your file path
    const anchor = document.createElement('a');
    anchor.href = fileUrl;
    anchor.download = 'patients.xlsx'; // Set the downloaded file name
    anchor.click();
  }

  previousRowsPerPage = 15;
  first = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
      this.itemsPerPage = event.rows
    } else {
      this.first = event.first;
    }
  }

  previousRowsPerPage1 = 15;
  first1 = 0;
  onPageChange1(event: any) {
    if (event.rows !== this.previousRowsPerPage1) {
      this.first1 = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage1 = event.rows;
      this.itemsPerPage1 = event.rows
    } else {
      this.first1 = event.first;
    }
  }

    getAllProvider() {
    this.profileService.getAllProvider().subscribe((res) => {
      if (res) {
        this.providerList = [];
        this.casemanagerList = [];
        for (const item of res?.physicianVOList) {
          this.providerList.push({
            label:
              item.firstName + ' ' + item.lastName + '(' + item.email + ')',
            id: item.watchrxUserID,
          });
        }
        for (const item of res?.clinicianVOList) {
          this.casemanagerList.push({
            label:
              item.firstName + ' ' + item.lastName + '(' + item.email + ')',
            id: item.userID,
          });
        }
      }
    });
  }
}
