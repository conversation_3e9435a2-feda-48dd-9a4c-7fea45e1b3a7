import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import {
  ReportResponse,ReportRequest,ReportExport
} from '../../../api/reports';
@Injectable({
  providedIn: 'root',
})
export class ReportService {
  constructor(private http: HttpClient) {}
   static readonly GRAPH_URL = '/service/patient/vitalGraphWeekly';
   static readonly VITAL_XLS_URL = '/service/charts/getVitalsForPatient';
   static readonly ALL_VITAL_XLS_URL = '/service/charts/getAllVitalsForPatient';
   static readonly ALERTS_XLS_URL = '/service/patient/getAlertsReportsXls';
   static readonly MEDICATION_XLS_URL = '/service/charts/missedmedicationReport';

  setReportData(reportReq : ReportRequest): Observable<ReportResponse> {
    return this.http.post<ReportResponse>(
      environment.BASE_URL + ReportService.GRAPH_URL, reportReq
    );
  }

  setReportOtherData(reportReq : ReportRequest, url:string): Observable<ReportResponse> {
    return this.http.post<ReportResponse>(
      environment.BASE_URL + url, reportReq
    );
  }

  exportData(reportReq : ReportRequest): Observable<ReportExport> {
    return this.http.post<ReportExport>(
      environment.BASE_URL + ReportService.VITAL_XLS_URL, reportReq
    );
  }
  exportAllData(reportReq : ReportRequest): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + ReportService.ALL_VITAL_XLS_URL, reportReq
    );
  }
  exportAlerts(reportReq : ReportRequest): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + ReportService.ALERTS_XLS_URL, reportReq
    );
  }
  exportMedication(reportReq : ReportRequest): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + ReportService.MEDICATION_XLS_URL, reportReq
    );
  }
 
}
