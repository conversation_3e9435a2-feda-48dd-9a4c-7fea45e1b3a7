import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { LazyLoadEvent } from 'primeng/api';
import { TableLazyLoadEvent } from 'primeng/table';
import * as XLSX from 'xlsx';
import { AdminAlert } from '../../../api/adminAlerts';
import { LoaderService } from '../../../loader/loader/loader.service';
import { GenericDateService } from '../../../service/generic-date.service';
import { MenuService } from '../../../service/menu.service';
import { AdminAlertsService } from './service/admin-alerts.service';

@Component({
  selector: 'app-admin-alerts',
  templateUrl: './admin-alerts.component.html',
  styleUrls: ['./admin-alerts.component.css'],
})
export class AdminAlertsComponent implements OnInit {
  constructor(
    private service: AdminAlertsService,
    private loaderService: LoaderService,
    private dateService: GenericDateService,
    private menuService: MenuService
  ) {}

  datePipe: DatePipe = new DatePipe('en-US');
  startDate: Date | undefined;
  endDate: Date | undefined;
  orgsList: any[] = [];
  selectedOrg: any;
  alertsList: AdminAlert[] = [];
  totalCount: number = 0;
  itemsPerPage: number = 10;
  patientsList: any[] = [];
  alertType: any = null;
  selectedPatient: any;

  alertTypeList: any[] = [
    { label: 'All', value: 'All' },
    { label: 'Alarm', value: 'Alarm' },
    { label: 'Critical', value: 'Critical' },
    { label: 'Warning', value: 'Warning' },
    { label: 'Info', value: 'Info' },
  ];

  columns: string[] = [
    'DATE_TIME',
    'SEVERITY',
    'PATIENT_NAME',
    'PHONE_NUMBER',
    'ALERT_DESCRIPTION',
  ];

  ngOnInit() {
    this.menuService.changeMenu('Alerts');
    this.startDate = this.dateService.getThreeMonthOldDate();
    this.endDate = this.dateService.getCurrentDate();
    this.getOrgsList();
  }

  getOrgsList() {
    this.service.getAllOrgsList().subscribe((res) => {
      if (res?.data) {
        this.orgsList = res?.data!;
        this.selectedOrg = this.orgsList[0]?.groupId;
        this.loadAlerts({ first: 0, rows: 10 });
        this.getPatientsByOrgId();
      }
    });
  }

  loadAlerts($event?: LazyLoadEvent | TableLazyLoadEvent) {
    if (this.alertType && this.alertType !== '') {
      this.alertByType($event);
      return;
    }
    let pageSize = $event?.rows || 10;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let payload = {
      startDate: this.getFormattedDate(
        this.startDate || this.dateService.getThreeMonthOldDate()
      ),
      endDate: this.getFormattedDate(
        this.endDate || this.dateService.getCurrentDate()
      ),
      index: pageNo,
      pageSize: pageSize,
      orgId: this.selectedOrg,
    };

    this.service.getAdminAlerts(payload).subscribe((res) => {
      if (res.success) {
        this.alertsList = res.medicationalertList;
        this.totalCount = res.count!;
      }
    });
  }

  getPatientsByOrgId() {
    this.service.getPatienstForAlerts(this.selectedOrg).subscribe((res) => {
      if (res?.success) {
        this.patientsList = res?.filterPatientData!;
      }
    });
  }

  alertByCriteria(
    filterType: 'severity' | 'patientName',
    filterValue: string | null,
    $event?: LazyLoadEvent | TableLazyLoadEvent
  ) {
    if (filterValue === null) {
      this.loadAlerts({ first: 0, rows: 10 });
      return;
    }

    const pageSize = $event?.rows || 10;
    const first = $event?.first || 0;
    const pageNo = first / pageSize;

    const payload = {
      startDate: this.getFormattedDate(
        this.startDate || this.dateService.getThreeMonthOldDate()
      ),
      endDate: this.getFormattedDate(
        this.endDate || this.dateService.getCurrentDate()
      ),
      index: pageNo,
      itemsPerPage: pageSize,
      orgId: this.selectedOrg,
      filterType: filterType,
      filterValue: filterValue,
    };

    this.service.getAdminAlertsByFilter(payload).subscribe((res) => {
      if (res.success) {
        this.alertsList = res.medicationalertList;
        this.totalCount = res.count!;
      }
    });
  }

  alertByType($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.alertByCriteria('severity', this.alertType, $event);
  }

  alertByPatient($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.alertByCriteria('patientName', this.selectedPatient, $event);
  }

  submitFilter() {
    this.loadAlerts();
  }

  getFormattedDate(date: Date) {
    return this.datePipe.transform(date, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
  }

  downlaodXLSX() {
    this.loaderService.show();
    let payload = {
      startDate: this.getFormattedDate(
        this.startDate || this.dateService.getThreeMonthOldDate()
      ),
      endDate: this.getFormattedDate(
        this.endDate || this.dateService.getCurrentDate()
      ),
    };
    this.service.exportData(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        let finalArray = new Array();
        res.forEach((x) => {
          finalArray.push({
            DATE_TIME: x.dateTime,
            SEVERITY: x.alertType,
            PATIENT_NAME: x.patientName,
            PHONE_NUMBER: x.patientPhoneNumber,
            ALERT_DESCRIPTION: x.alertDescription,
          });
        });
        const worksheet = XLSX.utils.json_to_sheet(finalArray, {
          header: this.columns,
        });
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Alerts');
        XLSX.writeFile(workbook, 'Alert.xlsx');
      }
    });
  }

  clearFilter() {
    this.startDate = this.dateService.getThreeMonthOldDate();
    this.endDate = this.dateService.getCurrentDate();
    this.loadAlerts();
  }

  getSeverity(status: string): string {
    switch (status) {
      case 'Info':
        return 'info';
      case 'Alarm':
        return 'warning';
      case 'Warning':
        return 'warning';
      case 'Critical':
        return 'danger';
      default:
        return 'info';
    }
  }
}
