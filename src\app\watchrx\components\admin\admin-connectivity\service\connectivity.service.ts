import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { ConnectivityResponse } from '../../../../api/connectivity';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class ConnectivityService {
  constructor(private http: HttpClient) {}

  getConnectedDeviceList(): Observable<ConnectivityResponse> {
    return this.http.get<ConnectivityResponse>(
      environment.BASE_URL + Constants.CONNECTIVITY_STATUS
    );
  }

  heartBeatOrLogs(payload: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.HEART_BEAT,
      payload
    );
  }
}
