import { Component, OnInit, ViewChild } from '@angular/core';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FileUpload } from 'primeng/fileupload';
import { Template, TemplateDetails } from '../../../api/adminTemplates';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { TemplateService } from './service/template.service';

@Component({
  selector: 'app-admin-templates',
  templateUrl: './admin-templates.component.html',
  styleUrls: ['./admin-templates.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminTemplatesComponent implements OnInit {
  constructor(
    private loaderService: LoaderService,
    private messageService: MessageService,
    private templateService: TemplateService,
    private confirmationService: ConfirmationService,
    private menuService: MenuService
  ) { }

  @ViewChild('fileUpload') fileUpload: FileUpload | undefined;

  ccNameList: Template[] = [];
  selectedChronic: any;
  templateDetails: TemplateDetails[] = [];

  manual: boolean = false;
  question: string = '';
  answers: string = '';
  loader: boolean = false;

  editQuestionId: number = 0;
  isEdit: boolean = false;
  uploadFile: any | undefined;


  selectedChronicProgram: any = null;
  selectedCategory: any = null;
  categoryList: any[] = [
    { name: 'Mandatory', label: 'Mandatory' },
    { name: 'Monitoring', label: 'Monitoring' },
    { name: 'Medication', label: 'Medication' },
    { name: 'Lifestyle', label: 'Lifestyle' },
    { name: 'SDOH', label: 'SDOH' },
    { name: 'Preventive Care', label: 'Preventive Care' },
    { name: 'Follow-Up with Specialists', label: 'Follow-Up with Specialists' },]

  ngOnInit() {
    this.menuService.changeMenu('Templates');
    this.getAllTemplateNames();
    this.getChronicConditionsList();

  }

  getAllTemplateNames() {
    this.loaderService.show();
    this.templateService.getAllChroniConditions().subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.ccNameList = res?.eList;
        this.ccNameList.unshift({
          chonicConditionName: 'ALL',
          chonicConditionId: 0,
          createdByUserId: 0,
          createdByUserName: '',
          createdDate: '',
          updatedDate: '',
          icdCode: ''
        });
        this.selectedChronic = this.ccNameList[0];
        this.selectedChronicProgram = this.ccNameList[0];
        this.onChangeTemplate();
      }
    });
  }

  onChangeTemplate() {
    if (this.selectedChronic === null) {
      this.templateDetails = [];
      return;
    }
    this.loaderService.show();
    this.templateService
      .getTemplateDetailsById(this.selectedChronic?.chonicConditionName)
      .subscribe((res) => {
        this.loaderService.hide();
        this.templateDetails = res?.templates;
      });
  }

  getDate(date: string) {
    return moment.utc(date).local().format('DD MMM YYYY, h:mm A');
    // return moment(date).format('DD MMM YYYY, h:mm A');
  }

  openManulaAddPopup() {
    this.manual = true;
    this.resetFileds();
  }

  adManualData() {
    if (this.question === '' || this.answers === '' || this.selectedChronicProgram === null || this.selectedCategory === null || this.selectedChronicCondition === null) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill all the fields',
      });
      return;
    }


    let payload = {
      chronicConditionId: this.selectedChronicCondition?.chonicConditionId,
      questions: this.question,
      answer: this.answers,
      programName: this.selectedChronicProgram?.chonicConditionName,
      category: this.selectedCategory?.name,
    };
    this.loader = true;
    this.templateService.addTemplateManual(payload).subscribe((response) => {
      this.loader = false;
      if (response.success) {
        this.manual = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Added successfully',
        });
        this.onChangeTemplate();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0]
            ? response?.messages[0]
            : 'Failed to add ',
        });
      }
    });
  }

  onDeleteQuestion(event: Event, questionId: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = {
          questionId: questionId,
        };
        this.templateService.deleteQuestion(payload).subscribe((res) => {
          if (res.success) {
            this.onChangeTemplate();
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Question deleted successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete Question.',
            });
          }
        });
      },
      reject: () => { },
    });
  }

  onEditQuestion(item: TemplateDetails) {
    this.question = item?.questions;
    this.answers = item?.answer;
    this.editQuestionId = item?.questionId;
    this.isEdit = true;
    this.manual = true;
    this.selectedCategory = item?.category;
    this.selectedChronicProgram = item?.programName;
    this.selectedChronicCondition = item?.chronicConditionId;
  }

  saveEditedQuestion() {
    let payload = {
      questions: this.question,
      answer: this.answers,
      questionId: this.editQuestionId,
    };

    this.loader = true;
    this.templateService.updateQuestion(payload).subscribe((response) => {
      this.loader = false;
      if (response.success) {
        this.manual = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: response?.messages[0],
        });
        this.onChangeTemplate();
        this.resetFileds();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0]
            ? response?.messages[0]
            : 'Failed to update ',
        });
      }
    });
  }

  toggleStatus(id: number) {
    console.log('Clikcked.....');
    this.loaderService.show();
    this.templateService.toggleStatus(id).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.manual = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: response?.messages[0],
        });
        this.onChangeTemplate();
        this.resetFileds();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0]
            ? response?.messages[0]
            : 'Failed to update ',
        });
      }
    });
  }

  resetFileds() {
    this.question = '';
    this.answers = '';
    this.editQuestionId = 0;
    this.isEdit = false;
  }

  onFileSelected(event: any) {
    const file: File = event.files[0];
    if (
      file &&
      file.type ===
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ) {
      const formData = new FormData();
      formData.append('file', file);
      this.loaderService.show();
      this.templateService.updateTemplateFile(formData).subscribe((res) => {
        this.onChangeTemplate();
        this.loaderService.hide();
        if (this.fileUpload) {
          this.fileUpload.clear();
        }
        if (res.status) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Template uploaded successfully',
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update Template',
          });
        }
      });
    } else {
      this.loaderService.hide();
      console.error('Invalid file type');
    }
  }
  downloadFile() {
    const fileUrl = `assets/pdf/chronic-template.xlsx`; // Update with your file path
    const anchor = document.createElement('a');
    anchor.href = fileUrl;
    anchor.download = 'chronic-template.xlsx'; // Set the downloaded file name
    anchor.click();
  }

  chronicList: any[] = [];
  selectedChronicCondition: any = null;
  getChronicConditionsList() {
    this.templateService.getChronicConditions().subscribe((res) => {
      this.chronicList = res?.eList;
      this.chronicList.unshift({
        chonicConditionName: 'ALL',
        chonicConditionId: 1,
        createdByUserId: 0,
        createdByUserName: '',
        createdDate: '',
        updatedDate: '',
        icdCode: ''
      });

    });
   // this.selectedChronicCondition = this.chronicList[0];
  }
}
