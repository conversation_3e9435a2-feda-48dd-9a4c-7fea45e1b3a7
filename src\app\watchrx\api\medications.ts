export interface Prescription {
  prescriptionId: number;
  patientId: number;
  beforeOrAfterFood: string;
  color: string;
  quantity: string|number;
  createdDate: number;
  updatedDate: number;
  daysOfWeek: string;
  description: string;
  dosage: string;
  imagePath: string;
  imageUrl: string;
  medicineForm: number;
  medicineName: string;
  quantities: string;
  medTimeRelativeToFood: string;
  timeSlots: string;
  existingOrNewschedule: string;
  isReviewed: boolean;
  isReminderReq: boolean;
}

export interface ResponseData {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  prescriptions: Prescription[];
}

export interface MedicationResp {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: any[];
  createdPrescription: number;
}

export interface MedicationEHResp {
  messages: message[];
}

export interface message
{
  patientId:number,
  medicationSummaryId:number,
  medicationSummary:string
}