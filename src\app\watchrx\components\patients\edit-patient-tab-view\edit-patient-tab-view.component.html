<p-toast />
<p-confirmDialog />
<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/nurse.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Patients / {{ patientName }}</span
  >
  <span>{{displayTime}}</span>
</div>
<div
  class="flex gap-2 justify-content-between"
  style="background-color: white; border: 2px solid #e8e8e8; padding: 10px"
>
  <div
    class="flex gap-2 flex-row justify-content-between w-full align-items-center flex-wrap"
    *ngIf="patientName; else emptytemplate"
  >
    <div class="flex flex-1 gap-2 justify-content-between pl-2 pr-2">
      <div>
        <p class="font-bold mb-2 font-16">{{ patientName }}</p>
        <p class="m-0 p-0">
          {{patientInformation?.dateOfBirth}}
          <!-- {{ getFormattedDate(patientInformation?.dob, "MM/DD/YYYY") }} -->
          <!-- {{ "," }} {{ getTotalYears(patientInformation?.dob) }} -->
        </p>
        <p>{{ patientInformation && patientInformation.phoneNumber }}</p>
      </div>
      <div>
        <p class="font-bold mb-2 font-16">Readings Taken</p>
        <p>
          {{ (patientInformation && patientInformation.daysMeasured) || 0 }}
          Days
        </p>
      </div>
      <ng-container
        *ngIf="
          patientInformation.programs &&
          patientInformation.programs.selectedPrograms &&
          patientInformation.programs.selectedPrograms.length > 0
        "
      >
        <div
          *ngFor="
            let selectedPrograms of patientInformation.programs.selectedPrograms
          "
        >
          <p class="font-bold mb-2 font-16">
            {{ selectedPrograms.programName }}
          </p>
          <p class="p-text-warning font-bold">{{ convert(selectedPrograms.mins) }}</p>
        </div>
      </ng-container>
      <div></div>
    </div>
    <div class="flex justify-content-between ">
      <!-- <div
      *ngIf="activeIndex == 0"
      class="flex align-items-center md:justify-content-between button-background gap-3"
      style="
        border-radius: 5px;
        cursor: pointer;
        color: #ffffff;
        background: #475569;
        border: 1px solid #475569;
        padding-left: 5px;
        padding-right: 5px;
      "
    >
      <div class="mt-1 text-600 font-bold">
        <span style="color: white">{{ patientStatus }}</span>
      </div>
      <div class="mt-2 md:mt-2 flex">
        <span class="text-black-500 ml-10 font-medium">
          <p-inputSwitch
            [(ngModel)]="pStatus"
            (onChange)="onChangeStatus(pStatus)"
          />
        </span>
      </div>
    </div> -->
      <!-- <p-button
        *ngIf="pStatus"
        label="InActivate"
        [outlined]="true"
        severity="secondary"
        (onClick)="onChangeStatus(false)"
        class="mr-3"
      /> -->
      <!-- <p-button
        *ngIf="!pStatus"
        label="Activate"
        [outlined]="true"
        severity="secondary"
        (onClick)="onChangeStatus(true)"
        class="mr-3"
      /> -->
      <div class="button-group">
        <audio-recorder></audio-recorder>
        <p-button
          label="Video Call"
          [outlined]="true"
          severity="secondary"
          [disabled]="false"
          icon="pi pi-video"
          class="mr-3"
          (onClick)="showVideoCallWindow()"
        />
      </div>
      <p-button
        label="Call"
        [outlined]="true"
        severity="secondary"
        [disabled]="false"
        icon="pi pi-phone"
        class="mr-3"
        (onClick)="showCallWindow()"
      />
      <p-button
        label="Delete"
        [outlined]="true"
        severity="danger"
        icon="pi pi-trash"
        *ngIf="activeIndex == 0 && userRole!==3"
        (onClick)="onDeletePatient()"
        class="mr-3"
      />
      <!-- *ngIf="activeIndex == 0 && (connectingDevice === 'Mobile'||connectingDevice === 'mobile')"-->
      <p-button
        label="Resend OTP"
        [link]="true"
        severity="secondary"
        (onClick)="resendOtp($event)"
      />
    </div>
  </div>
  <ng-template #emptytemplate>
    <div
      class="flex gap-2 flex-row justify-content-between w-full align-items-center"
    >
      Loading...
    </div>
  </ng-template>
  <!-- <div class="gray-box" *ngIf="showMins">
    <div class="white-text" [innerHTML]="safeHtml"></div>
  </div> -->
</div>
<div  class="editpatientscreen">
  <p-tabView [(activeIndex)]="activeIndex" (onChange)="onMainTabClick($event)" *ngIf="pStatus">
    <p-tabPanel
      header="Dashboard"
      *ngIf="pStatus"
      [selected]="activeIndex === 0"
      class="dashboard"
    >
      <app-dashboard 
      *ngIf="activeIndex === 0"
        [id]="patientId"
        (dataEmitter)="handleDataFromChild($event)"
      ></app-dashboard>
    </p-tabPanel>

    <p-tabPanel header="Profile" [selected]="activeIndex === 1" class="profile">
      <ng-container>
        <app-profile *ngIf="activeIndex === 1" (dataEmitter)="handleDataFromChild($event)"></app-profile>
      </ng-container>
    </p-tabPanel>

    <p-tabPanel
      header="Care Plan" 
      *ngIf="pStatus && isNameFound('RPM/CCM/PCM')"
      [selected]="activeIndex === 2"
      class="profile"
    >
      <app-care-plan *ngIf="activeIndex === 2"
        (dataEmitter)="handleDataFromChild($event)"
      ></app-care-plan>
    </p-tabPanel>

    <p-tabPanel
      header="Encounter"
      *ngIf="pStatus"
      [selected]="activeIndex === 3"
      class="encounter"
    >
      <app-encounter-tab-view *ngIf="activeIndex === 3"
        (dataEmitter)="handleDataFromChild($event)" [patientData]="patientInformation"
      ></app-encounter-tab-view>
    </p-tabPanel>

    <p-tabPanel header="Alerts" *ngIf="pStatus" [selected]="activeIndex === 4"  class="alerts">
      <app-individual-alerts *ngIf="activeIndex === 4"
        (dataEmitter)="handleDataFromChild($event)"
      ></app-individual-alerts>
    </p-tabPanel>

    <p-tabPanel
      header="Vital" class="profile"
      *ngIf="pStatus && isNameFound('Vitals')"
      [selected]="activeIndex === 5"
    >
      <app-vitals (dataEmitter)="handleDataFromChild($event)" *ngIf="activeIndex === 5"></app-vitals>
    </p-tabPanel>

    <p-tabPanel
      header="Medication"
      *ngIf="pStatus && isNameFound('Medication adherence')"
      [selected]="activeIndex === 6"
      class="medi"
    >
      <app-medication-tab-view
        (dataEmitter)="handleDataFromChild($event)" *ngIf="activeIndex === 6"
      ></app-medication-tab-view>
    </p-tabPanel>

    <p-tabPanel
      header="GPS"
      *ngIf="pStatus && isNameFound('GPS') && userRole!=3"
      [selected]="activeIndex === 7"
    >
      <app-gps (dataEmitter)="handleDataFromChild($event)" *ngIf="activeIndex === 7"></app-gps>
    </p-tabPanel>

    <p-tabPanel
      header="Custom Reminder"
      *ngIf="pStatus&& userRole!=3"
      [selected]="activeIndex === 8"
      class="customremainder"
    >
      <app-custom-reminder
        (dataEmitter)="handleDataFromChild($event)" *ngIf="activeIndex === 8"
      ></app-custom-reminder>
    </p-tabPanel>
  </p-tabView>
  <p-tabView [(activeIndex)]="activeIndex" (onChange)="onMainTabClick($event)" *ngIf="!pStatus">
    <p-tabPanel header="Profile" [selected]="activeIndex === 1" class="profile">
      <ng-container>
        <app-profile *ngIf="activeIndex === 1" (dataEmitter)="handleDataFromChild($event)"></app-profile>
      </ng-container>
    </p-tabPanel>
  </p-tabView>
</div>
<app-calls  [openVoiceVideoDlg]="true" [selectedPatientInfo]="patientInformation" *ngIf="openVoiceVideoDlg" (closeModel)="openVoiceVideoDlg=false"></app-calls>
<app-video-calls  [openVideoDlg]="true" [selectedPatientInfo]="patientInformation" *ngIf="openVideoDlg" (closeModel)="openVideoDlg=false"></app-video-calls>