.p-sidebar-header {
    border-bottom: 1px solid #000000;
}
.p-sidebar-header {
    border-bottom: 1px solid #000000;
}

::ng-deep .p-tabview-nav-link
{
    border: 1px solid !important;
    padding: 0.95rem 1.25rem !important;
    border-radius: 3px;
    margin: 0px !important;
    border-color:#e5e7eb !important;
}
::ng-deep .p-tabview-nav
{
    border: 0px !important;
    padding: 1.25rem;
}
::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link
{
    background: #f8f8fa !important;
    border-color:#e5e7eb !important;
    color: #6366F1;
}
::ng-deep .p-tabview-nav-content
{
    border-bottom: 1px solid #e8e8e8;
}
.bulkadd
{
    position: absolute;
    top: 18px;
    left: 340px;
    z-index: 1;
    align-items: center;
    display: flex;
    justify-content: center;
    ::ng-deep .p-button.p-button-secondary.p-button-outlined, .p-buttonset.p-button-secondary > .p-button.p-button-outlined, .p-splitbutton.p-button-secondary > .p-button.p-button-outlined 
    {
        background-color: transparent;
        color: #64748b;
        border: 1px solid #e5e7eb !important;
        border-radius: 2px;
        line-height: 19px;
    }
    ::ng-deep .p-button.p-button-secondary:enabled:focus
    {
        box-shadow: 0;
        background: #f8f8fa !important;
        border-color: #e5e7eb !important;
        color: #6366F1;
    }
}

::ng-deep .p-sidebar-footer {
    border-top: 1px solid #000000;
}
::ng-deep .p-sidebar-header {
    border-bottom: 1px solid #000000;
}