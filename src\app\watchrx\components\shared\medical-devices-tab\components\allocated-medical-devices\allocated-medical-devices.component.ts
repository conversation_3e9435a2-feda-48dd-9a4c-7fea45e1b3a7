import { CommonModule, formatDate } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { debounceTime, Subject } from 'rxjs';
import { allocation } from '../../../../../api/deviceAllocation';
import { MenuService } from '../../../../../service/menu.service';
import { MedicalDeviceService } from '../../service/medical-device.service';
import * as XLSX from 'xlsx';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule } from '@angular/forms';
@Component({
  standalone: true,
  selector: 'app-allocated-medical-devices',
  templateUrl: './allocated-medical-devices.component.html',
  styleUrls: ['./allocated-medical-devices.component.css'],
  imports: [CommonModule, ToastModule, ConfirmDialogModule, TableModule, DropdownModule, FormsModule],
})
export class AllocatedMedicalDevicesComponent implements OnInit {
  orgsList: any;
  selectedOrg: any = 0;
  constructor(
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private deviceService: MedicalDeviceService,
    private menuService: MenuService
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      this.debouncedValue = value;
      console.log('Search Value:', this.debouncedValue);
      this.onSearch(this.debouncedValue);
    });
  }

  response: allocation[] = [];
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: string = '';
  loadingdevices: boolean = true;
  totalDeviceCount: number = 0;
  userRole:any='';
  searchText="";
  rows:number=15;
  ngOnInit() {
    let user = localStorage.getItem('user');
    this.userRole = user ? JSON.parse(user)?.roleType : null;
    this.getOrgList()
    this.menuService.changeMenu('Medical Devices');
  }

  unassignDevice(event: Event, device: allocation) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message:
        'Are you sure you want to delete ' +
        device?.sourceInventoryForWatchPurchase?.deviceName +
        ' from ' +
        device?.patientName +
        '?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-primary',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',

      accept: () => {
        this.deviceService
          .unassignDevice(device.watchAllocatedByAdmin.watchId.toString())
          .subscribe((res) => {
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Unassigned successfully.',
              });
              this.loadDevices();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to un-assign device.',
              });
            }
          });
      },
      reject: () => { },
    });
  }

  loadDevices($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loadingdevices = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize + '/' + 0;
    this.deviceService.setAllocatedDevice(url).subscribe((res) => {
      if (res.success) {
        this.response = res.purchasedWatchList;
        this.totalDeviceCount = res.resultCount;
        this.loadingdevices = false;
      }
    });
  }

  onInput(event: any): void {
    this.inputSubject.next(this.searchText);
  }

  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.deviceService.searchDevice(searchTerm).subscribe((res) => {
        if (res.success) {
          this.response = res.purchasedWatchList;
          this.totalDeviceCount = res.resultCount;
          this.loadingdevices = false;
          this.rows=res.resultCount;
        }
      });
    } else {
      if (searchTerm !== undefined && searchTerm.length === 0) {
        this.rows=15;
        this.first=0;
        this.deviceService.setAllocatedDevice('0/15/0').subscribe((res) => {
          if (res.success) {
            this.response = res.purchasedWatchList;
            this.totalDeviceCount = res.resultCount;
            this.loadingdevices = false;
          }
        });
      }
    }
  }

  exportToExcel(type: string) {
    let fileName = this.getFilename();
    this.deviceService.exportData(this.selectedOrg, fileName).subscribe((response) => {
      if (response.success) {
        console.log('File downloaded successfully');
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'File downloaded successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Error downloading file',
        });
      }
    });
  }

  getOrgList() {
    this.deviceService.getAllOrgsList().subscribe((res) => {
      if (res?.data) {
        this.orgsList = res?.data!;
        this.orgsList.unshift({ groupName: "All Clinics", groupId: 0 })
        this.selectedOrg = this.orgsList[0]?.groupId;
      }
    })
  }
  onOrgChange(eve: any) {
    this.selectedOrg = eve.value;
    let filterValue = this.orgsList.filter((org:any) => org.groupId == this.selectedOrg);
    if (filterValue.length > 0 && filterValue[0].groupName!='All Clinics') {
      this.onSearch(filterValue[0].groupName)
    }
    else
    {
      this.onSearch("")
    }
  }
  getFilename() {
    let orgName = 'All_Clinics'
    let filterValue = this.orgsList.filter((org:any) => org.groupId == this.selectedOrg);
    if (filterValue.length > 0) {
      orgName = filterValue[0].groupName
    }
    return orgName + '_Allocated_Devices' + '.xlsx'
  }

  first=0
  previousRowsPerPage=15
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }
}
