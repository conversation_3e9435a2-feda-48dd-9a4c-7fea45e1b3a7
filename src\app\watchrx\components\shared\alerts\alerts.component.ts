import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ConfirmationService, LazyLoadEvent } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import * as XLSX from 'xlsx';
import { AlertRequest } from '../../../api/alert';
import { PatientsInfo } from '../../../api/dashboard';
import { GenericDateService } from '../../../service/generic-date.service';
import { MenuService } from '../../../service/menu.service';
import { PatientAlertsRoutingModule } from '../../patient-alerts/patient-alerts-routing.module';
import { AlertService } from './service/alert.service';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { Router } from '@angular/router';

@Component({
  selector: 'app-alerts',
  standalone: true,
  templateUrl: './alerts.component.html',
  styleUrls: ['./alerts.component.css'],
  imports: [
    CommonModule,
    PatientAlertsRoutingModule,
    FormsModule,
    TableModule,
    CalendarModule,
    ButtonModule,
    TagModule,
    DropdownModule,
    ConfirmDialogModule
  ],
  providers: [ ConfirmationService],
})
export class AlertsComponent implements OnInit {
  startDate: Date | null = null;
  endDate: Date | null = null;

  response: any;
  rows: number = 20;
  rowsPerPageOptions: number = 20;
  first: number = 0;
  patientName: any = null;
  patient?: PatientsInfo;
  totalAlertCount!: number;
  loadingAlerts: boolean = true;
  alertType: any = null;

  @Input() patientId: number = 0;

  alertTypeList: any[] = [
    { label: 'All', value: 'All' },
    { label: 'Alarm', value: 'Alarm' },
    { label: 'Critical', value: 'Critical' },
    { label: 'Warning', value: 'Warning' },
    { label: 'Info', value: 'Info' },
  ];

  columns: string[] = [
    'DATE_TIME',
    'SEVERITY',
    'PATIENT_NAME',
    'PHONE_NUMBER',
    'ALERT_DESCRIPTION',
  ];

  getRowClass(row: string): string {
    switch (row) {
      case 'Critical':
        return 'redish';
      case 'Alarm':
        return 'alarm';
      case 'Warning':
        return 'warning';
      case 'Info':
        return 'info';
      default:
        return 'ok';
    }
  }

  getSeverity(status: string): string {
    switch (status) {
      case 'Info':
        return 'info';
      case 'Alarm':
        return 'warning';
      case 'Warning':
        return 'warning';
      case 'Critical':
        return 'danger';
      default:
        return 'info';
    }
  }

  constructor(
    public menuService: MenuService,
    private alertService: AlertService,
    private dateService: GenericDateService,
     public confirmationService:ConfirmationService,
     public router:Router
  ) {}

  ngOnInit(): void {
    if (this.patientId > 0) {
      this.menuService.changeMenu('Patients');
    } else {
      this.menuService.changeMenu('Alerts');
    }
    this.startDate = this.dateService.getStartDateOfMonth();
    this.endDate = new Date();
  }

  alertByDate() {
    let req: AlertRequest = {
      index: 0,
      itemsPerPage: this.previousRowsPerPage||15,
      patientId: new Array(),
      alertSeverity: (this.alertType=="All"||this.alertType==null)?null:this.alertType,
      startDate: this.startDate ? this.onDateSelect(this.startDate) : null,
      endDate:this.endDate ? this.onDateSelect(this.endDate) : null,
    };
    this.alertData(req);
  }

  onAlertDataByPage($event: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event.rows || 15;
    let first = $event.first || 0;
    let pageNo = first / pageSize;

    let req: AlertRequest = {
      index: pageNo,
      itemsPerPage: pageSize,
      patientId: new Array(),
      alertSeverity: (this.alertType=="All"||this.alertType==null)?null:this.alertType,
      startDate: this.startDate ? this.onDateSelect(this.startDate) : null,
      endDate:this.endDate ? this.onDateSelect(this.endDate) : null,
    };
    this.alertData(req);
  }

   onDateSelect(event: Date) {
    const year = event.getFullYear();
    const month = (event.getMonth() + 1).toString().padStart(2, '0');
    const day = event.getDate().toString().padStart(2, '0');

    // Construct fixed string without timezone shift
   return `${year}-${month}-${day}T00:00:00.000`;

   
  }

  exportToExcel(type: string) {
    let req: AlertRequest = {
      index: 0,
      itemsPerPage: this.previousRowsPerPage||15,
      patientId: new Array(),
      alertSeverity: this.alertType||'All',
      startDate: this.startDate,
      endDate: this.endDate,
    };

    let patientIds = new Array();
    if (this.patient) {
      patientIds.push(this.patient.patientId);
    }
    if (this.patientId > 0) {
      patientIds.push(this.patientId);
    }
    req.patientId = patientIds;
    this.alertService.exportData(req).subscribe((res) => {
      if (res) {
        let finalArray = new Array();
        res.forEach((x) => {
          finalArray.push({
            DATE_TIME: x.dateTime,
            SEVERITY: x.alertType,
            PATIENT_NAME: x.patientName,
            PHONE_NUMBER: x.patientPhoneNumber,
            ALERT_DESCRIPTION: x.alertDescription,
          });
        });
        const worksheet = XLSX.utils.json_to_sheet(finalArray, {
          header: this.columns,
        });
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Alerts');
        XLSX.writeFile(workbook, 'Alert.' + type);
      }
    });
  }

  private alertData(req: AlertRequest) {
    this.loadingAlerts = true;
    let patientIds = new Array();

    if (this.patient) {
      patientIds.push(this.patient.patientId);
    }
    if (this.patientId > 0) {
      patientIds.push(this.patientId);
    }
    req.patientId = patientIds;
    this.alertService.alertData(req).subscribe((res) => {
      if (res.success) {
        this.response = res;
        this.totalAlertCount = res.totalCount;
        this.loadingAlerts = false;
      }
    });
  }

   deleteAlert(req:any)
  {
    this.confirmationService.confirm({
      message: 'Are you sure that you want to delete the alert?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-circle',
      acceptIcon:"none",
      rejectIcon:"none",
      rejectButtonStyleClass:"p-button-text",
      accept: () => {
        this.alertService.deleteData(req).subscribe(()=>{
          this.alertByDate()
        })
      },
      reject: () => {
          
      }
  });
 
  }

  editPatient(patientId: string) {
    this.router.navigate(['/editpatient'], { queryParams: { id: patientId } });
  }

  
  previousRowsPerPage = 15;
  firstPage = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.firstPage = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.firstPage = event.first;
    }
  }
}
