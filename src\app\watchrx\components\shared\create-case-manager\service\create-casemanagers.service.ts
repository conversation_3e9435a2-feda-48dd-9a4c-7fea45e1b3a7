import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { GroupsAndRoles } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { APIConfig } from './APIConfig';
@Injectable({
  providedIn: 'root',
})
export class CreateCasemanagersService {
  constructor(private http: HttpClient) {}

  getGroupsandRolesList(): Observable<GroupsAndRoles> {
    return this.http.get<GroupsAndRoles>(
      environment.BASE_URL + APIConfig.GROUPS_AND_ROLES
    );
  }
  createCaseManager(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.CREATE_CASEMANAGER,
      details
    );
  }
}
