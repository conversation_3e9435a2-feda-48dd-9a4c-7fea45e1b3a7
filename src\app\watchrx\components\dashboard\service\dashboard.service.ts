import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { saveAs } from 'file-saver';
import { Observable, catchError, map, throwError } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { AddPatientResp } from '../../../api/addPatient';
import {
  AlertCount,
  CalendarTask,
  CriticalAlert,
  DeleteTask,
  PatientBilling,
  PatientCount,
  PatientDueTask,
  PatientGraph,
  PatientTask,
} from '../../../api/dashboard';
import { AddEncounter, ApiResponse } from '../../../api/patientEncounter';
import { Constants } from '../dashboard.constant';
@Injectable({
  providedIn: 'root',
})
export class DashboardService {
  constructor(private http: HttpClient) { }

  setAlertCount(fromToDetails?:string): Observable<AlertCount> {
    return this.http.get<AlertCount>(
      //environment.BASE_URL + Constants.ALERT_COUNT_URL+'/'+fromToDetails
      environment.BASE_URL + Constants.ALERT_COUNT_URL
    );
  }

  setPatientCount(fromToDetails: string): Observable<PatientCount> {
    return this.http.get<PatientCount>(
      environment.BASE_URL + Constants.PATIENT_COUNT_URL + fromToDetails
    );
  }

  setPatientGraph(fromToDetails: string): Observable<PatientGraph> {
    console.log(
      environment.BASE_URL + Constants.PATIENT_GRAPH_URL + fromToDetails
    );
    return this.http.get<PatientGraph>(
      environment.BASE_URL + Constants.PATIENT_GRAPH_URL + fromToDetails
    );
  }

  setPatientBilling(fromToDetails: string): Observable<PatientBilling> {
    return this.http.get<PatientBilling>(
      environment.BASE_URL + Constants.PATIENT_BILLING_URL + fromToDetails
    );
  }

  setPatientTask(): Observable<PatientTask> {
    return this.http.get<PatientTask>(
      environment.BASE_URL + Constants.PATIENT_TASK_URL
    );
  }

  setPatientDueTask(fromToIndex: string): Observable<PatientDueTask> {
    return this.http.get<PatientDueTask>(
      environment.BASE_URL + Constants.NON_CLOSED_TASK_URL + fromToIndex
    );
  }

  setPatientCriticalAlert(fromToIndex: string): Observable<CriticalAlert> {
    return this.http.get<CriticalAlert>(
      environment.BASE_URL + Constants.CRITICAL_ALERT_URL + fromToIndex
    );
  }

  setTodayCalendarTasks(fromToDetails: string): Observable<CalendarTask[]> {
    return this.http.post<CalendarTask[]>(
      environment.BASE_URL + Constants.CALENDAR_TASK_URL + fromToDetails, { timezone: Intl.DateTimeFormat().resolvedOptions().timeZone }
    );
  }

  setLastCallPatients(fromToDetails: string): Observable<PatientBilling> {
    return this.http.get<PatientBilling>(
      environment.BASE_URL +
      Constants.LATEST_CALLLED_PATIENT_URL +
      fromToDetails
    );
  }

  downloadStatistics(
    startDate: string,
    endDate: string,
    fileName: string
  ): Observable<any> {
    const url =
      environment.BASE_URL +
      Constants.DASHBOARD_STATS_PATIENT_URL +
      `${startDate}/${endDate}`;
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });

    return this.http.get(url, { headers, responseType: 'arraybuffer' }).pipe(
      map((response: BlobPart) => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        saveAs(blob, fileName);
        return { success: true };
      }),
      catchError((error) => {
        return throwError({ success: false });
      })
    );
  }

  deleteTask(taskId: number): Observable<DeleteTask> {
    return this.http.delete<DeleteTask>(
      environment.BASE_URL + Constants.DELETE_TASK_URL + taskId
    );
  }

  getPatientEncounter(details: string): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.ENCOUNTER_LIST + details
    );
  }

  addEncounter(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ADD_ENCOUNTER,
      details
    );
  }

  updateAlert(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ACK_ALERT,
      details
    );
  }

  sendMessage(details: any): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.SEND_MESSAGE,
      details
    );
  }
    getReasonList(): Observable<any> {
      return this.http.get<any>(
        environment.BASE_URL + Constants.GET_REASON_LIST
      );  
    }

    acknowledgeAlert(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ACK_ALERT,
      details
    );
  }
}
