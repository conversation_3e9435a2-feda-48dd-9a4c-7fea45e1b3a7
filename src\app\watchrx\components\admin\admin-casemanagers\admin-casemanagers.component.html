<p-toast />
<p-confirmDialog />
<div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb">
  <span class="font-bold font-16 flex align-items-center">
    <img src="assets/watchrx/svg/nurses.svg" alt="patients" width="16" height="16" class="mr-2" />
    Care Managers</span>
</div>
<div class="grid">
  <div class="col-12">
    <div class="card pt-3">
      <div style="text-align: left" class="flex flex-row col-12 p-0">
        <div class="col-12 md:col-6 flex-row pl-0">
          <!-- <label htmlfor="state">Search By Org/DeviceId</label> -->
          <div class="flex flex-1 flex-row">
            <input pinputtext="" id="org" type="text" placeholder="Search By Name"
              class="p-inputtext p-component p-element w-full" (input)="onInput($event)" [(ngModel)]="searchText" />
            <p-button class="ml-2 " label="Submit" severity="primary" (onClick)="onInput($event)"></p-button>
          </div>
        </div>
        <div class="col-12 md:col-6 flex align-items-center justify-content-end">
          <p-button label="Add Case Manager" [raised]="false" severity="secondary" class="mr-1" [outlined]="true"
            icon="pi pi-plus" (click)="addCaseMaanger()" />
        </div>
      </div>
      <!-- <div class="flex flex-row md:justify-content-end mb-2">
        <p-button label="Add Case Manager" [raised]="true" severity="secondary" class="mr-1" [outlined]="true"
          icon="pi pi-plus" (click)="addCaseMaanger()" />
      </div> -->

      <div class="grid p-fluid mt-1">
        <div class="field col-12">
          <p-table #dt [value]="caseManagersList" [paginator]="true" [totalRecords]="totalCaseManagers" [rows]="rows"
            [lazy]="true" [loading]="loader" (onLazyLoad)="loadActivePatients($event)" responsiveLayout="scroll"
            styleClass="p-datatable-gridlines p-datatable-striped" [rowsPerPageOptions]="searchText.length==0?[15,25, 50,75, 100]:[rows]"
            (onPage)="onPageChange($event)" [first]="first">
            <ng-template pTemplate="header">
              <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>phone Number</th>
                <th>User Name</th>
                <th>Address</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </ng-template>
            <ng-template let-managerInfo pTemplate="body">
              <tr>
                <td>
                  {{ managerInfo.firstName }}
                </td>
                <td>{{ managerInfo.lastName }}</td>
                <td>{{ managerInfo.phoneNumber }}</td>
                <td>{{ managerInfo.userName }}</td>
                <td>{{ "--" }}</td>
                <td>{{ managerInfo.status == "Y" ? "Active" : "Inactive" }}</td>
                <td>
                  <div class="flex">
                    <p-button *ngIf="managerInfo.status == 'Y'" label="Suspend" [rounded]="false" severity="danger"
                      size="small" class="mr-2" [outlined]="true" (click)="onToggleCaseManager(managerInfo, 'N')" />
                    <p-button *ngIf="managerInfo.status == 'N'" label="Activate" [rounded]="false" severity="success"
                      size="small" class="mr-2" [outlined]="true" (click)="onToggleCaseManager(managerInfo, 'Y')" />
                    <p-button [rounded]="false" icon="pi pi-trash" severity="secondary" size="small" class="mr-1"
                      [outlined]="true" (click)="onDeleteCaseManager(managerInfo['clinicianId'])" [disabled]="true" />
                  </div>
                </td>
              </tr>
            </ng-template>
            <ng-template pTemplate="paginatorleft">
              <span class="text-900 font-bold">Total {{ totalCaseManagers }} Care Managers</span>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="visible" position="right" [style]="{ width: '50rem' }">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Add Case Manager
      </span>
    </div>
  </ng-template>
  <app-create-case-manager (submit)="visible = false" (cancel)="visible = false" [isOrgNeeded]="true"
    [roleTypeList]="roleTypeList" class="create" />
</p-sidebar>