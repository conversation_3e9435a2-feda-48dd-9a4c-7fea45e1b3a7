export interface BillingReport {
  roles: report[];
  status: any;
}

export interface report {
  active: boolean;
  roleDescription: string;
  roleId: number;
  roleName: string;
}

export interface StatisticsReport {
  data: statistics[];
  status: any;
  totalCount: any;
}
export interface statistics {
  patientName: string;
  ccmMins: string;
  patientId: string;
  pcmMins: string;
  rpmMins: string;
  caseManagerName: string;
  patientPhone: string;
  daysCounted: any;
}
