import { Component } from '@angular/core';
import { ICountry, IState } from 'country-state-city';
import { ProfileService } from '../../services/profile.service';
import { MessageService } from 'primeng/api';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';

@Component({
  selector: 'app-profile',

  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent {

  formData: FormData = {
    firstName: '',
    lastName: '',
    address1: '',
    address2: '',
    zipCode: '',
    country: '',
    state: '',
    city: null,
    email: '',
    phoneNumber: '',
    password: '',
    secondaryPhoneNumber: ''
  };
  medImage: any = null;
  profilePic: any = null;
  isLoading: boolean = false
  userRole: any;
  constructor(private fb: FormBuilder, private profileService: ProfileService, private messageService: MessageService,
    private router: Router
  ) {

  }
  ngOnInit() {
    this.getProfileDetails();
    if (localStorage.getItem('user')) {
      let user: any = localStorage.getItem('user');
      this.userRole = JSON.parse(user)?.roleType;
    }
  }
  onFileSelect(event: any): void {
    this.medImage = event.files[0];
  }
  onSubmitInfo(form: any) {
     if (form.invalid) {
      form.control.markAllAsTouched();
      return;
    }
    if (!this.validatePhoneNumber() || !this.validateSecondaryPhoneNumber()) {
      return
    }
    this.isLoading = true
    const formData = new FormData();
    formData.append('firstName', this.formData.firstName);
    formData.append('lastName', this.formData.lastName);
    formData.append('address1', this.formData.address1);
    formData.append('address2', this.formData.address2);
    formData.append('city', this.formData.city);
    formData.append('state', this.formData.state);
    formData.append('zip', this.formData.zipCode);
    formData.append('country', this.formData.country);
    formData.append('phoneNumber', this.formData.phoneNumber);
    formData.append('altPhoneNumber', this.formData.secondaryPhoneNumber);
    formData.append('userName', this.formData.email);
    formData.append('password', this.formData.password);
    if (this.medImage) {
      formData.append('imageFile', this.medImage || null);
    }
    // if(!this.profilePic)
    // {
    //   formData.append('imageFile', this.medImage||null);
    // }
    else {
      formData.append('picPath', this.profilePic);
    }

    this.profileService.updateProfileDetails(formData).subscribe((res) => {
      this.isLoading = false
      if (res.success == true) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'profile updated successfully.',
          life: 3000,
        });
        this.getProfileDetails()
      }
      else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: res.responseMessage,
          life: 3000,
        });
      }
    })

  }
  resetForm() {
    this.getProfileDetails()
  }
  getProfileDetails() {
    this.profileService.getProfileDetails().subscribe((res) => {
      console.log(res);
      this.formData = {
        firstName: res?.clinician?.firstName,
        lastName: res?.clinician?.lastName,
        phoneNumber: res?.clinician?.phoneNumber,
        address1: res?.clinician?.address.address1,
        address2: res?.clinician?.address.address2,
        zipCode: res?.clinician?.address.zip,
        country: res?.clinician?.address.country,
        state: res?.clinician?.address.state,
        city: res?.clinician?.address.city,
        email: res?.clinician?.userName,
        password: res?.clinician?.password,
        secondaryPhoneNumber: res?.clinician?.altPhoneNumber,
      };
      this.profilePic = res?.clinician?.picPath;
      this.medImage = null
    })
  }
  validatePhoneNumber() {
    const phoneNumberPattern = /^\+\d{1,3}\d{4,14}$/;
    return phoneNumberPattern.test(this.formData.phoneNumber);
  }
  validateSecondaryPhoneNumber()
  {
    if (!this.formData.secondaryPhoneNumber.trim()) {
      return true; // If secondary phone number is not provided, consider it valid
    } 
    const phoneNumberPattern = /^\+\d{1,3}\d{4,14}$/;
    return phoneNumberPattern.test(this.formData.secondaryPhoneNumber);
  }

  goBack() {
    if (this.userRole == 2) {
      this.router.navigate(['/admin/billing-reports']);
    } else if (this.userRole == 1) {
      this.router.navigate(['/admin/inventory']);
    } else {
      this.router.navigate(['/dashboard']);
    }

  }

}

interface FormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  address1: string;
  address2: string;
  country: string;
  state: string;
  city: any;
  zipCode: string;
  email: string;
  password: string;
  secondaryPhoneNumber: string
}


