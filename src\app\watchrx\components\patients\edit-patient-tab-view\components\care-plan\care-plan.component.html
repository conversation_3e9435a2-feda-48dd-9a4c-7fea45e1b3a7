<div class="col-12 searchheading">
  <p-toast />
  <div class="p-fluid p-formgrid grid">
    <div class="field col-12 md:col-7 m-0 gap-3 flex">
      <div class="">
        <label htmlfor="state" class="text-600 font-bold">Select Program(s)</label>
        <div class="flex justify-content-left gap-5">
          <div class="flex flex-wrap gap-3">
            <div class="flex align-items-center" *ngFor="let option of programs">
              <p-radioButton name="consent" [value]="option" [(ngModel)]="selectedProgram"
                [inputId]="option.label.toLowerCase()" variant="filled" class="mt-3" />
              <label [for]="option.label.toLowerCase()" class="ml-2 mt-3 text-600 font-bold">{{ option.label }}</label>
            </div>
          </div>
        </div>
      </div>
      <div class="" [ngClass]="selectedProgram==''?'disabled':''">
        <label htmlfor="state" class="text-600 font-bold mb-1">Select Chronic Conditions</label>
        <p-multiSelect [options]="templateList" optionLabel="label" placeholder="Chronic Conditions" display="chip"
          [showClear]="true" appendTo="body" class="mt-1 w-full" [(ngModel)]="selectedChonic1" />

        <!-- <p-dropdown [options]="templateList" [(ngModel)]="selectedChonic" optionLabel="label"
          placeholder="Select a template" [showClear]="true">
          <ng-template pTemplate="item" let-item>
            {{ item.label }}
          </ng-template>

           <ng-template pTemplate="option" let-item>
            <span *ngIf="item.value === 'template'">Template</span>
            <span *ngIf="item.value !== 'template'">{{ item.label }}</span>
          </ng-template> 
        </p-dropdown> -->
      </div>
      <div class="mt-4">
        <p-button (click)="getTabsQuestions(true)"  severity="primary" class="mr-3" label="Submit">
        </p-button>
      </div>

    </div>
    <div class="field col-12 md:col-5 m-0 flex gap-2 align-items-center justify-content-end"
      style="margin-top:19px !important">
      <p-button (click)="getHistoryData()" [outlined]="true" severity="secondary" class="mr-3" label="View History">
      </p-button>
      <p-button (click)="showDialog()" [outlined]="true" severity="secondary" class="mr-3" label="Send Dialog">
      </p-button>
      <p-button (click)="showSDOH()" [outlined]="true" severity="secondary" class="mr-3" label="SDOH">
      </p-button>
      <p-button (click)="showTriage()" [outlined]="true" severity="secondary" class="mr-3" label="Triage">
      </p-button>

    </div>
  </div>
</div>
<div class="grid m-0">
  <div class="col-12 md:col-9 p-0" [ngClass]="selectedProgram==''?'disabled':''">
    <div class="p-card-header flex justify-content-between section searchheading align-items-center section"
      id="mandatorysection">
      <h6 class="m-0"><span class="p-text-danger">*</span>Mandatory Questions</h6>
      <div class="w-10rem">
        <p class="p-0 m-0">{{manditorySelectedCount}}/{{manditoryTotalCount}}</p>
        <p-progressBar [value]="manditoryCount">
        </p-progressBar>
      </div>
    </div>
    <ng-container *ngFor="let man of tabsQuestions; let i= index">
      <div class="searchheading pt-3 pb-3" *ngIf="man.category=='Mandatory'">
        <div class="mb-3">{{i+1}}. {{man.questions}}</div>
        <div class="flex align-items-center flex-row jus ">
          <ng-container *ngFor="let option of man.defaultAnswers">
            <p-radioButton name="consent" [value]="option" [(ngModel)]="man.answer" [inputId]="option+i"
              variant="filled" (onClick)="changeProgressbarStatus($event,'Mandatory')" />
            <label [for]="option+i" class="ml-2  mr-4 text-600 font-bold">{{ option }}</label>
          </ng-container>
          <textarea [(ngModel)]="man.notes" name="notes" class="form-control w-full" rows="1" cols="30"
            placeholder="Notes" pInputTextarea [autoResize]="true"></textarea>
        </div>

      </div>
    </ng-container>
    <ng-container *ngIf="sideMenu.length>0  &&sideMenu[1] &&sideMenu[1].count>0">
      <div class="p-card-header flex justify-content-between section searchheading align-items-center section"
        id="commonsection">
        <h6 class="m-0">Common Questions</h6>
        <div class="w-10rem">
          <p class="p-0 m-0">{{commonSelectedCount}}/{{commonTotalCount}}</p>
          <p-progressBar [value]="commonCount">
          </p-progressBar>
        </div>
      </div>
      <ng-container *ngFor="let man of tabsQuestions; let i= index">
        <div class="searchheading pt-3 pb-3" *ngIf="man.category=='Common'">
          <div class="mb-3">{{i+1}}. {{man.questions}}</div>
          <div class="flex align-items-center flex-row jus ">
            <ng-container *ngFor="let option of man.defaultAnswers">
              <p-radioButton name="consent" [value]="option" [(ngModel)]="man.answer" [inputId]="option+i"
                variant="filled" (onClick)="changeProgressbarStatus($event,'Common')" />
              <label [for]="option+i" class="ml-2  mr-4 text-600 font-bold">{{ option }}</label>
            </ng-container>
            <textarea [(ngModel)]="man.notes" name="notes" class="form-control w-full" rows="1" cols="30"
              placeholder="Notes" pInputTextarea [autoResize]="true"></textarea>
          </div>

        </div>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="sideMenu.length>0  && sideMenu[2]&& sideMenu[2].count>0">
      <!--chronic data-->
      <div class="p-card-header flex justify-content-between section searchheading align-items-center section"
        id="ccsection">
        <h6 class="m-0">Chronic Condition Questions</h6>
        <div class="w-10rem">
          <p class="p-0 m-0">{{chronicSelectedCount}}/{{chronicTotalCount}}</p>
          <p-progressBar [value]="chronicCount">
          </p-progressBar>
        </div>
      </div>
      <ng-container *ngFor="let man of tabsQuestions; let i= index">
        <div class="searchheading pt-3 pb-3" *ngIf="man.category!='Common'&&man.category!='Mandatory'">
          <div class="mb-3">{{i+1}}. {{man.questions}}</div>
          <div class="flex align-items-center flex-row jus ">
            <ng-container *ngFor="let option of man.defaultAnswers">
              <p-radioButton name="consent" [value]="option" [(ngModel)]="man.answer" [inputId]="option+i"
                variant="filled" (onClick)="changeProgressbarStatus($event,'Chronic')" />
              <label [for]="option+i" class="ml-2  mr-4 text-600 font-bold">{{ option }}</label>
            </ng-container>
            <textarea [(ngModel)]="man.notes" name="notes" class="form-control w-full" rows="1" cols="30"
              placeholder="Notes" pInputTextarea [autoResize]="true"></textarea>
          </div>

        </div>
      </ng-container>
    </ng-container>
    <div class="flex justify-content-end gap-3 p-4">
      <p-button label="Cancel" [outlined]="true" severity="secondary" />
      <p-button label="Save" (click)="onSubmitFormData()" />
    </div>

  </div>
  <div class="col-12 md:col-3 p-0 careplanmenu">
    <ul class="p-0">
      <li *ngFor="let menu of sideMenu" [ngClass]="{'active':selectedMenu==menu.value}"
        (click)="scrollToDiv(menu.value)">{{menu.label}} ({{menu.count}})</li>
    </ul>
  </div>
</div>



<p-sidebar [(visible)]="isShowHistory" position="right" styleClass="w-40rem" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center justify-content-between gap-2 w-full">
      <span class="font-bold">
        History
      </span>

      <p-button [raised]="true" severity="primary" label="Download" [outlined]="true"
        (onClick)="downloadTemplateHistory()" [loading]="fileDownloadLoading" iconPos="right"> <ng-template
          pTemplate="icon">
          <img src="assets/watchrx/svg/download_arrow.svg" alt="Download Stats" width="16" height="16" class="mr-2" />
        </ng-template>
      </p-button>


      <div class="button-group mr-2">
        <button pButton type="button" class="left-button" icon="pi pi-angle-double-left"
          (click)="onPreviousClick()"></button>
        <div class="center-buttons">
          <button pButton type="button" label="{{ currentMonthCp }}" class="center-button"></button>
        </div>
        <button pButton type="button" class="right-button" icon="pi pi-angle-double-right"
          (click)="onNextClick()"></button>
      </div>
    </div>

  </ng-template>
  <div *ngIf="hstData?.length === 0">
    <strong class="align-items-center justify-content-center">No Data Found!!</strong>
  </div>
  <p-accordion [activeIndex]="0">
    <p-accordionTab *ngFor="let item of hstData">
      <ng-template pTemplate="header">
        <div class="flex justify-content-between gap-4">
          <div class="flex flex-column align-items-center">
            <div class="subheader">Program</div>
            <div class="content">{{item.program}}</div>
          </div>
          <div class="flex flex-column align-items-center">
            <div class="subheader">Chronic Conditions</div>
            <div class="content">{{item.chronicCondition}}</div>
          </div>
          <div class="flex flex-column align-items-center">
            <div class="subheader">Questions</div>
            <div class="content">{{item.questionCount}}</div>
          </div>
          <div class="flex flex-column align-items-center">
            <div class="subheader">Created Date</div>
            <div class="content">{{item.createdDate}}</div>
          </div>
        </div>
      </ng-template>
      <div class="mb-3" *ngFor="let Que of item.question; let i=index">
        <p class="mb-1">{{i+1}}. {{Que.question}}</p>
        <!-- <div class="flex gap-2"> -->
        <p class="m-0">Ans: {{Que.answer||'-'}}</p>
        <p class="m-0">Notes: {{Que.comments ||'-'}}</p>
        <!-- </div> -->
      </div>
    </p-accordionTab>
  </p-accordion>

</p-sidebar>

<p-sidebar [(visible)]="isShowSDOH" position="right" styleClass="w-40rem sdohscreen" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center justify-content-between gap-2 w-full">
      <span class="font-bold">
        SDOH
      </span>
    </div>
  </ng-template>
  <div class=" position-relative ">
    <p-table [value]="sdohList" [paginator]="false" [totalRecords]="totalSDOHCount" [lazy]="true" [loading]="loader"
      responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 120px;">Date</th>
          <th>Dialog Name</th>
          <th>Triage Event</th>
          <th>Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-sdoh>
        <tr>
          <td style="width: 120px;">
            {{ sdoh.date }}
          </td>
          <td style="word-break: break-all;">
            {{ sdoh.dialogName || "-" }}
          </td>
          <td style="word-break: break-all;">
            {{ "-" }}
          </td>
          <td>
            <div class="flex">
              <button pButton icon="pi pi-comment" class="p-button-secondary p-button-outlined mr-2"
                (click)="showConversation(sdoh?.comm)"></button>
              <button pButton icon="pi pi-trash" class="p-button-secondary p-button-outlined"
                (click)="deleteDialog(sdoh?.id)" severity="danger"></button>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</p-sidebar>

<p-sidebar [(visible)]="isShowTriage" position="right" styleClass="w-40rem sdohscreen" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center justify-content-between gap-2 w-full">
      <span class="font-bold">
        Care
      </span>
    </div>
  </ng-template>
  <div class=" position-relative ">
    <p-table [value]="triageList" [paginator]="false" [totalRecords]="totalTriageCount" [lazy]="true" [loading]="loader"
      responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped">
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 120px;">Date</th>
          <th>Dialog Name</th>
          <th>Triage Event</th>
          <th>Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-triage>
        <tr>
          <td style="width: 120px;">
            {{ triage.date }}
          </td>
          <td style="word-break: break-all;">
            {{ triage.dialogName || "-" }}
          </td>
          <td style="word-break: break-all;">
            {{ "-" }}
          </td>
          <td>
            <div class="flex">
              <button pButton icon="pi pi-comment" class="p-button-secondary p-button-outlined mr-2"
                (click)="showConversation(triage?.comm)"></button>
              <button pButton icon="pi pi-trash" class="p-button-secondary p-button-outlined"
                (click)="deleteDialog(triage.id)" severity="danger"></button>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</p-sidebar>

<p-sidebar [(visible)]="isShowDialog" position="right" styleClass="w-40rem sdohscreen" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center justify-content-between gap-2 w-full">
      <span class="font-bold">
        Send Dialog
      </span>
    </div>
  </ng-template>
  <div class=" position-relative ">
    <p-table [value]="dialogList" [paginator]="false" [totalRecords]="totalDialogCount" [lazy]="true" [loading]="loader"
      responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped">
      <ng-template pTemplate="header">
        <tr>
          <th>Dialog Name</th>
          <th>Actions</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-dialog>
        <tr>
          <td style="word-break: break-all;">
            {{ dialog.dialogName || "-" }}
          </td>
          <td>
            <div class="flex">
              <button pButton class="p-button-secondary p-button-outlined mr-2" (click)="sendDialog(dialog)"
                label="Send"></button>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</p-sidebar>

<p-dialog [(visible)]="openDialog" [modal]="true" [style]="{ width: '900px' }" [contentStyle]="{ overflow: 'auto' }"
  position="top" [header]="'Chat'">
  <div *ngFor="let chat of chatArray" style="width: 100%;display: flex;flex-direction: column;">
    <div class="question">
      {{ chat.question }}
    </div>
    <div class="answer">
      {{ chat.answer }}
    </div>
  </div>
</p-dialog>