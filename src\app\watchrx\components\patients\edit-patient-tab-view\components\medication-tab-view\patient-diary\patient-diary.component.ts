import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TableModule } from 'primeng/table';
import { Diary } from '../../../../../../api/patientDiary';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { DiaryService } from './service/diary.service';

@Component({
  selector: 'app-patient-diary',
  standalone: true,
  imports: [TableModule],
  templateUrl: './patient-diary.component.html',
  styleUrl: './patient-diary.component.scss',
})
export class PatientDiaryComponent {
  diaryResponse: Diary[] = [];
  patientId: number = 0;
  constructor(
    private service: DiaryService,
    private route: ActivatedRoute,
    private loaderService: LoaderService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getDiaries();
  }

  getDiaries() {
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
      index: 0,
      pageSize: 500,
    };
    this.service.getDiaries(req).subscribe((res) => {
      this.loaderService.hide();
      if (res?.status) {
        this.diaryResponse = res?.patientDiaryResponseVOs;
        this.diaryResponse.forEach(d=>{
          let med = d.medicationTime.split("-");
          let mel=d.mealTime.split("-");
          d.medicationTime = med[1];
          d.mealTime=mel[1];
        })
      }
    });
  }
}
