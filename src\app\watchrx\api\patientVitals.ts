export interface PatientVitalReadings {
  success: boolean;
  responseCode: string;
  responsecode: string;
  messages: any[];
  eList: VitalReading[];
  count: number;
  phoneNumber: string;
  rpmMins: string;
  ccmMins: string;
  pcmMins: string;
  minsDetails: string;
  programs: string;
}

export interface VitalReading {
  vitalTypeValueVOList: Vital[];
  patientId: number;
  createdDate: string;
  updatedDate: string;
  userId: number;
  configuration: string;
  measuredDateTime: string;
  vitalScheduleId: string;
  status: boolean;
  errorDescription: string;
}

export interface Vital {
  vitalId: number;
  vitalTypeName: string;
  vitalTypeId: string;
  vitalValue: string;
}

export interface PedometerReadings {
  success: boolean;
  responseCode: string;
  responsecode: string;
  messages: any[];
  eList: Pedometer[];
  count: number;
  phoneNumber: string;
  rpmMins: string;
  ccmMins: string;
  pcmMins: string;
  minsDetails: string;
  programs: string;
}

export interface Pedometer {
  createdDate: string;
  createdTime: string;
  count: number;
  distanceInKm: string;
  distanceInMiles: string;
}

export interface Sleep {
  createdDate: string;
  deepSleep: string;
  shallowSleep: string;
  sleepTime: string;
}

export interface SleepMonitorReadings {
  success: boolean;
  responseCode: string;
  responsecode: string;
  messages: any[];
  eList: Sleep[];
  count: number;
  phoneNumber: string;
  rpmMins: string;
  ccmMins: string;
  pcmMins: string;
  minsDetails: string;
  programs: string;
}

export interface ThresholdConfigResp {
  success: boolean;
  messages: any[];
  eList: VitalThreshold[];
}

export interface VitalThreshold {
  success: boolean;
  thresholdId: number;
  vitalMin: number;
  vitalMax: number;
  vitalCriticalMin: number;
  vitalCriticalMax: number;
}

export interface VitalEnableStatus {
  success: boolean;
  status: string;
  messages: string[];
}

export interface PedometerEnableStatus {
  success: boolean;
  state: string;
  timeInterval: number;
}

export interface VitalScheduleResponse {
  success: boolean;
  messages: string[];
  eList: VitalSchedule[];
}

export interface VitalSchedule {
  vitalScheduleId: number;
  vitalTypeId: number;
  vitalTypeName: string;
  patientId: number;
  status: string;
  scheduleType: string;
  collectMode: string;
  frequency: number;
  timeSlots: string;
  timeSlotsArray: any;
  recurrence: string;
  scheduleDayOfWeek: string;
  startDate: string;
  startTime: string;
  endDate: string;
  createdDate: string;
  updatedDate: string;
  scheduleTitle: string;
}
