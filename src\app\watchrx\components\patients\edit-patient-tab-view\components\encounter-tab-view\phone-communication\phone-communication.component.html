<div class="position-relative">
  <p-confirmDialog />
  <p-toast />
  <div class="flex md:justify-content-end mb-2 mt-2">
    <p-button
      label="Add Phone Record"
      severity="secondary"
      [outlined]="true"
      (onClick)="showDialog()"
      icon="pi pi-plus"
    ></p-button>
  </div>
  <p-table
    [value]="phoneCommsList"
    [paginator]="true"
    [totalRecords]="totalCount"
    [rows]="25"
    [lazy]="true"
    (onLazyLoad)="loadPhoneCommsList($event)"
    responsiveLayout="scroll"
    styleClass="p-datatable-gridlines p-datatable-striped"
  >
    <ng-template pTemplate="header">
      <tr>
        <th>Timestamp</th>
        <th>Duration</th>
        <th>Notes</th>
        <!-- <th>Call Start Time</th> -->
        <th>Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-enc>
      <tr>
        <td>{{ enc.timeStamp | date : "MM-dd-YYYY hh:mm:ss a" }}</td>
        <td>{{ formattedTime(enc.duration) }}</td>
        <td>{{ enc.note }}</td>
        <!-- <td>{{ enc.callStartTime }}</td> -->
        <td>
          <div class="flex">
            <button
              pButton
              icon="pi pi-pencil"
              class="p-button-outlined p-button-secondary mr-2"
              (click)="editNotes(enc)"
            ></button>
            <button
              pButton
              icon="pi pi-trash"
              class="p-button-outlined p-button-secondary"
              (click)="deletePhoneComm($event, enc?.phoneCommunicationId)"
            ></button>
          </div>
        </td>
      </tr>
    </ng-template>
   
  </p-table>
  <div class="footer1"> Total {{totalCount}} Phone Communications </div>
</div>
<p-sidebar position="right" [style]="{ width: '40rem' }" class="addencountersidebar"
  [(visible)]="visible"
>
<ng-template pTemplate="header">
  <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isEdit"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isEdit"></i>
        {{title}}
      </span>
  </div>
</ng-template>
  <div class="p-fluid p-formgrid grid">
    <hr />
    <div class="field col-12 md:col-12">
      <label htmlfor="state" class="font-bold block mb-2"
        >Select Program(s)</label
      >
      <div class="flex justify-content-left gap-3">
        <div class="flex flex-wrap gap-3">
          <div
            class="flex align-items-center"
            *ngFor="let option of programList"
          >
            <p-radioButton
              name="consent"
              [value]="option.label"
              [(ngModel)]="selectedProgram"
              [inputId]="option.label.toLowerCase()"
              variant="filled"
            />
            <label [for]="option.label.toLowerCase()" class="ml-2">{{
              option.label
            }}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="field col-12 md:col-6">
      <label htmlfor="description" class="font-bold block mb-2"
        >Call Start time <span class="p-text-danger">*</span></label
      >
      <p-calendar
        inputId="calendar-12h"
        [(ngModel)]="callStartTime"
        [showTime]="false"
        hourFormat="12"
        appendTo="body"
        [showIcon]="true"
      >
      </p-calendar>
    </div>
    <div class="field col-12 md:col-6">
      <label htmlfor="description" class="font-bold block mb-2"
        >Duration <small>(In Minutes)</small></label
      ><p-inputNumber
        [(ngModel)]="durationInMins"
        inputId="minmax"
        mode="decimal"
        [min]="0"
        [max]="100"
      />
    </div>
    <div class="field col-12 md:col-12">
      <div class="flex flex-wrap justify-content-between">
        <label htmlfor="description" class="font-bold block mb-2"
          >Note(s)</label
        >
        <label htmlfor="description" class="font-bold block mb-2"
          >Limit ( {{ remainingChars() }})</label
        >
      </div>

      <textarea
        rows="3"
        cols="30"
        pInputTextarea
        autoResize="true"
        class="p-inputtext p-component p-element"
        [(ngModel)]="noteDescription"
        [attr.maxLength]="maxLength"
        placeholder="Enter your notes here..."
      >
      </textarea>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
    <p-button
      label="Cancel"
      [outlined]="true"
      severity="primary"
      (click)="visible = false"
    />
    <p-button
      label="Submit"
      severity="primary"
      (click)="addPhoneComms()"
    />
  </div>
  </ng-template>
</p-sidebar>
