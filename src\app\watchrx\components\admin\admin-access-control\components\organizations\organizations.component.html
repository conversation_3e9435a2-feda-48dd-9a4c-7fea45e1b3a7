<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="col-12">
    <div class="flex flex-row md:justify-content-end mb-2">
      <p-button label="Add Organization" [raised]="false" severity="secondary" class="mr-1" [outlined]="true"
        (click)="addOrganization()" />
    </div>

    <div class="grid p-fluid mt-3">
      <div class="field col-12">
        <p-table #dt [value]="organizationList" [paginator]="false" [totalRecords]="totalCaseManagers"
          [rows]="itemsPerPage" [lazy]="true" [loading]="orgLoader" (onLazyLoad)="loadOrganizations($event)"
          responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped table-min-height">
          <ng-template pTemplate="header">
            <tr>
              <th>Org Id</th>
              <th>Organization Name and Description</th>
              <th>Org Features</th>
              <th>Assigned Casemanagers</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template let-featureInfo pTemplate="body">
            <tr>
              <td>
                {{ featureInfo.groupId }}
              </td>
              <td>
                {{ featureInfo.groupName }}<br>
                <span class="font-12">{{ featureInfo.groupDescription }}</span>
              </td>
              <td>
                <span (click)="onUpdateFeature(featureInfo,false)" class="cursor-pointer underline">
                  {{featureInfo.roles.length}} Features </span>
                <!-- <ul>
                  <li *ngFor="let roles of featureInfo.roles">
                    {{ roles.roleName }}
                  </li>
                </ul> -->
              </td>
              <td>
                <span (click)="onShowUserSidebar(featureInfo)" class="cursor-pointer underline"> 
                  {{featureInfo.users.length}} users</span>
                <!-- <ul>
                  <li *ngFor="let users of featureInfo.users">
                    {{ users.userName }}
                  </li>
                </ul> -->
              </td>
              <td>
                <div class="flex">
                  <p-button label="Update Org" [rounded]="false" [outlined]="true" severity="primary" size="small"
                    class="mr-2" title="update" (click)="onUpdateFeature(featureInfo,true)" />
                  <p-button label="Delete Org" [rounded]="false" [outlined]="true" severity="danger" size="small"
                    class="mr-1" title="delete" (click)="onDeleteFeature(featureInfo['groupId'])" />
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="visible" position="right" [style]="{ width: '40rem' }" (onShow)="onSidebarOpen()" (onHide)="onSidebarClose()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isUpdate"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isUpdate"></i>
        {{ OrganizationTitle}}
      </span>
    </div>
  </ng-template>
  <form [formGroup]="organizationForm" (ngSubmit)="onSubmit()" *ngIf="showForm">
    <div class="p-fluid p-formgrid grid">
      <div class=" col-12 md:col-12">
        <label for="groupname" class="font-bold block mb-2"> Name <span class="p-text-danger">*</span></label>
        <input id="groupname" formControlName="groupName" pInputText />
        <div *ngIf="
            organizationForm.get('groupName')?.invalid &&
            organizationForm.get('groupName')?.touched
          ">
        <small class="ng-dirty ng-invalid p-text-danger"> Name is required.</small>  
        </div>
      </div>

      <div class=" col-12 md:col-12">
        <label for="groupdescription" class="font-bold block mb-2"> Description <span class="p-text-danger">*</span></label>
        <textarea id="groupdescription" formControlName="groupDescription" pInputText></textarea>
        <div *ngIf="
            organizationForm.get('groupDescription')?.invalid &&
            organizationForm.get('groupDescription')?.touched
          ">
         <small class="ng-dirty ng-invalid p-text-danger"> Description is required.</small> 
        </div>
      </div>
      <div class=" col-12 md:col-12">
        <label for="groupEmail" class="font-bold block mb-2"> Email</label>
        <input id="groupEmail" formControlName="groupEmail" pInputText />
      </div>
      <div class=" col-12 md:col-12">
        <label for="groupFax" class="font-bold block mb-2"> Fax</label>
        <input id="groupFax" formControlName="groupFax" pInputText />
      </div>
      <div class=" col-12 md:col-12">
        <label for="groupPhoneNo" class="font-bold block mb-2">Phone Number</label>
        <input id="groupPhoneNo" formControlName="groupPhoneNo" pInputText type="tel" pattern="^\+?[0-9]{10,15}$" placeholder="e.g. +1234567890" />
        <div *ngIf="organizationForm.get('groupPhoneNo')?.value && !(groupPhoneNoValid())">
          <small class="p-text-danger">Please enter a valid phone number with country code.</small>
        </div>
      </div>
      <div class=" col-12 md:col-12">
        <label for="roleIds" class="font-bold block mb-2">Select Features <span class="p-text-danger">*</span></label>
        <p-multiSelect [options]="rolesList" formControlName="roleIds" checkmark="true" optionLabel="roleName"
          [showClear]="true" placeholder="Select Features" />
        <div *ngIf="
            organizationForm.get('roleIds')?.invalid &&
            organizationForm.get('roleIds')?.touched
          ">
          <small class="ng-dirty ng-invalid p-text-danger">Features is required.</small>

        </div>
      </div>
    </div>
  </form>
  <form [formGroup]="organizationForm" (ngSubmit)="onSubmit()" *ngIf="!showForm">
    <div class="p-fluid p-formgrid grid">
      <div class="field col-12 md:col-12">
        <div class="field col-12 md:col-12">
          <label for="roleIds" class="font-bold block mb-2">Add Feature</label>
          <p-dropdown [options]="rolesList" optionLabel="roleName" placeholder="Select Feature"
            (onChange)="onCheckboxChange($event,'selected')" [(ngModel)]="selectedRole" />
        </div>
        <p-table #dt [value]="selectedTempOrgRoles" [paginator]="false" [totalRecords]="selectedTempOrgRoles.length"
          [rows]="itemsPerPage" [lazy]="true" [loading]="loader" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>Select</th>
              <th>Feature Name</th>
            </tr>
          </ng-template>
          <ng-template let-organizationInfo pTemplate="body">
            <tr>
              <td>
                <input type="checkbox" [value]="organizationInfo"
                  (change)="onCheckboxChange($event, 'checkbox',organizationInfo)" [checked]="true" />
              </td>
              <td>{{ organizationInfo.roleName }}</td>

            </tr>
          </ng-template>
        </p-table>

      </div>
    </div>
  </form>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="onCancel()" />
      <p-button label="Save" [outlined]="false" severity="primary" [disabled]="!organizationForm.valid"
        (click)="onSubmit()" />
    </div>
  </ng-template>
</p-sidebar>


<p-sidebar [(visible)]="visibleUsers" position="right" [style]="{ width: '40rem' }">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isUpdate"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isUpdate"></i>
        {{ OrganizationTitle}}
      </span>
    </div>
  </ng-template>
    <div class="p-fluid p-formgrid grid">
      <div class="field col-12 md:col-12">
        <div class="grid flex align-items-center ">
          <div class="col-7 md:col-7">
            <label for="roleIds" class="font-bold block mb-2">Add User</label>
            <p-dropdown
            [options]="usersList"
            checkmark="true"
            optionLabel="userName"
            [filter]="true"
            [showClear]="true"
            placeholder="Select Users"
            (onChange)="userChange($event)"
            filterBy="label"
            class="mr-2"
            [(ngModel)]="dropdownUser"
          />
          </div>
          <div class="col-5 md:col-5 mt-1">
            <p-button label="Assign Case Manager" severity="primary"  (click)="assignOrg()" />
          </div>
        </div>
        <hr class="m-0 mb-1"/>
        <div class="col-12 md:col-12">
          <div class="flex justify-content-between align-items-center ">
            <p class="p-0 m-0">Assigned Case Managers</p>
            <p-button label="Unassign" severity="danger" [outlined]="true" (click)="unAssignOrg()" />
          </div>

        </div>
        <p-table #dt [value]="selectedTempOrgUsers" [paginator]="false" [totalRecords]="selectedTempOrgUsers.length"
          [rows]="itemsPerPage" [lazy]="true" [loading]="loader" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>Select</th>
              <th>Case Manger</th>
            </tr>
          </ng-template>
          <ng-template let-userInfo pTemplate="body" let-i="rowIndex">
            <tr>
              <td>
                <input type="checkbox" [value]="userInfo"
                  (change)="onUserCheckboxChange($event,userInfo,i)" [checked]="false" />
              </td>
              <td>{{ userInfo.userName }}</td>

            </tr>
          </ng-template>
        </p-table>

      </div>
    </div>
</p-sidebar>