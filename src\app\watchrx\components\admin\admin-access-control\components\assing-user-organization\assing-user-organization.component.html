<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="col-12">
    <div class="flex justify-content-between">
      <p-dropdown
        [options]="usersList"
        checkmark="true"
        optionLabel="userName"
        [filter]="true"
        [showClear]="true"
        placeholder="Select Users"
        (onChange)="userChange($event)"
        filterBy="label"
        class="mr-2"
      />

      <div class="flex flex-row md:justify-content-end mb-2">
        <p-button
          label="Assign Org"
          severity="secondary"
          class="mr-2"
          [outlined]="true"
          icon="pi pi-plus"
          (click)="assignOrg()"
        />
        <p-button
          label="Unassign Org"
          severity="secondary"
          class="mr-2"
          [outlined]="true"
          icon="pi pi-minus"
          (click)="unAssignOrg()"
        />
      </div>
    </div>

    <div class="grid p-fluid mt-3">
      <div class="field col-12">
        <p-table
          #dt
          [value]="organizationList"
          [paginator]="false"
          [totalRecords]="totalCaseManagers"
          [rows]="itemsPerPage"
          [lazy]="true"
          [loading]="loader"
          (onLazyLoad)="loadAssignedUsersandRoles($event)"
          [(selection)]="selectedGroups"
          responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped"
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Select Organization</th>
              <th>Organization Name</th>
              <th>Org Description</th>
              <th>Attached Users</th>
            </tr>
          </ng-template>
          <ng-template let-featureInfo pTemplate="body">
            <tr>
              <td>
                <p-tableCheckbox [value]="featureInfo" />
              </td>
              <td>{{ featureInfo.groupName }}</td>
              <td>{{ featureInfo.groupDescription }}</td>
              <td>
                <ul>
                  <li *ngFor="let users of featureInfo.users">
                    {{ users.userName }}
                  </li>
                </ul>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Register Case Manager"
  [(visible)]="visible"
  [modal]="true"
  [style]="{ width: '800px', height: 'auto' }"
  [contentStyle]="{ overflow: 'visible' }"
  [draggable]="false"
  *ngIf="visible"
  [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
>
  <app-create-case-manager (submit)="visible = false">
  </app-create-case-manager>
</p-dialog>
