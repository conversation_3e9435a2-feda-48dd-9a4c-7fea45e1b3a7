export abstract class Constants {
  static readonly GET_CHRONIC =
    'service/patient/getChronicConditionByPatientId/';
  static readonly GET_TABS_CONFIG = 'service/clinician/getCarePlanTabs';
  //static readonly CAREPLAN_QUESTIONS = 'service/clinician/getCarePlanQuestions';
  static readonly CAREPLAN_QUESTIONS = 'service/template/templateDetailsByPatient';
  static readonly LATEST_CAREPLAN = 'service/patient/getLatestCarePlanDetails/';
  static readonly SUBMIT_NOTES = 'service/patient/savePatientCarePlanQuestions';
  static readonly CAREPLAN_DETAILS = 'service/patient/carePlanQandAnsHistory/';
  static readonly TEMPLATE_DETAILS =
    'service/template/templateDetailsByChronicAndPatient';
  static readonly SUBMIT_TEMPLATE =
    'service/template/savePatientTemplateChronicDetails';
  static readonly TEMPLATE_HISTORY =
    'service/template/patientsTemplateHistory/';

  static readonly DOWNLAOD_TEMPLATE_HISTORY =
    'service/template/patientsTemplateReport/';
    static readonly SHOW_DIALOG =
    'service/patient/getDialogByPatId/';
    static readonly SHOW_TRIAGE='service/patient/dialogCommByPatient/';
    static readonly SHOW_SDOH='service/patient/dialogCommByPatient/';
    static readonly SEND_DIALOG='service/patient/sendDialog/';
    static readonly DELETE_DIALOG='service/patient/deleteDialogCommById/';
}
