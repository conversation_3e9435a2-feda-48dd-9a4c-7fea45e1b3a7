import { Component, OnInit } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { MenuService } from '../../../service/menu.service';

@Component({
  selector: 'app-admin-inventory',
  templateUrl: './admin-inventory.component.html',
  styleUrls: ['./admin-inventory.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminInventoryComponent implements OnInit {
  activeTabIndex: any = 0;
  constructor(public menuService: MenuService) {}

  ngOnInit() {
    this.menuService.changeMenu('Inventory');
  }

  tabChange(event: any) {
    console.log(event);
  }
}
