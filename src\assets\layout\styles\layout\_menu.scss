.layout-sidebar {
    position: fixed;
    width: 215px;
    height: 100%;
    z-index: 999;
    overflow-y: auto;
    user-select: none;
    transition: transform $transitionDuration, left $transitionDuration;
    background-color: var(--surface-overlay);
}

.space {
    padding: 2px;
}

.single-line {
    display: flex;
    align-items: center;
    gap: 5px;
}

.single-line span {
    display: inline-block;
    color: black;
}

.non-clickable {
    pointer-events: none;
}

.layout-menu {
    margin: 0;
    padding: 0;
    list-style-type: none;

    img {
        height: 55px;
        width: 100%;
    }

    ::ng-deep .p-divider.p-divider-horizontal {
        margin: 0px !important;
    }

    .layout-root-menuitem {
        >.layout-menuitem-root-text {
            font-size: .857rem;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--surface-900);
            margin: .75rem 0;
        }

        >a {
            display: none;
        }
    }

    a {
        user-select: none;

        &.active-menuitem {
            >.layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    li.active-menuitem {
        >a {
            .layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    ul {
        margin: 0;
        padding: 0;
        list-style-type: none;

        a {
            display: flex;
            align-items: center;
            position: relative;
            outline: 0 none;
            color: var(--text-color);
            cursor: pointer;
            padding: 0.5rem 1rem;
            //border-radius: $borderRadius;
            transition: background-color $transitionDuration, box-shadow $transitionDuration;

            svg {
                fill: #c6c8cb;
            }

            .layout-menuitem-icon {
                margin-right: .5rem;
            }

            .layout-submenu-toggler {
                font-size: 75%;
                margin-left: auto;
                transition: transform $transitionDuration;
            }

            &.active-route {
                font-weight: 700;
                color: var(--primary-color);

                svg {
                    fill: var(--primary-color);
                    transition: all ease 0.3s;
                }
            }

            &:hover {
                background-color: var(--surface-hover);

                svg {
                    fill: var(--primary-color);
                    transition: all ease 0.3s;
                }
            }

            &:focus {
                @include focused-inset();
            }
        }

        ul {
            overflow: hidden;
            border-radius: $borderRadius;

            li {
                a {
                    margin-left: 1rem;
                }

                li {
                    a {
                        margin-left: 2rem;
                    }

                    li {
                        a {
                            margin-left: 2.5rem;
                        }

                        li {
                            a {
                                margin-left: 3rem;
                            }

                            li {
                                a {
                                    margin-left: 3.5rem;
                                }

                                li {
                                    a {
                                        margin-left: 4rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}