import { ChangeDetectorRef, Component, OnInit, viewChild, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { AdminPhysicianService } from './service/admin-physician.service';
import { debounceTime, Subject } from 'rxjs';
@Component({
  selector: 'app-admin-physicians',
  templateUrl: './admin-physicians.component.html',
  styleUrls: ['./admin-physicians.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminPhysiciansComponent implements OnInit {
  loader: boolean = false;
  caseManagersList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 15;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;

  roleTypeList = [
    // { type: 'Case Manager' },
    // { type: 'Care Giver' },
    // { type: 'Provider' },
    // { type: 'Physician' },
    { type: 'Case Manager',value:'Case Manager' },
    { type: 'Care Giver',value:'Care Giver' },
    //{ type: 'Physician',value:'Physician' },
    //{ type: 'Provider', value:'Provider' },
    {type:'MD',value:'Physician'},
    {type:'DO',value:'Physician'},
    {type:'NP',value:'Physician'},
    {type:'PA',value:'Physician'},
  ];

  private inputSubject: Subject<string> = new Subject();
  debouncedValue: any;
  searchText: any="";
  rows:number=15;
  constructor(
    public menuService: MenuService,
    private router: Router,
    private service: AdminPhysicianService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef
  ) {  this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
            this.debouncedValue = value;
            console.log('Search Value:', this.debouncedValue);
            this.onSearch(this.debouncedValue);
          }); }

  ngOnInit() {
    this.cdr.detectChanges();
    this.menuService.changeMenu('Physicians');
  }

  loadPhysicians($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loader = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize;

    this.service.getPhysicianList(url).subscribe((res) => {
      if (res.success) {
        this.caseManagersList = res.clinicianList!;
        this.totalCaseManagers = res.count!;
        this.loader = false;
      }
    });
  }

  onDeleteCaseManager(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.service.deletePhysician({ clinicianId: id }).subscribe((res) => {
          this.loaderService.hide();
          if (res?.success) {
            this.messageService.add({
              severity: 'success',
              summary: 'Confirmed',
              detail: 'Physician deleted successfully.',
              life: 3000,
            });
            setTimeout(() => {
              this.loadPhysicians();
            }, 1000);
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        }, err => {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'Something went wrong',
            life: 3000,
          });
          this.loaderService.hide();
        });
      },
      reject: () => {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Something went wrong',
          life: 3000,
        });
        this.loaderService.hide();
      },
    });
  }

  onToggleCaseManager(managerInfo: any, type: any) {
    this.loaderService.show();
    this.service
      .togglePhysicianStatus({
        clinicianId: managerInfo.clinicianId,
        type: 'physician',
      })
      .subscribe((res) => {
        this.loaderService.hide();
        managerInfo.status = type;
        this.loadPhysicians();
      });
  }

  addUser() {
    this.visible = true;
  }

  previousRowsPerPage = 15;
  first = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }

  onInput(event: any): void {
    this.inputSubject.next(this.searchText);
  }
  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.loader = true;
      this.service.searchPhysician('3/'+searchTerm).subscribe((res) => {
        if (res.success) {
          this.caseManagersList = res.clinicianList!;
          this.totalCaseManagers = res.count!;
          this.loader = false;
          this.rows = res.count! || 15;
        }
      },err=>{
        this.loader = false;
      });
    } else {
      if (searchTerm !== undefined && searchTerm.length === 0) {
       this.loadPhysicians();
       this.rows=15
      }
    }
  }
}


