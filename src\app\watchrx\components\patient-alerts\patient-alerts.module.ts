import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { AlertsComponent } from '../shared/alerts/alerts.component';
import { PatientAlertsRoutingModule } from './patient-alerts-routing.module';
import { PatientAlertsComponent } from './patient-alerts.component';

@NgModule({
  declarations: [PatientAlertsComponent],
  imports: [
    CommonModule,
    PatientAlertsRoutingModule,
    FormsModule,
    TableModule,
    CalendarModule,
    ButtonModule,
    TagModule,
    DropdownModule,
    AlertsComponent,
  ],
})
export class PatientAlertsModule {}
