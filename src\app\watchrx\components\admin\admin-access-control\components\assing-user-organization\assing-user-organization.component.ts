import { ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { AdminAccessControlService } from '../../service/admin-access-control.service';
@Component({
  selector: 'app-assing-user-organization',
  templateUrl: './assing-user-organization.component.html',
  styleUrl: './assing-user-organization.component.scss',
})
export class AssingUserOrganizationComponent {
  loader: boolean = false;
  organizationList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 10;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  usersList: any[] = [];
  selectedUser: any;
  selectedGroups: any[] = [];
  constructor(
    private router: Router,
    private adminAccessControlService: AdminAccessControlService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.cdr.detectChanges();
    this.loadAllUsers();
  }

  loadAllUsers() {
    //this.loader = true;
    this.adminAccessControlService.getUsersList().subscribe((res) => {
      if (res.status) {
        console.log(res);
        this.usersList = res.users!;
        //this.loader = false;
      }
    });
  }

  loadAssignedUsersandRoles($event?: LazyLoadEvent | TableLazyLoadEvent) {
   // this.loader = true;
    this.adminAccessControlService
      .getAssigndUsersandRolesList()
      .subscribe((res) => {
        if (res.status) {
          console.log(res);
          this.organizationList = res.groupsAndAssignedUsers!;
          //this.loader = false;
        }
      });
  }

  onDeleteFeature(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.adminAccessControlService
          .deleteAssignedUsersandRoles({ groupId: id })
          .subscribe(
            (res) => {
              this.loaderService.hide();
              if (res?.success) {
                this.messageService.add({
                  severity: 'success',
                  summary: 'Confirmed',
                  detail: 'deleted successfully.',
                  life: 3000,
                });
                setTimeout(() => {
                  this.loadAssignedUsersandRoles();
                }, 1000);
              } else {
                this.messageService.add({
                  severity: 'error',
                  summary: 'Rejected',
                  detail: 'Something went wrong',
                  life: 3000,
                });
              }
            },
            (err) => {
              this.loaderService.hide();
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          );
      },
      reject: () => {},
    });
  }

  addOrganization() {
    this.visible = true;
  }

  userChange(event: any) {
    this.selectedUser = event.value;
  }

  assignOrg() {
    if (this.selectedUser && this.selectedGroups.length > 0) { 
      let selectedUser = this.selectedUser.userId;
      let selectedGroupIds = this.selectedGroups.map((d) => d.groupId);
      this.loaderService.show();
      this.adminAccessControlService
        .assignUsersToGroups({
          userId: selectedUser,
          groupIds: selectedGroupIds,
        })
        .subscribe(
          (res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'User Assigned to Group successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadAssignedUsersandRoles();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          },
          (err) => {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        );
    } else {
    }
  }
  unAssignOrg() {
    if (this.selectedUser && this.selectedGroups.length > 0) {
      //this.selectedUser = this.selectedUser.
      let selectedUser = this.selectedUser.userId;
      let selectedGroupIds = this.selectedGroups.map((d) => d.groupId);
      this.loaderService.show();
      this.adminAccessControlService
        .unassignUsersToGroups({
          userId: selectedUser,
          groupIds: selectedGroupIds,
        })
        .subscribe(
          (res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'User Unassigned to Group successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadAssignedUsersandRoles();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          },
          (err) => {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        );
    } else {
    }
  }
}
