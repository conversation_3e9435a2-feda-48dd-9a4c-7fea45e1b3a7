export function convertToMins(min: number): string {
  const minutes: number = Math.floor(min);
  const seconds: number = Math.round((min - minutes) * 60);
  return `${minutes}m:${seconds}s`;
  
}


export function convertToMins1(min: number): string {
  const minutes: number = Math.floor(min);
  const seconds: number = Math.round((min - minutes) * 60);
 // return `${minutes} min`;
  if (minutes > 0 && seconds > 0) {
    return `${minutes} min ${seconds} sec`;
  } else if (minutes > 0) {
    return `${minutes} min`;
  } else {
    return `${seconds} sec`;
  }
}