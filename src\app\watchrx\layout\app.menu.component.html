
<p-confirmDialog />
<ul class="layout-menu">
  <li class="user-logo-section">
    <div class="user-logo">
      <a class="layout-topbar-logo" routerLink="/dashboard">
        <img src="assets/watchrx/svg/app_logo.svg" alt="logo" />
      </a>
    </div>
    <p-divider></p-divider>
  </li>

  <li class="user-profile-section">
    <div class="user-profile">
      <img src="/assets/watchrx/images/avatar/user-icon.png" alt="User Icon" class="user-icon" />
      <span class="user-name">{{ userName }}</span>
      <p-menu #menu [model]="items" [popup]="true" appendTo="body" />
      <p-button (onClick)="menu.toggle($event)" [link]="true" icon="pi pi-angle-down" />
    </div>
    <p-divider></p-divider>
  </li>
  <li style="height:calc(100vh - 192px); overflow:auto">
    <ul>
      <ng-container *ngFor="let item of model[0].items; let i = index">
        <li *ngIf="!item.separator" class="menu-separator space">
          <a [routerLink]="item.routerLink" class="p-menuitem-link space"
            [ngClass]="{ 'active-route': item.label == selectedLabel }" (click)="setActiveLink(item.label, $event)">
            <div [innerHTML]="item.icon"></div>
            <span class="ml-2 menu-font">{{ item.label }}</span>
          </a>
        </li>
      </ng-container>
    </ul>
  </li>
  <li> <div class="layout-footer" style="padding-top: 0.7rem;">App Version : {{version}}</div></li>
</ul>