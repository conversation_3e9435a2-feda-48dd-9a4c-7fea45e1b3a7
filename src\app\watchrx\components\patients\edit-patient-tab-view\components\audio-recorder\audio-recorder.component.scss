.audio-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  max-width: 200px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  color: #c62828;
  font-size: 12px;
  width: 100%;
  
  i {
    color: #d32f2f;
    font-size: 12px;
  }
  
  .clear-error-btn {
    margin-left: auto;
    background: none;
    border: none;
    color: #c62828;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    font-size: 10px;
    
    &:hover {
      background-color: #ffcdd2;
    }
  }
}

::ng-deep .audio-recorder {
  .p-button {
    &.recording-active {
      background-color: #d32f2f !important;
      border-color: #d32f2f !important;
      color: white !important;
      
      &:hover {
        background-color: #b71c1c !important;
        border-color: #b71c1c !important;
      }
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.2) !important;
      }
    }
  }
}

.recording-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #d32f2f;
  margin-top: 5px;
  
  .status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    
    .pulse-dot {
      width: 6px;
      height: 6px;
      background-color: #d32f2f;
      border-radius: 50%;
      animation: pulse 1.5s infinite;
    }
  }
}

.audio-playback {
  margin-top: 10px;
  width: 100%;
  
  audio {
    width: 100%;
    height: 30px;
    border-radius: 4px;
  }
}

@keyframes pulse {
  0%, 100% { 
    opacity: 1;
    transform: scale(1);
  }
  50% { 
    opacity: 0.5;
    transform: scale(1.2);
  }
}

@media (max-width: 600px) {
  .audio-recorder {
    max-width: 160px;
  }
}
