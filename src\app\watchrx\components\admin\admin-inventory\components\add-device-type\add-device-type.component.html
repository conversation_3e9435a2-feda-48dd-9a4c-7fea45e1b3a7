<p-toast />
<p-confirmDialog />
<div class="grid p-fluid mt-3">
  <div class="field col-12">
    <p-table
      [value]="deviceTypes"
      [paginator]="true"
      [totalRecords]="totalCount"
      [rows]="itemsPerPage"
      [lazy]="true"
      responsiveLayout="scroll"
      styleClass="p-datatable-gridlines p-datatable-striped"
    >
      <ng-template pTemplate="caption">
        <div class="flex align-items-center justify-content-between">
          <div>
            <p-button
              icon="pi pi-cart-plus"
              severity="contrast"
              label="Total Device Types:
          {{ deviceTypes.length }}"
            />
          </div>
          <div>
            <button
              pButton
              pRipple
              severity="secondary"
              icon="pi pi-plus"
              class="p-button-rounded p-button-success mr-2"
              style="height: 30px; width: 30px"
              (click)="openAddDeviceType()"
            ></button>
            <button
              pButton
              pRipple
              icon="pi pi-refresh"
              class="p-button-rounded p-button-primary mr-2"
              style="height: 30px; width: 30px"
              (click)="getDeviceTypes()"
            ></button>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th>S.No</th>
          <th>Device Type</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template let-inv pTemplate="body">
        <tr>
          <td>{{ inv.deviceTypeId }}</td>
          <td>{{ inv.typeName }}</td>
          <td>
            <div class="flex">
              <button
                pButton
                pRipple
                icon="pi pi-pencil"
                class="p-button-rounded p-button-success mr-2"
                style="height: 30px; width: 30px"
                (click)="openEditDeviceType(inv)"
              ></button>
              <button
                pButton
                pRipple
                icon="pi pi-trash"
                class="p-button-rounded p-button-warning"
                style="height: 30px; width: 30px"
                severity="danger"
                (click)="onDeleteDeviceType(inv.deviceTypeId)"
              ></button>
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-dialog
  header="Edit Device Type"
  [(visible)]="editDeviceModal"
  [modal]="true"
  [style]="{ width: '25rem' }"
>
  <div class="flex flex-column gap-3 mb-3">
    <label for="editDeviceType" class="font-bold w-6rem"> Device Type </label>
    <input
      pInputText
      id="editDeviceType"
      class="flex-auto"
      autocomplete="off"
      [(ngModel)]="editDeviceType"
    />
  </div>

  <ng-template pTemplate="footer">
    <p-button
      label="Cancel"
      [text]="true"
      severity="danger"
      (onClick)="editDeviceModal = false"
    />
    <p-button
      label="Save"
      [outlined]="true"
      severity="success"
      (onClick)="onEditDeviceType()"
    />
  </ng-template>
</p-dialog>
