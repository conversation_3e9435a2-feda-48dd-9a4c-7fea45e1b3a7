<p-toast></p-toast>
<div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb">

    <span class="font-bold font-16 flex align-items-center">
        <img src="assets/watchrx/svg/chat.svg" alt="patients" width="16" height="16" class="mr-2" />
        Profile</span>
</div>
<div class="card">
    <form #form="ngForm" (ngSubmit)="onSubmitInfo(form)">
        <div class="formdata">
            <div class="p-fluid p-formgrid grid">
                <div class="col-12 p-formgrid grid rightpanel">
                    <div class="field col-12 md:col-4">
                        <label for="firstName">First Name <span class="p-text-danger"> *</span></label>
                        <input id="firstName" type="text" pInputText [(ngModel)]="formData.firstName" name="firstName" autocomplete="new-firstname"
                            required />
                        <div class="red" *ngIf="form.submitted && !formData.firstName">
                            First Name is required.
                        </div>
                    </div>

                    <!-- <div class="field col-12 md:col-4">
                        <label for="lastName">Last Name</label>
                        <input id="lastName" type="text" pInputText [(ngModel)]="formData.lastName" name="lastName" autocomplete="new-lastname"
                            required />
                        <div class="red" *ngIf="form.submitted && !formData.lastName">
                            Last Name is required.
                        </div>
                    </div> -->

                     <div class="field col-12 md:col-4">
                        <label for="lastName">Last Name</label>
                        <input id="lastName" type="text" pInputText [(ngModel)]="formData.lastName" name="lastName" autocomplete="new-lastname"
                             />
                        
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="email">Email</label>
                        <input id="email" type="email" pInputText [(ngModel)]="formData.email" name="email" required autocomplete="new-email"
                            disabled="true" />
                        <div class="red" *ngIf="form.submitted && !formData.email">
                            Email is required and must be a valid email address.
                        </div>
                    </div>

                    <!-- <div class="field col-12 md:col-4">
                        <label for="email">Password</label>
                        <input id="password" type="password" pInputText [(ngModel)]="formData.password" name="password"
                            required disabled="true" />
                        <div class="red" *ngIf="form.submitted && !formData.password">
                            Password is required and must be a valid password
                        </div>
                    </div> -->


                    <div class="field col-12 md:col-4">
                        <label for="address1">Address 1<span class="p-text-danger"> *</span></label>
                        <input id="address1" type="text" pInputText [(ngModel)]="formData.address1" name="address1" autocomplete="new-address"
                            required />
                        <div class="red" *ngIf="form.submitted && !formData.address1">
                            Address 1 is required.
                        </div>
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="address2">Address 2</label>
                        <input id="address2" type="text" pInputText [(ngModel)]="formData.address2" name="address2"autocomplete="new-address1" />
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="country">Country<span class="p-text-danger"> *</span></label>
                        <input id="country" pInputText [(ngModel)]="formData.country" name="country" type="text" autocomplete="new-country"
                            required />
                        <div class="red" *ngIf="form.submitted && !formData.country">
                            Country is required.
                        </div>
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="state">State<span class="p-text-danger"> *</span></label>
                        <input id="state" pInputText [(ngModel)]="formData.state" name="state" type="text" autocomplete="new-state"
                            placeholder="state" />
                        <div class="red" *ngIf="form.submitted && !formData.state">
                            State is required.
                        </div>
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="city">City<span class="p-text-danger"> *</span></label>
                        <input id="city" pInputText [(ngModel)]="formData.city" name="city" type="text"  autocomplete="new-city"
                            placeholder="city" />
                        <div class="red" *ngIf="form.submitted && !formData.city">
                            City is required.
                        </div>
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="zipCode">Zip Code<span class="p-text-danger"> *</span></label>
                        <input id="zipCode" type="text" pInputText [(ngModel)]="formData.zipCode" name="zipCode" autocomplete="new-zipcode"
                            required />
                        <div class="red" *ngIf="form.submitted && !formData.zipCode">
                            Zip Code required.
                        </div>
                    </div>

                    <div class="field col-12 md:col-4">
                        <label for="phoneNumber">Phone Number<span class="p-text-danger"> *</span></label>
                        <input id="phoneNumber" type="text" pInputText [(ngModel)]="formData.phoneNumber" autocomplete="new-phonenumber"
                            name="phoneNumber" />
                        <span><small class="red">Note:(Example:+18001232323)</small></span>
                        <div *ngIf="!validatePhoneNumber() && formData.phoneNumber">
                            <p class="error-message p-error">
                                Invalid phone number format. Please enter a number in the format +[country
                                code][phone number], e.g., +1999999999 .
                            </p>
                        </div>
                    </div>
                    <div class="field col-12 md:col-4">
                        <label for="secondaryphoneNumber">Secondary Phone Number</label>
                        <input id="secondaryphoneNumber" type="text" pInputText
                            [(ngModel)]="formData.secondaryPhoneNumber" name="secondaryphoneNumber" autocomplete="new-secondaryphonenumber"/>
                        <span><small class="red">Note:(Example:+18001232323)</small></span>
                         <div *ngIf="!validateSecondaryPhoneNumber() && formData.secondaryPhoneNumber">
                            <p class="error-message p-error">
                                Invalid phone number format. Please enter a number in the format +[country
                                code][phone number], e.g., +1999999999 .
                            </p>
                        </div>
                    </div>

                    <div class="col-12 flex align-items-center">
                       
                        <div class="card p-0 mr-3 flex justify-content-center align-items-center img ">
                            <ng-container *ngIf="profilePic">
                                <img [src]="profilePic" class="profilepic" />
                            </ng-container>
                            <!-- <ng-container *ngIf="!profilePic">
                                <p>No Image Uploaded</p>
                            </ng-container> -->
    
                        </div>
                        <div class="field col-10 ">
                            <label for="profile upload"></label>
                            <p-fileUpload mode="basic" chooseLabel="Upload Photo" chooseIcon="pi pi-image" accept="image/*"
                                [maxFileSize]="1000000" (onSelect)="onFileSelect($event)" name="medImage"
                                class="upload flex"></p-fileUpload>
                        </div>
                       
                    </div>

                    <div class="field flex justify-content-end actionbuttons p-3 w-full">
                        <p-button label="Back" severity="secondary" [outlined]="true" class="mr-3"
                            (onClick)="goBack()"></p-button> 
                        <p-button label="Cancel Changes" severity="secondary" [outlined]="true" class="mr-3"
                            (onClick)="resetForm()" ></p-button>
                        <p-button label="Update Changes" severity="primary" class="mr-3" 
                            [loading]="isLoading" type="submit" ></p-button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>