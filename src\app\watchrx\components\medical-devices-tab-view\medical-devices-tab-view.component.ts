import { Component, Input, OnInit } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { MenuService } from '../../service/menu.service';
import { SharedModule } from '../shared/shared.module';

@Component({
  standalone: true,
  selector: 'app-medical-devices-tab-view',
  templateUrl: './medical-devices-tab-view.component.html',
  styleUrl: './medical-devices-tab-view.component.scss',
  imports: [SharedModule],
  providers: [ConfirmationService, MessageService],
})
export class MedicalDevicesTabViewComponent implements OnInit {
  constructor(public menuService: MenuService) {}
  activeIndex: number = 0;

  ngOnInit(): void {
    this.menuService.changeMenu('Medical Devices');
  }
}
