import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { FormsModule } from '@angular/forms';
import { ForgotPwdRoutingModule } from './forgot-password-routing.module';
import { ForgotPwdComponent } from './forgot-password.component';
import { ToastModule } from 'primeng/toast';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
    imports: [
        CommonModule,
        ForgotPwdRoutingModule,
        ButtonModule,
        FormsModule,
        ToastModule,
        InputTextModule,

    ],
    declarations: [ForgotPwdComponent]
})
export class AccessModule { }
