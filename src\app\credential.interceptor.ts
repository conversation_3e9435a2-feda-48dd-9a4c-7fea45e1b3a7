import {
  <PERSON>ttp<PERSON><PERSON>,
  <PERSON>tt<PERSON><PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';

import CryptoJS from 'crypto-js';

@Injectable()
export class HttpRequestInterceptor implements HttpInterceptor {
  intercept(
    req: HttpRequest<any>,
    next: <PERSON>ttpHand<PERSON>
  ): Observable<HttpEvent<any>> {
    let loginData = JSON.parse(localStorage.getItem('user')!);
    if(loginData != null){
      delete loginData['featuresEnabled']
      delete loginData['authToken']
    }
    if (loginData != null && loginData != undefined) {
      const encryptedAES = CryptoJS.AES.encrypt(
        JSON.stringify(loginData),
        'patient@watCRX'
      ).toString();
      let loginResponse = JSON.parse(localStorage.getItem('user')!);
      req = req.clone({
        headers: req.headers.set('userdetails', encryptedAES).set('auth-token', loginResponse.authToken),
      });
    }
    return next.handle(req);
  }
}
