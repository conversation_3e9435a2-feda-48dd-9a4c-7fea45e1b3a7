import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { PatientListResp,voice,video } from '../../../../api/allPatients';
import { APIConfig } from './APIConfig';
import { PatientAlerts, PatientCount } from '../../../../api/dashboard';

@Injectable({
  providedIn: 'root',
})
export class AllPatientsService {
  constructor(private http: HttpClient) {}

  setActivePatientList(details: string): Observable<PatientListResp> {
    return this.http.get<PatientListResp>(
      environment.BASE_URL + APIConfig.ACTIVE_PATIENT_LIST + details
    );
  }

  setInActivePatientList(details: string): Observable<PatientListResp> {
    return this.http.get<PatientListResp>(
      environment.BASE_URL + APIConfig.INACTIVE_PATIENT_LIST + details
    );
  }

  setSearchPatientList(details: string): Observable<PatientListResp> {
    return this.http.get<PatientListResp>(
      environment.BASE_URL + APIConfig.INACTIVE_PATIENT_LIST + details
    );
  }

  voiceToken(details: number): Observable<voice> {
    return this.http.get<voice>(
      environment.BASE_URL + APIConfig.VOICE_CALL_TOKEN + details
    );
  }

  pushToken(details: number): Observable<voice> {
    return this.http.get<voice>(
      environment.BASE_URL + APIConfig.PUSH_CALL_TOKEN + details
    );
  }

  getPatientPrograms(details:number):Observable<any>
  {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.PATIENT_ENROLLED_PROGRAM + details
    );
  }

  videoToken(details: any): Observable<video> {
    return this.http.post<video>(
      environment.BASE_URL + APIConfig.VIDEO_CALL_TOKEN, details
    );
  }

  setPatientCount(fromToDetails: string): Observable<PatientCount> {
    return this.http.get<PatientCount>(
      environment.BASE_URL + APIConfig.PATIENT_COUNT_URL + fromToDetails
    );
  }

  viewPatientAlerts(patientId:any): Observable<PatientAlerts>
  {
    return this.http.get<PatientAlerts>(
      environment.BASE_URL + APIConfig.PATIENT_ALERT_COUNT + patientId
    );
  }
  patientBulkUpload(details:any): Observable<any>
  {
    return this.http.post<any>(
      environment.BASE_URL + APIConfig.PATIENT_BULK_UPLOAD,details
    );
  }
}
