import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { TabViewModule } from 'primeng/tabview';
import { AllocatedMedicalDevicesComponent } from './components/allocated-medical-devices/allocated-medical-devices.component';
import { BulkAddDevicesComponent } from './components/bulk-add-devices/bulk-add-devices.component';
import { UnallocatedMedicalDevicesComponent } from './components/unallocated-medical-devices/unallocated-medical-devices.component';
import { ButtonModule } from 'primeng/button';
import { SidebarModule } from 'primeng/sidebar';

@Component({
  standalone: true,
  selector: 'app-medical-devices-tab',
  templateUrl: './medical-devices-tab.component.html',
  styleUrls: ['./medical-devices-tab.component.css'],
  imports: [
    TabViewModule,
    AllocatedMedicalDevicesComponent,
    UnallocatedMedicalDevicesComponent,
    BulkAddDevicesComponent,
    CommonModule,
    ButtonModule,
    SidebarModule
  ],
})
export class MedicalDevicesTabComponent implements OnInit {
  userRole: any;
  constructor() { }
  @Input() isBulkNeed: boolean = false;
  activeIndex: number = 0;
  visible = false;
  loading=false;
  @ViewChild(BulkAddDevicesComponent) bulkAddDevices!: BulkAddDevicesComponent;
  ngOnInit() {
    this.userRole = JSON.parse(localStorage.getItem('user')!);
  }
  openBulkAdd() {
    this.visible = true;
    this.bulkAddDevices.reset()
  }
  allocateDevie(event: any) {
    this.loading=true
    this.bulkAddDevices.bulkAddDevice(event);
  }
  onTabChange(event: any) {
    this.activeIndex = event.index;

    if (this.activeIndex == 2) {
      this.visible = true;
    }
    else {
      this.visible = false
    }
  }
  closeTab()
  {
    this.visible=false;
    this.loading=false;
  }
}
