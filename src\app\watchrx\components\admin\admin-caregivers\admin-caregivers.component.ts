import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { AdminCaregiverService } from './service/admin-caregiver.service';
import { debounceTime, Subject } from 'rxjs';

@Component({
  selector: 'app-admin-caregivers',
  templateUrl: './admin-caregivers.component.html',
  styleUrls: ['./admin-caregivers.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminCaregiversComponent implements OnInit {
  loader: boolean = false;
  caseManagersList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 15;
  @ViewChild('dt') table!: Table;
  visible: boolean = false;

  roleTypeList = [
    { type: 'Case Manager',value:'Case Manager' },
    { type: 'Care Giver',value:'Care Giver' },
    //{ type: 'Provider', value:'Provider' },
    {type:'MD',value:'Physician'},
    {type:'DO',value:'Physician'},
    {type:'NP',value:'Physician'},
    {type:'PA',value:'Physician'},
  ];
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: any;
  searchText: string="";
  rows:number=15

  constructor(
    public menuService: MenuService,
    private router: Router,
    private adminCaregiverService: AdminCaregiverService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
          this.debouncedValue = value;
          console.log('Search Value:', this.debouncedValue);
          this.onSearch(this.debouncedValue);
        });
   }

  ngOnInit() {
    this.cdr.detectChanges();
    this.menuService.changeMenu('Caregivers');
  }

  loadActivePatients($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loader = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize;

    this.adminCaregiverService.getCaregiversList(url).subscribe((res) => {
      if (res.success) {
        console.log(res);
        this.caseManagersList = res.clinicianList!;
        this.totalCaseManagers = res.count!;
        this.loader = false;

      }
    });
  }

  onDeleteCaseManager(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.adminCaregiverService
          .deleteCaregiver({ clinicianId: id })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Caregiver deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadActivePatients();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          });
      },
      reject: () => { },
    });
  }

  onToggleCaseManager(managerInfo: any, type: any) {
    console.log(managerInfo, 'managerInfo');
    this.loaderService.show();
    this.adminCaregiverService
      .toggleCaregiverStatus({
        clinicianId: managerInfo.clinicianId,
        type: 'caregiver',
      })
      .subscribe((res) => {
        this.loaderService.hide();
        managerInfo.status = type;
        this.loadActivePatients();

      });
  }

  addCaseMaanger() {
    this.visible = true;
  }

  previousRowsPerPage = 15;
  first = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }
  onInput(event: any): void {
    this.inputSubject.next(this.searchText);
  }
  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.loader = true;
      this.adminCaregiverService.searchCaregiver(searchTerm).subscribe((res) => {
        if (res.success) {
          this.caseManagersList = res.clinicianList!;
          this.totalCaseManagers = res.count!;
          this.loader = false;
          this.rows = res.count!||15
        }
      },err=>{
        this.loader = false;
      });
    } else {
      if (searchTerm !== undefined && searchTerm.length === 0) {
       this.loadActivePatients();
       this.rows=15
      }
    }
  }
}
