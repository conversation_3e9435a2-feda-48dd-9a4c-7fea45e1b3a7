<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="col-12">
    <div class="card">
      <div class="flex flex-row md:justify-content-end mb-2">
        <p-button
          label="Add Admin"
          [raised]="true"
          severity="secondary"
          class="mr-1"
          [outlined]="true"
          icon="pi pi-plus"
          (click)="addAdmin()"
        />
      </div>

      <div class="grid p-fluid mt-3">
        <div class="field col-12">
          <p-table
            #dt
            [value]="adminList"
            [paginator]="true"
            [totalRecords]="totalCount"
            [rows]="itemsPerPage"
            [lazy]="true"
            (onLazyLoad)="loadAdminsList($event)"
            responsiveLayout="scroll"
            styleClass="p-datatable-gridlines p-datatable-striped"
          >
            <ng-template pTemplate="header">
              <tr>
                <th>First Name</th>
                <th>Last Name</th>
                <th>User Name</th>
                <th>Status</th>
                <th>Action</th>
              </tr>
            </ng-template>
            <ng-template let-managerInfo pTemplate="body">
              <tr>
                <td>
                  {{ managerInfo.firstName }}
                </td>
                <td>{{ managerInfo.lastName }}</td>
                <td>{{ managerInfo.userName }}</td>

                <td>
                  <p-tag
                    [value]="getStatus(managerInfo.status)"
                    [severity]="getSeverity(managerInfo.status)"
                  />
                </td>
                <td>
                  <div class="flex">
                    <p-button
                      *ngIf="managerInfo.status == 'Y'"
                      label="Suspend User?"
                      [rounded]="false"
                      severity="danger"
                      size="small"
                      class="mr-1"
                      (click)="onToggleAdmin(managerInfo, 'N')"
                    />
                    <p-button
                      *ngIf="managerInfo.status == 'N'"
                      label="Activate User?"
                      [rounded]="false"
                      severity="success"
                      size="small"
                      class="mr-1"
                      (click)="onToggleAdmin(managerInfo, 'Y')"
                    />
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog
  header="Register Admin"
  [(visible)]="showPopup"
  [modal]="true"
  [style]="{ width: '800px', height: 'auto' }"
  [contentStyle]="{ overflow: 'visible' }"
  [draggable]="false"
  *ngIf="showPopup"
  [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
>
  <app-create-case-manager
    (submit)="showPopup = false"
    (cancel)="showPopup = false"
    [isOrgNeeded]="false"
    [roleTypeList]="roleTypeList"
  />
</p-dialog>
