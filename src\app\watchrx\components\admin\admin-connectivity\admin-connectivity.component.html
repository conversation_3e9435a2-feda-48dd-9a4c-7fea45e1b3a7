<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/gear.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Connectivity</span
  >
</div>
<div class="card">
  <p-toast />
  <div class="grid p-fluid mt-3">
    <div class="field col-12">
      <p-table
        #dt
        [value]="deviceList"
        [paginator]="true"
        [totalRecords]="totalCount"
        [rows]="totalCount"
        [lazy]="true"
        [lazy]="true"
        responsiveLayout="scroll"
        styleClass="p-datatable-gridlines p-datatable-striped"
      >
        <ng-template pTemplate="header">
          <tr>
            <th>Device ID</th>
            <th>Connectivity</th>
            <th>Last Communication</th>
            <th>Action</th>
          </tr>
        </ng-template>
        <ng-template let-device pTemplate="body">
          <tr>
            <td>
              {{ device.imei }}
            </td>
            <td>
              <p-tag
                [value]="getName(device.status)"
                [severity]="getColor(device.status)"
              />
            </td>
            <td>{{ formatDate(device.lastHeartBeatTime) }}</td>
            <td>
              <div class="flex">
                <p-button
                  icon="pi pi-wifi"
                  severity="primary"
                  [raised]="true"
                  (onClick)="openModal(device)"
                />
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<p-dialog
  header="Header"
  [(visible)]="visible"
  [modal]="true"
  [style]="{ width: '25rem' }"
>
  <ng-template pTemplate="header">
    <div class="inline-flex align-items-center justify-content-center gap-2">
      <p-tag
        [value]="getName(editDeviceStatus)"
        [severity]="getColor(editDeviceStatus)"
      />
      <span class="font-bold white-space-nowrap"> {{ editDeviceId }} </span>
    </div>
  </ng-template>
  <span class="p-text-secondary block mb-5 font-bold"
    >Collect logs OR send heart beat</span
  >
  <div class="inline-flex align-items-center justify-content-center gap-2">
    <p-button
      label="Heart Beat"
      [outlined]="false"
      severity="primary"
      [loading]="loader"
      (onClick)="takeAction('heartbeat')"
    />
    <p-button
      label="Get Logs"
      [outlined]="false"
      severity="secondary"
      (onClick)="takeAction('logupdate')"
      [loading]="loader"
    />
  </div>
</p-dialog>
