export interface physicians {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  resultCount: number;
  physicianVOList: physicianVO[];
}

export interface physicianVO {
  physicianId: 9;
  watchrxUserID: null;
  address: null;
  firstName: 'vikas';
  lastName: 'G';
  phoneNumber: null;
  email: '<EMAIL>';
  patientCount: 0;
  altPhoneNumber: null;
  hospitalName: null;
  shiftId: null;
  status: null;
  gcmRegistrationId: null;
  platformType: null;
  createdDate: null;
  updatedDate: null;
}
