import { Injectable, HostListener, Inject } from '@angular/core';
import { BnNgIdleService } from 'bn-ng-idle';
import { Router } from '@angular/router';
import { MenuService } from './menu.service';
import { DOCUMENT } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class IdleService {
  private hasLoggedOut = false;
  private isWatching = false;

  constructor(
    private bnIdle: BnNgIdleService,
    private router: Router,
    private menuService: MenuService,
    private document: Document
  ) {
    // Listen globally for user interaction to reset timer
    this.addUserActivityListeners();
  }

  startWatchingIdle(): void {
    if (this.isWatching) return;

    this.hasLoggedOut = false;
    this.isWatching = true;

    this.bnIdle.startWatching(1800).subscribe((isTimedOut: boolean) => {
      if (isTimedOut && !this.hasLoggedOut) {
        this.hasLoggedOut = true;
        this.isWatching = false;

        this.menuService.doLogout().subscribe(() => {
          localStorage.removeItem('user');
          this.router.navigate(['/login']);
        });
      }
    });
  }

  stopWatchingIdle(): void {
    this.bnIdle.stopTimer();
    this.isWatching = false;
  }

  private addUserActivityListeners(): void {
    const events = ['mousemove', 'keydown', 'click'];

    events.forEach(event =>
      this.document.addEventListener(event, () => this.resetIdleTimer())
    );
  }

  private resetIdleTimer(): void {
    if (!this.hasLoggedOut && this.isWatching) {
      this.bnIdle.resetTimer();
    }
  }
}
