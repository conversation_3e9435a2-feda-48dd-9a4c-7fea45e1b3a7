<p-toast />
<p-confirmDialog />
<!-- <div class="flex flex-row justify-content-between flex-wrap">
  <div class="flex col-2">
    <div class=" col-12 md:col-12">
      <label htmlfor="casemaager" class="font-bold block mb-2 font-12">Select Case Manager <span
          class="p-text-danger">*</span></label>
      <p-dropdown [options]="clinicianList" [(ngModel)]="downloadedManager" checkmark="true" optionLabel="label"
        [showClear]="downloadedManager?true:false" placeholder="Select Case Manager" [filter]="true" filterBy="label"
        id="casemaager" />
    </div>
  </div>

  <div class="flex col-2">
    <div class=" col-12 md:col-12">
      <label htmlfor="organization" class="font-bold block mb-2 font-12">Select Clinic/Organization <span
          class="p-text-danger">*</span></label>
      <p-dropdown [options]="orgsList" [(ngModel)]="downloadedManagerClinic" checkmark="true" optionLabel="label"
        [showClear]="downloadedManagerClinic?true:false" placeholder="Select Organization" [filter]="true"
        filterBy="label" id="organization" />
    </div>
  </div>
  <div class="flex col-2">
    <div class=" col-12 md:col-12">
      <label htmlfor="startdate" class="font-bold block mb-2 font-12">Start Date <span class="p-text-danger">*</span></label>
      <p-calendar [(ngModel)]="startDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
        placeholder="Start Date" [showButtonBar]="true" id="startdate" />
    </div>
  </div>
  <div class="flex col-2">
    <div class=" col-12 md:col-12">
      <label htmlfor="enddate" class="font-bold block mb-2 font-12">End Date <span class="p-text-danger">*</span></label>
      <p-calendar [(ngModel)]="endDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
        placeholder="End Date" [showButtonBar]="true" id="enddate" />
    </div>
  </div>
  <div class="flex col-4 justify-content-end align-items-center" style="margin-top: 15px;">
  <p-button [raised]="true" severity="primary" label="Download Statistics" [outlined]="true"
  (onClick)="generateStatistics()" [loading]="fileDownloadLoading" iconPos="right">
  <ng-template pTemplate="icon">
    <img src="assets/watchrx/svg/download_arrow.svg" alt="Download Stats" width="16" height="16" class="mr-2" />
  </ng-template>
</p-button>
</div>
</div>
<hr class="w-full" /> -->
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Total Billable CPT’S Generated per Clinic</h6>
  </div>
  <hr class="w-full" />
  <div class="col-12">
    <div class="flex flex-row " [ngClass]="userRole!=3?'justify-content-between':''">
      <div class="col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="orgsList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedClinic" filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData1('clinic', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [showClear]="true" placeholder="Select Time" [(ngModel)]="selectedClinicTime"
              (onChange)="filterDate1('clinic',$event.value)" class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="col-2" *ngIf="userRole!=3">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="submit('clinic', $event)" />
      </div>
      <div class="flex justify-content-end"  [ngClass]="userRole!=3?'col-6 ':'col-10'">
        <p-button [rounded]="false" icon="pi pi-download" severity="primary" class="ml-1" title="delete"
          label="Download Statistics" (click)="displayModal=true" />
      </div>
      <!--enable this once dynamic code starts-->
      <!-- <div class="flex col-6" style="display:none">
        <div class="flex flex-row justify-content-between gap-2">
          <div>
            <span class="p-float-label">
              <p-calendar
                [(ngModel)]="fromDate"
                [showIcon]="true"
                inputId="buttondisplay"
                [showOnFocus]="false"
                placeholder="Start Date"
                [showButtonBar]="true"
              />
              <label for="calendar">Start Date</label>
            </span>
          </div>
          <div>
            <span class="p-float-label">
              <p-calendar
                [(ngModel)]="toDate"
                [showIcon]="true"
                inputId="buttondisplay"
                [showOnFocus]="false"
                placeholder="Start Date"
                [showButtonBar]="true"
              />
              <label for="calendar">End Date</label>
            </span>
          </div>
          <p-button
            [rounded]="false"
            icon="pi pi-refresh"
            severity="primary"
            class="ml-1"
            title="delete"
            (click)="filterData('button', $event)"
          />
          <p-button
            [rounded]="false"
            icon="pi pi-download"
            severity="primary"
            class="ml-1"
            title="delete"
            (click)="filterData('button', $event)"
          />
        </div>
      </div> -->
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2" style="padding:10px 20px;">
    <div class="flex justify-content-between">
      <p>Billable CPT’S</p>
      <p>Number of Patients</p>
    </div>
    <p-chart type="bar" [data]="data" [options]="options" [height]="'370px'" />
    <div class="legend-container" *ngIf="selectedClinic?.id!=0">
      <div class="legend-item">
        <span class="color-box blue"></span>
        <span>Billable CPT's</span>
      </div>
      <div class="legend-item">
        <span class="color-box yellow"></span>
        <span>Patients</span>
      </div>
    </div>
    <div class="legend-container" *ngIf="selectedClinic?.id==0">
      <div class="legend-container">
        <div class="legend-item" *ngFor="let item of data?.datasets; let i = index">
          <span class="color-box " [ngStyle]="{'background-color':item.backgroundColor}"></span>
          <span>{{item.label}}</span>
        </div>
      </div>
    </div>
  </div>
</div>
<hr class="w-full mt-3" />
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Total Billable CPT’S Generated per Case manager</h6>
  </div>
  <hr class="w-full" />
  <div class="col-12 mt-3">
    <div class="flex flex-row ">
      <div class="col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="orgsList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedManagerClinic" filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData1('caseManager', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="selectedManagerList" checkmark="true" optionLabel="label" [filter]="true"
              filterBy="label" [showClear]="true" [(ngModel)]="selectedManager" placeholder="Select Organization"
              (onChange)="filterCaseManager('caseManager', $event.value)" class="mr-2" />
            <label for="calendar">Case Manager</label>
          </span>
        </p-inputGroup>
      </div>

      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [(ngModel)]="selectedManagerTime" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterDate1('caseManager',$event.value)" class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="col-2">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="submit('caseManager', $event)" />
      </div>
      <div class="col-4"></div>
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2">
    <div class="grid">
      <div class="col-6" style="padding:10px 20px;">
        <p-chart type="bar" [data]="data1" [options]="options" [height]="'370px'" />
        <div class="legend-container" *ngIf="selectedManagerClinic?.id!=0">
          <div class="legend-item">
            <span class="color-box blue"></span>
            <span>Billable CPT's</span>
          </div>
          <div class="legend-item">
            <span class="color-box yellow"></span>
            <span>Patients</span>
          </div>
        </div>
        <div class="legend-container" *ngIf="selectedManagerClinic?.id==0">
          <div class="legend-container">
            <div class="legend-item" *ngFor="let item of data1?.datasets; let i = index">
              <span class="color-box " [ngStyle]="{'background-color':item.backgroundColor}"></span>
              <span>{{item.label}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-6" style="padding:10px 20px;">
        <p-chart type="doughnut" [data]="pieData1" [options]="pieoptions" [height]="'200px'" />
        <div class="legend-container flex-wrap">
          <div class="legend-item flex-column" *ngFor="let item of pieData1?.datasets[0]?.data; let i = index">
            <span class="font-12 flex gap-2"><span class="color-box rounded"
                [ngStyle]="{'background-color':pieData1?.datasets[0]?.backgroundColor[i]}"></span>
              {{pieData1.labels[i]}}</span>
            <span class="font-bold">{{item}}</span>
          </div>
          <!-- <div>
            <span>{{pieData1?.labels[0]}}- {{pieData1?.labels[pieData1?.labels?.length-1]}}</span>
            <span>{{pieData1Total}}</span>
            <span>Billing CPT's</span>
          </div> -->
        </div>
        <div class="mb-2 ">
          <span class="font-12">16 Day Reading Completed</span>
          <p-progressBar [value]="100" [style]="{'background-color': '#EBF0FF'}" />
          <span class="font-12">{{totalReadings||0}} Readings</span>
        </div>
        <div>
          <span class="font-12">Encounter Min Completed</span>
          <p-progressBar [value]="100" [style]="{'background-color': '#EBF0FF'}" />
          <span class="flex justify-content-between font-12">
            <span>{{totalCPT||0}} CPT’s for {{totalPatients||0}} Patients</span>
            <!-- <span>Avg <br />2 CPT’s / Patient</span> -->
          </span>
        </div>
      </div>
    </div>

  </div>

  <!-- <div class="col-12">
     
      <div class="flex flex-row justify-content-between">
        <div class="col-3">
          <p-inputGroup>
            <span class="p-float-label">
              <p-dropdown
                [options]="orgsList"
                checkmark="true"
                optionLabel="label"
                [filter]="true"
                [(ngModel)]="selectedOrg"
                filterBy="label"
                [showClear]="true"
                placeholder="Select Organization"
                (onChange)="filterData('clinic', $event.value)"
                class="mr-2"
              />
              <label for="calendar">Select Clinic/Organization</label>
            </span>
          </p-inputGroup>
        </div>
        <div class="flex col-3">
          <p-inputGroup>
            <span class="p-float-label">
              <p-dropdown
                [options]="clinicianList"
                checkmark="true"
                optionLabel="label"
                [filter]="true"
                filterBy="label"
                [showClear]="true"
                placeholder="Select Organization"
                (onChange)="filterData('ca', $event.value)"
                class="mr-2"
              />
              <label for="calendar">Case Manager</label>
            </span>
          </p-inputGroup>
        </div>
        <div class="flex col-6">
          <div class="flex flex-row justify-content-between gap-2">
            <div>
              <span class="p-float-label">
                <p-calendar
                  [(ngModel)]="fromDate"
                  [showIcon]="true"
                  inputId="buttondisplay"
                  [showOnFocus]="false"
                  placeholder="Start Date"
                  [showButtonBar]="true"
                />
                <label for="calendar">Start Date</label>
              </span>
            </div>
            <div>
              <span class="p-float-label">
                <p-calendar
                  [(ngModel)]="toDate"
                  [showIcon]="true"
                  inputId="buttondisplay"
                  [showOnFocus]="false"
                  placeholder="Start Date"
                  [showButtonBar]="true"
                />
                <label for="calendar">End Date</label>
              </span>
            </div>
            <p-button
              [rounded]="false"
              icon="pi pi-refresh"
              severity="primary"
              class="ml-1"
              title="delete"
              (click)="filterData('button', $event)"
            />
            <p-button
              [rounded]="false"
              icon="pi pi-download"
              severity="primary"
              class="ml-1"
              title="delete"
              (click)="filterData('button', $event)"
            />
          </div>
        </div>
      </div>
      <div class="flex justify-content-between grid p-fluid">
        <p-card header="RPM" class="col-3">
          <div class="flex justify-content-between">
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'rpm20Minless')"
              >
                {{ statisticCount?.rpm20Minless || 0 }}
              </p>
              <p class="font-bold"><20M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'rpm20Min')"
              >
                {{ statisticCount?.rpm20Min || 0 }}
              </p>
              <p class="font-bold">20M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'rpm40Min')"
              >
                {{ statisticCount?.rpm40Min || 0 }}
              </p>
              <p class="font-bold">40M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'rpm60Min')"
              >
                {{ statisticCount?.rpm60Min || 0 }}
              </p>
              <p class="font-bold">60M</p>
            </div>
          </div>
        </p-card>
        <p-card header="CCM" class="col-3">
          <div class="flex justify-content-between">
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'ccm20Minless')"
              >
              {{ statisticCount?.ccm20Minless || 0 }}
              </p>
              <p class="font-bold"><20M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'ccm20Min')"
              >
              {{ statisticCount?.ccm20Min || 0 }}
              </p>
              <p class="font-bold">20M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'ccm40Min')"
              >
                {{ statisticCount?.ccm40Min || 0 }}
              </p>
              <p class="font-bold">40M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'ccm60Min')"
              >
                {{ statisticCount?.ccm60Min || 0 }}
              </p>
              <p class="font-bold">60M</p>
            </div>
          </div>
        </p-card>
        <p-card header="PCM" class="col-3">
          <div class="flex justify-content-between">
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'pcm30Minless')"
              >
                {{ statisticCount?.pcm30Minless || 0 }}
              </p>
              <p class="font-bold"><30M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'pcm30Min')"
              >
                {{ statisticCount?.pcm30Min || 0 }}
              </p>
              <p class="font-bold">30M</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'pcm60Min')"
              >
                {{ statisticCount?.pcm60Min || 0 }}
              </p>
              <p class="font-bold">60M</p>
            </div>
          </div>
        </p-card>
        <p-card header="Data Collection" class="col-3">
          <div class="flex justify-content-around">
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'dayMeasuredless')"
              >
                {{ statisticCount?.daysMeasuredless || 0 }}
              </p>
              <p class="font-bold"><16 days</p>
            </div>
            <div claass="flex flex-column">
              <p
                class="font-bold m-0"
                style="font-size: 2rem; color: var(--primary-color)"
                (click)="filterData('link', 'dayMeasured')"
              >
                {{ statisticCount?.daysMeasured || 0 }}
              </p>
              <p class="font-bold">16 days</p>
            </div>
          </div>
        </p-card>
      </div>
      <div class="flex grid p-fluid mt-2">
        <p-inputGroup class="col-6">
          <p-inputGroupAddon>Selected Filter</p-inputGroupAddon>
          <p-dropdown
            [options]="filterList"
            checkmark="true"
            optionLabel="label"
            [filter]="true"
            filterBy="label"
            [showClear]="true"
            placeholder="Select Filter"
            (onChange)="filterData('filter', $event.value)"
            class="mr-2"
          />
        </p-inputGroup>
      </div>
      <div class="grid p-fluid mt-3">
        <div class="field col-12">
          <p-table
            [value]="statisticList"
            [paginator]="true"
            [totalRecords]="totalRecords"
            (onLazyLoad)="loadBillingStatisticList($event)"
            [rows]="itemsPerPage"
            [lazy]="true"
            [loading]="loader"
            [globalFilterFields]="['orgName', 'physicianName', 'createdDate']"
            responsiveLayout="scroll"
            styleClass="p-datatable-gridlines p-datatable-striped"
          >
            <ng-template pTemplate="header">
              <tr>
                <th>Patient ID</th>
                <th>Patient Name</th>
                <th>Phone Number</th>
                <th>Case Manager</th>
                <th>RPM Mins</th>
                <th>CCM Mins</th>
                <th>PCM Mins</th>
                <th>Data Collection</th>
              </tr>
            </ng-template>
            <ng-template let-featureInfo pTemplate="body">
              <tr [ngClass]="getClass(featureInfo)">
                <td>{{ featureInfo.patientId }}</td>
                <td>{{ featureInfo.patientName }}</td>
                <td>{{ featureInfo.patientPhone }}</td>
                <td>{{ featureInfo.caseManagerName }}</td>
                <td>{{ featureInfo.rpmMins }}</td>
                <td>{{ featureInfo.ccmMins }}</td>
                <td>{{ featureInfo.pcmMins }}</td>
                <td>{{ featureInfo.daysCounted }}</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
  </div> -->
</div>
<hr class="w-full mt-3" />
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Performance Metrics of Billable CPT's (6 month Trend) </h6>
  </div>
  <hr class="w-full" />
  <div class="col-12">
    <div class="flex flex-row">
      <div class="col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="orgsList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedMetricClinic" filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData1('metric', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [(ngModel)]="selectedMetricTime" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterDate1('metric',$event.value)" class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="col-2" *ngIf="userRole!=3">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="submit('metric', $event)" />
      </div>
      <div class="col-6"></div>
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2" style="padding:10px 20px;" >
    <p-chart type="line" [data]="lineData" [options]="lineOptions" [height]="'370px'" />
    <div class="legend-container">
      <div class="legend-item" *ngFor="let item of lineData?.datasets; let i = index">
        <span class="color-box blue rounded" [ngStyle]="{'background-color':item.backgroundColor}"></span>
        <span>{{item.label}}</span>
      </div>
    </div>
  </div>
  <!-- <ng-template #noData *ngIf="lineData?.datasets?.length > 0 else noData">
    <div class="flex justify-content-center align-items-center w-full" style="height: 370px;">
      <p>No Data Available</p>
    </div>
  </ng-template> -->
</div>
<hr class="w-full mt-3" />
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Patient - 16 Day Reporting</h6>
  </div>
  <hr class="w-full" />
  <div class="col-12 mt-3">
    <div class="flex flex-row ">
      <div class="flex col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="selectedReportClinicList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedReportClinic" filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData1('report', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="selectedReportManagerList" checkmark="true" optionLabel="label" [filter]="true"
              filterBy="label" [showClear]="true" [(ngModel)]="selectedReportManager" placeholder="Select Organization"
              (onChange)="filterCaseManager('report', $event.value)" class="mr-2" />
            <label for="calendar">Case Manager</label>
          </span>
        </p-inputGroup>
      </div>

      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [(ngModel)]="selectedReportTime" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterDate1('report',$event.value)" class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="col-2">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="submit('report', $event)" />
      </div>
      <div class="col-4"></div>
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2">
    <div class="grid">
      <div class="col-12" style="padding:10px 20px;">
        <p-chart type="bar" [data]="data2" [options]="options2" [height]="'370px'" />
        <div class="legend-container" *ngIf="selectedReportManager?.id!=0">
          <div class="legend-item">
            <span class="color-box lightyellow"></span>
            <span>Total number of Patients </span>
          </div>
          <div class="legend-item">
            <span class="color-box yellow"></span>
            <span>Patients Completed 16 Day Reading</span>
          </div>
        </div>
        <div class="legend-container" *ngIf="selectedReportManager?.id==0">
          <div class="legend-container">
            <div class="legend-item" *ngFor="let item of data2?.datasets; let i = index">
              <span class="color-box " [ngStyle]="{'background-color':item.backgroundColor}"></span>
              <span>{{item.label}}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="col-6" style="padding:10px 20px;">
        <p-chart type="doughnut" [data]="pieData2" [options]="pieoptions" [height]="'300px'" />
        <div class="legend-container">
          <div class="legend-item flex-column ">
            <span class="font-12 flex gap-2"><span class="color-box yellow rounded"></span> Manager A</span>
            <span>120/150</span>
            <span>patients</span>
          </div>
          <div class="legend-item flex-column">
            <span class="font-12 flex gap-2"><span class="color-box orange rounded"></span> Manager B</span>
            <span>33/150</span>
            <span>patients</span>
          </div>
          <div class="legend-item flex-column">
            <span class="font-12 flex gap-2"> <span class="color-box black rounded"></span> Manager C</span>
            <span>67/150</span>
            <span>patients</span>
          </div>
          <div class="legend-item flex-column">
            <span class="font-12 flex gap-2"> <span class="color-box blue rounded"></span> Manager D</span>
            <span>120/150</span>
            <span>patients</span>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</div>
<hr class="w-full mt-3" />
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Total Encounter minutes</h6>
  </div>
  <hr class="w-full" />
  <div class="col-12 mt-3">
    <div class="flex flex-row ">
      <div class="col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="selectedEncounterClinicList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedEncounterClinic" filterBy="label" [showClear]="true"
              placeholder="Select Organization" (onChange)="filterData1('encounter', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="selectedEncounterManagerList" checkmark="true" optionLabel="label" [filter]="true"
              filterBy="label" [showClear]="true" [(ngModel)]="selectedEncounterManager"
              placeholder="Select Organization" (onChange)="filterCaseManager('encounter', $event.value)"
              class="mr-2" />
            <label for="calendar">Case Manager</label>
          </span>
        </p-inputGroup>
      </div>

      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [(ngModel)]="selectedEncounterTime" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterDate1('encounter',$event.value)" class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="col-2">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="submit('encounter', $event)" />
      </div>
      <div class="col-4"></div>
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2">
    <div class="grid">
      <div class="col-6" style="padding:10px 20px;">
        <p-chart type="bar" [data]="data3" [options]="options2" [height]="'370px'" />
        <div class="legend-container" *ngIf="selectedEncounterManager?.id!=0">
          <div class="legend-item">
            <span class="color-box pink"></span>
            <span>Encounter Minutes </span>
          </div>
        </div>
        <div class="legend-container" *ngIf="selectedEncounterManager?.id==0">
          <div class="legend-container">
            <div class="legend-item" *ngFor="let item of data3?.datasets; let i = index">
              <span class="color-box " [ngStyle]="{'background-color':item.backgroundColor}"></span>
              <span>{{item.label}}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-6" style="padding:10px 20px;">
        <p-chart type="doughnut" [data]="pieData3" [options]="pieoptions" [height]="'200px'" />
        <div class="legend-container flex-wrap">
          <div class="legend-item flex-column" *ngFor="let item of pieData3?.datasets[0]?.data; let i = index">
            <span class="font-12 flex gap-2"><span class="color-box rounded"
                [ngStyle]="{'background-color':pieData3?.datasets[0]?.backgroundColor[i]}"></span>
              {{pieData3.labels[i]}}</span>
            <span class="font-bold">{{item}}</span>
          </div>

        </div>

      </div>
    </div>
  </div>
</div>
<hr class="w-full mt-3" />
<div class="grid stat mt-1">
  <div class="col-12 heading">
    <h6 class="font-bold">Patient Monitoring Status</h6>
  </div>
  <hr class="w-full" />
  <div class="col-12 mt-3">
    <div class="flex flex-row ">
      <div class="col-2" *ngIf="userRole!=3">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="patientMonitoringList" checkmark="true" optionLabel="label" [filter]="true"
              [(ngModel)]="selectedOrg" filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData('clinic', $event.value)" class="mr-2" />
            <label for="calendar">Select Clinic/Organization</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="pmclinicianList" checkmark="true" optionLabel="label" [filter]="true"
              filterBy="label" [showClear]="true" placeholder="Select Organization"
              (onChange)="filterData('ca', $event.value)" [(ngModel)]="pmSelectedClinician" class="mr-2" />
            <label for="calendar">Case Manager</label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
              <p-calendar [(ngModel)]="startDate1" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
                placeholder="Start Date" [showButtonBar]="true" id="startdate" />
              <label for="startdate">Start Date <span class="p-text-danger">*</span></label>
          </span>
        </p-inputGroup>
      </div>
      <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-calendar [(ngModel)]="endDate1" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
              placeholder="End Date" [showButtonBar]="true" id="enddate" />
            <label for="enddate">End Date <span class="p-text-danger">*</span></label>
          </span>
        </p-inputGroup>
      </div>
      <!-- <div class="flex col-2">
        <p-inputGroup>
          <span class="p-float-label">
            <p-dropdown [options]="timeList" checkmark="true" optionLabel="label" [filter]="true" filterBy="label"
              [showClear]="true" placeholder="Select Organization" (onChange)="filterDate('clinic',$event.value)"
              class="mr-2" />
            <label for="calendar">Time Frame</label>
          </span>
        </p-inputGroup>
      </div> -->
      <div class="col-2">
        <p-button [rounded]="false" severity="primary" class="ml-1" title="submit" label="submit"
          (click)="filterData('button', $event)" />
      </div>
      <div class="col-4"></div>
    </div>
  </div>
  <hr class="w-full" />
  <div class="col-12 mb-2">
    <div class="grid p-fluid mt-3">
      <div class="field col-12">
        <p-table [value]="statisticList" [paginator]="true" [totalRecords]="totalRecords"
          (onLazyLoad)="loadBillingStatisticList($event)" [rows]="itemsPerPage" [lazy]="true" [loading]="loader"
          [globalFilterFields]="['orgName', 'physicianName', 'createdDate']" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>Patient ID</th>
              <th>Patient Name</th>
              <th>Phone Number</th>
              <th>Case Manager</th>
              <th>RPM Mins</th>
              <th>CCM Mins</th>
              <th>PCM Mins</th>
              <th>Data Collection</th>
            </tr>
          </ng-template>
          <ng-template let-featureInfo pTemplate="body">
            <tr><!--[ngClass]="getClass(featureInfo)"-->
              <td>{{ featureInfo.patientId }}</td>
              <td>{{ featureInfo.patientName }}</td>
              <td>{{ featureInfo.patientPhone }}</td>
              <td>{{ featureInfo.caseManagerName }}</td>
              <td>{{ featureInfo.rpmMins }}</td>
              <td>{{ featureInfo.ccmMins }}</td>
              <td>{{ featureInfo.pcmMins }}</td>
              <td>{{ featureInfo.daysCounted }}</td>
            </tr>
          </ng-template>
          <ng-template pTemplate="paginatorleft">
            <span class="text-900 font-bold">Total {{ totalRecords }} Patients</span>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<p-dialog header="Download Statistics" [(visible)]="displayModal" [modal]="true" [style]="{ width: '70vw' }"
  [baseZIndex]="10000" [dismissableMask]="true" [closeOnEscape]="true" (onHide)="displayModal=false">
  <div class="flex flex-row justify-content-between flex-wrap">
    <div class="flex col-6">
      <div class=" col-12 md:col-12">
        <label htmlfor="casemaager" class="font-bold block mb-2">Select Case Manager <span
            class="p-text-danger">*</span></label>
        <p-dropdown [options]="clinicianList" [(ngModel)]="downloadedManager" checkmark="true" optionLabel="label"
          [appendTo]="'body'" [showClear]="downloadedManager?true:false" placeholder="Select Case Manager"
          [filter]="true" filterBy="label" id="casemaager" />
      </div>
    </div>

    <div class="flex col-6">
      <div class=" col-12 md:col-12">
        <label htmlfor="organization" class="font-bold block mb-2">Select Clinic/Organization <span
            class="p-text-danger">*</span></label>
        <p-dropdown [options]="orgsList" [(ngModel)]="downloadedManagerClinic" checkmark="true" optionLabel="label"
          [appendTo]="'body'" [showClear]="downloadedManagerClinic?true:false" placeholder="Select Organization"
          [filter]="true" filterBy="label" id="organization" />
      </div>
    </div>
    <div class="flex col-6">
      <div class=" col-12 md:col-12">
        <label htmlfor="startdate" class="font-bold block mb-2">Start Date <span class="p-text-danger">*</span></label>
        <p-calendar [(ngModel)]="startDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          [appendTo]="'body'" placeholder="Start Date" [showButtonBar]="true" id="startdate" />
      </div>
    </div>
    <div class="flex col-6">
      <div class=" col-12 md:col-12">
        <label htmlfor="enddate" class="font-bold block mb-2">End Date <span class="p-text-danger">*</span></label>
        <p-calendar [(ngModel)]="endDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          [appendTo]="'body'" placeholder="End Date" [showButtonBar]="true" id="enddate" />
      </div>
    </div>

  </div>

  <p-footer class="w-full">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="displayModal = false" />
      <p-button label="Submit" severity="primary" (click)="generateStatistics()" [loading]="fileDownloadLoading" />
    </div>
  </p-footer>
</p-dialog>