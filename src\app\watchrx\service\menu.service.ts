import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  constructor(private http: HttpClient) {}
  LOGOUT_URL:string = 'service/user/logout'

  public messageSource = new BehaviorSubject<string>('Dashboard');
  currentMessage = this.messageSource.asObservable();

  changeMenu(message: string) {
    this.messageSource.next(message);
  }

  doLogout(): Observable<string> {
    return this.http.get<string>(
      environment.BASE_URL + this.LOGOUT_URL
    );
  }
  getChatCount(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + 'service/chat/chatCount'
    );
  }
  clearChatCount(): Observable<string> {
    return this.http.get<any>(
      environment.BASE_URL + '/service/chat/clearChatNotification', {
        responseType: 'text' as 'json' 
      }
    );
    
  }
}
