<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/papers.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Billing Report</span
  >
</div>
<div class="grid">
  <div class="col-12">
    <div class="card p-0">
      <div class="grid p-fluid m-0">
        <div class="field col-12">
          <p-tabView
            orientation="left"
            (onChange)="tabChange($event)"
            [(activeIndex)]="activeTabIndex"
          >
            <p-tabPanel header="Statistics" class="line-height-3 m-0">
              <app-statistics *ngIf="activeTabIndex == 0"></app-statistics>
            </p-tabPanel>
            <p-tabPanel header="Reports" class="line-height-3 m-0">
              <app-reports *ngIf="activeTabIndex == 1"></app-reports>
            </p-tabPanel>
          </p-tabView>
        </div>
      </div>
    </div>
  </div>
</div>
