<div class="p-fluid p-formgrid grid">
  <div class="field col-12 md:col-12">
    <label htmlfor="state" class="font-bold block mb-2">Select Program(s) <span class="p-text-danger">*</span></label>
    <div class="flex justify-content-left gap-3">
      <div class="flex flex-wrap gap-3">
        <div class="flex align-items-center" *ngFor="let option of programList">
          <p-radioButton name="consent" [value]="option.value" [(ngModel)]="selectedProgram"
            [inputId]="option.label.toLowerCase()" variant="filled" />
          <label [for]="option.label.toLowerCase()" class="ml-2">{{
            option.label
            }}</label>
        </div>
      </div>
    </div>
  </div>
  <div class="field col-12 md:col-12" *ngIf="selectedProgram=='rpm'">
    <label htmlfor="state" class="font-bold block mb-2">Device Type</label>
    <p-dropdown [options]="deviceTypes" [(ngModel)]="selectedDeviceType" checkmark="true" optionLabel="type"
      [showClear]="selectedDeviceType?true:false" placeholder="Select Device" (onChange)="deviceChange()" />
  </div>
  <div class="field col-12 md:col-12">
    <label htmlfor="state" class="font-bold block mb-2">Select Encounter Reason <span
        class="p-text-danger">*</span></label>
        <!--[showClear]="selectedEReason?true:false"-->
    <p-dropdown [options]="reasonList" [(ngModel)]="selectedEReason" checkmark="true" optionLabel="reason"
       placeholder="Select a Reason" (onChange)="reasonChange()" />
  </div>

  <div class="field col-12 md:col-4">
    <label htmlfor="description" class="font-bold block mb-2">Date <span class="p-text-danger">*</span></label>
    <p-calendar [(ngModel)]="encounterDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
      appendTo="body" />
  </div>
  <div class="field col-12 md:col-4">
    <label for="calendar-timeonly" class="font-bold block mb-2">Start Time <span class="p-text-danger">*</span> </label>
    <p-calendar [(ngModel)]="encounterTime" [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true"
      inputId="templatedisplay" appendTo="body" [showTime]="true" hourFormat="12" [stepMinute]="15">
      <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
        <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
      </ng-template>
    </p-calendar>
  </div>
  <div class="field col-12 md:col-4">
    <label for="calendar-timeonly" class="font-bold block mb-2">
      Duration (Mins)
    </label>
    <p-inputNumber [(ngModel)]="value4" inputId="minmax" mode="decimal" [min]="0" [max]="100" />
  </div>
  <div class="field col-12 md:col-12 position-relative">
    <label htmlfor="description" class="font-bold block mb-2">Description <span class="p-text-danger">*</span></label>
    <div class="flex align-items-center flex-column justify-content-end foot" *ngIf="showSpeech">
      <ng-container >
      <div class="flex flex-row align-items-center justify-content-between w-full">
        <p *ngIf="!isListening" class="font-bold"></p>
        <p-button *ngIf="!isListening" icon="pi pi-microphone" class="start" [rounded]="true" [text]="true"
          severity="primary" (click)="startSpeechToText()" />
      </div>

      <div class="flex flex-row align-items-center justify-content-between w-full">
        <p *ngIf="isListening" class="font-bold list m-0 p-2"><i class="pi pi-microphone" style="color: slateblue"></i>Listening....</p>
        <p-button *ngIf="isListening" class="stop" icon="pi pi-stop-circle" [rounded]="true" [text]="true"
          severity="danger" (click)="stopSpeechToText()" />
      </div>
      </ng-container>
    </div>
    <div  [ngClass]="selectedEReason.reason==undefined?'disabled-textarea':''" class="bold-textarea" contenteditable="true" (blur)="updateText($event)" [innerHTML]="safeDescription" (input)="onContentChange($event)"></div>

  </div>
</div>