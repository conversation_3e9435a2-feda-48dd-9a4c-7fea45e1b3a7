import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../api/editPatientProfile';
import { CustomReminder } from '../../../../../../api/reminder';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class ReminderService {
  constructor(private http: HttpClient) {}

  getCustomReminderList(data: any): Observable<CustomReminder[]> {
    return this.http.post<CustomReminder[]>(
      environment.BASE_URL + Constants.GET_REMINDERS,
      data
    );
  }

  updateCustomReminderList(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_REMINDERS,
      data
    );
  }

  addCustomReminderList(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_NEW_ALERT,
      data
    );
  }

  deleteCustomReminderList(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_ALERT,
      data
    );
  }
}
