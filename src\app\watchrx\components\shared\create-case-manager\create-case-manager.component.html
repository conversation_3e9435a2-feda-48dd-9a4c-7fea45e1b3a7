<div>
  <p-toast />
  <p-confirmDialog />
</div>
<form [formGroup]="userForm" (ngSubmit)="onSubmit()" autocomplete="off">
  <div class="p-fluid p-formgrid grid">
    <div class="field col-12 md:col-6">
      <label for="firstname" class="font-bold block mb-2">First Name <span class="p-text-danger">*</span></label>
      <input id="firstname" formControlName="firstname" pInputText />
      <div *ngIf="
          userForm.get('firstname')?.invalid &&
          userForm.get('firstname')?.touched
        ">
        First Name is required.
      </div>
    </div>

    <div class="field col-12 md:col-6">
      <label for="lastname" class="font-bold block mb-2">Last Name <span class="p-text-danger">*</span></label>
      <input id="lastname" formControlName="lastname" pInputText />
      <div *ngIf="
          userForm.get('lastname')?.invalid && userForm.get('lastname')?.touched
        ">
        Last Name is required.
      </div>
    </div>

    <div class="col-12 md:col-6">
      <label for="email" class="font-bold block mb-2">Email <span class="p-text-danger">*</span></label>
      <input id="email" formControlName="email" pInputText autocomplete="off" />
      <div *ngIf="userForm.get('email')?.invalid && userForm.get('email')?.touched">
        <div *ngIf="userForm.get('email')?.errors?.['required']">
          Email is required.
        </div>
        <div *ngIf="userForm.get('email')?.errors?.['email']">
          Invalid email format.
        </div>
      </div>
    </div>
    <div class=" col-12 md:col-6">
      <label for="phoneNumber" class="font-bold block mb-2">Phone Number</label>
      <input id="phoneNumber" type="text" pInputText formControlName="phoneNumber" name="phoneNumber"
        pattern="^\+[1-9]{1}[0-9]{3,14}$" minlength="10" />
      <div *ngIf="userForm.get('phoneNumber')?.invalid && userForm.get('phoneNumber')?.touched">
        <small class="p-error" *ngIf="userForm.get('phoneNumber')?.errors?.['pattern']">
          This value length is invalid. It should be between 10 and 17 characters long.
        </small>
      </div>
      <span><small>Note:(Example:+18001232323) Country code is manditory</small></span>
    </div>

    <div class="field col-12 md:col-6">
      <label for="password" class="font-bold block mb-2">Password <span class="p-text-danger">*</span></label>
      <input type="password" id="password" formControlName="password" pInputText autocomplete="new-password" />
      <div *ngIf="
          userForm.get('password')?.invalid && userForm.get('password')?.touched
        ">
        <div *ngIf="userForm.get('password')?.errors?.['required']" class="text-danger">
          Password is required.
        </div>
        <div *ngIf="userForm.get('password')?.errors?.['pattern']" class="text-danger">
          Password must contain at least one uppercase letter, one lowercase
          letter, and one non-alpha character.
        </div>
      </div>
    </div>

    <div class="field col-12 md:col-6">
      <label for="confirmpassword" class="font-bold block mb-2">Confirm Password <span
          class="p-text-danger">*</span></label>
      <input type="password" id="confirmpassword" formControlName="confirmpassword" pInputText />
      <div *ngIf="userForm.errors?.['mismatch'] && userForm.get('confirmpassword')?.touched" class="text-danger">
        Passwords must match.
      </div>
    </div>

    <div class="field col-12 md:col-6">
      <label htmlfor="state" class="font-bold block mb-2">Select Role Type <span
          class="p-text-danger">*</span></label>
      <p-dropdown [options]="roleTypeList" formControlName="roleType" checkmark="true" optionLabel="type"
        [showClear]="true" placeholder="Select Role Type" (onChange)="reasonChange()" />
    </div>

    <div class="field col-12 md:col-6" *ngIf="isOrgNeeded">
      <label htmlfor="state" class="font-bold block mb-2">Select Organization <span
          class="p-text-danger">*</span></label>
      <p-multiSelect [options]="organizationList" formControlName="organization" checkmark="true"
        optionLabel="groupName" [showClear]="true" placeholder="Select Organization" (onChange)="reasonChange()" />
    </div>

    <div class="field col-12 md:col-12">
      <p-checkbox formControlName="enabledMultiFactor" [binary]="true" (onChange)="onMultiFactorChange($event)"
        label="Multi Factor Authentication"></p-checkbox>
    </div>
  </div>
</form>

<div class="footer">
  <p-button label="Cancel" [outlined]="true" severity="primary" class="mr-2" (onClick)="onCancel()" />
  <p-button label="Save" severity="primary" [loading]="loading" (click)="onSubmit()" [disabled]="userForm.invalid || loading" />
</div>