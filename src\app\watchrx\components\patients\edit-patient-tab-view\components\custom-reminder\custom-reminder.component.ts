import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { CustomReminder } from '../../../../../api/reminder';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { ReminderService } from './service/reminder.service';
import { SidebarModule } from 'primeng/sidebar';

@Component({
  selector: 'app-custom-reminder',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    CalendarModule,
    DialogModule,
    DropdownModule,
    RadioButtonModule,
    ToastModule,
    ConfirmDialogModule,
    SidebarModule
  ],
  templateUrl: './custom-reminder.component.html',
  styleUrl: './custom-reminder.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class CustomReminderComponent implements OnInit {
  constructor(
    private reminderService: ReminderService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) {}

  patientId: number = 0;
  reminderList: CustomReminder[] = [];
  alertId: number = 0;
  isEdit: boolean = false;

  visible: boolean = false;
  response: any;
  selectedAlertType: any = '';
  selectedTime: any = '';
  alertTypes: any[] = [];
  scheduleType: any = 'daily';
  description: any = '';
  maxLength: number = 50;
  otherAlertType: any = '';
  startDate: Date | undefined;
  endDate: Date | undefined;
  title="Add Custom Reminder"
  setDefaultTime() {
    const now = new Date();
    const minutes = now.getMinutes();
    const roundedMinutes = Math.floor(minutes / 5) * 5;
    now.setMinutes(roundedMinutes, 0, 0);
    this.selectedTime = now;
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getCustomRemindersList();
    this.alertTypes = [
      { id: 0, alertType: 'Select Alert Type', value: null },
      {
        id: 1,
        alertType: 'Battery Alert',
        value: 'Battery Alert',
      },
      {
        id: 1,
        alertType: 'Food Alert',
        value: 'Food Alert',
      },
      {
        id: 1,
        alertType: 'Other',
        value: 'Other',
      },
    ];
    this.selectedAlertType = this.alertTypes[0];
  }

  getCustomRemindersList() {
    this.loaderService.show();
    this.reminderService
      .getCustomReminderList({ patientId: this.patientId })
      .subscribe((res) => {
        this.loaderService.hide();
        if (res) {
          this.reminderList = res;
        }
      });
  }

  submitRecord() {
    let payload = {
      patientId: this.patientId,
      customAlerts: [
        {
          alertType:
            this.selectedAlertType?.alertType === 'Other'
              ? this.otherAlertType
              : this.selectedAlertType?.alertType,
          alertTime: moment(this.selectedTime, 'hh:mm A').format('H:m'),
          detail: this.description,
          startDate:
            this.scheduleType === 'daily'
              ? ''
              : moment(this.startDate).format('MM-DD-YYYY'),
          endDate:
            this.scheduleType === 'daily'
              ? ''
              : moment(this.endDate).format('MM-DD-YYYY'),
          type: this.scheduleType,
        },
      ],
    };
    this.loaderService.show();
    this.reminderService.addCustomReminderList(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Custom reminder created successfully',
        });
        this.getCustomRemindersList();
        this.visible = false;
      } else {
        console.log(res);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to create custom reminder.',
        });
      }
    });
  }

  submitEditRecord() {
    let payload = {
      patientId: this.patientId,
      alertId: this.alertId,
      alertTime: moment(this.selectedTime, 'hh:mm A').format('H:m'),
      alertType: this.selectedAlertType?.alertType,
      detail: this.description,
      startDate:
        this.scheduleType === 'daily'
          ? ''
          : moment(this.startDate).format('MM-DD-YYYY'),
      endDate:
        this.scheduleType === 'daily'
          ? ''
          : moment(this.endDate).format('MM-DD-YYYY'),
      type: this.scheduleType,
    };
    this.loaderService.show();
    this.reminderService.updateCustomReminderList(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Updated successfully',
        });
        this.getCustomRemindersList();
        this.visible = false;
      } else {
        console.log(res);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update.',
        });
      }
    });
  }

  deleteRecord(event: any, alertId: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = {
          patientId: this.patientId,
          itemId: alertId,
          itemName: 'customAlert',
        };
        this.loaderService.show();
        this.reminderService
          .deleteCustomReminderList(payload)
          .subscribe((res) => {
            this.loaderService.hide();
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Deleted successfully',
              });
              this.getCustomRemindersList();
            } else {
              console.log(res);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete.',
              });
            }
          });
      },
      reject: () => {},
    });
  }

  editRecord(data: CustomReminder) {
     this.title="Edit Custom Reminder"
    const foundRecord = this.alertTypes.find(
      (obj) => obj.alertType === data?.alertType
    );
    this.visible = !this.visible;
    this.selectedAlertType = foundRecord;
    this.otherAlertType = '';
    this.selectedTime = this.getFormatedHours(data?.alertTime);
    this.scheduleType = data?.type;
    this.startDate =
      this.scheduleType === 'daily' ? new Date() : new Date(data?.startDate);
    this.endDate =
      this.scheduleType === 'daily' ? new Date() : new Date(data.endDate);
    this.description = data?.detail;
    this.alertId = data?.alertId;
    this.isEdit = true;
  }

  showDialog(data: any) {
     this.title="Add Custom Reminder"
    this.visible = !this.visible;
    this.selectedAlertType = this.alertTypes[0];
    this.otherAlertType = '';
    this.setDefaultTime();
    this.scheduleType = 'daily';
    this.startDate = undefined;
    this.endDate = undefined;
    this.description = '';
    this.isEdit = false;
  }

  getFormatedHours(alertTime: string) {
    if (alertTime) {
      return moment(alertTime, 'H:m').format('hh:mm A');
    }
    return alertTime;
  }
}
