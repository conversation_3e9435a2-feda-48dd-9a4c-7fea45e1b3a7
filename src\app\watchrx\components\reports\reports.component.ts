import { formatDate } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { MessageService } from 'primeng/api';
import { DashboardService } from '../..../../dashboard/service/dashboard.service';
import { ReportRequest, exportData } from '../../api/reports';
import { MenuService } from '../../service/menu.service';
import { ExcelService } from './service/excel.service';
import { ReportService } from './service/report.service';
import { PatientData, SelectedProgram } from '../../api/editPatientProfile';
import { EditPatientDashboardService } from '../patients/edit-patient-tab-view/components/dashboard/service/edit-patient-dashboard.service';
import moment from 'moment';
import { VitalService } from '../patients/edit-patient-tab-view/components/vitals/service/vital.service';
import { LabelType, Options } from '@angular-slider/ngx-slider';
@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
  providers: [ExcelService, MessageService],
})
export class ReportsComponent implements OnInit {
  defaultButton: string = 'w';
  weeklyEnabled: boolean = false;
  monthlyEnabled: boolean = true;

  selectedReportType: any | undefined;
  reportTypes: any[] = [];
  filteredReportTypes: any[] = [];

  selectedPatient: any | undefined;
  patientList: any[] = [];
  filteredPatients: any[] = [];

  currentMonthDate!: Date;

  currentWeekDate!: Date;

  dateToUse: string = '';
  startOfWeek!: Date;
  endOfWeek!: Date;
  startOfMonth: Date = new Date();
  endOfMonth: Date = new Date();
  currentMonth: string;
  currentDate!: Date;

  data: any;
  options: any;
  documentStyle = getComputedStyle(document.documentElement);
  static readonly PEDOMETER_URL = '/service/patient/vitalPedometerWeeklyGraph';
  static readonly MEDICATION_URL = '/service/patient/missedmedication';
  static readonly ALERTS_URL = '/service/patient/getAlertsCountReports';









  heartRateStatus: boolean = false;
  stepsStatus: boolean = false;
  spo2Status: boolean = false;
  weightStatus: boolean = false;
  tempStatus: boolean = false;
  bpStatus: boolean = false;
  bsStatus: boolean = false;
  sleepStatus: boolean = false;
  medicationStatus: boolean = true;
  headers: string[] = [];
  tabledData: any[] = [];
  threshold: any = {};
  thresholdStatus: any = {};
  graphOptions: any;
  medicationData: any;
  alertsData: any;
  patientData: PatientData | undefined;
  totalEnabled: number = 0;
  data1: any[] = [];
  chartOptions: any;
  graphOptions1: any;
  chartOptions1: any;
  chartOptions2: any;
  chartOptions3: any;
  chartOptions4: any;
  chartOptions5: any;
  chartOptions6: any;
  chartOptions7: any;
  attentionList: any[] = [];
  programs: SelectedProgram[] = [];
  id: any = -1;
  Format: string = "MONTHLY";
  maxSBP: any = 0;
  minSBP: any = 0;
  maxDBP: any = 0;
  minDBP: any = 0;
  sliderOptions = {
    floor: 0,
    ceil: 100,
    step: 10,
    disabled: true,
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '<b>Min:</b>' + value;
        case LabelType.High:
          return '<b>Max:</b>' + value;
        case LabelType.Floor:
          return '<b>Critical Min:</b>' + value;
        case LabelType.Ceil:
          return '<b>Critical Max:</b>' + value;
        default:
          return '' + value;
      }
    }
  };
  sbpOptions: Options = this.sliderOptions;
  dbpOptions: Options = this.sliderOptions

  maxBS: any = 0;
  minBS: any = 0;
  bsOptions: Options = this.sliderOptions;

  maxSP: any = 0;
  minSP: any = 0;
  spOptions: Options = this.sliderOptions;

  maxHR: any = 0;
  minHR: any = 0;
  hrOptions: Options = this.sliderOptions;

  maxTemp: any = 0;
  minTemp: any = 0;
  tempOptions: Options = this.sliderOptions;

  maxSC: any = 0;
  minSC: any = 0;
  scOptions: Options = this.sliderOptions;

  maxW: any = 0;
  minW: any = 0;
  wOptions: Options = this.sliderOptions;

  maxSLP: any = 0;
  minSLP: any = 0;
  slpOptions: Options = this.sliderOptions;
  medicationGraphOptions: any;

  constructor(
    private msgservice: MessageService,
    public menuService: MenuService,
    public dashboardService: DashboardService,
    public reportService: ReportService,
    public excelService: ExcelService,
    public editPatientDashboardService: EditPatientDashboardService,
    public vitalService: VitalService
  ) {
    this.currentMonthDate = new Date();
    this.currentWeekDate = new Date();
    this.currentDate = new Date();
    this.currentMonth = formatDate(this.currentDate, 'MMMM-yyyy', 'en-US');
  }
  maxThreshold: string = '';
  minThreshold: string = '';
  ngOnInit(): void {
    this.menuService.changeMenu('Reports');
    this.dashboardService.setPatientTask().subscribe((res) => {
      if (res.success) {
        //   console.log(res.patientMinimalVOList)
        this.patientList = res.patientMinimalVOList!;
        this.selectedPatient = this.patientList[0];
        this.onPatientSelect(this.patientList[0]);
      }
    });
    // this.reportTypes = [
    //   { name: 'Alert' },
    //   { name: 'Medication' },
    //   { name: 'Blood Pressure' },
    //   { name: 'Blood Sugar' },
    //   { name: 'Scale' },
    //   { name: 'Temperature ' },
    //   { name: 'Heart Rate' },
    //   { name: 'SPO2' },
    //   { name: 'Pedometer' },
    // ];

    // this.selectedReportType = this.reportTypes[0];
    this.selectedPatient = this.patientList[0];

    const textColor = this.documentStyle.getPropertyValue('--text-color');
    const textColorSecondary = this.documentStyle.getPropertyValue(
      '--text-color-secondary'
    );
    const surfaceBorder =
      this.documentStyle.getPropertyValue('--surface-border');

    this.data = {};

    this.startOfMonth.setDate(this.currentDate.getDate() - 30);



    this.getPatientProfileInfo();
  }

  graphTypeClick(type: string) {
    if (type === 'w') {
      this.Format = "WEEKLY"
      this.setCurrentWeek();
      this.weeklyEnabled = true;
      this.monthlyEnabled = false;
    }
    if (type === 'm') {
      this.Format = "MONTHLY";
      this.currentDate = new Date();
      this.currentMonth = formatDate(this.currentDate, 'MMMM-yyyy', 'en-US');
      this.weeklyEnabled = false;
      this.monthlyEnabled = true;
    }

    this.getVitalGraphWeekly();
    this.getAlertGraph();
    this.getMedicationGraph();
    //this.generateRecords('Medication', ReportsComponent.MEDICATION_URL);
    //this.onPatientSelect();
  }

  filterPatient(event: any) {
    const query = event.query.toLowerCase();
    this.filteredPatients = this.patientList.filter((p) =>
      p.patientName.toLowerCase().includes(query)
    );
    this.monthlyEnabled = true;
    this.weeklyEnabled = false;
  }

  filterReportType(event: any) {
    const query = event.query.toLowerCase();
    this.filteredReportTypes = this.reportTypes.filter((p) =>
      p.name.toLowerCase().includes(query)
    );
  }

  setCurrentWeek() {
    const currentDate = new Date();
    this.startOfWeek = this.getStartOfWeek(currentDate);
    this.endOfWeek = this.getEndOfWeek(currentDate);
  }

  getStartOfWeek(date: Date): Date {
    const currentDay = date.getDay();
    const distanceToMonday = (currentDay + 6) % 7; // Calculate how far the current day is from Monday
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - distanceToMonday);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek;
  }

  getEndOfWeek(date: Date): Date {
    const startOfWeek = this.getStartOfWeek(date);
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);
    return endOfWeek;
  }

  onPreviousClick() {
    if (this.monthlyEnabled) {
      this.currentMonthDate.setMonth(this.currentMonthDate.getMonth() - 1);
      this.currentMonth = formatDate(this.currentMonthDate, 'MMMM-yyyy', 'en-US');
      this.startOfMonth.setDate(this.currentDate.getDate() - 30);
      this.endOfMonth.setDate(this.currentDate.getDate());
    } else if (this.weeklyEnabled) {
      this.currentWeekDate.setDate(this.currentWeekDate.getDate() - 7);
      this.startOfWeek.setDate(this.startOfWeek.getDate() - 7);
      this.endOfWeek.setDate(this.endOfWeek.getDate() - 7);
    }
    // this.onPatientSelect();
    this.getVitalGraphWeekly();
    this.getAlertGraph();
    this.getMedicationGraph();
  }

  onNextClick() {
    if (this.monthlyEnabled) {
      this.currentMonthDate.setMonth(this.currentMonthDate.getMonth() + 1);
      this.currentMonth = formatDate(this.currentMonthDate, 'MMMM-yyyy', 'en-US');
      this.startOfMonth.setDate(this.currentDate.getDate() + 30);
      this.endOfMonth.setDate(this.currentDate.getDate());
    } else if (this.weeklyEnabled) {
      this.currentWeekDate.setDate(this.currentWeekDate.getDate() + 7);
      this.startOfWeek.setDate(this.startOfWeek.getDate() + 7);
      this.endOfWeek.setDate(this.endOfWeek.getDate() + 7);
    }
    // this.onPatientSelect();
    this.getVitalGraphWeekly();
    this.getAlertGraph();
    this.getMedicationGraph();
  }

  formatDate(date: Date): string {
    return date == undefined
      ? new Date().toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
      : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  // generateBloodSugarRecords() {
  //   this.data = {
  //     labels: [],
  //     datasets: [
  //       {
  //         label: 'Fasting Blood Sugar',
  //         backgroundColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         borderColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         data: [],
  //       },
  //       {
  //         label: 'Random Blood Sugar',
  //         backgroundColor: this.documentStyle.getPropertyValue('--pink-500'),
  //         borderColor: this.documentStyle.getPropertyValue('--pink-500'),
  //         data: [],
  //       },
  //     ],
  //   };
  //   let selectedDuration = 'WEEKLY';
  //   if (this.weeklyEnabled) {
  //     selectedDuration = 'WEEKLY';
  //     this.dateToUse = formatDate(this.currentWeekDate, 'yyyy-MM-dd', 'en-US');
  //   } else {
  //     selectedDuration = 'MONTHLY';
  //     this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
  //   }
  //   let reportRequest: ReportRequest = {
  //     vitalTypeNameList: ['Fasting Blood Sugar', 'Random Blood Sugar'],
  //     patientId: this.selectedPatient.patientId,
  //     periodType: selectedDuration,
  //     format: selectedDuration,
  //     requestedDate: this.dateToUse,
  //     endDate: this.dateToUse,
  //     startDate: this.dateToUse,
  //   };
  //   this.reportService.setReportData(reportRequest).subscribe((res) => {
  //     if (res.success) {
  //       this.data.labels = res.measuredDates;
  //       this.data.datasets[0].data = res.vitalsCountGraphVOs[0].counts;
  //       this.data.datasets[1].data = res.vitalsCountGraphVOs[1].counts;
  //     }
  //   });
  // }

  // generateBloodPressureRecords() {
  //   this.data = {
  //     labels: [],
  //     datasets: [
  //       {
  //         label: 'Systolic Blood Pressure',
  //         backgroundColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         borderColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         data: [],
  //       },
  //       {
  //         label: 'Diastolic Blood Pressure',
  //         backgroundColor: this.documentStyle.getPropertyValue('--pink-500'),
  //         borderColor: this.documentStyle.getPropertyValue('--pink-500'),
  //         data: [],
  //       },
  //     ],
  //   };
  //   let selectedDuration = 'WEEKLY';
  //   if (this.weeklyEnabled) {
  //     selectedDuration = 'WEEKLY';
  //     this.dateToUse = formatDate(this.currentWeekDate, 'yyyy-MM-dd', 'en-US');
  //   } else {
  //     selectedDuration = 'MONTHLY';
  //     this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
  //   }
  //   let reportRequest: ReportRequest = {
  //     vitalTypeNameList: [
  //       'Systolic Blood Pressure',
  //       'Diastolic Blood Pressure',
  //     ],
  //     patientId: this.selectedPatient.patientId,
  //     periodType: selectedDuration,
  //     requestedDate: this.dateToUse,
  //     endDate: this.dateToUse,
  //     format: selectedDuration,
  //     startDate: this.dateToUse,
  //   };
  //   this.reportService.setReportData(reportRequest).subscribe((res) => {
  //     if (res.success) {
  //       this.data.labels = res.measuredDates;
  //       this.data.datasets[0].data = res.vitalsCountGraphVOs[0].counts;
  //       this.data.datasets[1].data = res.vitalsCountGraphVOs[1].counts;
  //       console.log(this.data);
  //     }
  //   });
  // }

  // generateRecords(name: string, url: string) {
  //   this.data = {
  //     labels: [],
  //     datasets: [
  //       {
  //         label: name,
  //         backgroundColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         borderColor: this.documentStyle.getPropertyValue('--blue-500'),
  //         data: [],
  //       },
  //     ],
  //   };
  //   let selectedDuration = 'WEEKLY';
  //   if (this.weeklyEnabled) {
  //     selectedDuration = 'WEEKLY';
  //     this.dateToUse = formatDate(this.currentWeekDate, 'yyyy-MM-dd', 'en-US');
  //   } else {
  //     selectedDuration = 'MONTHLY';
  //     this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
  //   }
  //   let reportRequest: any = {
  //     //vitalTypeNameList: [name],
  //     patientId: this.selectedPatient.patientId,
  //     periodType: selectedDuration,
  //     //requestedDate: this.dateToUse,
  //     endDate: this.dateToUse,
  //    // format: selectedDuration,
  //    // startDate: this.dateToUse,
  //   };

  //     this.editPatientDashboardService.getMedicationGraph(reportRequest).subscribe((res) => {
  //       if (res.success) {
  //         if (res?.success) {
  //           let firstData = res?.vitalsCountGraphVOs[0];
  //           let secondData = res?.vitalsCountGraphVOs[1];
  //           console.log(secondData, 'secondData');
  //           let dataSets: any[] = [];
  //           if (firstData?.counts) {
  //             dataSets.push({
  //               label: firstData?.vitalTypeName,
  //               backgroundColor: '#38bfc6',
  //               borderColor: '#38bfc6',
  //               data: firstData?.counts,
  //               borderWidth: 3,
  //               barThickness: 20,
  //               spanGaps: true,
  //             });
  //           }
  //           if (secondData?.counts) {
  //             dataSets.push({
  //               label: secondData?.vitalTypeName,
  //               backgroundColor: '#f9a11d',
  //               borderColor: '#f9a11d',
  //               data: secondData?.counts,
  //               borderWidth: 3,
  //               barThickness: 20,
  //               spanGaps: true,
  //             });
  //           }

  //           let lables = (res?.measuredDates);
  //           let obj = {
  //             labels: lables,
  //             datasets: dataSets,
  //           };
  //           this.data.push({ obj, name });
  //           this.data = [...this.data];
  //           this.setGraphOptions(lables);
  //           console.log(this.data)
  //         }
  //       }
  //     });

  //  // this.setGraphOptions(this.data.labels);
  // }

  onPatientSelect(event?: any) {
    console.log(event, "selected patiedn");
    this.id = event?.patientId;

    this.getPatientProfileInfo()
    //console.log(this.selectedReportType.name)
    // if (this.selectedReportType.name == 'Blood Pressure') {
    //   this.generateBloodPressureRecords();
    // } else if (this.selectedReportType.name == 'Blood Sugar') {
    //   this.generateBloodSugarRecords();
    // } else if (this.selectedReportType.name == 'Scale') {
    //   this.generateRecords('Weight', '');
    // } else if (this.selectedReportType.name == 'Temperature') {
    //   this.generateRecords('Temperature', '');
    // } else if (this.selectedReportType.name == 'Heart Rate') {
    //   this.generateRecords('Heart Rate', '');
    // } else if (this.selectedReportType.name == 'SPO2') {
    //   this.generateRecords('Oxygen Saturation', '');
    // } else if (this.selectedReportType.name == 'Pedometer') {
    //   this.generateRecords('Pedometer', ReportsComponent.PEDOMETER_URL);
    // } else if (this.selectedReportType.name == 'Alert') {
    //   this.generateRecords('Alert', ReportsComponent.ALERTS_URL);
    // } else if (this.selectedReportType.name == 'Medication') {
    //   this.generateRecords('Medication', ReportsComponent.MEDICATION_URL);
    // }
  }
  exportToXlsx(type: string) {
    this.selectedReportType = type;
    let reportRequest: ReportRequest = {
      vitalTypeNameList: [],
      patientId: this.selectedPatient.patientId,
      periodType: '',
      requestedDate: this.dateToUse,
      endDate: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
      format: '',
      startDate: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
    };

    if (!this.weeklyEnabled) {
      const currentDate = new Date();
      reportRequest.startDate = formatDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1), 'yyyy-MM-dd HH:mm:ss',
        'en-US');

      // Get last day of the month
      reportRequest.endDate = formatDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0), 'yyyy-MM-dd HH:mm:ss',
        'en-US');
      // reportRequest.endDate = formatDate(
      //   this.endOfMonth,
      //   'yyyy-MM-dd HH:mm:ss',
      //   'en-US'
      // );
      // reportRequest.startDate = formatDate(
      //   this.startOfMonth,
      //   'yyyy-MM-dd HH:mm:ss',
      //   'en-US'
      // );
    } else {
      reportRequest.endDate = formatDate(this.endOfWeek, 'yyyy-MM-dd HH:mm:ss', 'en-US');
      reportRequest.startDate = formatDate(
        this.startOfWeek,
        'yyyy-MM-dd HH:mm:ss',
        'en-US'
      );
    }

    if (this.selectedReportType == 'Blood Pressure') {
      reportRequest.vitalTypeNameList = [
        'Systolic Blood Pressure',
        'Diastolic Blood Pressure',
      ];
      this.exportData(reportRequest);
    } else if (this.selectedReportType == 'Blood Sugar') {
      reportRequest.vitalTypeNameList = [
        'Fasting Blood Sugar',
        'Random Blood Sugar',
      ];
      this.exportData(reportRequest);
    } else if (this.selectedReportType == 'Scale') {
      reportRequest.vitalTypeNameList = ['Weight'];
      this.exportData1(reportRequest);
    } else if (this.selectedReportType == 'Temperature') {
      reportRequest.vitalTypeNameList = ['Temperature'];
      this.exportData1(reportRequest);
    } else if (this.selectedReportType == 'Heart Rate') {
      reportRequest.vitalTypeNameList = ['Heart Rate'];
      this.exportData1(reportRequest);
    } else if (this.selectedReportType == 'SpO2') {
      reportRequest.vitalTypeNameList = ['Oxygen Saturation'];
      this.exportData1(reportRequest);
    }
    else if (this.selectedReportType == 'Alerts') {
      let request = {
        patientId: this.selectedPatient.patientId,
        endDate: reportRequest.endDate,
        startDate: reportRequest.startDate,
      }
      this.exportData3(request);
    }
    else if (this.selectedReportType == 'Medication') {
      let request = {
        patientId: this.selectedPatient.patientId,
        endDate: reportRequest.endDate,
        startDate: reportRequest.startDate,
      }
      this.exportData4(request);
    }
    else if (this.selectedReportType == "All") {
      let req = {
        patientId: this.selectedPatient.patientId,
        endDate: reportRequest.endDate,
        startDate: reportRequest.startDate
      }
      this.exportData2(req);

    }
    else {
      this.msgservice.add({
        key: 'tst',
        severity: 'info',
        summary: 'Info',
        detail:
          'Export is not supported for report type ' +
          this.selectedReportType,
      });
    }
  }
  private exportData(reportRequest: ReportRequest) {
    this.reportService.exportData(reportRequest).subscribe((res) => {
      if (res.success) {
        let data: exportData[] = res.vitalReportsGraphVO;
        let jsonData: any = new Array();
        data.forEach((item) => {
          let xlsData: any = {};
          xlsData[res.vitalTypeName1] = item.vitalName1Count;
          xlsData[res.vitalTypeName2] = item.vitalName2Count;
          xlsData['Date'] = item.period;
          jsonData.push(xlsData);
        });
        this.excelService.exportJsonAsExcelFile(jsonData, `${res.vitalTypeName1}_vital_report`);
      }
    });
  }
  private exportData1(reportRequest: ReportRequest) {
    this.reportService.exportData(reportRequest).subscribe((res) => {
      if (res.success) {
        let data: exportData[] = res.vitalReportsGraphVO;
        let jsonData: any = new Array();
        data.forEach((item) => {
          let xlsData: any = {};
          xlsData[res.vitalTypeName1] = item.vitalName1Count;
          xlsData['Date'] = item.period;
          jsonData.push(xlsData);
        });
        this.excelService.exportJsonAsExcelFile(jsonData, `${res.vitalTypeName1}_vital_report`);
      }
    });
  }

  private exportData2(reportRequest: any) {
    this.reportService.exportAllData(reportRequest).subscribe((resp) => {
      resp.forEach((res: any) => {
        if (res.success) {
          let data: exportData[] = res.vitalReportsGraphVO;
          let jsonData: any = new Array();
          data.forEach((item) => {
            let xlsData: any = {};
            xlsData[res.vitalTypeName1] = item.vitalName1Count;
            if (res.vitalTypeName2) xlsData[res.vitalTypeName2] = item.vitalName2Count;
            xlsData['Date'] = item.period;
            jsonData.push(xlsData);
          });
          this.excelService.exportJsonAsExcelFile(jsonData, `${res.vitalTypeName1}_vital_report`);
        }
      });

    });
  }

  private exportData3(reportRequest: any) {
    this.reportService.exportAlerts(reportRequest).subscribe((resp) => {
      this.excelService.exportJsonAsExcelFile(resp, 'Alerts_');
    });
  }

  private exportData4(reportRequest: any) {
    this.reportService.exportMedication(reportRequest).subscribe((res) => {
      if (res.success) {
        this.excelService.exportJsonAsExcelFile(res.medication, 'Patient_Medication_Adherence_vital_Report');
      }
    });
  }

  getPatientProfileInfo() {
    let req = {
      patientId: this.id,
    };
    this.editPatientDashboardService.getPatientProfile(req).subscribe((res) => {
      this.patientData = res;
      this.programs = res.programs?.selectedPrograms;
      this.heartRateStatus = res.thresholdData?.heartRateIsEnabled;
      this.stepsStatus = res.thresholdData?.pedometerStepIsEnabled;
      this.spo2Status = res.thresholdData?.spo2IsEnabled;
      this.weightStatus = res.thresholdData?.weightIsEnabled;
      this.tempStatus = res.thresholdData?.temperatureIsEnabled;
      this.bpStatus = res.thresholdData?.diastolicBloodPressureIsEnabled;
      this.bsStatus = res.thresholdData?.bloodSugarIsEnabled;
      this.sleepStatus = res.thresholdData?.sleepMonitorIsEnabled;
      this.medicationStatus = true;
      this.totalEnabled =
        (this.heartRateStatus == true ? 1 : 0) +
        (this.stepsStatus == true ? 1 : 0) +
        (this.spo2Status == true ? 1 : 0) +
        (this.weightStatus == true ? 1 : 0) +
        (this.tempStatus == true ? 1 : 0) +
        (this.bpStatus == true ? 1 : 0) +
        (this.bsStatus == true ? 1 : 0) +
        (this.sleepStatus == true ? 1 : 0);

      this.getVitalGraphWeekly();
      this.getAlertGraph();
      //this.getMedicationGraph('Medication', ReportsComponent.MEDICATION_URL);
      this.getMedicationGraph()
    });
  }




  setGraphOptions(lables: any, graphName = "other") {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');

    this.graphOptions = {
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
        },
        legend: {
          display: false,
          labels: {
            color: textColor,
          },
        },
      },
      scales: {
        x: {
          stacked: false,
          ticks: {
            callback: (value: any) => {
              return lables[value];
            },
          },
          grid: {
            display: false,
          },
        },
        y: {
          min:0,
          stacked: false,
          ticks: {
            stepSize: 10,
          },
          grid: {
            display: true,
            color: '#000000',
            borderDash: [3, 5],
          },
        },
      },
    };

    this.medicationGraphOptions = {
      ...this.graphOptions,
      scales: {
        ...this.graphOptions.scales,
        x: {
          ...this.graphOptions.scales.x,
          stacked: true, // Update stacked to true for the x-axis
        },
        y: {
          ...this.graphOptions.scales.y,
          stacked: true, // Update stacked to true for the y-axis
        },
      },
    };


  }


  getAlertGraph() {
    if (this.weeklyEnabled) {
      this.dateToUse = formatDate(this.endOfWeek, 'yyyy-MM-dd', 'en-US');
    } else {
      this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
    }
    let req = {
      patientId: Number(this.id),
      format: this.Format,
      endDate: this.dateToUse
    };
    this.alertsData={}

    this.editPatientDashboardService.getAlertGraph(req).subscribe((res) => {
      if (res.success) {
        const documentStyle = getComputedStyle(document.documentElement);
        let vitalsCountGraphVOs = res.vitalsCountGraphVOs;
        let lables = res.measuredDates;
        this.alertsData = {
          labels: lables,
          datasets: [
            // {
            //   type: 'line',
            //   label: 'Alarm',
            //   backgroundColor: documentStyle.getPropertyValue('--orange-500'),
            //   data: vitalsCountGraphVOs[0].counts,
            //   spanGaps: true,
            // },
            {
              type: 'line',
              label: 'Critical',
              backgroundColor: documentStyle.getPropertyValue('--red-500'),
              data: vitalsCountGraphVOs[1].counts,
              spanGaps: true,

            },
            // {
            //   type: 'line',
            //   label: 'Info',
            //   backgroundColor: documentStyle.getPropertyValue('--primary-500'),
            //   data: vitalsCountGraphVOs[2].counts,
            //   spanGaps: true,

            // },
            // {
            //   type: 'line',
            //   label: 'Warning',
            //   backgroundColor: documentStyle.getPropertyValue('--yellow-500'),
            //   data: vitalsCountGraphVOs[3].counts,
            //   spanGaps: true,

            // },
          ],
        };
        this.setGraphOptions(lables);
      }
    });
  }

  getMedicationGraph() {
    if (this.weeklyEnabled) {
      this.dateToUse = formatDate(this.endOfWeek, 'yyyy-MM-dd', 'en-US');
    } else {
      this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
    }
    let req = {
      patientId: Number(this.id),
      periodType: this.Format,
      endDate: this.dateToUse
    };

    this.editPatientDashboardService.getMedicationGraph(req).subscribe((res) => {
      if (res.success) {
        const documentStyle = getComputedStyle(document.documentElement);
        let vitalsCountGraphVOs = res.vitalsCountGraphVOs;
        let lables = (res.measuredDates);
        this.medicationData = {
          labels: lables,
          datasets: [

            {
              type: 'bar',
              label: 'Missed Medication',
              backgroundColor: documentStyle.getPropertyValue('--orange-400'),
              data: vitalsCountGraphVOs[0].counts,
              spanGaps: true,
              barPercentage: 0.5, 
              categoryPercentage: 0.5,
              maxBarThickness:30  
            },
            // {
            //   type: 'line',

            //   backgroundColor: documentStyle.getPropertyValue('--orange-500'),
            //   data: vitalsCountGraphVOs[0].counts,
            //   spanGaps: true,

            // },
            {
              type: 'bar',
              label: 'Medication to Take',
              backgroundColor: '#38bfc6',
              data: vitalsCountGraphVOs[1].counts,
              spanGaps: true,
              maxBarThickness:30  // restrict maximum bar width
            },
          ],
        };
        this.setGraphOptions(lables, "medication");
      }
    });
  }


  getVitalGraphWeekly() {
    this.graphOptions1 = {
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
        },
        legend: {
          display: false,

        },
      },
      scales: {
        x: {
          ticks: {

          },
          grid: {
            display: false,
          },
        },
        y: {
          stacked: false,
          ticks: {
            stepSize: 10,
          },
          grid: {
            display: true,
            color: '#000000',
            borderDash: [3, 5],
          },
        },
      },
    };

    this.data1 = [];
    if (this.weeklyEnabled) {
      this.dateToUse = formatDate(this.endOfWeek, 'yyyy-MM-dd', 'en-US');
    } else {
      this.dateToUse = formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US');
    }
    let payload: any = {
      patientId: this.patientData?.patientId,
      requestedDate: this.dateToUse,
      periodType: this.Format,
    };
    if (this.heartRateStatus) {
      payload.vitalTypeNameList = ['Heart Rate'];
      this.getVitalData(payload, 'Heart Rate');
      this.chartOptions = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions.scales.y.min = (this.patientData?.thresholdMinMax?.heartRateCriticalMin || 0) - 30,
        this.chartOptions.scales.y.max = (this.patientData?.thresholdMinMax?.heartRateCriticalMax || 0) + 30

      this.maxHR = this.patientData?.thresholdMinMax?.heartRateMax;
      this.minHR = this.patientData?.thresholdMinMax?.heartRateMin;
      this.hrOptions = {
        ...this.hrOptions, // Preserve other options
        floor: (this.patientData?.thresholdMinMax?.heartRateCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.heartRateCriticalMax || 0) ,
        stepsArray:[
             { value: this.patientData?.thresholdMinMax?.heartRateCriticalMin},  { value: this.minHR }, { value: this.maxHR },{ value: this.patientData?.thresholdMinMax?.heartRateCriticalMax },
          ]
      };
    }
    if (this.bpStatus) {
      payload.vitalTypeNameList = [
        'Systolic Blood Pressure',
        'Diastolic Blood Pressure',
      ];
      this.getVitalData(payload, 'Blood Pressure');
      this.chartOptions1 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions1.scales.y.min = (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin || 0) - 30,
        this.chartOptions1.scales.y.max = (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax || 0) + 30
      this.maxSBP = this.patientData?.thresholdMinMax?.systolicBloodPressureMax;
      this.minSBP = this.patientData?.thresholdMinMax?.systolicBloodPressureMin;
      this.maxDBP = this.patientData?.thresholdMinMax?.diastolicBloodPressureMax;
      this.minDBP = this.patientData?.thresholdMinMax?.diastolicBloodPressureMin;
      this.sbpOptions = {
        ...this.sbpOptions, // Preserve other options
        floor: (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax || 0) ,
         stepsArray:[
             { value: this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin},  { value: this.minSBP }, { value: this.maxSBP },{ value: this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax },
          ]
      };

      this.dbpOptions = {
        ...this.sbpOptions, // Preserve other options
        floor: (this.patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMax || 0) ,
        stepsArray:[
             { value: this.patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMin},  { value: this.minDBP }, { value: this.maxDBP },{ value: this.patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMax },
          ]
      };
    }
    if (this.stepsStatus) {
      //payload.vitalTypeNameList = ['Pedometer'];
      this.getVitalDataForPedometer(payload, 'Steps Count');
      this.chartOptions2 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions2.scales.y.min = (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMin || 0) - 30,
        this.chartOptions2.scales.y.max = (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMax || 0) + 30
      this.maxSC = this.patientData?.thresholdMinMax?.pedometerStepCountMax;
      this.minSC = this.patientData?.thresholdMinMax?.pedometerStepCountMin;
      this.scOptions = {
        ...this.scOptions,
        floor: (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMax || 0) ,
         stepsArray:[
             { value: this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMin},  { value: this.minSC }, { value: this.maxSC },{ value: this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMax },
          ]
      };
    }
    if (this.spo2Status) {
      payload.vitalTypeNameList = ['Oxygen Saturation'];
      this.getVitalData(payload, 'SpO2');
      this.chartOptions3 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions3.scales.y.min = (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) - 30,
        this.chartOptions3.scales.y.max = (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) + 30

      this.maxSP = this.patientData?.thresholdMinMax?.spo2Max;
      this.minSP = this.patientData?.thresholdMinMax?.spo2Min;
      this.spOptions = {
        ...this.spOptions,
        floor: (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) ,
         stepsArray:[
             { value: this.patientData?.thresholdMinMax?.spo2CriticalMin},  { value: this.minSP }, { value: this.maxSP },{ value: this.patientData?.thresholdMinMax?.spo2CriticalMax },
          ]
      };
    }
    if (this.weightStatus) {
      payload.vitalTypeNameList = ['Weight'];
      this.getVitalData(payload, 'Weight');
      this.chartOptions4 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions4.scales.y.min = (this.patientData?.thresholdMinMax?.weightCriticalMin || 0) - 30,
        this.chartOptions4.scales.y.max = (this.patientData?.thresholdMinMax?.weightCriticalMax || 0) + 30

      this.maxW = this.patientData?.thresholdMinMax?.weightMax;
      this.minW = this.patientData?.thresholdMinMax?.weightMin;
      this.wOptions = {
        ...this.wOptions,
        floor: (this.patientData?.thresholdMinMax?.weightCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.weightCriticalMax || 0) ,
         stepsArray:[
             { value: this.patientData?.thresholdMinMax?.weightCriticalMin},  { value: this.minW }, { value: this.maxW },{ value: this.patientData?.thresholdMinMax?.weightCriticalMax },
          ]
      };
    }
    if (this.tempStatus) {
      payload.vitalTypeNameList = ['Temperature'];
      this.getVitalData(payload, 'Temperature');
      this.chartOptions5 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions5.scales.y.min = (this.patientData?.thresholdMinMax?.temperatureCriticalMin || 0) - 30,
        this.chartOptions5.scales.y.max = (this.patientData?.thresholdMinMax?.temperatureCriticalMax || 0) + 30

      this.maxTemp = this.patientData?.thresholdMinMax?.temperatureMax;
      this.minTemp = this.patientData?.thresholdMinMax?.temperatureMin;
      this.tempOptions = {
        ...this.tempOptions,
        floor: (this.patientData?.thresholdMinMax?.temperatureCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.temperatureCriticalMax || 0) ,
          stepsArray:[
             { value: this.patientData?.thresholdMinMax?.temperatureCriticalMin},  { value: this.minTemp }, { value: this.maxTemp },{ value: this.patientData?.thresholdMinMax?.temperatureCriticalMax },
          ]
      };
    }

    if (this.bsStatus) {
      payload.vitalTypeNameList = ['Fasting Blood Sugar', 'Random Blood Sugar'];
      this.getVitalData(payload, 'Blood Sugar');
      this.chartOptions6 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions6.scales.y.min = (this.patientData?.thresholdMinMax?.bloodSugarCriticalMin || 0) - 30,
        this.chartOptions6.scales.y.max = (this.patientData?.thresholdMinMax?.bloodSugarCriticalMax || 0) + 30

      this.maxBS = this.patientData?.thresholdMinMax?.bloodSugarMax;
      this.minBS = this.patientData?.thresholdMinMax?.bloodSugarMin;
      this.bsOptions = {
        ...this.bsOptions, // Preserve other options
        floor: (this.patientData?.thresholdMinMax?.bloodSugarCriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.bloodSugarCriticalMax || 0) ,
          stepsArray:[
             { value: this.patientData?.thresholdMinMax?.bloodSugarCriticalMin},  { value: this.minBS }, { value: this.maxBS },{ value: this.patientData?.thresholdMinMax?.bloodSugarCriticalMax },
          ]
      };
    }
    if (this.sleepStatus) {
      payload.vitalTypeNameList = ['Sleep Monitor'];
      this.getVitalData(payload, 'Sleep Monitor');
      this.chartOptions7 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions7.scales.y.min = (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) - 30,
        this.chartOptions7.scales.y.max = (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) + 30

      this.maxSLP = this.patientData?.thresholdMinMax?.spo2Max;
      this.minSLP = this.patientData?.thresholdMinMax?.spo2Min;
      this.slpOptions = {
        ...this.slpOptions, // Preserve other options
        floor: (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) ,
        ceil: (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) ,
        stepsArray:[
             { value: this.patientData?.thresholdMinMax?.spo2CriticalMin},  { value: this.minSLP }, { value: this.maxSLP },{ value: this.patientData?.thresholdMinMax?.spo2CriticalMax },
          ]
      };
    }

    // if (this.selectedVitalType?.name === 'Pedometer') {
    //   this.getPedometerGraph();
    //   return;
    // }
  }

  getVitalData(payload: any, name: string) {
    this.vitalService.getVitalGraph(payload, true).subscribe((res) => {
      if (res?.success) {
        let firstData = res?.vitalsCountGraphVOs[0];
        let secondData = res?.vitalsCountGraphVOs[1];
        console.log(secondData, 'secondData');
        let dataSets: any[] = [];
        if (firstData?.counts) {
          dataSets.push({
            label: firstData?.vitalTypeName,
            backgroundColor: '#38bfc6',
            borderColor: '#38bfc6',
            data: firstData?.counts,
            borderWidth: 3,
            maxBarThickness: 30,
            spanGaps: true,
          });
        }
        if (secondData?.counts) {
          dataSets.push({
            label: secondData?.vitalTypeName,
            backgroundColor: '#f9a11d',
            borderColor: '#f9a11d',
            data: secondData?.counts,
            borderWidth: 3,
            maxBarThickness: 30,
            spanGaps: true,
          });
        }

        let lables = (res?.measuredDates);
        let obj = {
          labels: lables,
          datasets: dataSets,
        };
        this.data1.push({ obj, name });
        this.data1 = [...this.data1];
        this.setGraphOptions(lables);
        console.log(this.data1)
      }
    });
  }
  getVitalDataForPedometer(payload: any, name: string) {
    this.vitalService.getPedometerGraph(payload, true).subscribe((res) => {
      if (res?.success) {
        let firstData = res?.vitalsCountGraphVOs[0];
        let dataSets: any[] = [];
        if (firstData?.counts) {
          dataSets.push({
            label: 'Pedometer',
            backgroundColor: '#38bfc6',
            borderColor: '#38bfc6',
            data: firstData?.counts,
            borderWidth: 3,
            maxBarThickness: 20,
            spanGaps: true,
          });
        }
        let obj = {
          labels: res?.period,
          datasets: dataSets,
        };

        this.data1.push({ obj, name });
        this.data1 = [...this.data1];
      }
    });
  }




  formatDatesWithNewline(dates: string[]): string[] {
    return dates.map((date) => {
      const day = moment(date).format('ddd');
      const datePart = moment(date).format('MM/DD');
      return `${day}\n${datePart}`;
    });
  }

























}
