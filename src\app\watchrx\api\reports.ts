export interface ReportResponse {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  measuredDates: string[];
  period: string[];
  vitalsCountGraphVOs: graphData[];
  thresholdConfig: {
    success: boolean;
    responseCode: number;
    responsecode: number;
    messages: string[];
    eList: threshold[];
  };
  vitalCollnStatus: any;
}

export interface graphData {
  vitalTypeName: string;
  vitalTypeId: number;
  counts: number[];
  counts1: number[];
  alerts: any[];
}

export interface threshold {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  thresholdId: number;
  patientId: number;
  status: string;
  vitalTypeName: string;
  vitalMin: number;
  vitalMax: number;
  vitalCriticalMin: number;
  vitalCriticalMax: number;
}

export interface ReportRequest {
  vitalTypeNameList: string[];
  patientId: number;
  periodType: string;
  format: string;
  requestedDate: string;
  endDate: string;
  startDate: string;
}

export interface ReportExport {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  vitalTypeName1: string;
  vitalTypeName2: string;
  vitalReportsGraphVO: exportData[];
}

export interface exportData {
  period: string;
  vitalName1Count: number;
  vitalName2Count: number;
}
