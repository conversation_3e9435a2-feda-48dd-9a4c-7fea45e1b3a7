const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getDesktopSources: () => ipcRenderer.invoke('get-desktop-sources'),
  getSystemAudioStream: (sourceId) => ipcRenderer.invoke('get-system-audio-stream', sourceId),
  platform: process.platform,
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  },
  getEnhancedMediaStream: async (constraints) => {
    try {
      console.log('🎬 getEnhancedMediaStream called with:', constraints);

      if (constraints.video && constraints.video.mandatory && constraints.video.mandatory.chromeMediaSource === 'desktop') {
        console.log('📺 Using desktop capture constraints');
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Desktop stream obtained:', {
          audioTracks: stream.getAudioTracks().length,
          videoTracks: stream.getVideoTracks().length,
          tracks: stream.getTracks().map(t => `${t.kind}: ${t.label}`)
        });
        return stream;
      }

      console.log('🎤 Using regular getUserMedia');
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ Regular stream obtained:', {
        audioTracks: stream.getAudioTracks().length,
        videoTracks: stream.getVideoTracks().length
      });
      return stream;
    } catch (error) {
      console.error('❌ getEnhancedMediaStream error:', error);
      throw error;
    }
  }
});

window.addEventListener('DOMContentLoaded', () => {
  if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
    const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia.bind(navigator.mediaDevices);
    
    navigator.mediaDevices.getDisplayMedia = async function(constraints) {
      try {
        console.log('🎯 getDisplayMedia called with constraints:', constraints);
        const sources = await window.electronAPI.getDesktopSources();
        console.log('📺 Available sources:', sources.length);

        if (sources && sources.length > 0) {
          // Prioritize screen sources for better audio capture
          let screenSource = sources.find(source => source.id.startsWith('screen:'));

          // If no screen source, try to find a source that might have audio
          if (!screenSource) {
            screenSource = sources[0];
          }

          console.log('🖥️ Selected source:', screenSource?.name, screenSource?.id);
          console.log('🔍 All available sources:', sources.map(s => `${s.name} (${s.id})`));

          if (screenSource) {
            const electronConstraints = {
              audio: constraints.audio ? {
                mandatory: {
                  chromeMediaSource: 'desktop',
                  chromeMediaSourceId: screenSource.id,
                  echoCancellation: false,
                  noiseSuppression: false,
                  autoGainControl: false,
                  googEchoCancellation: false,
                  googAutoGainControl: false,
                  googNoiseSuppression: false,
                  googTypingNoiseDetection: false
                }
              } : false,
              video: constraints.video ? {
                mandatory: {
                  chromeMediaSource: 'desktop',
                  chromeMediaSourceId: screenSource.id,
                  minWidth: constraints.video.width || 1,
                  maxWidth: constraints.video.width || 1920,
                  minHeight: constraints.video.height || 1,
                  maxHeight: constraints.video.height || 1080
                }
              } : false
            };

            console.log('🔧 Using electron constraints:', electronConstraints);
            const stream = await window.electronAPI.getEnhancedMediaStream(electronConstraints);
            console.log('🎵 Got stream with tracks:', stream.getTracks().map(t => `${t.kind}: ${t.label} - enabled: ${t.enabled} - readyState: ${t.readyState}`));

            // Check if we actually got audio tracks
            const audioTracks = stream.getAudioTracks();
            console.log(`🔊 Audio tracks found: ${audioTracks.length}`);

            if (constraints.audio && audioTracks.length === 0) {
              console.warn('⚠️ No audio tracks in stream despite requesting audio');
            }

            return stream;
          }
        }

        console.log('⚠️ Falling back to original getDisplayMedia');
        return await originalGetDisplayMedia(constraints);

      } catch (error) {
        console.error('❌ getDisplayMedia error:', error);
        console.log('⚠️ Falling back to original getDisplayMedia due to error');
        return await originalGetDisplayMedia(constraints);
      }
    };
  }
});