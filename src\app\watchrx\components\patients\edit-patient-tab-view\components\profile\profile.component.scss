.red {
    color: red;
}

.consent-logo {
    width: 150px;
    height: auto;
    margin-right: 25px;
}

.company-name {
    margin-left: auto;
    font-weight: bold;
}

.orange-strip {
    background-color: orange;
    height: 35px;
    width: 100%;
    color: black;
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: normal;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
}

.profileinfo {
    ::ng-deep .p-datatable-striped
    {
        padding:10px
    }
    h6 {
        font-weight: 700;
    }

    h6,
    hr {
        margin: 0px !important;
    }

    ::ng-deep .p-card-header {
        padding: 15px 10px;
        margin: 0px !important;
        background-color: #f5f5f5;
    }

    ::ng-deep .p-card-body {
        padding: 0px !important
    }

    .formdata {
        padding: 15px
    }

    ::ng-deep .field {
        margin-bottom: 0.5rem !important;
    }
}

    .profilemenu {
        border-left: 1px solid #E8E8E8;

        li {
            list-style: none;
            padding: 20px;
            cursor: pointer;
            font-size: 14px;
        }

        .active {
            border-left: 4px solid #336CFB;
            color: #336CFB
        }

        ul {
            position: sticky;
            top: 0px;
        }
    }

    ::ng-deep .p-datatable-tbody>tr:nth-child(odd) {
        background-color: #F6F8FB;
        /* Light gray for odd rows */
    }

    ::ng-deep .p-datatable-tbody>tr:nth-child(even) {
        background-color: #ffffff;
        /* White for even rows */
    }

    .upload {
        ::ng-deep .p-fileupload-choose {
            background-color: #ffffff;
            color: #336CFB;
            margin-top: 23px;
        }
    }

    ::ng-deep .p-multiselect {
        width: 100%;
    }

    .actionbuttons {
        background-color: #F6F8FB;
    }

    ::ng-deep .p-sidebar-header {
        border-bottom: 1px solid #000000;
    }

    ::ng-deep .p-sidebar-footer {
        border-top: 1px solid #000000;
    }

    .error-field {
        border: 2px solid red !important;
        background-color: #ffe6e6;
    }