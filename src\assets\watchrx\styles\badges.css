.customer-badge,
.product-badge,
.order-badge {
  border-radius: var(--border-radius);
  padding: 0.25em 0.5rem;
  text-transform: uppercase;
  font-weight: 700;
  font-size: 12px;
  letter-spacing: 0.3px;
}

.product-badge.status-instock {
  background: #C8E6C9;
  color: #256029;
}
.product-badge.status-outofstock {
  background: #FFCDD2;
  color: #C63737;
}
.product-badge.status-lowstock {
  background: #FEEDAF;
  color: #8A5340;
}

.customer-badge.status-qualified {
  background: #C8E6C9;
  color: #256029;
}
.customer-badge.status-unqualified {
  background: #FFCDD2;
  color: #C63737;
}
.customer-badge.status-negotiation {
  background: #FEEDAF;
  color: #8A5340;
}
.customer-badge.status-new {
  background: #B3E5FC;
  color: #23547B;
}
.customer-badge.status-renewal {
  background: #ECCFFF;
  color: #694382;
}
.customer-badge.status-proposal {
  background: #FFD8B2;
  color: #805B36;
}

.order-badge.order-delivered {
  background: #C8E6C9;
  color: #256029;
}
.order-badge.order-cancelled {
  background: #FFCDD2;
  color: #C63737;
}
.order-badge.order-pending {
  background: #FEEDAF;
  color: #8A5340;
}
.order-badge.order-returned {
  background: #ECCFFF;
  color: #694382;
}
