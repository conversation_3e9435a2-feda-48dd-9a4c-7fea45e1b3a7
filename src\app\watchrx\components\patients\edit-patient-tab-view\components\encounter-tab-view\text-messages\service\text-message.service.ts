import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import { ScheduleTextMessageResp } from '../../../../../../../api/scheduleTextMessageResp';
import { TextMessageRep } from '../../../../../../../api/textMessageResponse';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class TextMessageService {
  constructor(private http: HttpClient) {}

  getTextMessages(details: any): Observable<TextMessageRep> {
    return this.http.get<TextMessageRep>(
      environment.BASE_URL + Constants.GET_TEXT_MESSAGES + details
    );
  }

  getScheduleTextMessages(details: any): Observable<ScheduleTextMessageResp> {
    return this.http.get<ScheduleTextMessageResp>(
      environment.BASE_URL + Constants.GET_SCHEDULE_TEXT_MESSAGES + details
    );
  }

  sendMessage(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SEND_MESSAGE,
      details
    );
  }

  createScheduleMessage(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SCHEDULE_MESSAGE,
      details
    );
  }

  deletScheduleMessage(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_MESSAGE,
      details
    );
  }

  saveChat(details:any): Observable<string> {
    return this.http.post<string>(
      environment.BASE_URL + Constants.SAVE_CHAT,
      details, {
        responseType: 'text' as 'json' 
      }
    );
  }
  getChatData(details:any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.GET_CHAT_DATA,
      details
    );
  }
}
