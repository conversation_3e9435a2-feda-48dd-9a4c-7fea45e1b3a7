export interface CaseManagerListResp {
    success: boolean;
    responseCode: string | string;
    responsecode: string | string;
    messages: string[];
    clinicianList: ManagerInfo[];
    count: number;
}

export interface ManagerInfo {
    address: address;
    allWatches: string|null;
    altPhoneNumber: string;
    available: boolean;
    clinicianId: number;
    createdDate: string|null;
    email: string;
    fileModified: boolean;
    firstName: string;
    fullAddress: string|null;
    hospital: string|null;
    lastName: string;
    patientCount: number;
    patientsWithWatchesHavingPrimaryRelToClinician: string|null;
    patientsWithWatchesHavingSecondaryRelToClinician: string|null;
    phoneNumber: string;
    picPath: string|null;
    roleType: string|null;
    shift: boolean;
    shiftName: string|null;
    speciality: string|null;
    specialityName: string|null;
    status: string;
    updatedDate: string|null;
    userName: string;
}

export interface address {
    address1: string;
    address2: string;
    addressId: string | null;
    city: string | null;
    country: string | null;
    name: string | null;
    phoneNumber: string | null;
    state: string | null;
    status: string | null;
    zip: string | null;
}

export interface GroupsAndRoles {
    groupsAndAssignedRoles: any[];
    status: any;
}

export interface Roles {
    roles: any[];
    status: any;
}


