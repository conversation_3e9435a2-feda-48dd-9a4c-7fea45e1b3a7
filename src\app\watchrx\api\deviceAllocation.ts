export interface Device {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  resultCount: number;
  purchasedWatchList: allocation[];
}
export interface allocation {
  allocationId: number;
  caregiver: Caregiver;
  sourceInventoryForWatchPurchase: SourceInventoryForWatchPurchase;
  watchAllocatedByAdmin: WatchAllocatedByAdmin;
  planAssociatedWithAllocatedWatch: any;
  subscriptions: any;
  watchOrder: any;
  groupVo: GroupVo;
  patientName: string;
  pvos: any;
}

export interface Caregiver {
  success: boolean;
  responseCode: any;
  responsecode: any;
  messages: any;
  caregiverId: any;
  name: any;
  address: any;
  phoneNumber: any;
  email: any;
  status: any;
  clinician: any;
}

export interface SourceInventoryForWatchPurchase {
  inventoryId: number;
  make: string;
  model: string;
  color: string;
  sellingPrice: any;
  quantity: any;
  imgUrl: any;
  specUrl: any;
  forsale: any;
  custom: any;
  listPrice: any;
  deviceMeasures: string;
  deviceMeasuresArray: string[];
  deviceType: string;
  deviceName: string;
}

export interface WatchAllocatedByAdmin {
  success: boolean;
  responseCode: any;
  responsecode: any;
  messages: any;
  status: string;
  assignStatus: any;
  watchId: string;
  imei: string;
  phoneNumber: any;
  watchMake: any;
  watchModel: any;
  color: any;
  picPath: any;
  isActive: boolean;
  isCustom: boolean;
  appColors: any;
  appLanguages: any;
  appColor: any;
  appLanguage: any;
  deviceMeasures: any;
  deviceMeasuresArray: any;
  deviceType: any;
  deviceName: any;
  model: any;
  make: any;
}

export interface GroupVo {
  groupName: string;
  groupId: number;
  createdDate: any;
}

export interface DeviceId {
  watchId: string;
}

export interface Patient {
  name: string;
  id: number;
}

export interface AllocateDevice {
  allocationId: number;
  imei: string;
  orgId: number;
  patientId: number;
  phoneNumber?:string;
  selectedFeatures?:any;
}

export interface BulkAddDeviceInventoryResp {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  inventory: InventoryItem[];
  groupVO: GroupVo[];
}

export interface InventoryItem {
  inventoryId: number;
  make: string;
  model: string;
  color: string;
  sellingPrice: string;
  quantity: number;
  imgUrl: string;
  specUrl: string;
  forsale: boolean;
  listPrice: string;
  deviceMeasures: string;
  deviceMeasuresArray: string[];
  deviceType: string;
  deviceName: string;
}

export interface GroupVo {
  groupName: string;
  groupId: number;
}
