import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import { GpsInfoResponse } from '../../../../../../api/gps';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class GpsService {
  constructor(private http: HttpClient) {}

  getGpsInfo(details: any): Observable<GpsInfoResponse> {
    return this.http.post<GpsInfoResponse>(
      environment.BASE_URL + Constants.GET_GPS_INFO,
      details
    );
  }

  updateGpsInfo(details: any): Observable<GpsInfoResponse> {
    return this.http.post<GpsInfoResponse>(
      environment.BASE_URL + Constants.UPDATE_GPS_INFO,
      details
    );
  }
}
