export interface ApiResponse {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string[];
  cumulativeTime: number;
  count: number;
  rpmTime: number;
  ccmTime: number;
  pcmTime: number;
  programs: Program[];
  minsDetails: string;
  phoneCommunicationVOList: PhoneCommunication[];
}

export interface Program {
  id: number;
  label: string;
  value: string;
}

export interface PhoneCommunication {
  phoneCommunicationId: number;
  note: string;
  callStartTime: string;
  duration: number;
  timeStamp: string;
  patientId: number;
  review: string | null;
}
