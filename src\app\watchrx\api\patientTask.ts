export interface PatientTask {
  id: string;
  title: string;
  description: string;
  start: string;
  end: string;
  className: string;
  patientId: number;
  userId: number;
  priority: string;
  patinetName: string;
  timezone: string;
}

export interface PatientList {
  responseCode?: number;
  responsecode?: number;
  status?: boolean;
  success?: boolean;
  messages?: [];
  patientMinimalVOList?: MinimalInfo[];
}

export interface MinimalInfo {
  patientId: number;
  patientName: string;
}
