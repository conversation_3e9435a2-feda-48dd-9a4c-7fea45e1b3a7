import {
    PatientsInfo
} from './dashboard';
export interface AlertResponse {
    success: boolean,
    responseCode: number,
    responsecode: number,
    messages: string[],
    patientWatchCaregiver: PatientsInfo[],
    patientAlert: [
        {
            dateTime: string,
            severity: string,
            description: string,
            patientInfo: string,
            patientId: number,
            acknowledgementStatus: string,
            alertId: number,
            patDetails: any[],
        }
    ],
    totalCount: number,
}

export interface AlertRequest {
  index: number,
  itemsPerPage:number,
  patientId:number[],
  alertSeverity:any,
  startDate:any,
  endDate:any
}