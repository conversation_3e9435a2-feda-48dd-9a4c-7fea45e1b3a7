import { Component, OnInit } from '@angular/core';
import { MessageService } from 'primeng/api';
import { DashboardService } from '../..../../dashboard/service/dashboard.service';
import { AssignDialog, Dialog } from '../../api/dialog';
import { MenuService } from '../../service/menu.service';
import { MultiSelectModule } from 'primeng/multiselect';
import { DialogService } from './service/dialog.service';
@Component({
  selector: 'app-reports',
  templateUrl: './dialog.component.html',
  styleUrl: './dialog.component.scss',
  providers: [MessageService],
})
export class DialogComponent implements OnInit {


  selectedPatient: any[] | undefined;
  patientList: any[] = [];
  filteredPatients: any[] = [];
  response: any[] = [];
  loadingDialogs: boolean = false;
  patientId: number[] = [];
  dialogId: number[] = [];
  PatientsListPopup: boolean=false;
  selectedDialog: any=null;

  constructor(
    private msgservice: MessageService,
    public menuService: MenuService,
    public dashboardService: DashboardService,
    public dialogService: DialogService
  ) { }
  ngOnInit(): void {
    this.menuService.changeMenu('Dialog');
    this.dashboardService.setPatientTask().subscribe((res) => {
      if (res.success) {
        this.patientList = res.patientMinimalVOList!;
      }
    });
    this.dialogService.dialogData().subscribe((res) => {
      this.response = res;
    });
  }

  assignDialog() {
     this.selectedPatient?.forEach(p => {
      this.patientId.push(p.patientId);
    });
    if(this.patientId.length==0 || this.dialogId.length==0){
      this.msgservice.add({ key: 'tst', severity: 'error', summary: 'Failed', detail: "Please select both patient and dialog!!" });

    }else{
    let assignDialogRequest: AssignDialog = {
      patientId: this.patientId,
      dialogId: this.dialogId
    }
    this.dialogService.assign(assignDialogRequest).subscribe((res) => {
    });
    this.patientId = [];
    this.dialogId = [];
    this.dialogService.dialogData().subscribe((res) => {
      this.response = res;
    });
    this.msgservice.add({ key: 'tst', severity: 'info', summary: 'Success', detail: "Dialog Assigned Successfully." });
  }
  }

  checkValue(dialog: Dialog) {
    if (this.dialogId.indexOf(dialog.watchDialogId) !== -1) {
      this.dialogId = this.dialogId.filter(function (element, i) {
        return element !== dialog.watchDialogId;
      });
    } else {
      this.dialogId.push(dialog.watchDialogId)
    }
  }

  openSidebar(selectedValue:any)
  {
    this.selectedDialog=selectedValue;
    this.PatientsListPopup=true;
  }
}
