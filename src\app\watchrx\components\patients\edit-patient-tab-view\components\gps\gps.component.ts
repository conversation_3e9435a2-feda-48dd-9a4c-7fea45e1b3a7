import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  City,
  Country,
  ICity,
  ICountry,
  IState,
  State,
} from 'country-state-city';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { GpsService } from './service/gps.service';

@Component({
  selector: 'app-gps',
  standalone: true,
  imports: [
    DropdownModule,
    CommonModule,
    FormsModule,
    ButtonModule,
    InputSwitchModule,
    AutoCompleteModule,
    InputTextModule,
    InputGroupModule,
    InputSwitchModule,
    DividerModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  templateUrl: './gps.component.html',
  styleUrl: './gps.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class GpsComponent {
  countries: ICountry[] = [];
  states: IState[] = [];
  cities: ICity[] = [];
  patientId: number = 0;

  form!: FormGroup;
  formData: FormData = {
    address1: '',
    address2: '',
    country: undefined,
    state: undefined,
    city: null,
    zipCode: '',
    gpsId: '0',
    radius: '',
    unit: '',
    gpsStatus: false,
    trackStatus: false,
    latitude: '',
    longitude: '',
    trackLatitude: '',
    trackLongitude: '',
  };
  allCountries: any[]=[];

  constructor(
    private gpsService: GpsService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getGpsInfo();
  }

  getGpsInfo() {
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
    };
    this.gpsService.getGpsInfo(req).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.formData = {
          address1: res?.address1,
          address2: res?.address2,
          zipCode: res?.zip,
          country: this.getCountryByName(res?.country),
          //state: this.getStateByName(res?.state, res?.country),
         // city: this.getCityByName(res?.state, res?.country, res?.city),
          state: res?.state,
          city:  res?.city,
          gpsId: res?.gpsId,
          radius: res?.radius,
          unit: res?.unit,
          gpsStatus: res?.gpsStatus,
          trackStatus: res?.trackStatus,
          latitude: res?.latitude,
          longitude: res?.longitude,
          trackLatitude: res?.trackLatitude,
          trackLongitude: res?.trackLongitude,
        };
      }
    });
  }

  getCountryByName(name: string): ICountry | undefined {
       //code added to keep US on top
       const all = Country.getAllCountries();
       const usa:any = all.find(c => c.isoCode === 'US');
       const rest = all.filter(c => c.isoCode !== 'US');
       this.allCountries = [usa, ...rest];
    return Country.getAllCountries().find((item) => item.name === name);
  }

  getStateByName(name: string, countryName: string): IState | undefined {
    if (!this.formData?.country?.isoCode) {
      let cnt = Country.getAllCountries().find(
        (item) => item.name === countryName
      );
      return State.getStatesOfCountry(cnt?.isoCode).find(
        (item) => item.name === name
      );
    } else {
      return State.getStatesOfCountry(this.formData?.country?.isoCode).find(
        (item) => item.name === name
      );
    }
  }

  getCityByName(name: string, countryName: string, cityName: string): ICity | undefined {
    if (!this.formData?.country?.isoCode) {
      let cnt: ICountry | undefined = Country.getAllCountries().find(
        (item) => item.name === countryName
      );
      let stat = State.getStatesOfCountry(cnt?.isoCode).find(
        (item) => item.name === name
      );
      return City.getCitiesOfState(cnt?.isoCode || '', stat?.isoCode || '').find(
        (item) => item.name === cityName
      );
    } else {
      let stat = State.getStatesOfCountry(this.formData?.country?.isoCode).find(
        (item) => item.name === name
      );
      return City.getCitiesOfState(this.formData?.country?.isoCode || '', stat?.isoCode || '').find(
        (item) => item.name === cityName
      );
      // return State.getStatesOfCountry(this.formData?.country?.isoCode).find(
      //   (item) => item.name === name
      // );
    }
  }


  onSubmit(form: any) {
    if (form.invalid) {
      form.control.markAllAsTouched();
      return;
    }
    let payload = {
      address1: this.formData?.address1,
      address2: this.formData?.address2,
      city: this.formData?.city,
      state: this.formData?.state,
      zip: this.formData?.zipCode,
      country: this.formData?.country?.name,
      gpsStatus: this.formData?.gpsStatus,
      trackStatus: this.formData?.trackStatus,
      radius: this.formData?.radius,
      latitude: this.formData?.city?.latitude,
      longitude: this.formData?.city?.longitude,
      unit: this.formData?.unit,
      patientId: this.patientId,
    };
    console.log('Request Body:', payload);
    this.loaderService.show();
    this.gpsService.updateGpsInfo(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'GPS info updated successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update GPS info',
        });
      }
    });
  }

  filterCountries(event: any) {
    const query = event.query.toLowerCase();
   //code added to keep US on top
   this.countries = this.allCountries.filter((p) =>
    p.name.toLowerCase().includes(query)
  );
  //end of code added to keep US on top
    // this.countries = Country.getAllCountries().filter((p) =>
    //   p.name.toLowerCase().includes(query)
    // );
    this.formData.state = undefined;
    this.cities = [];
    this.formData.city = null;
  }

  filterStates(event: any) {
    const query = event.query.toLowerCase();
    if (this.formData.country) {
      this.states = State.getStatesOfCountry(this.formData?.country?.isoCode);
      this.states = State.getStatesOfCountry(
        this.formData?.country?.isoCode
      ).filter((p) => p.name.toLowerCase().includes(query));
    }
  }
  filterCities(event: any) {
    const query = event.query.toLowerCase();
    if (this.formData?.country && this.formData?.state) {
      let countryCode = this.formData?.state;
      let stateCode = this.formData?.state;
      this.cities = City.getCitiesOfState(countryCode, stateCode);
      this.cities = City.getCitiesOfState(countryCode, stateCode).filter((p) =>
        p.name.toLowerCase().includes(query)
      );
    }
  }
  
}

interface FormData {
  gpsId: string | null;
  address1: string;
  address2: string;
  country: ICountry | undefined;
  state: string | undefined;
  city: any;
  zipCode: string;
  radius: string;
  unit: string;
  gpsStatus: boolean;
  trackStatus: boolean;
  latitude: string;
  longitude: string;
  trackLatitude: string;
  trackLongitude: string;
}
