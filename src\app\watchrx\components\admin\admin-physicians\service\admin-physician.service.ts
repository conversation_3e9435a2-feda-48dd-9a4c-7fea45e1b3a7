import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { CaseManagerListResp } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class AdminPhysicianService {
  constructor(private http: HttpClient) {}

  getPhysicianList(details: string): Observable<CaseManagerListResp> {
    return this.http.get<CaseManagerListResp>(
      environment.BASE_URL + Constants.ADMIN_PHYSICIAN_LIST + details
    );
  }

  deletePhysician(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_PHYSICIAN,
      details
    );
  }

  togglePhysicianStatus(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.CHANGE_STATUS_PHYSICIAN,
      details
    );
  }

  searchPhysician(details:any)
      {
          return this.http.get<any>(
            environment.BASE_URL + Constants.SEARCH_PHYSICIAN+details
          );
      }
}
