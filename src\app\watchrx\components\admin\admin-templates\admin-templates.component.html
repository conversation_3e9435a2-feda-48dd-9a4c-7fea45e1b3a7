<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="col-12">
    <div class="card">
      <div class="flex flex-row md:justify-content-start mb-2 gap-3">
        <p-dropdown [(ngModel)]="selectedChronic" name="chonicConditionName" [options]="ccNameList"
          placeholder="Select chornic condition" optionLabel="chonicConditionName" checkmark="true" [filter]="true"
          filterBy="label" [showClear]="true" class="fixed-width dropdown" (onChange)="onChangeTemplate()"></p-dropdown>
        <p-button label="Add Q and A" [raised]="false" severity="primary" class="mr-1" [outlined]="true"
          icon="pi pi-plus" (onClick)="openManulaAddPopup()" [disabled]="!this.selectedChronic" />

        <p-fileUpload #fileUpload mode="basic" chooseLabel="Upload Template" chooseIcon="pi pi-upload" accept=".xlsx"
          [maxFileSize]="1000000" name="templateFile" (onSelect)="onFileSelected($event)" [auto]="true"
          [disabled]="!this.selectedChronic"></p-fileUpload>

        <p-button label="Template Download" [raised]="false" severity="primary" class="mr-1" [outlined]="true"
          icon="pi pi-download" (onClick)="downloadFile()" />
      </div>
      <div class="field col-12">
        <p-table #dt [value]="templateDetails" [paginator]="true" [totalRecords]="templateDetails.length"
          [rows]="templateDetails.length" [lazy]="true" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>ID</th>
              <th>Question</th>
              <th>Answers</th>
              <th>Programs</th>
              <th>Chronic Conditions</th>
              <th>Category</th>
              <th>Create at</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
          </ng-template>

          <ng-template let-qa pTemplate="body">
            <tr>
              <td>{{ qa.questionId }}</td>
              <td>{{ qa.questions }}</td>
              <td>{{ qa.answer }}</td>
              <td>{{qa.programName}}</td>
              <td>{{qa.chronicConditionName}}</td>
              <td>{{qa.category}}</td>
              <td>{{ getDate(qa.createdDate) }}</td>
              <td>
                <p-inputSwitch [(ngModel)]="qa.status" (onChange)="toggleStatus(qa.questionId)" />
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
                    style="width: 30px; height: 30px" [disabled]="!qa.status" (click)="onEditQuestion(qa)"></button>
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="width: 30px; height: 30px" [disabled]="!qa.status"
                    (click)="onDeleteQuestion($event, qa.questionId)"></button>
                </div>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="emptymessage">
            <tr>
              <td colspan="6" style="text-align: center">
                <h4>No records found</h4>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<p-dialog header="Header" [(visible)]="manual" [modal]="true" [style]="{ width: '60rem' }">
  <ng-template pTemplate="header">
    <div class="inline-flex align-items-center justify-content-center gap-2">
      <!-- <span class="font-bold white-space-nowrap"
        >{{ selectedChronic?.chonicConditionName }}
      </span> -->
      <span class="p-text-secondary block mb-0 font-bold">
        {{ isEdit ? "Edit" : "Add" }} question and answers</span>
    </div>
  </ng-template>

  <div class="p-fluid p-formgrid grid questions">
    <div class="flex flex-row flex-wrap col-12">
      <div class="field col-6 md:col-6">
        <label htmlfor="state">Question <span class="p-text-danger"> *</span></label>
        <input pinputtext="" id="question" type="text" placeholder="Any Question"
          class="p-inputtext p-component p-element" [(ngModel)]="question" />
      </div>
      <div class="field col-6 md:col-6">
        <label htmlfor="state">Answers[Comma seperated] <span class="p-text-danger"> *</span></label>
        <input pinputtext="" id="answer" type="text" placeholder="Answers with comma seperated"
          class="p-inputtext p-component p-element" [(ngModel)]="answers" />
      </div>
      <div class="field col-6 md:col-6" *ngIf="!isEdit">
        <label htmlfor="state">Category <span class="p-text-danger"> *</span></label>
        <p-dropdown [(ngModel)]="selectedCategory" name="selectedCategory" [options]="categoryList"
          placeholder="Select category" optionLabel="label" checkmark="true" [filter]="false" filterBy="label"  [appendTo]="'body'"
          [showClear]="true" class="fixed-width"></p-dropdown>
      </div>
      <div class="field col-6 md:col-6" *ngIf="!isEdit">
        <label htmlfor="state">Programs <span class="p-text-danger"> *</span></label>
        <p-dropdown [(ngModel)]="selectedChronicProgram" name="chonicConditionName" [options]="ccNameList"
          placeholder="Select program" optionLabel="chonicConditionName" checkmark="true" [filter]="false"  [appendTo]="'body'"
          filterBy="label" [showClear]="true" class="fixed-width"></p-dropdown>
      </div>
      <div class="field col-6 md:col-6" *ngIf="!isEdit">
        <label htmlfor="state">Chornic Conditions <span class="p-text-danger"> *</span></label>
        <p-dropdown [(ngModel)]="selectedChronicCondition" name="chonicConditionName1" [options]="chronicList"  [appendTo]="'body'"
          placeholder="Select chornic condition" optionLabel="chonicConditionName" checkmark="true" [filter]="false"
          filterBy="label" [showClear]="true" class="fixed-width"></p-dropdown>
      </div>

    </div>
  </div>
  <ng-template pTemplate="footer">
    <p-button label="Cancel" [outlined]="true" severity="secondary" (onClick)="manual = false" />
    <p-button label="Save" [outlined]="false" severity="primary"
      (onClick)="isEdit ? saveEditedQuestion() : adManualData()" [disabled]="!question || !answers "
      [loading]="loader" />
  </ng-template>
</p-dialog>