import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map, Observable, throwError } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { AssignUsersandRoles, Users } from '../../../../api/adminAccessControl';
import { StatisticsReport } from '../../../../api/adminBillingReports';
import { Roles } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { APIConfig } from './APIConfig';
import { Reports } from '../../../../api/adminReports';
import { saveAs } from 'file-saver';
@Injectable({
  providedIn: 'root',
})
export class AdminBillingReportService {
  constructor(private http: HttpClient) {}
  getBillingReportList(id: any): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.ADMIN_BILLING_REPORT_LIST + id
    );
  }

  getAllOrgsList() {
    return this.http.get<any>(environment.BASE_URL + APIConfig.ALL_ORGS_LIST);
  }
  generateBilling(id: any, date: any):Observable<any> {
    const encodedId = (id);
    const encodedDate = (date);
    const url = `${environment.BASE_URL}${APIConfig.GENERATE_BILLING}${encodedId}/${encodedDate}`;
    return this.http.get(url, { responseType: 'text' as 'json' });
  }

  getRoles(): Observable<Roles> {
    return this.http.get<Roles>(environment.BASE_URL + APIConfig.GET_ROLES);
  }
  getAllOrgsClinicList(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.ALL_ORGS_CLINIC_LIST
    );
  }
  getBilligStatisticsList(url: any) {
    return this.http.get<StatisticsReport>(
      environment.BASE_URL + APIConfig.ADMIN_BILLING_STATISTICS + url
    );
  }
  getBilligStatisticsCount(url: any) {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.ADMIN_BILLING_STATISTICS_COUNT + url
    );
  }

  //assign users to organization

  getUsersList() {
    return this.http.get<Users>(
      environment.BASE_URL + APIConfig.ALL_USERS_LIST
    );
  }
  getAssigndUsersandRolesList(): Observable<AssignUsersandRoles> {
    return this.http.get<AssignUsersandRoles>(
      environment.BASE_URL + APIConfig.ASSIGN_USERS_ROLES
    );
  }

  assignUsersToGroups(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.ASSIGN_USER_TO_GROUPs,
      details
    );
  }

  unassignUsersToGroups(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.UNASSIGN_USER_TO_GROUPs,
      details
    );
  }

  encounterReport(orgId:number, usrId:number, duration:number) {
    return this.http.get<Reports>(
      environment.BASE_URL + APIConfig.ENCOUNTER_REPORT+ '/'+ orgId + '/' + usrId+ '/' + duration
    );
  }

  vitalReport(orgId:number, usrId:number, duration:number) {
    return this.http.get<Reports>(
      environment.BASE_URL + APIConfig.VITAL_REPORT+ '/'+ orgId + '/' + usrId+ '/' + duration
    );
  }

  cptPatientOrgReports(orgId:number, duration:number) {
    return this.http.get<Reports>(
      environment.BASE_URL + APIConfig.CPT_BY_PATIENT_ORG_REPORT+ '/'+ orgId + '/' + duration
    );
  }

  cptPatientOrgAndClinician(orgId:number, usrId:number, duration:number) {
    return this.http.get<Reports>(
      environment.BASE_URL + APIConfig.CPT_BY_PATIENT_ORG_CLINICIAN_REPORT+ '/'+ orgId + '/' + usrId+ '/' + duration
    );
  }

  cptTotalPatientOrgAndClinician(orgId:number, usrId:number, duration:number) {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.TOTAL_CPT_BY_PATIENT_ORG_CLINICIAN_REPORT+ '/'+ orgId + '/' + usrId+ '/' + duration
    );
  }

  vital16days(orgId:number, usrId:number, duration:number) {
    return this.http.get<any>(
      environment.BASE_URL + APIConfig.VITAL_16_REPORT+ '/'+ orgId + '/' + usrId+ '/' + duration
    );
  }
   downloadStatistics(
      startDate: string,
      endDate: string,
      cmId:string,
      orgId:string,
      fileName: string
    ): Observable<any> {
      const url =
        environment.BASE_URL +
        APIConfig.DOWNLOAD_STATISTICS_REPORT +
        `/${orgId}/${startDate}/${endDate}/${cmId}`;
      const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
  
      return this.http.get(url, { headers, responseType: 'arraybuffer' }).pipe(
        map((response: BlobPart) => {
          const blob = new Blob([response], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          });
          saveAs(blob, fileName);
          return { success: true };
        }),
        catchError((error) => {
          return throwError({ success: false });
        })
      );
    }
}
