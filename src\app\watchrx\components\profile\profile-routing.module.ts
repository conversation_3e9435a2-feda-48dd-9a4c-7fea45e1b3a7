import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProfileComponent } from './components/profile/profile.component';
import { PasswordResetComponent } from './components/password-reset/password-reset.component';

const routes: Routes = [];

@NgModule({
 imports: [
      RouterModule.forChild([
        { path: '', component: ProfileComponent },
        { path: 'resetPassword', component: PasswordResetComponent },
      ]),
    ],
  exports: [RouterModule]
})
export class ProfileRoutingModule { }
