import {
  Component,
  EventEmitter,
  Input,
  Output,
  ViewChild,
} from '@angular/core';
import { Router } from '@angular/router';
import { LazyLoadEvent } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { debounceTime, Subject } from 'rxjs';
import { PatientInfo, Program, voice } from '../../../api/allPatients';
import { MenuService } from '../../../service/menu.service';
import { AllPatientsService } from '../../patients/patients-view/service/all-patients.service';

//import { Device } from '../../../../twilio/twilio.min.js';

import { KJUR } from 'jsrsasign';

import { Device } from '@twilio/voice-sdk';
import uitoolkit from '@zoom/videosdk-ui-toolkit';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { CommonModule } from '@angular/common';
import { TabViewModule } from 'primeng/tabview';

@Component({
  selector: 'app-video-calls',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    DropdownModule,
    TableModule,
    CommonModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    AvatarModule,
    RadioButtonModule,
    InputNumberModule,
    ConfirmDialogModule,
    TabViewModule,
  ],
  templateUrl: './video-calls.component.html',
  styleUrl: './video-calls.component.scss'
})
export class VideoCallsComponent {
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: string = '';
  patientName: string = '';
  selectedProgram: any = '';
  sessionContainer: any;
  authEndpoint = '';
  inSession: boolean = false;
  config = {
    videoSDKJWT: '',
    sessionName: '',
    userName: '',
    sessionPasscode: '',
    features: [
      // 'preview',
      'video',
      'audio',
      'settings',
      'users',
      'chat',
      //'share',
    ],
    options: { init: {}, audio: {}, video: {}, share: {} },
    virtualBackground: {
      allowVirtualBackground: true,
      allowVirtualBackgroundUpload: true,
      virtualBackgrounds: [
        'https://images.unsplash.com/photo-1715490187538-30a365fa05bd?q=80&w=1945&auto=format&fit=crop',
      ],
    },
  };
  role = 1;
  constructor(
    public menuService: MenuService,
    private router: Router,
    private allPatientService: AllPatientsService
  ) {
    // this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
    //   this.debouncedValue = value;
    //   console.log('Search Value:', this.debouncedValue);
    //   this.onSearch(this.debouncedValue);
    // });
  }

  @ViewChild('dt')
  table!: Table;

  // itemsPerPage: number = 10;
  // patientList: PatientInfo[] = [];
  // totalActivePatients: number = 0;
  // loader: boolean = false;
  // activeTabIndex = 0;
  // inActivePatientList: PatientInfo[] = [];
  // inActiveTotalPatients: number = 0;
  // inActiveLoader: boolean = false;
  voiceData!: any;
  activeIndex: number = 0;
  programs: Program[] = [];
  @Input() openVideoDlg: boolean = false;
  @Input() selectedPatientInfo: any;
  @Output() closeModel = new EventEmitter<any>();
  ngOnInit() {
    this.menuService.changeMenu('Patients');
    this.makeCall(
      this.selectedPatientInfo['patientId'],
      this.selectedPatientInfo.patientName
    );
  }


  onInput(event: any): void {
    this.inputSubject.next(event.target.value);
  }


  makeCall(patientId: string, patientName: string) {
    this.patientName = patientName;
    this.getAudioDevices();
    this.openVideoDlg = true;
    this.voiceToken(Number(patientId));

    // this.config.videoSDKJWT = this.generateSignature(
    //   'dCQGBVj35xjP3KZL8lDPAN7MooAQ91Jc1Z26',
    //   '8Lk5gzjCwfm6dU3QCOTB7JdUiQE2PbqOtiLp',
    //   this.patientName,
    //   1,
    //   patientId,
    //   this.patientName
    // );
    // this.config.sessionPasscode = patientId;
    // this.config.userName = this.patientName;
    // this.config.sessionName = this.patientName;
  }

  onTextSubmit() { }

  closeModalPopup() { }

  disconnect() { }

  async videoToken(patientId: any) {
    let obj = { patientId: patientId, program: this.selectedProgram };
    this.allPatientService.videoToken(obj).subscribe((token) => {
      this.config.videoSDKJWT = token.zoomToken;
      this.config.sessionPasscode = token.sessionPasscode;
      this.config.userName = token.userName;
      this.config.sessionName = token.sessionName;
      this.sessionContainer = document.getElementById(
        'sessionContainer'
      ) as HTMLTextAreaElement;
      this.joinSession();
    });
  }
  private voiceToken(id: number) {
    this.allPatientService.getPatientPrograms(id).subscribe((token) => {
      this.voiceData = token;
      // (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
      //   true;
      // const callStatus = document.getElementById(
      //   'callStatus'
      // ) as HTMLTextAreaElement;
      // const hangUpButton = document.getElementById(
      //   'button-hangup-outgoing'
      // ) as HTMLTextAreaElement;
      // (document.getElementById('phonenumber') as HTMLTextAreaElement).value =
      //   this.voiceData.phoneNumber;
      // (document.getElementById('button-call') as HTMLTextAreaElement).disabled =
      //   true;
      // (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
      //   true;
      // (
      //   document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
      // ).disabled = true;
      // (
      //   document.getElementById('cancel-voice-call') as HTMLTextAreaElement
      // ).disabled = false;
      //createRadioButton(response.data.programs, 'voice');
      this.programs = this.voiceData.program;

//       callStatus.innerHTML =
//         '<h5>Connecting to: ' + this.voiceData.phoneNumber + '</h5>';
//       this.resetVolumerController();

//       let device = new Device(this.voiceData.token, {
//         logLevel: 1,
//         // codecPreferences: ["opus", "pcmu"]
//       });
//       device.register();
//       device.on('ready', function (device: any) {
//         console.log('Twilio.Device is now ready for connections');
//       });

//       device.on('registered', function () {
//         callStatus.innerHTML =
//           '<h5>Now your device ready to make a call.Select a program to call </h5>';
//         (
//           document.getElementById('button-call') as HTMLTextAreaElement
//         ).disabled = false;
//       });

//       device.on('error', function (error: any) {
//         console.log('Twilio.Device Error: ' + error.message);
//         device.disconnectAll();
//         device.destroy();
//         //getAudioDevices();
//         // $('#voiceCalls').modal('toggle');
//         //  FlashService.Error("Server communication Error, Please try again...", true);
//       });

//       (document.getElementById('button-call') as HTMLTextAreaElement).onclick =
//         () => {
//           /* var selectedRadioButton = (document.querySelector('input[name="program"]:checked')as HTMLTextAreaElement);
          
//           var selectedReview = null;
//           if (selectedRadioButton) {
// selectedReview = selectedRadioButton.value; 
// } else {
// FlashService.Error("Please select a program before making a call.", true);
// return;     
// } */

//           let ph = (
//             document.getElementById('phonenumber') as HTMLTextAreaElement
//           ).value;
//           if (ph) {
//             (
//               document.getElementById('button-call') as HTMLTextAreaElement
//             ).disabled = true;
//             var params = {};
//             if (localStorage.getItem('patientId') == null) {
//               params = {
//                 To: (
//                   document.getElementById('phonenumber') as HTMLTextAreaElement
//                 ).value,
//                 patientId: 574,
//                 review: 'RPM',
//               };
//             } else {
//               params = {
//                 To: (
//                   document.getElementById('phonenumber') as HTMLTextAreaElement
//                 ).value,
//                 patientId: localStorage.getItem('patientId'),
//                 review: 'RPM',
//               };
//             }
//             console.log(params);
//             this.makeCallToNumber(device, params);
//           }
//         };

//       (
//         document.getElementById('cancel-voice-call') as HTMLTextAreaElement
//       ).onclick = () => {
//         var selectedRadioButton = document.querySelector(
//           'input[name="program"]:checked'
//         ) as HTMLTextAreaElement;
//         if (selectedRadioButton) {
//           var selectedReview = selectedRadioButton.value;
//           if (selectedReview === undefined || selectedReview === null) {
//             selectedReview = 'rpm';
//           }
//         }
//         this.openVideoDlg = false;
//         device.disconnectAll();
//         this.closeModel.emit();
//       };
    }); //
  }

  private async getAudioDevices() {
    await navigator.mediaDevices.getUserMedia({
      audio: true,
    });
  }
//   private resetVolumerController() {
//     (
//       document.getElementById('input-volume') as HTMLTextAreaElement
//     ).style.width = '';
//     (
//       document.getElementById('input-volume') as HTMLTextAreaElement
//     ).style.background = '';
//     (
//       document.getElementById('output-volume') as HTMLTextAreaElement
//     ).style.width = '';
//     (
//       document.getElementById('output-volume') as HTMLTextAreaElement
//     ).style.background = '';
//   }

//   private async makeCallToNumber(device: any, params: any) {
//     const callStatus = document.getElementById(
//       'callStatus'
//     ) as HTMLTextAreaElement;

//     const call = await device.connect({
//       params,
//     });
//     (document.getElementById('muteUnmute') as HTMLTextAreaElement).disabled =
//       false;
//     call.on('accept', function () {
//       callStatus.innerHTML = '<h5>Connected to :' + params.To + '</h5>';
//       // document.getElementById("input-volume-controller").classList.remove("hide");
//       // document.getElementById("output-volume-controller").classList.remove("hide");
//       (
//         document.getElementById('animation') as HTMLTextAreaElement
//       ).classList.remove('hide');
//       // bindVolumeIndicators(call);
//       (
//         document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
//       ).disabled = false;
//       (
//         document.getElementById('cancel-voice-call') as HTMLTextAreaElement
//       ).disabled = true;
//     });
//     call.on('disconnect', () => {
//       call.disconnect();
//       device.disconnectAll();
//       callStatus.innerHTML = '<h5>Call Disconnected.</h5>';
//       setTimeout(() => {
//         this.closeModel.emit();  // ✅ this now refers to the correct context
//       }, 2000);
//       //$('#voiceCalls').modal('toggle');
//     });
//     call.on('cancel', () => {
//       call.disconnect();
//       device.disconnectAll();
//       callStatus.innerHTML = '<h5>Call Disconnected.</h5>';
//       setTimeout(() => {
//         this.closeModel.emit();  // ✅ this now refers to the correct context
//       }, 2000);
//       //$('#voiceCalls').modal('toggle');
//     });

//     (
//       document.getElementById('button-hangup-outgoing') as HTMLTextAreaElement
//     ).onclick = () => {
//       //$('#voiceCalls').modal('toggle');
//       if (call) {
//         call.disconnect();
//       }
//       //code to close the connection and close modal
//       setTimeout(() => {
//         var selectedRadioButton = document.querySelector(
//           'input[name="program"]:checked'
//         ) as HTMLTextAreaElement;
//         if (selectedRadioButton) {
//           var selectedReview = selectedRadioButton.value;
//           if (selectedReview === undefined || selectedReview === null) {
//             selectedReview = 'rpm';
//           }
//         }
//         this.openVideoDlg = false;
//         device.disconnectAll();
//         this.closeModel.emit();
//       }, 2000);
//       //code end
//     };
//     call.on('reject', () => {
//       call.disconnect();
//       device.disconnectAll();
//       callStatus.innerHTML = '<h5>Call Disconnected.</h5>>';
//       setTimeout(() => {
//         this.closeModel.emit();  // ✅ this now refers to the correct context
//       }, 2000);
//       // $('#voiceCalls').modal('toggle');
//     });

//     let muteUnmute = document.getElementById(
//       'muteUnmute'
//     ) as HTMLTextAreaElement;
//     muteUnmute.onclick = () => {
//       if (call.isMuted()) {
//         call.mute(false);
//         this.iconClass = 'fa fa-microphone';
//         //$('#muteUnmute').find('i').toggleClass('fa fa-microphone-slash fa-2x').toggleClass('fa fa-microphone fa-2x');
//         (
//           document.getElementById('palypauseanim') as HTMLTextAreaElement
//         ).classList.remove('call-animation-pause');
//         (
//           document.getElementById('palypauseanim') as HTMLTextAreaElement
//         ).classList.add('call-animation-play');
//       } else {
//         call.mute(true);
//         this.iconClass = 'fa fa-microphone-slash';
//         //$('#muteUnmute').find('i').toggleClass('fa fa-microphone fa-2x').toggleClass('fa fa-microphone-slash fa-2x');
//         (
//           document.getElementById('palypauseanim') as HTMLTextAreaElement
//         ).classList.remove('call-animation-play');
//         (
//           document.getElementById('palypauseanim') as HTMLTextAreaElement
//         ).classList.add('call-animation-pause');
//       }
//     };
//   }
  iconClass = 'pi pi-microphone'; // default icon
  generateSignature(
    sdkKey: string,
    sdkSecret: string,
    sessionName: string,
    role: number,
    sessionKey: string,
    userIdentity: string
  ) {
    const iat = Math.round(new Date().getTime() / 1000) - 30;
    const exp = iat + 60 * 60 * 2;
    const oHeader = { alg: 'HS256', typ: 'JWT' };

    const oPayload = {
      app_key: sdkKey,
      tpc: sessionName,
      role_type: role,
      session_key: sessionKey,
      user_identity: userIdentity,
      version: 1,
      iat: iat,
      exp: exp,
    };

    const sHeader = JSON.stringify(oHeader);
    const sPayload = JSON.stringify(oPayload);
    const sdkJWT = KJUR.jws.JWS.sign('HS256', sHeader, sPayload, sdkSecret);
    return sdkJWT;
  }

  async getVideoSDKJWT() {
    await this.videoToken(this.selectedPatientInfo['patientId'].toString());
    // console.log('getVideoSDKJWT');
    // this.sessionContainer = document.getElementById(
    //   'sessionContainer'
    // ) as HTMLTextAreaElement;

    // console.log('getVideoSDKJWT', this.config.videoSDKJWT);
    // this.joinSession();
  }

  joinSession() {
    this.inSession = true;
    uitoolkit.joinSession(this.sessionContainer, this.config);

    uitoolkit.onSessionClosed(this.sessionClosed);
  }

  sessionClosed = () => {
    console.log('session closed');
    uitoolkit.closeSession(this.sessionContainer);
    this.inSession = false;
    this.closeModel.emit();
  };

  handleClose() {
    this.openVideoDlg = false;
    this.sessionClosed();
    this.inSession = false;
    this.closeModel.emit();
  }
}
