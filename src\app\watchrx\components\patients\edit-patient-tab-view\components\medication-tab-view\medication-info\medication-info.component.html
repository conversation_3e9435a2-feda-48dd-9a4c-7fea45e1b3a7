<div class="flex md:justify-content-end mb-5 addbutton">
<p-fileUpload mode="basic" chooseLabel="Upload Prescriptions" chooseIcon="pi pi-upload" #fileUpload
      [maxFileSize]="1000000000" accept="image/*" (onSelect)="uploadPrescriptions($event)" name="medImage" class="upload mr-2 fileupload"></p-fileUpload>
  <p-button
    label="Add"
    severity="secondary"
    [outlined]="true"
    (onClick)="showDialog()"
    [ngStyle]="{'margin-right':'8px'}"
    icon="pi pi-plus"
  ></p-button>
  <p-button
    label="Review"
    severity="secondary"
    [outlined]="true"
    (onClick)="reviewMedications()"
    [disabled]="!isAnyNotReviewed"
    icon="pi pi-check"
  ></p-button>
</div>
<p-toast />
<p-confirmDialog />

<p-table
  [value]="medicationList"
  styleClass="p-datatable-gridlines p-datatable-striped custom-table mg-bt"
>
  <ng-template pTemplate="header">
    <tr>
      <th>S.No</th>
      <th style="width: 10%">Image</th>
      <th style="width: 20%">Medicine</th>
      <th style="width: 20%">Dosage</th>
      <th>Days to take Medicine</th>
      <th>Medication Time</th>
      <th>Reminder/Action</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-med let-i="rowIndex">
    <tr class="medicationlist">
      <td>{{ i + 1 }}</td>
      <td>
        <div *ngIf="med.imageUrl">
          <img
            [src]="med.imageUrl"
            alt="{{ med.medicineName }}"
            width="100%"
            height="50"
          />
        </div>
        <div *ngIf="!med.imageUrl">
          <img
            [src]="'assets/watchrx/images/no_image.jpg'"
            alt="{{ med.medicineName }}"
            width="100%"
            height="50"
          />
        </div>
      </td>
      <td>
        <div class="">
          {{ med.medicineName | titlecase }} 
        </div>
        <div class="graytext" *ngIf="med.color">{{ med.color | titlecase }} Color</div>
      </td>
      <td>{{ med.dosage | titlecase }}</td>
      <td>
        <div class="">{{ getDays(med.daysOfWeek) }}</div>
       <!-- <div *ngIf="getTimes(med)!==0">{{ getTimes(med) }} times a day</div>-->
      </td>
      <td> {{formatTimeSlot(med.timeSlots)}}</td>
      <td>
        <div class="flex">
        <p-checkbox
                  name="reminder"
                  [binary]="true"
                  [(ngModel)]="med.isReminderReq"
                  class="p-button-secondary mr-2 p-button-outlined"
                  (onChange)="onCheckboxChange($event, med, i)"
        ></p-checkbox>
          <button
            pButton
            pRipple
            icon="pi pi-pencil"
            class="p-button-secondary mr-2 p-button-outlined"
            style="width: 30px; height: 30px"
            (click)="editMedicationView(med)"
          ></button>
          <button
            pButton
            pRipple
            icon="pi pi-trash"
            class="p-button-secondary p-button-outlined"
            (click)="deleteMeidcation($event, med.prescriptionId)"
            severity="danger"
            style="width: 30px; height: 30px"
          ></button>
        </div>
      </td>
    </tr>
  </ng-template>
</p-table>

<p-sidebar [(visible)]="visible" position="right" styleClass="w-55rem" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-plus mr-2" style="font-size: 1rem " *ngIf="!isEditMeds"></i>
        <i class="pi pi-pencil mr-2" style="font-size: 1rem " *ngIf="isEditMeds"></i>
       {{SideBarHeader}}
      </span>
    </div>
  </ng-template>

  <div class="p-fluid p-formgrid grid mt-2">
    <div class="field col-12 md:col-4">
      <label htmlfor="medicationName">Medication Name <span class="p-text-danger">*</span></label
      ><input
        pinputtext=""
        id="medicationName"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="medicationName"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="dosage">Dosage</label
      ><input
        pinputtext=""
        id="dosage"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="dosage"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="color">Color</label
      ><input
        pinputtext=""
        id="color"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="color"
      />
    </div>
    <div class="field col-12 md:col-4" *ngIf="relativeInterval === 'Regular'">
      <label htmlfor="quantity">Quantity <span class="p-text-danger">*</span></label
      ><input
        pinputtext=""
        id="quantity"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="quantity"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="description">Description</label
      ><input
        pinputtext=""
        id="description"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="description"
      />
    </div>
    <div class="field col-12">
      <label htmlfor="state" class="font-bold block mb-2 mt-2">Type <span class="p-text-danger">*</span></label>
      <div class="flex justify-content-left gap-3">
        <div class="flex flex-wrap gap-3">
          <div
            class="flex align-items-center"
            *ngFor="let option of medicationTypeList"
          >
            <p-radioButton
              name="medstype"
              [value]="option.id"
              [(ngModel)]="selectedMedicationType"
              [inputId]="option.name.toLowerCase()"
              variant="filled"
            />
            <label [for]="option.name.toLowerCase()" class="ml-2">{{
              option.name
            }}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="field col-12 md:col-12">
      <label htmlfor="state" class="font-bold block mb-2">Select Days <span class="p-text-danger">*</span></label>
      <div class="p-field-checkbox font-bold block mb-2"></div>
      <div class="flex justify-content-left gap-3">
        <p-checkbox
          name="allDays"
          [binary]="true"
          [(ngModel)]="allDaysSelected"
          (onChange)="toggleAllDays()"
          label="All Days"
        ></p-checkbox>
        <div *ngFor="let day of daysOfWeek">
          <p-checkbox
            name="days"
            [value]="day.value"
            [(ngModel)]="selectedDays"
            (onChange)="updateAllDaysSelected()"
            label="{{ day.name }}"
          ></p-checkbox>
        </div>
      </div>
    </div>
    <div class="field col-12 md:col-12">
      <label htmlfor="state" class="font-bold block mb-2"
        >Choose Interval Type</label
      >
      <div class="flex flex-wrap gap-3">
        <div class="flex align-items-center">
          <p-radioButton
            name="regularType"
            value="Regular"
            [(ngModel)]="relativeInterval"
            inputId="regular"
          />
          <label for="regular" class="ml-2"> Custom </label>
        </div>

        <div class="flex align-items-center">
          <p-radioButton
            name="regularType"
            value="Fixed"
            [(ngModel)]="relativeInterval"
            inputId="fixed"
          />
          <label for="fixed" class="ml-2"> Fixed </label>
        </div>
      </div>
    </div>
   <div class="filed col-12 md:col-12" *ngIf="relativeInterval === 'Regular'">
      <label htmlfor="state" class="font-bold block mb-2">Select Time <span class="p-text-danger">*</span></label>
      <div class="flex flex-wrap gap-3">
        <p-checkbox
          [binary]="true"
          inputId="earlyMorningCheckbox"
          label="Morning"
          [(ngModel)]="isEarlyMorningChecked"
        ></p-checkbox>
        <p-checkbox
          [binary]="true"
          inputId="breakfastCheckbox"
          label="Breakfast"
          [(ngModel)]="isBreakfastChecked"
        ></p-checkbox>
        <p-checkbox
          [binary]="true"
          inputId="lunchCheckbox"
          label="Lunch"
          [(ngModel)]="isLunchChecked"
        ></p-checkbox>
        <p-checkbox
          [binary]="true"
          inputId="afternoonSnackCheckbox"
          label="Afternoon Snack"
          [(ngModel)]="isAfsChecked"
        ></p-checkbox>
        <p-checkbox
          [binary]="true"
          inputId="dinnerCheckbox"
          label="Dinner"
          [(ngModel)]="isDinnerChecked"
        ></p-checkbox>
        <p-checkbox
          [binary]="true"
          inputId="bedtimeCheckbox"
          label="Bedtime"
          [(ngModel)]="isBedChecked"
        ></p-checkbox>
      </div>
     <!-- <p-table
        class="custom-header"
        responsiveLayout="scroll"
        styleClass="p-datatable-gridlines mt-2 p-datatable-striped"
      >
        <ng-template pTemplate="header">
          <tr style="border: 10px">
            <th>Quantity</th>
            <th>Time</th>
          </tr>
          <tr *ngIf="isEarlyMorningChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="emQnty"
                  name="emQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="emQnty"
                />
              </div>
            </td>
            <td>
              <div class="align-items-center">
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="emTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
          <tr *ngIf="isBreakfastChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="bfQnty"
                  name="bfQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="bfQnty"
                />
              </div>
            </td>
            <td>
              <div class="align-items-center">
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="bfTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
          <tr *ngIf="isLunchChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="lunchQnty"
                  name="lunchQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="lunchQnty"
                />
              </div>
            </td>
            <td>
              <div>
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="lunchTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
          <tr *ngIf="isAfsChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="afsQnty"
                  name="afsQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="afsQnty"
                />
              </div>
            </td>
            <td>
              <div>
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="afsTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
          <tr *ngIf="isDinnerChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="afsQnty"
                  name="afsQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="dinnerQnty"
                />
              </div>
            </td>
            <td>
              <div>
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="dinnerTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
          <tr *ngIf="isBedChecked">
            <td>
              <div>
                <input
                  type="text"
                  id="afsQnty"
                  name="afsQnty"
                  class="p-inputtext p-component p-element"
                  [(ngModel)]="bedQnty"
                />
              </div>
            </td>
            <td>
              <div>
                <p-dropdown
                  [options]="slotsType"
                  [(ngModel)]="bedTimeSlot"
                  optionLabel="name"
                  placeholder="Timeslot"
                  appendTo="body"
                ></p-dropdown>
              </div>
            </td>
          </tr>
        
        </ng-template>
      </p-table>-->
    </div>
    <div class="filed col-12 md:col-12" *ngIf="relativeInterval === 'Fixed'">
      <div class="flex flex-column md:flex-row md:justify-content-between">
        <p-button
          label="Add Time"
          severity="secondary"
          (click)="addTime()"
          [outlined]="true"
          icon="pi pi-plus"
        ></p-button>
      </div>
      <p-table
        class="custom-header col-6"
        responsiveLayout="scroll"
        [value]="fixedMedications"
        styleClass="p-datatable-gridlines p-datatable-striped"
      >
        <ng-template pTemplate="header">
          <tr>
            <th>Select Time</th>
            <th>Quantity</th>
            <th>Action</th>
          </tr>
        </ng-template>

        <ng-template pTemplate="body" let-fixedMeds>
          <tr>
            <td>
              <p-calendar
                [(ngModel)]="fixedMeds.time"
                [timeOnly]="true"
                hourFormat="12"
                [showIcon]="true"
                [iconDisplay]="'input'"
                [showButtonBar]="true"
                appendTo="body"
              ></p-calendar>
            </td>
            <td>
              <input
                type="text"
                id="afsQnty"
                name="afsQnty"
                class="p-inputtext p-component p-element"
                [(ngModel)]="fixedMeds.quantity"
              />
            </td>
            <td>
              <p-button
                icon="pi pi-trash"
                [outlined]="true"
                [text]="true"
                severity="secondary"
                (click)="removeTime(fixedMeds)"
              />
            </td>
          </tr>
        </ng-template>
      </p-table>
    </div>
    <div class="field col-12 mt-2 flex">
      <p-fileUpload mode="basic" chooseLabel="Choose Image" chooseIcon="pi pi-upload" accept="image/*" #fileUploader 
        [maxFileSize]="1000000" (onSelect)="onFileSelect($event)" name="medImage"></p-fileUpload>
        <div *ngIf="imagePreview" class="ml-3">
          <img [src]="imagePreview" alt="Image Preview" width="200" height="100" />
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button
        label="Cancel"
        [outlined]="true"
        severity="secondary"
        (click)="visible = false"
      />
      <p-button label="Save" severity="primary" (click)="addNewMedication()" />
    </div>
  </ng-template>
</p-sidebar>
