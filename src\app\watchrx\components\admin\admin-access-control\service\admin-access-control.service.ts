import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import {
  AssignUsersandRoles,
  Features,
  Users,
} from '../../../../api/adminAccessControl';
import { GroupsAndRoles, Roles } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { APIConfig } from './APIConfig';
@Injectable({
  providedIn: 'root',
})
export class AdminAccessControlService {
  constructor(private http: HttpClient) {}
  getFeaturesList(): Observable<Features> {
    return this.http.get<Features>(
      environment.BASE_URL + APIConfig.ADMIN_FEATURES_LIST
    );
  }
  deleteFeature(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.DELETE_FEATURE,
      details
    );
  }
  saveFeature(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.ADD_FEATURE,
      details
    );
  }

  //organizations functions

  getRoles(): Observable<Roles> {
    return this.http.get<Roles>(environment.BASE_URL + APIConfig.GET_ROLES);
  }
  getGroupsandRolesList(): Observable<GroupsAndRoles> {
    return this.http.get<GroupsAndRoles>(
      environment.BASE_URL + APIConfig.GROUPS_AND_ROLES
    );
  }
  deleteGroup(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.DELETE_GROUPS_AND_ROLES,
      details
    );
  }
  saveGroup(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.SAVE_GROUPS_AND_ROLES,
      details
    );
  }

  //assign users to organization

  getUsersList() {
    return this.http.get<Users>(
      environment.BASE_URL + APIConfig.ALL_USERS_LIST
    );
  }
  getAssigndUsersandRolesList(): Observable<AssignUsersandRoles> {
    return this.http.get<AssignUsersandRoles>(
      environment.BASE_URL + APIConfig.ASSIGN_USERS_ROLES
    );
  }
  deleteAssignedUsersandRoles(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.DELETE_FEATURE,
      details
    );
  }

  assignUsersToGroups(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.ASSIGN_USER_TO_GROUPs,
      details
    );
  }

  unassignUsersToGroups(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.UNASSIGN_USER_TO_GROUPs,
      details
    );
  }
}
