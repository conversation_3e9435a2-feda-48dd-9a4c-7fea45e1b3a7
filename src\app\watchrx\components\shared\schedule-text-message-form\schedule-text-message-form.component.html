<div class="p-fluid p-formgrid grid">
  <div class="field col-12 md:col-12">
    <label htmlfor="state" class="font-bold block mb-2"
      >Select Program(s) <span class="p-text-danger">*</span></label
    >
    <div class="flex justify-content-left gap-3">
      <div class="flex flex-wrap gap-3">
        <div class="flex align-items-center" *ngFor="let option of programList">
          <p-radioButton
            name="consent"
            [value]="option.value"
            [(ngModel)]="selectedProgram"
            [inputId]="option.label.toLowerCase()"
            variant="filled"
            [required]="true"
          />
          <label [for]="option.label.toLowerCase()" class="ml-2">{{
            option.label
          }}</label>
        </div>
      </div>
    </div>
  </div>
  <div class="field col-12 md:col-12">
    <div class="flex flex-wrap justify-content-between">
      <label htmlfor="description" class="font-bold block mb-2">Message <span class="p-text-danger">*</span></label>
      <label htmlfor="description" class="font-bold block mb-2"
        >Limit ( {{ remainingChars() }})</label
      >
    </div>

    <textarea
      rows="4"
      cols="30"
      pInputTextarea
      autoResize="true"
      class="p-inputtext p-component p-element"
      [(ngModel)]="messageDescription"
      [attr.maxLength]="maxLength"
      placeholder="Enter your message here..."
      required="true"
       #ngModels="ngModel"
    >
    </textarea>
  </div>
  <div class="field col-12 md:col-12" *ngIf="messageType == 'SMS'">
    <label htmlfor="phoneNumber" class="font-bold block mb-2"
      >Phone Number <span class="p-text-danger">*</span></label
    >
    <input
      id="phoneNumber"
      type="text"
      pInputText
      [(ngModel)]="phoneNumber"
      name="phoneNumber"
      required
    />
  </div>
  <div class="field col-12 md:col-12">
    <div class="flex flex-wrap gap-3">
      <div class="flex align-items-center">
        <p-radioButton
          name="message"
          value="Now"
          [(ngModel)]="messageType"
          inputId="now"
        />
        <label for="now" class="ml-2"> Now </label>
      </div>

      <div class="flex align-items-center" *ngIf="isSechedule">
        <p-radioButton
          name="message"
          value="Schedule"
          [(ngModel)]="messageType"
          inputId="schedule"
        />
        <label for="schedule" class="ml-2"> Schedule </label>
      </div>

      <div class="flex align-items-center" *ngIf="isShowSMS">
        <p-radioButton
          name="message"
          value="SMS"
          [(ngModel)]="messageType"
          inputId="sms"
        />
        <label for="sms" class="ml-2"> SMS </label>
      </div>
    </div>
  </div>
  <div class="field col-12 md:col-12" *ngIf="messageType == 'Schedule'">
    <div class="flex flex-wrap gap-3">
      <div class="flex align-items-center">
        <p-radioButton
          name="frequencyType"
          value="One Day"
          [(ngModel)]="frequencyType"
          inputId="oneday"
        />
        <label for="oneday" class="ml-2"> Oneday </label>
      </div>

      <div class="flex align-items-center">
        <p-radioButton
          name="frequencyType"
          value="Daily"
          [(ngModel)]="frequencyType"
          inputId="daily"
        />
        <label for="daily" class="ml-2"> Daily </label>
      </div>

      <div class="flex align-items-center">
        <p-radioButton
          name="frequencyType"
          value="Weekly"
          [(ngModel)]="frequencyType"
          inputId="weekly"
        />
        <label for="weekly" class="ml-2"> Weekly </label>
      </div>
    </div>
  </div>
  <div
    class="flex flex-row col-12 md:col-12"
    *ngIf="messageType == 'Schedule' && frequencyType == 'One Day'"
  >
    <div class="field col-12 md:col-6">
      <label htmlfor="description" class="font-bold block mb-2"> Date <span class="p-text-danger">*</span></label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        inputId="icondisplay"
        appendTo="body"
        [(ngModel)]="oneDayStartDate"
      />
    </div>
    <div class="field col-12 md:col-6">
      <label for="calendar-timeonly" class="font-bold block mb-2"> Time <span class="p-text-danger">*</span> </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        [timeOnly]="true"
        inputId="templatedisplay"
        appendTo="body"
        [showTime]="true"
        hourFormat="12"
        [(ngModel)]="oneDayStartTime"
        [stepMinute]="isEdit ? 15 : 1"
      >
        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
          <i
            class="pi pi-clock pointer-events-none"
            (click)="clickCallBack($event)"
          ></i>
        </ng-template>
      </p-calendar>
    </div>
  </div>
  <div
    class="flex flex-row col-12 md:col-12"
    *ngIf="messageType == 'Schedule' && frequencyType == 'Daily'"
  >
    <div class="field col-12 md:col-4">
      <label htmlfor="description" class="font-bold block mb-2">
        Start Date <span class="p-text-danger">*</span>
      </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        inputId="icondisplay"
        appendTo="body"
        [(ngModel)]="startDate"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="description" class="font-bold block mb-2">
        End Date <span class="p-text-danger">*</span>
      </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        inputId="icondisplay"
        appendTo="body"
        [(ngModel)]="endDate"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label for="calendar-timeonly" class="font-bold block mb-2"> Time </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        [timeOnly]="true"
        inputId="templatedisplay"
        appendTo="body"
        [showTime]="true"
        hourFormat="12"
        [(ngModel)]="time"
      >
        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
          <i
            class="pi pi-clock pointer-events-none"
            (click)="clickCallBack($event)"
          ></i>
        </ng-template>
      </p-calendar>
    </div>
  </div>
  <div
    class="p-fluid p-formgrid grid"
    *ngIf="messageType == 'Schedule' && frequencyType == 'Weekly'"
  >
    <div class="field col-12 md:col-4">
      <label htmlfor="description" class="font-bold block mb-2">
        Start Date <span class="p-text-danger">*</span>
      </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        inputId="icondisplay"
        appendTo="body"
        [(ngModel)]="startDate"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="description" class="font-bold block mb-2">
        End Date <span class="p-text-danger">*</span>
      </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        inputId="icondisplay"
        appendTo="body"
        [(ngModel)]="endDate"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label for="calendar-timeonly" class="font-bold block mb-2"> Time <span class="p-text-danger">*</span> </label>
      <p-calendar
        [iconDisplay]="'input'"
        [showIcon]="true"
        [timeOnly]="true"
        inputId="templatedisplay"
        appendTo="body"
        [showTime]="true"
        hourFormat="12"
        [(ngModel)]="time"
      >
        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
          <i
            class="pi pi-clock pointer-events-none"
            (click)="clickCallBack($event)"
          ></i>
        </ng-template>
      </p-calendar>
    </div>
    <div class="field col-12 md:col-12">
      <label for="calendar-timeonly" class="font-bold block mb-2"> Select Days </label>
      <div class="flex justify-content-left gap-3">
        <div class="flex align-items-center">
          <p-checkbox
          name="allDays"
          [binary]="true"
          [(ngModel)]="allDaysSelected"
          (onChange)="toggleAllDays()"
          label="All Days"
        ></p-checkbox>
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Sun"
            name="day"
            value="Sunday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Mon"
            name="day"
            value="Monday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Tue"
            name="day"
            value="Tuesday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Wed"
            name="day"
            value="Wednesday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Thu"
            name="day"
            value="Thursday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Fri"
            name="day"
            value="Friday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
        <div class="flex align-items-center">
          <p-checkbox
            [(ngModel)]="days"
            label="Sat"
            name="day"
            value="Saturday"
            (onChange)="updateAllDaysSelected()"
          />
        </div>
      </div>
    </div>
  </div>
  <div class="col-12" *ngIf="messageType != 'SMS'">
    <p-checkbox
      [(ngModel)]="isChecked"
      [binary]="true"
      label="Response Required?"
    ></p-checkbox>
  </div>
  <div class="flex flex-row md:col-12" *ngIf="isChecked">
    <div class="field col-12 md:col-4">
      <label htmlfor="option1">Option 1</label
      ><input
        pinputtext=""
        id="option1"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="option1"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="option1">Option 2</label
      ><input
        pinputtext=""
        id="option2"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="option2"
      />
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="option1">Option 3</label
      ><input
        pinputtext=""
        id="option3"
        type="text"
        class="p-inputtext p-component p-element"
        [(ngModel)]="option3"
      />
    </div>
  </div>
  <div class="flex flex-row md:col-12" *ngIf="isEnconterNeeded && userRole.roleType==2">
    <p-checkbox
      [(ngModel)]="isEnconterNeeded"
      [binary]="true"
      label="Create Encounter?"
    ></p-checkbox>
  </div>
</div>
