import { Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import {
  City,
  Country,
  ICity,
  ICountry,
  IState,
  State,
} from 'country-state-city';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AddPatient } from '../../../api/addPatient';
import { AddPatientService } from './service/add-patient.service';
import { ProfileService } from '../../profile/services/profile.service';
import { PatientEditProfileService } from '../edit-patient-tab-view/components/profile/service/patientEditProfile.service';

@Component({
  selector: 'app-add-patient',
  templateUrl: './add-patient.component.html',
  styleUrl: './add-patient.component.scss',
})
export class AddPatientComponent implements OnInit {
  countries: ICountry[] = [];
  states: IState[] = [];
  cities: ICity[] = [];
  providerList: any[] = [];
  connectingDevice: string = 'Watch';
  timeZoneList: any[] = [];
  form!: FormGroup;
  loader: boolean = false;

  formData: FormData = {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    dateOfBirth: '',
    address1: '',
    address2: '',
    zipCode: '',
    provider: '',
    mrn: '',
    connectingDevice: '',
    email: null,
    persNumber: '',
    chronicConditionName: '',
    timezone: '',
    country: null,
    state: "",
    city: "",
    gender:''
  };
  allCountries: (ICountry)[] = [];
  genderList: any[] = [{ label: 'Male', value: 'M' }, { label: 'Female', value: 'F' }, { label: 'Others', value: 'O' }];
  filteredGenders: any;
   timeZoneListFull= ['Europe/Andorra',
      'Asia/Dubai',
      'Asia/Kabul',
      'Europe/Tirane',
      'Asia/Yerevan',
      'Antarctica/Casey',
      'Antarctica/Davis',
      'Antarctica/DumontDUrville', 
      'Antarctica/Mawson',
      'Antarctica/Palmer',
      'Antarctica/Rothera',
      'Antarctica/Syowa',
      'Antarctica/Troll',
      'Antarctica/Vostok',
      'America/Argentina/Buenos_Aires',
      'America/Argentina/Cordoba',
      'America/Argentina/Salta',
      'America/Argentina/Jujuy',
      'America/Argentina/Tucuman',
      'America/Argentina/Catamarca',
      'America/Argentina/La_Rioja',
      'America/Argentina/San_Juan',
      'America/Argentina/Mendoza',
      'America/Argentina/San_Luis',
      'America/Argentina/Rio_Gallegos',
      'America/Argentina/Ushuaia',
      'Pacific/Pago_Pago',
      'Europe/Vienna',
      'Australia/Lord_Howe',
      'Antarctica/Macquarie',
      'Australia/Hobart',
      'Australia/Currie',
      'Australia/Melbourne',
      'Australia/Sydney',
      'Australia/Broken_Hill',
      'Australia/Brisbane',
      'Australia/Lindeman',
      'Australia/Adelaide',
      'Australia/Darwin',
      'Australia/Perth',
      'Australia/Eucla',
      'Asia/Baku',
      'America/Barbados',
      'Asia/Dhaka',
      'Europe/Brussels',
      'Europe/Sofia',
      'Atlantic/Bermuda',
      'Asia/Brunei',
      'America/La_Paz',
      'America/Noronha',
      'America/Belem',
      'America/Fortaleza',
      'America/Recife',
      'America/Araguaina',
      'America/Maceio',
      'America/Bahia',
      'America/Sao_Paulo',
      'America/Campo_Grande',
      'America/Cuiaba',
      'America/Santarem',
      'America/Porto_Velho',
      'America/Boa_Vista',
      'America/Manaus',
      'America/Eirunepe',
      'America/Rio_Branco',
      'America/Nassau',
      'Asia/Thimphu',
      'Europe/Minsk',
      'America/Belize',
      'America/St_Johns',
      'America/Halifax',
      'America/Glace_Bay',
      'America/Moncton',
      'America/Goose_Bay',
      'America/Blanc-Sablon',
      'America/Toronto',
      'America/Nipigon',
      'America/Thunder_Bay',
      'America/Iqaluit',
      'America/Pangnirtung',
      'America/Atikokan',
      'America/Winnipeg',
      'America/Rainy_River',
      'America/Resolute',
      'America/Rankin_Inlet',
      'America/Regina',
      'America/Swift_Current',
      'America/Edmonton',
      'America/Cambridge_Bay',
      'America/Yellowknife',
      'America/Inuvik',
      'America/Creston',
      'America/Dawson_Creek',
      'America/Fort_Nelson',
      'America/Vancouver',
      'America/Whitehorse',
      'America/Dawson',
      'Indian/Cocos',
      'Europe/Zurich',
      'Africa/Abidjan',
      'Pacific/Rarotonga',
      'America/Santiago',
      'America/Punta_Arenas',
      'Pacific/Easter',
      'Asia/Shanghai',
      'Asia/Urumqi',
      'America/Bogota',
      'America/Costa_Rica',
      'America/Havana',
      'Atlantic/Cape_Verde',
      'America/Curacao',
      'Indian/Christmas',
      'Asia/Nicosia',
      'Asia/Famagusta',
      'Europe/Prague',
      'Europe/Berlin',
      'Europe/Copenhagen',
      'America/Santo_Domingo',
      'Africa/Algiers',
      'America/Guayaquil',
      'Pacific/Galapagos',
      'Europe/Tallinn',
      'Africa/Cairo',
      'Africa/El_Aaiun',
      'Europe/Madrid',
      'Africa/Ceuta',
      'Atlantic/Canary',
      'Europe/Helsinki',
      'Pacific/Fiji',
      'Atlantic/Stanley',
      'Pacific/Chuuk',
      'Pacific/Pohnpei',
      'Pacific/Kosrae',
      'Atlantic/Faroe',
      'Europe/Paris',
      'Europe/London',
      'Asia/Tbilisi',
      'America/Cayenne',
      'Africa/Accra',
      'Europe/Gibraltar',
      'America/Godthab',
      'America/Danmarkshavn',
      'America/Scoresbysund',
      'America/Thule',
      'Europe/Athens',
      'Atlantic/South_Georgia',
      'America/Guatemala',
      'Pacific/Guam',
      'Africa/Bissau',
      'America/Guyana',
      'Asia/Hong_Kong',
      'America/Tegucigalpa',
      'America/Port-au-Prince',
      'Europe/Budapest',
      'Asia/Jakarta',
      'Asia/Pontianak',
      'Asia/Makassar',
      'Asia/Jayapura',
      'Europe/Dublin',
      'Asia/Jerusalem',
      'Asia/Kolkata',
      'Indian/Chagos',
      'Asia/Baghdad',
      'Asia/Tehran',
      'Atlantic/Reykjavik',
      'Europe/Rome',
      'America/Jamaica',
      'Asia/Amman',
      'Asia/Tokyo',
      'Africa/Nairobi',
      'Asia/Bishkek',
      'Pacific/Tarawa',
      'Pacific/Enderbury',
      'Pacific/Kiritimati',
      'Asia/Pyongyang',
      'Asia/Seoul',
      'Asia/Almaty',
      'Asia/Qyzylorda',
      'Asia/Qostanay', // https://bugs.chromium.org/p/chromium/issues/detail?id=928068
      'Asia/Aqtobe',
      'Asia/Aqtau',
      'Asia/Atyrau',
      'Asia/Oral',
      'Asia/Beirut',
      'Asia/Colombo',
      'Africa/Monrovia',
      'Europe/Vilnius',
      'Europe/Luxembourg',
      'Europe/Riga',
      'Africa/Tripoli',
      'Africa/Casablanca',
      'Europe/Monaco',
      'Europe/Chisinau',
      'Pacific/Majuro',
      'Pacific/Kwajalein',
      'Asia/Yangon',
      'Asia/Ulaanbaatar',
      'Asia/Hovd',
      'Asia/Choibalsan',
      'Asia/Macau',
      'America/Martinique',
      'Europe/Malta',
      'Indian/Mauritius',
      'Indian/Maldives',
      'America/Mexico_City',
      'America/Cancun',
      'America/Merida',
      'America/Monterrey',
      'America/Matamoros',
      'America/Mazatlan',
      'America/Chihuahua',
      'America/Ojinaga',
      'America/Hermosillo',
      'America/Tijuana',
      'America/Bahia_Banderas',
      'Asia/Kuala_Lumpur',
      'Asia/Kuching',
      'Africa/Maputo',
      'Africa/Windhoek',
      'Pacific/Noumea',
      'Pacific/Norfolk',
      'Africa/Lagos',
      'America/Managua',
      'Europe/Amsterdam',
      'Europe/Oslo',
      'Asia/Kathmandu',
      'Pacific/Nauru',
      'Pacific/Niue',
      'Pacific/Auckland',
      'Pacific/Chatham',
      'America/Panama',
      'America/Lima',
      'Pacific/Tahiti',
      'Pacific/Marquesas',
      'Pacific/Gambier',
      'Pacific/Port_Moresby',
      'Pacific/Bougainville',
      'Asia/Manila',
      'Asia/Karachi',
      'Europe/Warsaw',
      'America/Miquelon',
      'Pacific/Pitcairn',
      'America/Puerto_Rico',
      'Asia/Gaza',
      'Asia/Hebron',
      'Europe/Lisbon',
      'Atlantic/Madeira',
      'Atlantic/Azores',
      'Pacific/Palau',
      'America/Asuncion',
      'Asia/Qatar',
      'Indian/Reunion',
      'Europe/Bucharest',
      'Europe/Belgrade',
      'Europe/Kaliningrad',
      'Europe/Moscow',
      'Europe/Simferopol',
      'Europe/Kirov',
      'Europe/Astrakhan',
      'Europe/Volgograd',
      'Europe/Saratov',
      'Europe/Ulyanovsk',
      'Europe/Samara',
      'Asia/Yekaterinburg',
      'Asia/Omsk',
      'Asia/Novosibirsk',
      'Asia/Barnaul',
      'Asia/Tomsk',
      'Asia/Novokuznetsk',
      'Asia/Krasnoyarsk',
      'Asia/Irkutsk',
      'Asia/Chita',
      'Asia/Yakutsk',
      'Asia/Khandyga',
      'Asia/Vladivostok',
      'Asia/Ust-Nera',
      'Asia/Magadan',
      'Asia/Sakhalin',
      'Asia/Srednekolymsk',
      'Asia/Kamchatka',
      'Asia/Anadyr',
      'Asia/Riyadh',
      'Pacific/Guadalcanal',
      'Indian/Mahe',
      'Africa/Khartoum',
      'Europe/Stockholm',
      'Asia/Singapore',
      'America/Paramaribo',
      'Africa/Juba',
      'Africa/Sao_Tome',
      'America/El_Salvador',
      'Asia/Damascus',
      'America/Grand_Turk',
      'Africa/Ndjamena',
      'Indian/Kerguelen',
      'Asia/Bangkok',
      'Asia/Dushanbe',
      'Pacific/Fakaofo',
      'Asia/Dili',
      'Asia/Ashgabat',
      'Africa/Tunis',
      'Pacific/Tongatapu',
      'Europe/Istanbul',
      'America/Port_of_Spain',
      'Pacific/Funafuti',
      'Asia/Taipei',
      'Europe/Kiev',
      'Europe/Uzhgorod',
      'Europe/Zaporozhye',
      'Pacific/Wake',
      'America/New_York',
      'America/Detroit',
      'America/Kentucky/Louisville',
      'America/Kentucky/Monticello',
      'America/Indiana/Indianapolis',
      'America/Indiana/Vincennes',
      'America/Indiana/Winamac',
      'America/Indiana/Marengo',
      'America/Indiana/Petersburg',
      'America/Indiana/Vevay',
      'America/Chicago',
      'America/Indiana/Tell_City',
      'America/Indiana/Knox',
      'America/Menominee',
      'America/North_Dakota/Center',
      'America/North_Dakota/New_Salem',
      'America/North_Dakota/Beulah',
      'America/Denver',
      'America/Boise',
      'America/Phoenix',
      'America/Los_Angeles',
      'America/Anchorage',
      'America/Juneau',
      'America/Sitka',
      'America/Metlakatla',
      'America/Yakutat',
      'America/Nome',
      'America/Adak',
      'Pacific/Honolulu',
      'America/Montevideo',
      'Asia/Samarkand',
      'Asia/Tashkent',
      'America/Caracas',
      'Asia/Ho_Chi_Minh',
      'Pacific/Efate',
      'Pacific/Wallis',
      'Pacific/Apia',
      'Africa/Johannesburg' ];

  ngOnInit() {
    this.countries = Country.getAllCountries();
    //code added to keep US on top
    const all = Country.getAllCountries();
    const usa: any = all.find(c => c.isoCode === 'US');
    const rest = all.filter(c => c.isoCode !== 'US');
    this.allCountries = [usa, ...rest];
    this.formData.country = this.allCountries[0];
    //end of code added to keep US on top
    this.loadPhysicians();

  }

  loadPhysicians() {
    this.addPatientService.loadPhysicians().subscribe((res) => {
      if (res.success) {
        this.providerList = res?.physicianVOList.map((item: any) => {
          return {
            id: item.physicianId,
            name:
              item.firstName + ' ' + item.lastName + ' (' + item.email + ')',
          };
        });
      }
    });
  }

  constructor(
    private addPatientService: AddPatientService,
    private messageService: MessageService,
    private router: Router,
    private profileService: PatientEditProfileService,
    private confirmationService: ConfirmationService,
  ) { }

  onSubmit(form: any) {
    if (form.invalid) {
      form.control.markAllAsTouched();
      return;
    }
    let userId = 0;
    let logUser = localStorage.getItem('user');
    if (logUser) {
      userId = JSON.parse(logUser)['userId'];
    }
    console.log('logUser', logUser);
    this.loader = true;
    let req: AddPatient = {
      address1: this.formData.address1,
      city: this.formData.city,
      firstname: this.formData.firstName,
      lastname: this.formData.lastName,
      phonenumber: this.formData?.phoneNumber,
      state: this.formData?.state,
      zip: this.formData?.zipCode,
      timezone: this.formData?.timezone,
      country: this.formData?.country?.name,
      physicianId: this.formData?.provider?.id,
      primaryCaseManagerId: userId,
      connectingDevice: this.formData?.connectingDevice || 'watch',
      dob: this.formatDateToISO(new Date(this.formData?.dateOfBirth)),
      email: this.formData?.email || null,
      gender: this.formData.gender?.value || 'M',
      chronicConditions: {
        chronicConditions: this.chronicCondition
      },
    };
    console.log(req);
    this.addPatientService.addPatient(req).subscribe((res) => {
      this.loader = false;
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Patient added successfully',
          life: 1800,
        });
        setTimeout(() => {
          this.router.navigate(['/patients']);
        }, 2000);
      } else {

        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: res && res.messages && res.messages.length > 0 ? res.messages[0] : 'Failed to add patient',
        });

      }
    },err=>{
      this.loader = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail:  'Failed to add patient',
        });
    });
  }

  formatDateToISO(date: Date): string {
    return new Date(
      date.getTime() - date.getTimezoneOffset() * 60000
    ).toISOString();
  }

  filterCountries(event: any) {
    const query = event.query.toLowerCase();
    //code added to keep US on top
    this.countries = this.allCountries.filter((p) =>
      p.name.toLowerCase().includes(query)
    );
    //end of code added to keep US on top

    //working code to display all countries without US on top
    // this.countries = Country.getAllCountries().filter((p) =>
    //   p.name.toLowerCase().includes(query)
    // );

    this.formData.state = '';
    this.cities = [];
    this.formData.city = '';
    this.timeZoneList = [];
  }

  // onCountrySelect(selectedCountry: any): void {
  //   if (selectedCountry && selectedCountry.value.phonecode) {
  //     const dialCode = `+${selectedCountry.value.phonecode}`;
  //     if (!this.formData.phoneNumber.startsWith(dialCode)) {
  //       this.formData.phoneNumber = dialCode;
  //     }
  //     else
  //     {
  //       this.formData.phoneNumber =''
  //     }
  //   }
  // }

  filterStates(event: any) {
    const query = event.query.toLowerCase();
    if (this.formData.country) {
      this.states = State.getStatesOfCountry(this.formData?.country?.isoCode);
      if (this.formData?.country?.timezones) {
        this.timeZoneList = this.formData?.country?.timezones;
      }
      this.states = State.getStatesOfCountry(
        this.formData?.country?.isoCode
      ).filter((p) => p.name.toLowerCase().includes(query));
    }
  }

  filterCities(event: any) {
    // const query = event.query.toLowerCase();
    // if (this.formData?.country && this.formData?.state) {
    //   let countryCode = this.formData?.state.countryCode;
    //   let stateCode = this.formData?.state.isoCode;
    //   this.cities = City.getCitiesOfState(countryCode, stateCode);
    //   this.cities = City.getCitiesOfState(countryCode, stateCode).filter((p) =>
    //     p.name.toLowerCase().includes(query)
    //   );
    // }
  }

  filterTimeZones(event: any) {
    //const query = event.query.toLowerCase();
    // if (this.formData?.country && this.formData?.country?.timezones) {
    //   this.timeZoneList = this.formData?.country?.timezones;
    //   this.timeZoneList = this.formData?.country?.timezones.filter((p) =>
    //     p.zoneName.toLowerCase().includes(query)
    //   );
    // }
    const query = event.query.toLowerCase();
    this.timeZoneList = this.timeZoneListFull.filter(feature =>
      feature.toLowerCase().includes(query)
    );
  }
  resetForm() {
    this.formData = {
      firstName: '',
      lastName: '',
      phoneNumber: '',
      dateOfBirth: '',
      address1: '',
      address2: '',
      zipCode: '',
      provider: null,
      mrn: '',
      connectingDevice: '',
      email: null,
      persNumber: '',
      chronicConditionName: '',
      timezone: '',
      country: null,
      state: "",
      city: "",
      gender:"",
    };
    this.router.navigate(['/patients']);
  }

  showChronicPopup = false;
  availableChronicCondition: any[] = [];
  chronicCondition: any[] = [];
  selectedChronic: any[] = []
  openChronicPopup() {
    this.showChronicPopup = true;
    this.profileService.getAvailableChronicConditions().subscribe((res) => {
      if (res.success) {
        this.availableChronicCondition = res.eList.filter(
          (e) =>
            !this.chronicCondition.some(
              (c: any) =>
                c.chronicConditionName === e.chonicConditionName &&
                c.icdCode === e.icdCode
            )
        );
      }
    });
  }


  deleteItem(event: Event, type: string, id: number, index: number) {
        this.chronicCondition.splice(index, 1)
  }

  addNewChroniCondition() {
    let cc = this.selectedChronic;
    for (const item of cc) {
      this.chronicCondition.push({
        chronicConditionName: item.chonicConditionName,
        icdCode: item.icdCode,
      });
    }
    this.selectedChronic = [];
    this.showChronicPopup = false;
  }

  updateScrollBlocking() {
    const anySidebarOpen =this.showChronicPopup;
    if (anySidebarOpen) {
      document.body.classList.add('blocked-scroll');
    } else {
      document.body.classList.remove('blocked-scroll');
    }
  }
  filterGender(event: any): void {
    const query = event.query.toLowerCase();
    this.filteredGenders = this.genderList.filter(gender =>
      gender.label.toLowerCase().includes(query)
    );
  }
}

interface FormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  dateOfBirth: string;
  address1: string;
  address2: string;
  country: ICountry | null;
  //state: IState | null;
  state: string;
  //city: ICity | null;
  city: string;
  zipCode: string;
  provider: any;
  mrn: string;
  connectingDevice: string;
  email: string | null;
  persNumber: string;
  chronicConditionName: string;
  timezone: string;
  gender:any;
}
