import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { DailyScheduleComponent } from './daily-schedule/daily-schedule.component';
import { MedicationInfoComponent } from './medication-info/medication-info.component';
import { PatientDiaryComponent } from './patient-diary/patient-diary.component';
import { MedicationEhrComponent } from './medication-ehr/medication-ehr.component';

@Component({
  selector: 'app-medication-tab-view',
  standalone: true,
  imports: [
    CommonModule,
    TableModule,
    MedicationInfoComponent,
    PatientDiaryComponent,
    DailyScheduleComponent,
    MedicationEhrComponent,
    TabViewModule,
  ],
  templateUrl: './medication-tab-view.component.html',
  styleUrl: './medication-tab-view.component.scss',
})
export class MedicationTabViewComponent {
  activeIndex: any = 0;
  patientId: number = -1;

  constructor(private route: ActivatedRoute) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      console.log(params['id'], 'Medication tabView.');
      this.patientId = params['id'];
    });
  }
}
