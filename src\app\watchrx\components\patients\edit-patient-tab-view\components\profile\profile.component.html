<div class="grid m-0 profileedit">
  <div class="col-12 md:col-9 p-0 profileinfo">
    <div class="p-card-header section" id="personalsection">
      <h6>Personal Information</h6>
    </div>
    <hr />
    <p-toast position="center" />
    <p-confirmDialog />
    <form #form="ngForm" autocomplete="off">
      <div class="formdata ">
        <div class="p-fluid p-formgrid grid">
          <div class="field col-12 md:col-4">
            <label for="firstName">First Name <span class="p-text-danger"> *</span></label>
            <input id="firstName" type="text" pInputText [(ngModel)]="formData.firstName" name="firstName" autocomplete="new-firstname" required />
            <div class="red" *ngIf="submitted && !formData.firstName">
              First Name is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label for="lastName">Last Name <span class="p-text-danger">*</span></label>
            <input id="lastName" type="text" pInputText [(ngModel)]="formData.lastName" name="lastName" autocomplete="new-lastname" required />
            <div class="red" *ngIf="submitted && !formData.lastName">
              Last Name is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label for="country">Gender<span class="p-text-danger"> *</span></label>
            <p-autoComplete id="gender" [(ngModel)]="formData.gender" name="gender" [suggestions]="filteredGenders"
              (completeMethod)="filterGender($event)" placeholder="Select a gender" [dropdown]="true"
              [required]="true"></p-autoComplete>
            <div class="red" *ngIf="submitted && !formData.gender">
              Gender is required.
            </div>
          </div>


          <div class="field col-12 md:col-4">
            <label for="dateOfBirth">Date Of Birth<span class="p-text-danger"> *</span></label>
            <p-calendar id="dateOfBirth" [(ngModel)]="formData.dateOfBirth" name="dateOfBirth" dateFormat="mm-dd-yy"
              [iconDisplay]="'input'" [showIcon]="true" [required]="true"></p-calendar>
            <div class="red" *ngIf="submitted && !formData.dateOfBirth">
              Date of Birth is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label for="mrn">MRN</label>
            <input id="mrn" type="text" pInputText [(ngModel)]="formData.mrn" name="mrn" />
          </div>
          <div class="field col-12 md:col-4">
            <label for="connectingDevice">Connecting Device</label>
            <p-dropdown id="connectingDevice" [(ngModel)]="formData.connectingDevice" name="connectingDevice"
              [options]="['Watch', 'Mobile', 'Wireless MD']" placeholder="Select a device"
              [required]="true"></p-dropdown>
            <div class="red" *ngIf="submitted && !formData.connectingDevice">
              Connecting Device is required.
            </div>
          </div>

          <!-- *ngIf="formData.connectingDevice !== 'Watch'"-->

        </div>
        <div class="p-fluid p-formgrid grid">
          <div class="field col-12 md:col-4">
            <label for="phoneNumber">Phone Number</label>
            <input id="phoneNumber" type="text" pInputText [(ngModel)]="formData.phoneNumber" name="phoneNumber"
              pattern="^\+[1-9]{1}[0-9]{3,14}$" #phoneModel="ngModel" minlength="10" autocomplete="new-phonenumber"/>
            <div *ngIf="phoneModel.invalid && (phoneModel.dirty || phoneModel.touched)">
              <small class="p-error" *ngIf="phoneModel.errors?.['pattern']">
                This value length is invalid. It should be between 10 and 17 characters long.
              </small>
            </div>
            <span><small>Note:(Example:+18001232323) Country code is manditory and space is not allowed in between the
                number</small></span>
          </div>
          <div class="field col-12 md:col-4">
            <label for="persNumber">PERS Number</label>
            <input id="persNumber" type="text" pInputText [(ngModel)]="formData.persNumber" name="persNumber"
              pattern="^\+[1-9]{1}[0-9]{3,14}$" #psphoneModel="ngModel" minlength="10" />
            <div *ngIf="psphoneModel.invalid && (psphoneModel.dirty || psphoneModel.touched)">
              <small class="p-error" *ngIf="psphoneModel.errors?.['pattern']">
                This value length is invalid. It should be between 10 and 17 characters long.
              </small>
            </div>
            <span><small>Note:(Example:+18001232323) Country code is manditory and space is not allowed in between the
                number</small></span>
          </div>
          <!-- <div class="field col-12 md:col-4">
            <label for="email">Email<span class="p-text-danger"> *</span></label>
            <input id="email" type="email" pInputText [(ngModel)]="formData.email" name="email" required />
            <div class="red" *ngIf="submitted && !formData.email">
              Email is required and must be a valid email address.
            </div>
          </div> -->
          <div class="field col-12 md:col-4" *ngIf="formData.connectingDevice == 'Mobile'">
            <label for="email">Email <span class="p-text-danger">*</span></label>
            <input id="email" type="email" pInputText [(ngModel)]="formData.email" name="email" autocomplete="new-email" required
              #email="ngModel" />
            <div>{{email.errors?.['email']}}</div>
            <div class="red" *ngIf="(email.touched || form.submitted) && email.invalid">
              <div *ngIf="email.errors?.['required']">Email is required.</div>
              <!-- pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$" <div *ngIf="email.errors?.['pattern']">Must be a valid email address.</div> -->
            </div>
          </div>
          <div class="field col-12 md:col-4" *ngIf="formData.connectingDevice != 'Mobile'">
            <label for="email">Email</label>
            <input id="email" type="email" pInputText #email="ngModel" [(ngModel)]="formData.email" name="email" autocomplete="new-email"/>
            <!-- <div class="red" *ngIf="(email.touched || form.submitted) && email.errors?.['pattern']">
              pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$"<div *ngIf="email.errors?.['pattern']">Must be a valid email address.</div>
            </div> -->
          </div>


          <div class="field col-12 md:col-4">
            <label for="casemanager">Case Manager <span class="p-text-danger">*</span></label>
            <p-dropdown [(ngModel)]="formData.casemanager" name="casemanager" [options]="casemanagerList"
              optionLabel="label" placeholder="Select a CaseManager" [required]="true"></p-dropdown>
            <div class="red" *ngIf="form.submitted && !formData.casemanager">
              Casemanager is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <!-- <label for="profileupload"></label> -->
            <p-fileUpload mode="basic" chooseLabel="Upload Photo" chooseIcon="pi pi-image" accept="image/*"
              [maxFileSize]="1000000" (onSelect)="onFileSelect($event)" name="medImage" class="upload"></p-fileUpload>
          </div>
        </div>
      </div>
      <hr />
      <div class="p-card-header section" id="addresssection">
        <h6>Address</h6>
      </div>
      <hr />
      <div class="formdata">
        <div class="p-fluid p-formgrid grid">
          <div class="field col-12 md:col-4">
            <label for="address1">Address 1<span class="p-text-danger"> *</span></label>
            <input id="address1" type="text" pInputText [(ngModel)]="formData.address1" name="address1" autocomplete="new-address" required />
            <div class="red" *ngIf="submitted && !formData.address1">
              Address 1 is required.
            </div>
          </div>

          <div class="field col-12 md:col-4">
            <label for="address2">Address 2</label>
            <input id="address2" type="text" pInputText [(ngModel)]="formData.address2" name="address2" />
          </div>

          <div class="field col-12 md:col-4">
            <label for="zipCode">Zip Code<span class="p-text-danger"> *</span></label>
            <input id="zipCode" type="text" pInputText [(ngModel)]="formData.zipCode" name="zipCode" autocomplete="new-zipcode" required />
            <div class="red" *ngIf="submitted && !formData.zipCode">
              Zip Code required.
            </div>
          </div>

          <div class="field col-12 md:col-4">
            <label for="country">Country<span class="p-text-danger"> *</span></label>
            <input id="country" type="text" pInputText [(ngModel)]="formData.country" name="country" autocomplete="new-country" required />
            <!-- <p-autoComplete id="country" [(ngModel)]="formData.country" name="country" [suggestions]="countries"
              (completeMethod)="filterCountries($event)" field="name" placeholder="Select a country" appendTo="body"
              [dropdown]="true" emptyMessage="No country found" [showEmptyMessage]="true"
              [required]="true"></p-autoComplete> -->
            <div class="red" *ngIf="submitted && !formData.country">
              Country is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label for="state">State<span class="p-text-danger"> *</span></label>
            <input id="state" type="text" pInputText [(ngModel)]="formData.state" name="state" autocomplete="new-state" required />
            <!-- <p-autoComplete id="state" [(ngModel)]="formData.state" name="state" [suggestions]="states"
              (completeMethod)="filterStates($event)" field="name" placeholder="Select a state" appendTo="body"
              [dropdown]="true" emptyMessage="No state found" [showEmptyMessage]="true"
              [required]="true"></p-autoComplete> -->
            <div class="red" *ngIf="submitted && !formData.state">
              State is required.
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label for="city">City<span class="p-text-danger"> *</span></label>
            <input id="city" type="text" pInputText [(ngModel)]="formData.city" name="city" autocomplete="new-city" required />
            <!-- <p-autoComplete id="city" [(ngModel)]="formData.city" name="city" [suggestions]="cities"
              (completeMethod)="filterCities($event)" field="name" placeholder="Select a city" appendTo="body"
              [dropdown]="true" emptyMessage="No city found" [showEmptyMessage]="true"
              [required]="true"></p-autoComplete> -->
            <div class="red" *ngIf="submitted && !formData.city">
              City is required.
            </div>
          </div>


          <div class="field col-12 md:col-4">
            <label for="timezone">Timezone<span class="p-text-danger"> *</span></label>
            <p-autoComplete id="timezone" [(ngModel)]="formData.timezone" name="timezone" [suggestions]="timeZoneList"
              (completeMethod)="filterTimeZones($event)" field="" placeholder="Select a timezone" appendTo="body"
              [dropdown]="true" emptyMessage="No timezone found" [showEmptyMessage]="true"
              [required]="true"></p-autoComplete>
            <div class="red" *ngIf="submitted && !formData.timezone">
              Timezone is required.
            </div>
          </div>
        </div>
      </div>
      <hr />
      <div class="p-card-header section" id="programsenrolled">
        <h6>Programs Enrolled</h6>
      </div>
      <hr />
      <div class="formdata">
        <div class="p-fluid p-formgrid grid">
          <div class="field col-12 md:col-6">
            <label htmlfor="state">Select Program(s) <span class="p-text-danger"> *</span></label>
            <div class="flex justify-content-left gap-3">
              <div class="flex align-items-center">
                <div *ngFor="let category of categories" class="field-checkbox">
                  <p-checkbox [(ngModel)]="selectedCategories" [label]="category.programName" name="group"
                    [value]="category" class="mr-3" />
                </div>

              </div>
            </div>
            <div class="red" *ngIf="submitted && isConsentTaken=='Y' && selectedCategories.length==0">
              Program(s) is required.
            </div>
          </div>

          <div class="field col-12 md:col-6">
            <label htmlfor="state">Consented</label>
            <div class="flex flex-wrap gap-3">
              <div class="flex align-items-center">
                <p-radioButton name="consent" value="Y" [(ngModel)]="isConsentTaken" inputId="yes1" />
                <label for="yes1" class="ml-2"> Yes </label>
              </div>

              <div class="flex align-items-center">
                <p-radioButton name="consent" value="N" [(ngModel)]="isConsentTaken" inputId="no1" />
                <label for="no1" class="ml-2"> No </label>
              </div>

              <div class="flex align-items-center">
                <p-radioButton name="consent" value="D" [(ngModel)]="isConsentTaken" inputId="denied1" />
                <label for="denied1" class="ml-2"> Declined </label>
              </div>
            </div>
          </div>

          <div class="field col-12 md:col-12">
            <!-- <label htmlfor="state">Consent Form</label> -->
            <div class="flex "> <!--*ngIf="consentDataFound"-->
              <p-button (click)="showConsentViewPopUp()" [outlined]="true" severity="secondary" class="mr-3"
                *ngIf="isConsentFound" label="Click Here To View Consent Form" icon="pi pi-file">
              </p-button>
              <p-button (click)="showConsentAddPopUp()" [outlined]="true" icon="pi pi-file" severity="primary"
                *ngIf="!isConsentFound" class="mr-3" label="Click Here To Add Consent Form">
              </p-button>
            </div>
          </div>
        </div>
        <!-- <div class="field col-12 md:col-3">
        <p-button type="submit" label="Update" [raised]="true" severity="success" class="mr-3"></p-button>
        <p-button label="Cancel" [raised]="true" severity="danger" class="mr-3"></p-button>
      </div> -->
      </div>
    </form>
    <hr />
    <div class="grid" *ngIf="patientStatus !== 'Active'">
      <div  class="col-12 lg:col-12" id="cptcodes">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact"
          style="padding:8px 10px !important">
          <h6>Chronic Conditions</h6>
          <p-button label="Add Chronic Conditions" severity="secondary" [outlined]="true"  class="mr-3"
            (onClick)="openChronicPopup()" icon="pi pi-plus"></p-button>
        </div>
        <hr />
        <p-table [value]="chronicCondition" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="chronicCondition.length>0 else emptyCCTemplate">
          <ng-template pTemplate="header">
            <tr>
              <th>Condition Name</th>
              <th>ICD Code</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-cc let-i="rowIndex">
            <tr>
              <td style="min-width: 7rem">{{ cc.chronicConditionName }}</td>
              <td style="min-width: 8rem">
                {{ cc.icdCode }}
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="height: 30px; width: 30px" (click)="deleteItem($event, 'chronic', cc.chonicId,i)"></button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <ng-template #emptyCCTemplate>
          <p style="height:100px" class="flex align-items-center p-text-primary font-bold justify-content-center">Chronic Conditions Not Added</p> 
        </ng-template>
        <!-- </div> -->
      </div>
      <hr />
      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact">
          <h6>MD/DO/NP/PA</h6>
        </div>
        <hr />

        <div class="formdata">
          <div class="p-fluid p-formgrid grid">
            <div class="field col-12 md:col-6">
              <p-dropdown [options]="providerList" [(ngModel)]="selectedProvider" optionLabel="label" [showClear]="true"
                placeholder="Select a Provider">
              </p-dropdown>
            </div>
            <div class="field col-12 md:col-2">
              <p-button label="Update" severity="primary" class="mr-6" (click)="updateProvider()"
                [disabled]="userRole?.roleType == 3"></p-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="grid" *ngIf="patientStatus === 'Active'">
      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header flex justify-content-between section" id="contacttime">
          <h6>Preferred Contact Time</h6>
        </div>
        <hr />
        <div class="field col-12 md:col-12 pt-4 pl-3">
          <label htmlfor="state p-2">Select Days </label>
          <div class="flex justify-content-left gap-3 flex-wrap">
            <p-checkbox name="allDays" [binary]="true" [(ngModel)]="allDaysSelected" (onChange)="toggleAllDays()"
              label="All Days"></p-checkbox>
            <p-checkbox *ngFor="let day of daysOfWeek" name="days" [value]="day?.value" [(ngModel)]="selectedDays"
              (onChange)="updateAllDaysSelected()" label="{{ day?.name }}"></p-checkbox>

          </div>
          <div class="flex justify-content-left gap-3 pt-5">
            <p-calendar [(ngModel)]="contactFromTime" [timeOnly]="true" hourFormat="12" [showIcon]="true"
              placeholder="From Time" [iconDisplay]="'input'" [showButtonBar]="true" appendTo="body"></p-calendar>

            <p-calendar [(ngModel)]="contactToTime" [timeOnly]="true" hourFormat="12" [showIcon]="true"
              placeholder="To Time" [iconDisplay]="'input'" [showButtonBar]="true" appendTo="body"></p-calendar>

            <p-button label="Add Preference" [outlined]="true" severity="secondary" type="submit"
              (click)="addContact()" />
          </div>

        </div>
        <p-table [value]="contactInfos" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>Sno</th>
              <th>Days</th>
              <th>Time Range</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-ins let-i="rowIndex">
            <tr>
              <td style="min-width: 7rem">
                {{i+1}}
              </td>
              <td style="min-width: 8rem">
                {{ins?.contactDays}}
              </td>
              <td style="min-width: 8rem">
                {{ins?.contactFromTime }} - {{ins?.contactToTime }}
              </td>
              <td>
                <div class="flex">
                  <!-- <p-button pButton pRipple icon="pi pi-trash" [outlined]="true" severity="secondary"
                  style="height: 30px; width: 30px" (click)="deleteContact($event, ins.id)"></-button> -->
                  <p-button icon="pi pi-trash" [outlined]="true" severity="secondary" size="small" class="mr-2" (click)="
                           deleteContact($event, ins?.id,i)
                          " />
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <!-- </div> -->

      </div>


      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact"
          style="padding:8px 10px !important">
          <h6>Emergency Contact</h6>
          <!-- <i class="pi pi-plus-circle cursor-pointer " style="color: slateblue; font-size: 1.2rem"
            (click)="openEmergencyContactPopup()"></i> -->
          <p-button label="Add Emergency Contact" severity="secondary" [outlined]="true" class="mr-3"
            (onClick)="openEmergencyContactPopup()" icon="pi pi-plus"></p-button>
        </div>
        <hr />
        <p-table [value]="emergencyContacts" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="emergencyContacts.length>0 else emptyTemplate">
          <ng-template pTemplate="header">
            <tr>
              <th>Name</th>
              <th>Phone Number</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-contact let-i="rowIndex">
            <tr>
              <td style="min-width: 5rem">
                <input pInputText id="name" class="flex-auto" [(ngModel)]="contact.name" />
              </td>
              <td style="min-width: 5rem">
                <input pInputText id="phone" class="flex-auto" [(ngModel)]="contact.number" />
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
                    style="height: 30px; width: 30px" (click)="
                    editContactNumber(
                      contact.name,
                      contact.number,
                      contact.contactId,
                      i
                    )
                  "></button>
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="height: 30px; width: 30px" (click)="
                    deleteItem($event, 'emergencycontact', contact.contactId,i)
                  " [disabled]="emergencyContacts.length <= 1"></button>
                </div>
              </td>
            </tr>
          </ng-template>
          <p>
            Note:(Example:+18001232323)Country code is mandatory and space is not
            allowed in between the number
          </p>
        </p-table>
        <ng-template #emptyTemplate>
          <p style="height:100px" class="flex align-items-center p-text-primary font-bold justify-content-center">Emergency Contact Not Added</p>
        </ng-template>
        <hr />
      </div>

      <div class="col-12 lg:col-12" id="cptcodes">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact"
          style="padding:8px 10px !important">
          <h6>Chronic Conditions</h6>
          <!-- <i class="pi pi-plus-circle cursor-pointer " style="color: slateblue; font-size: 1.2rem"
            (click)="openEmergencyContactPopup()"></i> -->
          <p-button label="Add Chronic Conditions" severity="secondary" [outlined]="true"  class="mr-3"
            (onClick)="openChronicPopup()" icon="pi pi-plus"></p-button>
        </div>
        <hr />
        <!-- <div class="card"> -->
        <!-- <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
            <div class="mt-1 text-600 font-bold">{{ "Chronic Conditions" }}</div>
            <div class="mt-2 md:mt-2 flex cursor-pointer ">
              <i class="pi pi-plus-circle" style="color: slateblue; font-size: 1.2rem" (click)="openChronicPopup()"></i>
            </div>
          </div>
          <br /> -->
        <p-table [value]="chronicCondition" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="chronicCondition.length>0 else emptyCCTemplate">
          <ng-template pTemplate="header">
            <tr>
              <th>Condition Name</th>
              <th>ICD Code</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-cc let-i="rowIndex">
            <tr>
              <td style="min-width: 7rem">{{ cc.chronicConditionName }}</td>
              <td style="min-width: 8rem">
                {{ cc.icdCode }}
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="height: 30px; width: 30px" (click)="deleteItem($event, 'chronic', cc.chonicId,i)"></button>
                  <!-- <p-button
                  icon="pi pi-trash"
                  [outlined]="true" severity="secondary"
                  size="small"
                  class="mr-2"
                  (click)="
                   deleteContact($event, cc.id)
                  "
                /> -->
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <ng-template #emptyCCTemplate>
          <p style="height:100px" class="flex align-items-center p-text-primary font-bold justify-content-center">Chronic Conditions Not Added</p>
        </ng-template>
        <!-- </div> -->
      </div>

      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact">
          <h6>MD/DO/NP/PA</h6>
        </div>
        <hr />

        <div class="formdata">
          <div class="p-fluid p-formgrid grid">
            <div class="field col-12 md:col-6">
              <p-dropdown [options]="providerList" [(ngModel)]="selectedProvider" optionLabel="label" [showClear]="true"
                placeholder="Select a Provider">
              </p-dropdown>
            </div>
            <div class="field col-12 md:col-2">
              <p-button label="Update" severity="primary" class="mr-6" (click)="updateProvider()"
                [disabled]="userRole?.roleType == 3"></p-button>
            </div>
          </div>
        </div>
      </div>

      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header flex justify-content-between section align-items-center " id="insurancedetails"
          style="padding:8px 10px !important">
          <h6>Insurance Details</h6>
          <!-- <i class="pi pi-plus-circle cursor-pointer" style="color: slateblue; font-size: 1.2rem "
            (click)="openInsurancePopup()"></i> -->
          <p-button label="Add Insurance Details" severity="secondary" [outlined]="true" class="mr-3"
            (onClick)="openInsurancePopup()" icon="pi pi-plus"></p-button>
        </div>
        <hr />
        <!-- <div class="card"> -->
        <!-- <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Insurance Details" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <i class="pi pi-plus-circle" style="color: slateblue; font-size: 1.5rem" (click)="openInsurancePopup()"></i>
        </div>
      </div>
      <br /> -->
        <p-table [value]="insurance" responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="insurance.length>0 else emptyInsuranceTemplate">
          <ng-template pTemplate="header">
            <tr>
              <th>Company</th>
              <th>Group Id</th>
              <th>Member Number</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-ins let-i="rowIndex">
            <tr>
              <td style="min-width: 7rem">
                <input pInputText id="insuranceCompany" class="flex-auto" [(ngModel)]="ins.insuranceCompany" />
              </td>
              <td style="min-width: 8rem">
                <input pInputText id="insuranceNumber" class="flex-auto" [(ngModel)]="ins.insuranceNumber" />
              </td>
              <td style="min-width: 8rem">
                <input pInputText id="insuranceMemberNumber" class="flex-auto"
                  [(ngModel)]="ins.insuranceMemberNumber" />
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
                    style="height: 30px; width: 30px" (click)="
                    editInsurance(
                      ins.insuranceId,
                      ins.insuranceCompany,
                      ins.insuranceNumber,
                      ins.insuranceMemberNumber,
                      i
                    )
                  "></button>
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="height: 30px; width: 30px" (click)="deleteInsurance($event, ins.insuranceId,i)"></button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <ng-template #emptyInsuranceTemplate>
          <p style="height:100px" class="flex align-items-center p-text-primary font-bold justify-content-center">Insurance Details Not Added</p>
        </ng-template>
        <!-- </div> -->
      </div>

      <div class="col-12 lg:col-12">
        <hr />
        <div class="p-card-header section" id="devicelist">
          <h6>Assigned Medical Devices</h6>
        </div>
        <hr />
        <!-- <div class="card"> -->
        <!-- <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">
          {{ "Assigned Medical Devices" }}
        </div>
      </div>
      <br /> -->
        <p-table [value]="assignedDeviceList" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="assignedDeviceList.length>0 else emptyDevicesTemplate">
          <ng-template pTemplate="header">
            <tr>
              <th>Image</th>
              <th>Device Type</th>
              <th>Device Name</th>
              <th>Make and Model</th>
              <th>Measurement</th>

              <th>Device Unique Id</th>
              <th>Phone Number</th>
              <th>Device Active</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-device>
            <tr>
              <td>
                <img src="{{ device?.imgPath }}" height="100" alt="Image preview..." width="100px" />
              </td>
              <td>{{ device?.deviceType }}</td>
              <td>{{ device?.deviceName }}</td>
              <td>{{ device?.assignedWatch }}</td>
              <td>{{ getNames(device?.deviceMeasuresArray) }}</td>

              <td>{{ device.imei }}</td>
              <td>{{ device.watchPhoneNumber }}</td>
              <td>
                <span *ngIf="device.isActive">Yes</span><span *ngIf="!device.isActive">No</span>
              </td>
            </tr>
          </ng-template>
       
        </p-table>
        <ng-template #emptyDevicesTemplate>
          <p style="height:100px" class="flex align-items-center p-text-primary font-bold justify-content-center">Medical Devices Not Assigned</p>
        </ng-template>
        <!-- </div> -->
      </div>

    </div>

    <div class="field flex justify-content-end p-3">
      <p-button label="Cancel Changes" severity="secondary" [outlined]="true" class="mr-3"
        [disabled]="userRole?.roleType == 3" (onClick)="resetForm()"></p-button>
      <p-button label="Update Changes" severity="primary" class="mr-3" [disabled]="userRole?.roleType == 3"
        (onClick)="onSubmitInfo(form)"></p-button>
    </div>
    <!-- <div class="grid" *ngIf="patientStatus === 'Active'">
 
</div>
<div class="grid" *ngIf="patientStatus === 'Active'">

</div> -->
  </div>

  <div class="col-12 md:col-3 p-0 profilemenu">
    <ul class="p-0">
      <li *ngFor="let menu of sideMenu" [ngClass]="{'active':selectedMenu==menu.value}"
        (click)="scrollToDiv(menu.value)">{{menu.label}}</li>
    </ul>
  </div>
</div>
<p-dialog header="Consent Form" [modal]="true" [(visible)]="showConsentView" [style]="{ width: '50rem' }"
  [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }">
  <form name="form" id="consent-form" (ngSubmit)="downloadConsentForm()">
    <div class="flex justify-content-between gap-2">
      <img src="assets/watchrx/svg/app_logo.svg" alt="Logo" class="consent-logo" />
      <div class="company-name">
        Remote Patient Management <br />Chronic Care Management
      </div>
    </div>

    <div style="margin-top: 10px">
      <div class="orange-strip">
        <strong> Integrated Care Management Services</strong>
      </div>
      <div style="margin-top: 10px">
        <h5>
          <p>Consent for Services:</p>
        </h5>
      </div>
      <p>
        By signing this form, I,
        <strong>{{ patientNameConsent }}</strong>, hereby consent to participate in the Chronic Care Management (CCM),
        Remote Patient Monitoring (RPM), Principal Care Management (PCM), and
        Remote Therapeutic Monitoring (RTM) services provided by WatchRx.
        <b>I understand that these services are voluntary and that I can
          withdraw my consent at any time.</b>
      </p>

      <strong>
        <h5>Nature of Services:</h5>
      </strong>
      <p>
        a I understand that the services provided by WatchRx are not a
        substitute for direct in-person health care visits or health care
        emergencies. Non-emergency services will be provided during day
        time/week days from 8am-4 pm.
      </p>
      <p>
        b I am aware that WatchRx will collect, use, and disclose my
        health information as part of these services.
      </p>
      <p>
        c I understand that the data collected will be used to guide my
        treatment and care plan by the provider.
      </p>
      <p>
        d Benefits include improved access to care, continuous monitoring of
        health status, and personalized care management
      </p>

      <strong>
        <h5>Data Privacy and Security:</h5>
      </strong>
      <p>
        a I acknowledge that WatchRx will take appropriate measures to
        ensure the confidentiality and security of my health information.
      </p>
      <p>
        b I understand my rights to access and control my health information
        under applicable privacy laws
      </p>

      <strong>
        <h5>Patient Responsibilities:</h5>
      </strong>
      <p>a I agree to provide accurate and complete health information.</p>
      <p>
        b I will inform WatchRx of any changes in my health status or
        contact information.
      </p>
      <p>
        c agree to use the provided technology as instructed for the purpose of
        RPM and RTM services.
      </p>

      <strong>
        <h5>Billing and Costs:</h5>
      </strong>
      <p>
        a I understand that there may be costs associated with these services,
        which may be billed to my insurance.
      </p>
      <p>
        b I agree to be responsible for any copayments or deductibles as per my
        insurance plan.
      </p>

      <strong>
        <h5>Contact Information:</h5>
      </strong>
      <p>
        For any questions or concerns regarding these services, I can contact
        WatchRx at: <br />
        <strong class="mt-5">Phone: {{ phoneNumberConsent }}</strong> :
        <strong class="mt-5"> Email: {{ emailConsent }}</strong>
      </p>

      <strong>
        <h5>Patient Consent:</h5>
      </strong>
      <p>
        I have read, or have had read to me, this consent form. I have had the
        opportunity to ask questions, and all my questions have been answered to
        my satisfaction. I consent to participate in the services provided by
        WatchRx as described above.
      </p>
    </div>

    <div class="flex justify-content-start gap-2 mt-2">
      <p-button type="submit" label="Download" severity="success" />
      <p-button label="Cancel" severity="danger" (onClick)="showConsentView = false" />
    </div>
  </form>
</p-dialog>

<p-dialog header="Consent Form" [modal]="true" [(visible)]="showConsentAdd" [style]="{ width: '50rem' }"
  [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }">
  <form name="form" id="consent-form" (ngSubmit)="onConsentSubmit(form)">
    <div class="flex justify-content-between gap-2">
      <img src="assets/watchrx/svg/app_logo.svg" alt="Logo" class="consent-logo" />
      <div class="company-name">
        Remote Patient Management <br />Chronic Care Management
      </div>
    </div>

    <div style="margin-top: 10px">
      <div class="orange-strip">
        <strong> Integrated Care Management Services</strong>
      </div>
      <div style="margin-top: 10px">
        <h5>
          <p>Consent for Services:</p>
        </h5>
      </div>
      <p>
        By signing this form, I,
        <input id="patientNameConsent" type="text" pInputText [(ngModel)]="patientNameConsent" name="firstName"
          required />, hereby consent to participate in the Chronic Care Management (CCM),
        Remote Patient Monitoring (RPM), Principal Care Management (PCM), and
        Remote Therapeutic Monitoring (RTM) services provided by WatchRx.
        <b>I understand that these services are voluntary and that I can
          withdraw my consent at any time.</b>
      </p>

      <strong>
        <h5>Nature of Services:</h5>
      </strong>
      <p>
        a I understand that the services provided by WatchRx are not a
        substitute for direct in-person health care visits or health care
        emergencies. Non-emergency services will be provided during day
        time/week days from 8am-4 pm.
      </p>
      <p>
        b I am aware that WatchRx will collect, use, and disclose my
        health information as part of these services.
      </p>
      <p>
        c I understand that the data collected will be used to guide my
        treatment and care plan by the provider.
      </p>
      <p>
        d Benefits include improved access to care, continuous monitoring of
        health status, and personalized care management
      </p>

      <strong>
        <h5>Data Privacy and Security:</h5>
      </strong>
      <p>
        a I acknowledge that WatchRx will take appropriate measures to
        ensure the confidentiality and security of my health information.
      </p>
      <p>
        b I understand my rights to access and control my health information
        under applicable privacy laws
      </p>

      <strong>
        <h5>Patient Responsibilities:</h5>
      </strong>
      <p>a I agree to provide accurate and complete health information.</p>
      <p>
        b I will inform WatchRx of any changes in my health status or
        contact information.
      </p>
      <p>
        c agree to use the provided technology as instructed for the purpose of
        RPM and RTM services.
      </p>

      <strong>
        <h5>Billing and Costs:</h5>
      </strong>
      <p>
        a I understand that there may be costs associated with these services,
        which may be billed to my insurance.
      </p>
      <p>
        b I agree to be responsible for any copayments or deductibles as per my
        insurance plan.
      </p>

      <strong>
        <h5>Contact Information:</h5>
      </strong>
      <p>
        For any questions or concerns regarding these services, I can contact
        WatchRx at: <br />

        <strong class="mt-5">Phone</strong> :
        <input id="phoneNumberConsent" type="text" pInputText [(ngModel)]="phoneNumberConsent" name="phoneNumberConsent"
          required class="mt-5" />
        <strong class="mt-5"> Email</strong>:
        <input id="emailConsent" type="text" pInputText [(ngModel)]="emailConsent" name="emailConsent" required
          class="mt-5" />
      </p>

      <strong>
        <h5>Patient Consent:</h5>
      </strong>
      <p>
        I have read, or have had read to me, this consent form. I have had the
        opportunity to ask questions, and all my questions have been answered to
        my satisfaction. I consent to participate in the services provided by
        WatchRx as described above.
      </p>
    </div>

    <div class="flex justify-content-start gap-2 mt-2">
      <p-button type="submit" label="Save Consent" severity="success" />
      <p-button label="Cancel" severity="danger" (onClick)="showConsentAdd = false" />
    </div>
  </form>
</p-dialog>

<p-sidebar [(visible)]="showEmergencyContactPopup" position="right" styleClass="w-30rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        {{emergencyHeader}}
      </span>
    </div>
  </ng-template>
  <div class="flex flex-column align-items-left mb-3 mt-3">
    <label for="name" class="font-semibold mb-1">Contact Name</label>
    <input pInputText id="name" class="flex-auto" [(ngModel)]="contactName" />
  </div>
  <div class="flex flex-column align-items-left mb-5">
    <label for="phone" class="font-semibold mb-1">Contact Number</label>
    <input pInputText id="phone" class="flex-auto" [(ngModel)]="contactNumber" pattern="^\+\d{1,3}\d{4,14}$"
      title="Phone number should be in the format +[country code][phone number], e.g., +1999999999 or +918678888987" />
  </div>
  <div *ngIf="!validatePhoneNumber() && contactNumber">
    <p class="error-message p-error">
      Invalid phone number format. Please enter a number in the format +[country
      code][phone number], e.g., +1999999999 .
    </p>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="showEmergencyContactPopup = false" />
      <p-button label="Save" (click)="addContactNumbers()" [disabled]="!validatePhoneNumber()" />
    </div>
  </ng-template>
</p-sidebar>

<p-sidebar [(visible)]="showInsurancePopup" position="right" styleClass="w-30rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        {{insuranceHeader}}
      </span>
    </div>
  </ng-template>
  <div class="flex align-items-left flex-column mb-3 mt-3">
    <label for="insuranceCompany" class="font-semibold mb-1 ">Insurance Company<span class="p-text-danger">
        *</span></label>
    <input pInputText id="insuranceCompany" class="flex-auto" [(ngModel)]="insuranceCompany" />
  </div>
  <div class="flex align-items-left flex-column mb-3">
    <label for="insuranceGroupId" class="font-semibold mb-1">Insurance Group Id<span
        class="p-text-danger">*</span></label>
    <input pInputText id="insuranceGroupId" class="flex-auto" [(ngModel)]="insuranceGroupId" />
  </div>
  <div class="flex align-items-left flex-column mb-3">
    <label for="insuranceMemberNumber" class="font-semibold mb-1">Insurance Member Number<span class="p-text-danger">
        *</span></label>
    <input pInputText id="insuranceMemberNumber" class="flex-auto" [(ngModel)]="insuranceMemberNumber" />
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="showInsurancePopup = false" />
      <p-button label="Save" type="submit" (click)="addInsurance()" [disabled]="!validateInsurance()" />
    </div>
  </ng-template>
</p-sidebar>

<p-sidebar [(visible)]="showChronicPopup" position="right" styleClass="w-30rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Add Chronic Conditions
      </span>
    </div>
  </ng-template>
  <div class="flex align-items-left flex-column mb-5 mt-3">
    <p-multiSelect [options]="availableChronicCondition" [(ngModel)]="selectedChronic"
      placeholder="Select Chronic Conditions" optionLabel="chonicConditionName" display="chip" [showClear]="true"
      appendTo="body" class="mb-2 w-full"></p-multiSelect>
    <div *ngIf="selectedChronic && selectedChronic.length > 0">
      <label><strong>Selected Chronic Conditions:</strong></label>
      <div *ngFor="let condition of selectedChronic">
        <p>
          {{ condition?.chonicConditionName }} {{ " - "
          }}{{ condition?.icdCode }}
        </p>
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="showChronicPopup = false" />
      <p-button label="Save" (click)="addNewChroniCondition()" />
    </div>
  </ng-template>
</p-sidebar>