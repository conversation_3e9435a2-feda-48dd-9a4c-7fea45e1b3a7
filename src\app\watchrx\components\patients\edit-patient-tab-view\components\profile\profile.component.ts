import { CommonModule, DatePipe } from '@angular/common';
import { Component, EventEmitter, HostListener, OnInit, Output } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { ActivatedRoute, Route, Router } from '@angular/router';
import {
  City,
  Country,
  ICity,
  ICountry,
  IState,
  State,
} from 'country-state-city';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ChartModule } from 'primeng/chart';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import {
  ChronicCondition,
  contactInfo,
  DeviceInfo,
  EmergengenyContact,
  InsuranceVO,
  SelectedProgram,
} from '../../../../../api/editPatientProfile';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { PatientEditProfileService } from './service/patientEditProfile.service';
import { SidebarModule } from 'primeng/sidebar';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChartModule,
    MenuModule,
    TableModule,
    StyleClassModule,
    PanelMenuModule,
    ButtonModule,
    TableModule,
    InputGroupModule,
    InputGroupAddonModule,
    DropdownModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    FileUploadModule,
    RadioButtonModule,
    CheckboxModule,
    DialogModule,
    AutoCompleteModule,
    ToastModule,
    AvatarModule,
    PanelModule,
    ConfirmDialogModule,
    SidebarModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss',
  providers: [MessageService, ConfirmationService, DatePipe],
})
export class ProfileComponent implements OnInit {
  filteredGenders: any;
  medImage: any;
  casemanagerId: number | null = null;
  contactDetails: any;
  emergencyHeader: string = 'Add Emergency Contact';
  isEditEmergencyContact: boolean = false;
  isEditEmergencyContactIndex: number = -1;
  insuranceHeader: string = "Add Insurance Details";
  isEditInsuranceDetailIndex: number = -1;
  isEditInsuranceDetail: boolean = false;
  isConsentFound: boolean = false;
  submitted: boolean = false;
  timeZoneListFull: any[]=[];

  constructor(
    private profileService: PatientEditProfileService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService,
    private datePipe: DatePipe,
    private router: Router,
  ) { }
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();

  countries: ICountry[] = [];
  states: IState[] = [];
  cities: ICity[] = [];
  timeZoneList: any[] = [];
  isConsentTaken: string | undefined;
  selectedCategories: any[] = [];
  categories: any[] = [];

  patientNameConsent: string = '';
  phoneNumberConsent: string = '';
  emailConsent: string = '';
  consentDataFound: boolean = false;
  showConsentView: boolean = false;
  showConsentAdd: boolean = false;

  showEmergencyContactPopup: boolean = false;
  emergencyContacts: EmergengenyContact[] = [];
  contactName: string = '';
  contactNumber: string = '';
  deletedEmergencyContacts: EmergengenyContact[] = [];

  showInsurancePopup: boolean = false;
  insurance: InsuranceVO[] = [];
  insuranceCompany: string = '';
  insuranceGroupId: string = '';
  insuranceMemberNumber: string = '';
  addInsuranceForm!: FormGroup;
  deletedInsurance: InsuranceVO[] = [];

  showChronicPopup: boolean = false;
  chronicCondition: ChronicCondition[] = [];
  availableChronicCondition: ChronicCondition[] = [];
  selectedChronic: any = [];
  providerList: any = [];
  selectedProvider: any;
  casemanagerList: any = [];
  selectedCasemanager: any;

  assignedDeviceList: DeviceInfo[] = [];

  patientId: number = 0;
  patientStatus: string = '';
  form!: FormGroup;
  formData: FormData = {
    firstName: '',
    lastName: '',
    phoneNumber: '',
    dateOfBirth: '09-06-1993',
    address1: '',
    address2: '',
    zipCode: '',
    provider: '',
    mrn: '',
    connectingDevice: '',
    email: '',
    persNumber: '',
    chronicConditionName: '',
    timezone: '',
    country: "",
    state: "",
    city: null,
    addressId: 0,
    gender: '',
    casemanager: '',
  };
  genderList: any[] = [{ label: 'Male', value: 'M' }, { label: 'Female', value: 'F' }, { label: 'Others', value: 'O' }];
  selectedMenu: string = 'personalsection';
  sideMenu = [
    { label: 'Personal Information', value: 'personalsection' },
    { label: 'Address', value: 'addresssection' },
    { label: 'Programs Enrolled', value: 'programsenrolled' },
    { label: 'Preferred Contact Time', value: 'contacttime' },
    { label: 'Physician and Emergency Contact', value: 'emergencycontact' },
    { label: 'Chronic Conditions, CPT codes', value: 'cptcodes' },
    { label: 'Insurance Details', value: 'insurancedetails' },
    { label: 'Device List', value: 'devicelist' }
  ]
  allDaysSelected: boolean = false;
  daysOfWeek = [
    { name: 'Monday', value: 'Mon' },
    { name: 'Tuesday', value: 'Tue' },
    { name: 'Wednesday', value: 'Wed' },
    { name: 'Thursday', value: 'Thu' },
    { name: 'Friday', value: 'Fri' },
    { name: 'Saturday', value: 'Sat' },
    { name: 'Sunday', value: 'Sun' },
  ];
  selectedDays: string[] = [];

  showEmergencyPopup: boolean = false;
  contactInfos: contactInfo[] = [];
  contactDays: string = '';
  contactFromTime: string = '';
  contactToTime: string = '';
  deletedContactInfos: contactInfo[] = [];
  userRole: any = null;
  isLoading: boolean = false;
  @HostListener('window:scroll', [])
  onScroll(): void {
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
      const rect = section.getBoundingClientRect();
      // Check if the section is in view
      if (rect.top <= 5 && rect.bottom > 5) {
        this.selectedMenu = section.id;
      }
    });
  }
  ngOnInit(): void {
    this.timeZoneListFull= ['Europe/Andorra',
      'Asia/Dubai',
      'Asia/Kabul',
      'Europe/Tirane',
      'Asia/Yerevan',
      'Antarctica/Casey',
      'Antarctica/Davis',
      'Antarctica/DumontDUrville', 
      'Antarctica/Mawson',
      'Antarctica/Palmer',
      'Antarctica/Rothera',
      'Antarctica/Syowa',
      'Antarctica/Troll',
      'Antarctica/Vostok',
      'America/Argentina/Buenos_Aires',
      'America/Argentina/Cordoba',
      'America/Argentina/Salta',
      'America/Argentina/Jujuy',
      'America/Argentina/Tucuman',
      'America/Argentina/Catamarca',
      'America/Argentina/La_Rioja',
      'America/Argentina/San_Juan',
      'America/Argentina/Mendoza',
      'America/Argentina/San_Luis',
      'America/Argentina/Rio_Gallegos',
      'America/Argentina/Ushuaia',
      'Pacific/Pago_Pago',
      'Europe/Vienna',
      'Australia/Lord_Howe',
      'Antarctica/Macquarie',
      'Australia/Hobart',
      'Australia/Currie',
      'Australia/Melbourne',
      'Australia/Sydney',
      'Australia/Broken_Hill',
      'Australia/Brisbane',
      'Australia/Lindeman',
      'Australia/Adelaide',
      'Australia/Darwin',
      'Australia/Perth',
      'Australia/Eucla',
      'Asia/Baku',
      'America/Barbados',
      'Asia/Dhaka',
      'Europe/Brussels',
      'Europe/Sofia',
      'Atlantic/Bermuda',
      'Asia/Brunei',
      'America/La_Paz',
      'America/Noronha',
      'America/Belem',
      'America/Fortaleza',
      'America/Recife',
      'America/Araguaina',
      'America/Maceio',
      'America/Bahia',
      'America/Sao_Paulo',
      'America/Campo_Grande',
      'America/Cuiaba',
      'America/Santarem',
      'America/Porto_Velho',
      'America/Boa_Vista',
      'America/Manaus',
      'America/Eirunepe',
      'America/Rio_Branco',
      'America/Nassau',
      'Asia/Thimphu',
      'Europe/Minsk',
      'America/Belize',
      'America/St_Johns',
      'America/Halifax',
      'America/Glace_Bay',
      'America/Moncton',
      'America/Goose_Bay',
      'America/Blanc-Sablon',
      'America/Toronto',
      'America/Nipigon',
      'America/Thunder_Bay',
      'America/Iqaluit',
      'America/Pangnirtung',
      'America/Atikokan',
      'America/Winnipeg',
      'America/Rainy_River',
      'America/Resolute',
      'America/Rankin_Inlet',
      'America/Regina',
      'America/Swift_Current',
      'America/Edmonton',
      'America/Cambridge_Bay',
      'America/Yellowknife',
      'America/Inuvik',
      'America/Creston',
      'America/Dawson_Creek',
      'America/Fort_Nelson',
      'America/Vancouver',
      'America/Whitehorse',
      'America/Dawson',
      'Indian/Cocos',
      'Europe/Zurich',
      'Africa/Abidjan',
      'Pacific/Rarotonga',
      'America/Santiago',
      'America/Punta_Arenas',
      'Pacific/Easter',
      'Asia/Shanghai',
      'Asia/Urumqi',
      'America/Bogota',
      'America/Costa_Rica',
      'America/Havana',
      'Atlantic/Cape_Verde',
      'America/Curacao',
      'Indian/Christmas',
      'Asia/Nicosia',
      'Asia/Famagusta',
      'Europe/Prague',
      'Europe/Berlin',
      'Europe/Copenhagen',
      'America/Santo_Domingo',
      'Africa/Algiers',
      'America/Guayaquil',
      'Pacific/Galapagos',
      'Europe/Tallinn',
      'Africa/Cairo',
      'Africa/El_Aaiun',
      'Europe/Madrid',
      'Africa/Ceuta',
      'Atlantic/Canary',
      'Europe/Helsinki',
      'Pacific/Fiji',
      'Atlantic/Stanley',
      'Pacific/Chuuk',
      'Pacific/Pohnpei',
      'Pacific/Kosrae',
      'Atlantic/Faroe',
      'Europe/Paris',
      'Europe/London',
      'Asia/Tbilisi',
      'America/Cayenne',
      'Africa/Accra',
      'Europe/Gibraltar',
      'America/Godthab',
      'America/Danmarkshavn',
      'America/Scoresbysund',
      'America/Thule',
      'Europe/Athens',
      'Atlantic/South_Georgia',
      'America/Guatemala',
      'Pacific/Guam',
      'Africa/Bissau',
      'America/Guyana',
      'Asia/Hong_Kong',
      'America/Tegucigalpa',
      'America/Port-au-Prince',
      'Europe/Budapest',
      'Asia/Jakarta',
      'Asia/Pontianak',
      'Asia/Makassar',
      'Asia/Jayapura',
      'Europe/Dublin',
      'Asia/Jerusalem',
      'Asia/Kolkata',
      'Indian/Chagos',
      'Asia/Baghdad',
      'Asia/Tehran',
      'Atlantic/Reykjavik',
      'Europe/Rome',
      'America/Jamaica',
      'Asia/Amman',
      'Asia/Tokyo',
      'Africa/Nairobi',
      'Asia/Bishkek',
      'Pacific/Tarawa',
      'Pacific/Enderbury',
      'Pacific/Kiritimati',
      'Asia/Pyongyang',
      'Asia/Seoul',
      'Asia/Almaty',
      'Asia/Qyzylorda',
      'Asia/Qostanay', // https://bugs.chromium.org/p/chromium/issues/detail?id=928068
      'Asia/Aqtobe',
      'Asia/Aqtau',
      'Asia/Atyrau',
      'Asia/Oral',
      'Asia/Beirut',
      'Asia/Colombo',
      'Africa/Monrovia',
      'Europe/Vilnius',
      'Europe/Luxembourg',
      'Europe/Riga',
      'Africa/Tripoli',
      'Africa/Casablanca',
      'Europe/Monaco',
      'Europe/Chisinau',
      'Pacific/Majuro',
      'Pacific/Kwajalein',
      'Asia/Yangon',
      'Asia/Ulaanbaatar',
      'Asia/Hovd',
      'Asia/Choibalsan',
      'Asia/Macau',
      'America/Martinique',
      'Europe/Malta',
      'Indian/Mauritius',
      'Indian/Maldives',
      'America/Mexico_City',
      'America/Cancun',
      'America/Merida',
      'America/Monterrey',
      'America/Matamoros',
      'America/Mazatlan',
      'America/Chihuahua',
      'America/Ojinaga',
      'America/Hermosillo',
      'America/Tijuana',
      'America/Bahia_Banderas',
      'Asia/Kuala_Lumpur',
      'Asia/Kuching',
      'Africa/Maputo',
      'Africa/Windhoek',
      'Pacific/Noumea',
      'Pacific/Norfolk',
      'Africa/Lagos',
      'America/Managua',
      'Europe/Amsterdam',
      'Europe/Oslo',
      'Asia/Kathmandu',
      'Pacific/Nauru',
      'Pacific/Niue',
      'Pacific/Auckland',
      'Pacific/Chatham',
      'America/Panama',
      'America/Lima',
      'Pacific/Tahiti',
      'Pacific/Marquesas',
      'Pacific/Gambier',
      'Pacific/Port_Moresby',
      'Pacific/Bougainville',
      'Asia/Manila',
      'Asia/Karachi',
      'Europe/Warsaw',
      'America/Miquelon',
      'Pacific/Pitcairn',
      'America/Puerto_Rico',
      'Asia/Gaza',
      'Asia/Hebron',
      'Europe/Lisbon',
      'Atlantic/Madeira',
      'Atlantic/Azores',
      'Pacific/Palau',
      'America/Asuncion',
      'Asia/Qatar',
      'Indian/Reunion',
      'Europe/Bucharest',
      'Europe/Belgrade',
      'Europe/Kaliningrad',
      'Europe/Moscow',
      'Europe/Simferopol',
      'Europe/Kirov',
      'Europe/Astrakhan',
      'Europe/Volgograd',
      'Europe/Saratov',
      'Europe/Ulyanovsk',
      'Europe/Samara',
      'Asia/Yekaterinburg',
      'Asia/Omsk',
      'Asia/Novosibirsk',
      'Asia/Barnaul',
      'Asia/Tomsk',
      'Asia/Novokuznetsk',
      'Asia/Krasnoyarsk',
      'Asia/Irkutsk',
      'Asia/Chita',
      'Asia/Yakutsk',
      'Asia/Khandyga',
      'Asia/Vladivostok',
      'Asia/Ust-Nera',
      'Asia/Magadan',
      'Asia/Sakhalin',
      'Asia/Srednekolymsk',
      'Asia/Kamchatka',
      'Asia/Anadyr',
      'Asia/Riyadh',
      'Pacific/Guadalcanal',
      'Indian/Mahe',
      'Africa/Khartoum',
      'Europe/Stockholm',
      'Asia/Singapore',
      'America/Paramaribo',
      'Africa/Juba',
      'Africa/Sao_Tome',
      'America/El_Salvador',
      'Asia/Damascus',
      'America/Grand_Turk',
      'Africa/Ndjamena',
      'Indian/Kerguelen',
      'Asia/Bangkok',
      'Asia/Dushanbe',
      'Pacific/Fakaofo',
      'Asia/Dili',
      'Asia/Ashgabat',
      'Africa/Tunis',
      'Pacific/Tongatapu',
      'Europe/Istanbul',
      'America/Port_of_Spain',
      'Pacific/Funafuti',
      'Asia/Taipei',
      'Europe/Kiev',
      'Europe/Uzhgorod',
      'Europe/Zaporozhye',
      'Pacific/Wake',
      'America/New_York',
      'America/Detroit',
      'America/Kentucky/Louisville',
      'America/Kentucky/Monticello',
      'America/Indiana/Indianapolis',
      'America/Indiana/Vincennes',
      'America/Indiana/Winamac',
      'America/Indiana/Marengo',
      'America/Indiana/Petersburg',
      'America/Indiana/Vevay',
      'America/Chicago',
      'America/Indiana/Tell_City',
      'America/Indiana/Knox',
      'America/Menominee',
      'America/North_Dakota/Center',
      'America/North_Dakota/New_Salem',
      'America/North_Dakota/Beulah',
      'America/Denver',
      'America/Boise',
      'America/Phoenix',
      'America/Los_Angeles',
      'America/Anchorage',
      'America/Juneau',
      'America/Sitka',
      'America/Metlakatla',
      'America/Yakutat',
      'America/Nome',
      'America/Adak',
      'Pacific/Honolulu',
      'America/Montevideo',
      'Asia/Samarkand',
      'Asia/Tashkent',
      'America/Caracas',
      'Asia/Ho_Chi_Minh',
      'Pacific/Efate',
      'Pacific/Wallis',
      'Pacific/Apia',
      'Africa/Johannesburg' ];

    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getAllProvider();
    this.getPatientProfileInfo();
    this.getConsentMasterData();
    this.dataEmitter.emit({});
    this.checkforConsentValidation()
    this.userRole = JSON.parse(localStorage.getItem('user')!);

  }

  getPatientProfileInfo() {
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
    };
    this.profileService.getPatientProfile(req).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.patientStatus = res?.status;
        //this.isConsentTaken = res.isConsentTaken=="D" ? 'Ye' : 'No';
        this.isConsentTaken = res.consentStatus;
        this.categories = res.programs?.availablePrograms;
        this.selectedCategories = [];
        this.contactInfos = [];
        for (const item of res?.programs?.selectedPrograms) {
          this.selectedCategories.push({
            programName: item.programName,
            programId: item.programId,
          });
        }
        this.patientNameConsent = res.firstName + ' ' + res.lastName;
        let gender = { label: 'Male', value: 'M' };
        if (res.gender == "M") { gender = { label: 'Male', value: 'M' } }
        else if (res.gender == "F") {
          gender = { label: 'Female', value: 'F' }
        }
        else if (res.gender == "0") {
          gender = { label: 'Others', value: 'O' }
        }
        this.formData = {
          firstName: res.firstName,
          lastName: res.lastName,
          phoneNumber: res.phoneNumber,
          dateOfBirth: res.dateOfBirth,
          address1: res.address.address1,
          address2: res.address.address2,
          zipCode: res.address.zip,
          provider: '',
          casemanager: {
            label: res?.clinician?.firstName + ' ' +  res?.clinician?.lastName+ '(' + res?.clinician?.email + ')',
            id: res?.clinician?.clinicianId,
          },
          mrn: res.mrn,
          connectingDevice: res?.connectingDevice
            ? this.capitalizeFirstLetter(res?.connectingDevice)
            : 'Watch',
          email: res.email,
          persNumber: res.persPhoneNumber,
          chronicConditionName: '',
          timezone: res.timezone,
          //country: this.getCountryByName(res.address.country),
          country: res.address.country,
          //state: this.getStateByName(res.address.state, res.address.country),
          state:res.address.state,
          city: res.address.city,
          addressId: res.address.addressId,
          gender: gender,
        };
        // this.isConsentTaken=res?.consentStatus=='Y'?'Yes':'No'
        this.insurance = res?.insuranceVOList;
        this.chronicCondition = res?.chronicConditions;

        this.selectedProvider = {
          label: res?.physicianName + '(' + res?.physicianEmail + ')',
          id: res?.physicianId,
        };
        this.selectedCasemanager = {
          label: res?.clinician + '(' + res?.clinician?.email + ')',
          id: res?.clinician?.clinicianId,
        };
        this.insurance.forEach(data => {
          data.status = "Update";
          data.patientId = this.patientId;
        })

        res.contactDetails?.forEach((data: any) => {
          this.contactInfos.push({
            id: data.id,
            contactDays: data.contactDays,
            contactFromTime: data.contactFromTime,
            contactToTime: data.contactToTime,
            status: 'Update'
          })
        })
        this.casemanagerId = res?.casemanagerId;
        console.log(this.formData)
        this.getConsentPatientData();
        this.getEmergencyContacts();
        this.getAllAssignedDevice();

        if (this.patientStatus !== 'Active') {
          this.sideMenu = [
            { label: 'Personal Information', value: 'personalsection' },
            { label: 'Address', value: 'addresssection' },
            { label: 'Programs Enrolled', value: 'programsenrolled' },
            { label: 'Chronic Conditions, CPT codes', value: 'cptcodes' },
          ]
        }
      }
    });
  }

  getCountryByName(name: string): ICountry | undefined {
    return Country.getAllCountries().find((item) => item.name === name);
  }

  // getStateByName(name: string, countryName: string): IState | undefined {
  //   console.log('State:', this.formData?.country?.isoCode, name);
  //   if (!this.formData?.country?.isoCode) {
  //     let cnt = Country.getAllCountries().find(
  //       (item) => item.name === countryName
  //     );
  //     console.log('Got country:', cnt);
  //     return State.getStatesOfCountry(cnt?.isoCode).find(
  //       (item) => item.name === name
  //     );
  //   } else {
  //     return State.getStatesOfCountry(this.formData?.country?.isoCode).find(
  //       (item) => item.name === name
  //     );
  //   }
  // }

  capitalizeFirstLetter(str: string): string {
    if (!str) return str;
    if(str == "wireless md")
    {
      return "Wireless MD"
    }
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  filterCountries(event: any) {
    const query = event.query.toLowerCase();
    this.countries = Country.getAllCountries().filter((p) =>
      p.name.toLowerCase().includes(query)
    );
    this.formData.state = "";
    this.cities = [];
    this.formData.city = null;
    this.timeZoneList = [];
    console.log(this.countries)
  }

  filterStates(event: any) {
    const query = event.query.toLowerCase();
    // if (this.formData.country) {
    //   this.states = State.getStatesOfCountry(this.formData?.country?.isoCode);
    //   if (this.formData?.country?.timezones) {
    //     this.timeZoneList = this.formData?.country?.timezones;
    //   }
    //   this.states = State.getStatesOfCountry(
    //     this.formData?.country?.isoCode
    //   ).filter((p) => p.name.toLowerCase().includes(query));
    // }
  }
  // filterCities(event: any) {
  //   const query = event.query.toLowerCase();
  //   if (this.formData?.country && this.formData?.state) {
  //     let countryCode = this.formData?.state.countryCode;
  //     let stateCode = this.formData?.state.isoCode;
  //     this.cities = City.getCitiesOfState(countryCode, stateCode);
  //     this.cities = City.getCitiesOfState(countryCode, stateCode).filter((p) =>
  //       p.name.toLowerCase().includes(query)
  //     );
  //   }
  // }

  filterTimeZones(event: any) {
    // const query = event.query.toLowerCase();
    // if (this.formData?.country && this.formData?.country?.timezones) {
    //   this.timeZoneList = this.formData?.country?.timezones;
    //   this.timeZoneList = this.formData?.country?.timezones.filter(
    //     (p: { zoneName: string }) => p.zoneName.toLowerCase().includes(query)
    //   );
    // }
    const query = event.query.toLowerCase();
    this.timeZoneList = this.timeZoneListFull.filter(feature =>
      feature.toLowerCase().includes(query)
    );
  }

  // onSubmit(form: any) {
  //   if (form.invalid) {
  //     form.control.markAllAsTouched();
  //     return;
  //   }

  //   let checkedPrograms = this.combinePrograms(
  //     this.categories,
  //     this.selectedCategories
  //   );

  //   let cons = this.isConsentTaken == 'Yes' ? true : false;
  //   if (cons) {
  //     if (this.selectedCategories?.length === 0) {
  //       this.messageService.add({
  //         severity: 'error',
  //         summary: 'Error',
  //         detail: 'Please select atleast one program',
  //       });
  //       return;
  //     }
  //   }
  //   let tz = '';
  //   if (typeof this.formData.timezone == 'string') {
  //     tz = this.formData.timezone;
  //   } else if (
  //     typeof this.formData.timezone == 'object' &&
  //     this.formData.timezone !== null
  //   ) {
  //     tz = this.formData.timezone?.zoneName;
  //   }

  //   let ct = '';
  //   if (typeof this.formData.city == 'string') {
  //     ct = this.formData.city;
  //   } else if (
  //     typeof this.formData.city == 'object' &&
  //     this.formData.city !== null
  //   ) {
  //     ct = this.formData.city?.name;
  //   }

  //   let req = {
  //     patientId: this.patientId,
  //     firstName: this.formData.firstName,
  //     lastName: this.formData.lastName,
  //     address: {
  //       addressId: this.formData.addressId,
  //       address1: this.formData.address1,
  //       address2: this.formData.address2,
  //       city: ct,
  //       state: this.formData.state?.name,
  //       zip: this.formData.zipCode,
  //       country: this.formData.country?.name,
  //     },
  //     dob: this.formatDateToISO(new Date(this.formData?.dateOfBirth)),
  //     phoneNumber: this.formData.phoneNumber,
  //     persPhoneNumber: this.formData.persNumber,
  //     mrn: this.formData.mrn,
  //     email: this.formData.email,
  //     connectingDevice: this.formData.connectingDevice?.toLowerCase(),
  //     timezone: tz,
  //     isConsentTaken: cons,
  //     checkedPrograms: checkedPrograms,
  //   };
  //   console.log('Req:', req);
  //   this.loaderService.show();
  //   this.profileService.updateBasicInfo(req).subscribe((res) => {
  //     this.loaderService.hide();
  //     if (res.success) {
  //       this.getPatientProfileInfo();
  //       this.messageService.add({
  //         severity: 'success',
  //         summary: 'Success',
  //         detail: 'Profile updated successfully',
  //       });
  //     } else {
  //       this.messageService.add({
  //         severity: 'error',
  //         summary: 'Error',
  //         detail: 'Failed to update profile',
  //       });
  //     }
  //   });
  // }
  formatDateToISO(date: Date): string {
    return new Date(
      date.getTime() - date.getTimezoneOffset() * 60000
    ).toISOString();
  }

  combinePrograms = (
    allPrograms: SelectedProgram[],
    selected: SelectedProgram[]
  ): SelectedProgram[] => {
    return allPrograms.map((program) => {
      const isSelected = selected.some(
        (selProgram) => selProgram.programId === program.programId
      );
      return { ...program, programStatus: isSelected };
    });
  };

  getConsentPatientData() {
    this.loaderService.show();
    this.profileService
      .getConsentPatientData(this.patientId)
      .subscribe((response) => {
        this.loaderService.hide();
        if (response.status) {
          this.consentDataFound = true;
          this.patientNameConsent = response.patientName;
          this.phoneNumberConsent = response.contactPhone;
          this.emailConsent = response.patientEmail;
        } else if (response.status === false) {
          this.consentDataFound = false;
        }
      });
  }

  getConsentMasterData() {
    this.loaderService.show();
    this.profileService.getConsentMasterData().subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.phoneNumberConsent = res.consentPhone;
        this.emailConsent = res.consentEmail;
      }
    });
  }

  onConsentSubmit(form: any) {
    // if (form.invalid) {
    //   form.control.markAllAsTouched();
    //   return;
    // }

    if (!this.patientNameConsent || !this.phoneNumberConsent || !this.emailConsent) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill the valid details.',
      });
      return
    }
    console.log(this.phoneNumberConsent, this.emailConsent);
    let req: any = {
      patientId: this.patientId,
      patientName: this.patientNameConsent,
      contactPhone: this.phoneNumberConsent,
      contactEmail: this.emailConsent,
    };
    this.loaderService.show();
    this.profileService.updateConsentData(req).subscribe((res) => {
      this.loaderService.hide();
      if (res.status) {
        this.checkforConsentValidation();
        // this.getPatientProfileInfo();
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Consent info updated successfully',
        });
        this.showConsentAdd = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update consent',
        });
      }
    });
  }

  downloadConsentForm() {
    let fileName = this.patientNameConsent + '_Consent_Form.pdf';
    this.profileService
      .downloadConsentForm(this.patientId, fileName)
      .subscribe((res) => {
        this.loaderService.hide();
      });
  }

  getEmergencyContacts() {
    this.profileService
      .getEmergenyContact({ patientId: this.patientId })
      .subscribe((res) => {
        if (res) {
          this.emergencyContacts = res;
          this.emergencyContacts.forEach(data => {
            data.status = "Update"
          })
        }
      });
  }

  showConsentViewPopUp(): void {
    this.showConsentView = true;
  }

  showConsentAddPopUp(): void {
    this.showConsentAdd = true;
  }

  openEmergencyContactPopup() {
    this.emergencyHeader = "Add Emergency Contact";
    this.showEmergencyContactPopup = true;
    this.isEditEmergencyContact = false;
    this.isEditEmergencyContactIndex = -1;
  }

  addContactNumbers() {
    //old working code
    // let payload = {
    //   patientId: this.patientId,
    //   emergencyContacts: [
    //     { name: this.contactName, number: this.contactNumber, selection: true },
    //   ],
    // };
    // this.profileService.addEmergenyContact(payload).subscribe((res) => {
    //   this.showEmergencyContactPopup = false;
    //   this.contactName = '';
    //   this.contactNumber = '';
    //   if (res.success) {
    //     this.getEmergencyContacts();
    //     this.messageService.add({
    //       severity: 'success',
    //       summary: 'Success',
    //       detail: 'Contact number added successfully',
    //     });
    //   } else {
    //     this.messageService.add({
    //       severity: 'error',
    //       summary: 'Error',
    //       detail: 'Failed to add contact number',
    //     });
    //   }
    // });

    //new code
    if (this.isEditEmergencyContact) {
      this.emergencyContacts[this.isEditEmergencyContactIndex].name = this.contactName;
      this.emergencyContacts[this.isEditEmergencyContactIndex].number = this.contactNumber;
    }
    else {
      this.emergencyContacts.push({
        contactId: 0, name: this.contactName, number: this.contactNumber, selection: true, peers: false, status: 'Add'
      });
    }

    this.contactName = this.contactNumber = "";
    this.showEmergencyContactPopup = false;
  }

  validatePhoneNumber() {
    const phoneNumberPattern = /^\+\d{1,3}\d{4,14}$/;
    return phoneNumberPattern.test(this.contactNumber);
  }

  validateInsurance() {
    if (this.insuranceCompany == "" || this.insuranceGroupId == "" || this.insuranceMemberNumber == "") {
      return false
    }
    else {
      return true
    }
  }

  deleteItem(event: Event, type: string, id: number, index: number) {
    console.log(event);
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        //new code
        if (type != 'chronic') {
          if (this.emergencyContacts[index].status != "Add") {
            this.emergencyContacts[index].status = "Delete"
            this.deletedEmergencyContacts.push(this.emergencyContacts[index])
          }
          this.emergencyContacts.splice(index, 1);
        }
        else
          this.chronicCondition.splice(index, 1)

        //oldworking code
        // console.log('Delete Yes');
        // let payload = {
        //   patientId: this.patientId,
        //   itemId: id,
        //   itemName: type,
        // };

        // this.profileService.deleteEmergenyContact(payload).subscribe((res) => {
        //   if (res.success) {
        //     type == 'emergencycontact'
        //       ? this.getEmergencyContacts()
        //       : type == 'chronic'
        //         ? this.getPatientProfileInfo()
        //         : '';
        //     this.messageService.add({
        //       severity: 'success',
        //       summary: 'Success',
        //       detail: 'Record deleted successfully',
        //     });
        //   } else {
        //     this.messageService.add({
        //       severity: 'error',
        //       summary: 'Error',
        //       detail: 'Failed to delete record.',
        //     });
        //   }
        // });
      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }

  editContactNumber(name: string, number: string, contactId: number, index = -1) {
    this.emergencyHeader = "Edit Emergency Contact";
    this.contactName = name;
    this.contactNumber = number;
    this.showEmergencyContactPopup = true;
    this.isEditEmergencyContact = true;
    this.isEditEmergencyContactIndex = index;
    // let payload = {
    //   contactId: contactId,
    //   name: name,
    //   number: number,
    //   selection: true,
    //   peers: false,
    // };
    // this.profileService.editEmergenyContact(payload).subscribe((res) => {
    //   if (res.success) {
    //     this.getEmergencyContacts();
    //     this.messageService.add({
    //       severity: 'success',
    //       summary: 'Success',
    //       detail: 'Contact updated successfully',
    //     });
    //   } else {
    //     this.messageService.add({
    //       severity: 'error',
    //       summary: 'Error',
    //       detail: 'Failed to edit contact',
    //     });
    //   }
    // });
  }

  openInsurancePopup() {
    this.showInsurancePopup = true;
    this.isEditInsuranceDetail = false;
    this.isEditInsuranceDetailIndex = -1;
    this.insuranceHeader = "Add Insurance Details"
  }

  deleteInsurance(event: Event, id: number, index: any) {
    console.log(event);
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        console.log('Delete Yes');

        if (this.insurance[index].status != "Add") {
          this.insurance[index].status = "Delete";
          this.deletedInsurance.push(this.insurance[index])
        }
        this.insurance.splice(index, 1);
        //let payload = { insuranceId: id };
        //old working code
        // this.profileService.deleteInsurance(payload).subscribe((res) => {
        //   if (res.success) {
        //     this.getPatientProfileInfo();
        //     this.messageService.add({
        //       severity: 'success',
        //       summary: 'Success',
        //       detail: 'Record deleted successfully',
        //     });
        //   } else {
        //     this.messageService.add({
        //       severity: 'error',
        //       summary: 'Error',
        //       detail: 'Failed to delete record.',
        //     });
        //   }
        // });
      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }

  editInsurance(
    insuranceId: number,
    insuranceCompany: string,
    insuranceNumber: string,
    insuranceMemberNumber: string,
    index = -1
  ) {
    this.insuranceHeader = "Edit Insurance Details";
    //this.insuranceGroupId = insuranceId;
    this.insuranceCompany = insuranceCompany;
    this.insuranceGroupId = insuranceNumber;
    this.insuranceMemberNumber = insuranceMemberNumber;
    this.isEditInsuranceDetail = true;
    this.isEditInsuranceDetailIndex = index;
    this.showInsurancePopup = true;
    // let payload = {
    //   patientId: this.patientId,
    //   insuranceVOList: [
    //     {
    //       insuranceId: insuranceId,
    //       insuranceCompany: insuranceCompany,
    //       insuranceNumber: insuranceNumber,
    //       insuranceMemberNumber: insuranceMemberNumber,
    //     },
    //   ],
    // };
    // this.profileService.editInsurance(payload).subscribe((res) => {
    //   if (res.success) {
    //     this.getPatientProfileInfo();
    //     this.messageService.add({
    //       severity: 'success',
    //       summary: 'Success',
    //       detail: 'Insurance updated successfully',
    //     });
    //   } else {
    //     console.log(res);
    //     this.messageService.add({
    //       severity: 'error',
    //       summary: 'Error',
    //       detail: res.messages[0],
    //     });
    //   }
    // });
  }

  addInsurance() {

    if (!this.insuranceCompany) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Insurance Company Name reuired',
      });
      return;
    }

    if (!this.insuranceGroupId) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Insurance GroupId reuired',
      });
      return;
    }

    if (!this.insuranceMemberNumber) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Insurance Member Number reuired',
      });
      return;
    }

    //old working code
    // let payload = {
    //   patientId: this.patientId,
    //   insuranceVOList: [
    //     {
    //       insuranceId: 0,
    //       insuranceCompany: this.insuranceCompany,
    //       insuranceNumber: this.insuranceGroupId,
    //       insuranceMemberNumber: this.insuranceMemberNumber,
    //     },
    //   ],
    // };
    // this.profileService.editInsurance(payload).subscribe((res) => {
    //   this.showInsurancePopup = false;
    //   if (res.success) {
    //     this.messageService.add({
    //       severity: 'success',
    //       summary: 'Success',
    //       detail: 'Insurance added successfully',
    //     });
    //     this.getPatientProfileInfo();
    //   } else {
    //     console.log(res);
    //     this.messageService.add({
    //       severity: 'error',
    //       summary: 'Error',
    //       detail: res.messages[0],
    //     });
    //   }
    // });
    //new code
    if (this.isEditInsuranceDetail) {
      this.insurance[this.isEditInsuranceDetailIndex].insuranceCompany = this.insuranceCompany;
      this.insurance[this.isEditInsuranceDetailIndex].insuranceNumber = this.insuranceGroupId;
      this.insurance[this.isEditInsuranceDetailIndex].insuranceMemberNumber = this.insuranceMemberNumber;
    }
    else {
      this.insurance.push({
        insuranceId: 0,
        insuranceCompany: this.insuranceCompany,
        insuranceNumber: this.insuranceGroupId,
        insuranceMemberNumber: this.insuranceMemberNumber,
        status: "Add",
        patientId: this.patientId
      },)
    }

    this.insuranceCompany = this.insuranceGroupId = this.insuranceMemberNumber = "";
    this.showInsurancePopup = false
  }

  openChronicPopup() {
    this.showChronicPopup = true;
    this.profileService.getAvailableChronicConditions().subscribe((res) => {
      this.showInsurancePopup = false;
      if (res.success) {
        this.availableChronicCondition = res.eList.filter(
          (e) =>
            !this.chronicCondition.some(
              (c) =>
                c.chronicConditionName === e.chonicConditionName &&
                c.icdCode === e.icdCode
            )
        );
      }
    });
  }

  addNewChroniCondition() {
    // let cc = this.selectedChronic;
    // let selectedCc: any[] = [];
    // for (const item of cc) {
    //   selectedCc.push({

    //     chronicConditionName: item.chonicConditionName,
    //     icdCode: item.icdCode,
    //   });
    // }
    // if (selectedCc.length == 0) {
    //   return;
    // }
    // let payload = {
    //   patientId: this.patientId,
    //   chronicConditions: selectedCc,
    // };

    // this.profileService.addChronicCondition(payload).subscribe((res) => {
    //   this.showChronicPopup = false;
    //   if (res.success) {
    //     this.messageService.add({
    //       severity: 'success',
    //       summary: 'Success',
    //       detail: 'Chronic Condition added successfully',
    //     });
    //     this.getPatientProfileInfo();
    //   } else {
    //     console.log(res);
    //     this.messageService.add({
    //       severity: 'error',
    //       summary: 'Error',
    //       detail: 'Failed to add chronic condition.',
    //     });
    //   }
    // });

    //newcode
    let cc = this.selectedChronic;
    for (const item of cc) {
      this.chronicCondition.push({
        chonicId: 0,
        chronicConditionName: item.chonicConditionName,
        icdCode: item.icdCode,
      });
    }
    this.selectedChronic = [];
    this.showChronicPopup = false;
  }

  getAllProvider() {
    this.profileService.getAllProvider().subscribe((res) => {
      if (res) {
        this.providerList = [];
        this.casemanagerList = [];
        for (const item of res?.physicianVOList) {
          this.providerList.push({
            label:
              item.firstName + ' ' + item.lastName + '(' + item.email + ')',
            id: item.physicianId,
          });
        }
        for (const item of res?.clinicianVOList) {
          this.casemanagerList.push({
            label:
              item.firstName + ' ' + item.lastName + '(' + item.email + ')',
            id: item.clinicianId,
          });
        }
      }
    });
  }

  updateProvider() {
    let physicianId = this.selectedProvider?.id;
    let payload = { physicianId: physicianId, patientId: this.patientId };
    this.profileService.updateProvider(payload).subscribe((res) => {
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Provider updated successfully',
        });
        this.getPatientProfileInfo();
      } else {
        console.log(res);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to provider condition.',
        });
      }
    });
  }

  getAllAssignedDevice() {
    this.profileService.getAllDevices(this.patientId).subscribe((res) => {
      if (res) {
        this.assignedDeviceList = res?.patientsInfo||[];
      }
    });
  }

  getNames(item: any): string {
    if (item === undefined || item === null) {
      return '';
    }
    return item.map((obj: any) => obj).join(', ');
  }

  filterGender(event: any): void {
    const query = event.query.toLowerCase();
    this.filteredGenders = this.genderList.filter(gender =>
      gender.label.toLowerCase().includes(query)
    );
  }

  toggleAllDays() {
    if (this.allDaysSelected) {
      this.selectedDays = this.daysOfWeek.map((day) => day.value);
    } else {
      this.selectedDays = [];
    }
  }

  updateAllDaysSelected() {
    this.allDaysSelected = this.selectedDays.length === this.daysOfWeek.length;
    // this.contactDays = this.selectedDays;
  }


  scrollToDiv(id: string): void {
    this.selectedMenu = id
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  addContact() {
    this.contactInfos.push({ id: this.contactInfos.length + 1, contactDays: this.selectedDays.join("|"), contactFromTime: this.datePipe.transform(this.contactFromTime ? this.contactFromTime : new Date(), "hh mm a"), contactToTime: this.datePipe.transform(this.contactToTime ? this.contactToTime : new Date(), "hh mm a"), status: 'Add' });
    this.selectedDays = [];
    this.contactFromTime = '';
    this.contactToTime = '';
    this.allDaysSelected = false;

  }
  deleteContact(event: any, id: string, index: any) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        //new code
        if (this.contactInfos[index].status != "Add") {
          this.contactInfos[index].status = "Delete"
          this.deletedContactInfos.push(this.contactInfos[index])
        }
        this.contactInfos.splice(index, 1);


      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }
  onFileSelect(event: any): void {
    this.medImage = event.files[0];
  }


  onSubmitInfo(form: any) {
    this.submitted = true;
    if (form.invalid) {
      form.control.markAllAsTouched();
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please fill the valid details.',
      });
      return;
    }

    let checkedPrograms = this.combinePrograms(
      this.categories,
      this.selectedCategories
    );

    let cons = this.isConsentTaken
    if (cons == "") {

    }
    if (this.selectedCategories?.length === 0 && this.isConsentTaken=='Y') {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select atleast one program from programs enrolled',
      });
      return;
    }
    let tz = '';
    if (typeof this.formData.timezone == 'string') {
      tz = this.formData.timezone;
    } else if (
      typeof this.formData.timezone == 'object' &&
      this.formData.timezone !== null
    ) {
      tz = this.formData.timezone?.zoneName;
    }

    let ct = '';
    if (typeof this.formData.city == 'string') {
      ct = this.formData.city;
    } else if (
      typeof this.formData.city == 'object' &&
      this.formData.city !== null
    ) {
      ct = this.formData.city?.name;
    }

    let emergencycontacts = [...this.emergencyContacts, ...this.deletedEmergencyContacts]
    let insurance = [...this.insurance, ...this.deletedInsurance];
    let contact = [...this.contactInfos, ...this.deletedContactInfos];


    let req = {
      address: {
        addressId: this.formData.addressId,
        housenumber: this.formData.address1,
        area: this.formData.address2,
        zip: this.formData.zipCode,
        city: ct,
        //state: this.formData.state?.name,
        //country: this.formData.country?.name,
         state: this.formData.state,
        country: this.formData.country,
        timezone: tz,
      },
      basicinfo: {
        firstname: this.formData.firstName,
        lastname: this.formData.lastName,
        phonenumber: this.formData.phoneNumber,
        persPhonenumber: this.formData.persNumber,
        zip: this.formData.zipCode,
        dob: this.formatDateToISO(new Date(this.formData?.dateOfBirth)),
        id: this.patientId,
        physicianId: this.selectedProvider.id,
        primaryCaseManagerId: this.formData.casemanager.id,
        mrn: this.formData.mrn,
        connectingDevice: this.formData.connectingDevice?.toLowerCase(),
        email: this.formData.email,
        timezone: tz,
        gender: this.formData.gender?.value || 'M',
        // consentTaken: this.isConsentTaken == "No" ? false : true
        consentStatus: this.isConsentTaken
      },
      emergencyContacts: {
        patientId: this.patientId,
        emergencyContacts: emergencycontacts
      },
      insuranceData: {
        //patientId: this.patientId,
        insuranceVOList: insurance
      },
      chronicConditions: {
        patientId: this.patientId,
        chronicConditions: this.chronicCondition
      },
      programsEnrolled: {
        patientId: this.patientId,
        // consentStatus: cons,
        programs: checkedPrograms,
      },

      contactDetails: {
        patientId: this.patientId,
        prefContactDetails: contact
      },


      // deviceList: {
      //   patientId: this.patientId,
      //   deviceList: this.assignedDeviceList
      // },
      // provider: this.selectedProvider,
      // timezone: tz,
      //phoneNumber: this.formData.phoneNumber,

    };
    console.log('Req:', JSON.stringify(req));
    this.loaderService.show();
    this.profileService.updateBasicInfoNew(req).subscribe((res) => {
      this.loaderService.hide();
      this.submitted = false;
      if (res.success) {
        if (this.patientStatus == 'InActive') {
          this.router.navigate(['/patients']);
        }
        this.resetData();
        this.getPatientProfileInfo();
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Profile updated successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update profile',
        });
      }
    });
  }

  resetData() {
    this.insurance = [];
    this.deletedInsurance = [];
    this.contactInfos = [];
    this.deletedContactInfos = [];
    this.emergencyContacts = [];
    this.deletedEmergencyContacts = [];

  }
  resetForm() {
    this.getPatientProfileInfo();
    this.router.navigate(['/patients']);
  }
  checkforConsentValidation() {
    this.profileService.validateConsent(this.patientId).subscribe((res) => {
      console.log(res)
      if (res && res.status == true) {
        this.isConsentFound = true
      }
      else if (res && res.status == false) {
        this.isConsentFound = false
      }
      //this.isConsent = res.
    })
  }

  trackByFn(index: number, item: any): any {
    return item.id || index;
  }

  unloadScreen()
  {
    return false
  }
}



interface FormData {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  dateOfBirth: string;
  address1: string;
  address2: string;
  // country: ICountry | undefined;
  // state: IState | undefined;
  country: string;
  state: string;
  city: any;
  zipCode: string;
  provider: any;
  mrn: string;
  connectingDevice: string;
  email: string;
  persNumber: string | null;
  chronicConditionName: string;
  timezone: any;
  addressId: number;
  gender?: any;
  casemanager: any;
}
