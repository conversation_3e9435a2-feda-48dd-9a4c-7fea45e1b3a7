<div class="p-fluid p-formgrid grid">
  <div style="text-align: left" class="flex flex-row col-12">
    <div class="col-12 md:col-6 flex-row">
      <!-- <label htmlfor="state">Search By Org/DeviceId</label> -->
      <div class="flex flex-1 flex-row">
        <input pinputtext="" id="org" type="text" placeholder="Search By Org / DeviceId"
          class="p-inputtext p-component p-element" (input)="onInput($event)" [(ngModel)]="searchText" />
        <p-button class="ml-2 " label="Submit" severity="primary" (onClick)="onInput($event)"></p-button>
      </div>
    </div>
    <div class="flex">

    </div>
    <div class="col-12 md:col-6 flex align-items-center justify-content-end">
      <p-dropdown [options]="orgsList" checkmark="true" optionLabel="groupName" optionValue="groupId" [filter]="true"
        [(ngModel)]="selectedOrg" filterBy="groupName" [showClear]="true" placeholder="Select Organization"
        class="mr-2" (onChange)="onOrgChange($event)" *ngIf="userRole==2"/>
      <p-button class="ml-2 " icon="pi pi-download" label="Download List" severity="primary" [outlined]="true"
        (onClick)="exportToExcel('xlsx')"></p-button>
    </div>
  </div>
</div>
<div>
  <div>
    <p-toast />
    <p-confirmDialog />
    <!-- <p-confirmDialog #cd [style]="{ width: '400px' }">
      <ng-template pTemplate="headless" let-message>
        <div class="flex flex-column p-5 surface-overlay border-round">
          <!-- <div class="border-circle bg-warning inline-flex justify-content-center align-items-center h-6rem w-6rem">
            <i class="pi pi-info text-5xl"></i>
          </div> -
          <span class="font-bold text-2xl block mb-2 mt-4">
            {{ message.header }}
          </span>
          <p class="mb-0">{{ message.message }}</p>
          <div class="flex justify-content-end align-items-end gap-2 mt-4">
            <button pButton label="OK" (click)="cd.accept()" class="w-8rem p-button-text"></button>
            <button pButton label="Cancel" (click)="cd.reject()" class="w-8rem p-button-primary"></button>
          </div>
        </div>
      </ng-template>
    </p-confirmDialog> -->
  </div>
  <p-table [value]="response" [totalRecords]="totalDeviceCount" styleClass="p-datatable-gridlines p-datatable-striped"
    [paginator]="true" [rows]="rows" [rowsPerPageOptions]="searchText.length==0?[15,25, 50, 75,100]:[rows]" [responsiveLayout]="'scroll'"
    [loading]="loadingdevices" (onLazyLoad)="loadDevices($event)" [lazy]="true"  [first]="first"
    (onPage)="onPageChange($event)">
    <ng-template pTemplate="header">
      <tr>
        <th>Device Name</th>
        <th>Make & Model</th>
        <th>Measurement</th>
        <th>Device ID</th>
        <th>Organization/Clinic</th>
        <th>Patient Name</th>
        <th>Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-device>
      <tr>
        <td>{{ device?.sourceInventoryForWatchPurchase?.deviceName }}</td>
        <td>
          {{ device?.sourceInventoryForWatchPurchase?.make }}{{ " "
          }}{{ device?.sourceInventoryForWatchPurchase?.model }}
        </td>
        <td>
          <ul>
            <li *ngFor="
                let feature of device?.sourceInventoryForWatchPurchase
                  ?.deviceMeasuresArray
              ">
              {{ feature }}
            </li>
          </ul>
        </td>
        <td>{{ device?.watchAllocatedByAdmin?.imei }}</td>
        <td>{{ device?.groupVo?.groupName }}</td>
        <td>{{ device?.patientName }}</td>
        <td>
          <div class="flex">
            <p-button [label]="'Unallocate'" (click)="unassignDevice($event, device)" severity="danger"
              [outlined]="true" class="p-button-rounded p-button-danger" />
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>