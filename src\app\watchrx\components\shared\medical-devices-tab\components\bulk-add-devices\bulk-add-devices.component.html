  <p-toast />
  <p-confirmDialog />
<div class="p-2">
  <div class="p-fluid p-formgrid grid">
    <div class="field col-12 md:col-6">
      <label for="deviceType" class="font-bold block mb-2">Select the Device Type <span class="p-text-danger">*</span></label>
      <p-dropdown
        [(ngModel)]="selectedDeviceType"
        name="deviceType"
        [options]="deviceTypeList"
        placeholder="Select a device type"
        (onChange)="onDeviceTypeChange(selectedDeviceType)"
      ></p-dropdown>
    </div>
    <div class="field col-12 md:col-6">
      <label for="org" class="font-bold block mb-2">Select the Org/Clinic <span class="p-text-danger">*</span></label>
      <p-dropdown
        [(ngModel)]="selectedOrg"
        name="org"
        [options]="orgList"
        optionLabel="groupName"
        optionValue="groupId"
        placeholder="Select a org/clinic"
        (onChange)="onModelChange(selectedModel)"
      ></p-dropdown>
    </div>
    <div class="field col-12 md:col-6">
      <label for="make" class="font-bold block mb-2">Make <span class="p-text-danger">*</span></label>
      <p-dropdown
        [(ngModel)]="selectedMake"
        name="make"
        [options]="makeList"
        placeholder="Select a make"
        (onChange)="onMakeChange(selectedMake)"
      ></p-dropdown>
    </div>
    <div class="field col-12 md:col-6">
      <label for="model" class="font-bold block mb-2">Model <span class="p-text-danger">*</span></label>
      <p-dropdown
        [(ngModel)]="selectedModel"
        name="model"
        [options]="filtertedModelList"
        optionLabel="label"
        placeholder="Select a model"
        (onChange)="onModelChange(selectedModel)"
      ></p-dropdown>
    </div>
    <div class="field col-12 md:col-6">
      <label for="devcieName" class="font-bold block mb-2">Device Name</label>
      <input
        id="devcieName"
        type="text"
        pInputText
        [(ngModel)]="deviceName"
        name="deviceName"
        [disabled]="true"
      />
    </div>
    <div class="field col-12 md:col-6">
      <label for="quantity" class="font-bold block mb-2">Quantity to add <span class="p-text-danger">*</span></label>
      <input
        id="quantity"
        type="number"
        pInputText
        [(ngModel)]="quantity"
        name="quantity"
        min="1"
      />
    </div>
  </div>
  <!-- <div class="col-12">
    <div class="flex flex-row md:justify-content-start gap-2">
      <p-button
        icon="pi pi-plus-circle"
        label="Add"
        [raised]="true"
        severity="success"
        (onClick)="bulkAddDevice($event)"
        [loading]="loader"
      ></p-button>
    </div>
  </div> -->
</div>
