import { Component, OnInit } from '@angular/core';
import { LazyLoadEvent } from 'primeng/api';
import { TableLazyLoadEvent } from 'primeng/table';
import { CareManager } from '../../api/careManagers';
import { MenuService } from '../../service/menu.service';
import { CareManagersService } from './service/care-managers.service';
import { CardModule } from 'primeng/card';
import { dA } from '@fullcalendar/core/internal-common';
import { Router } from '@angular/router';

@Component({
  selector: 'app-care-managers',
  templateUrl: './care-managers.component.html',
  styleUrl: './care-managers.component.scss',

})
export class CareManagersComponent implements OnInit {
  careManagers: CareManager[] = [];
  totalRecords: number = 0;
  loading: boolean = true;
  debouncedValue: string = "";
  userRole:any;
  firstItemIndex: number = 0;
  searchText:string='';
  rows:number=15;
  isSuperVisor:boolean=true;
  constructor(
    public menuService: MenuService,
    private careManagerService: CareManagersService,
    private router: Router,
  ) { }

  ngOnInit(): void {
    this.menuService.changeMenu('Case Managers');
    if(localStorage.getItem('user'))
    {
      let user:any =localStorage.getItem('user');
      this.userRole = JSON.parse(user)?.roleType;
      this.isSuperVisor = JSON.parse(user)?.superVisorRole
    }
  this.loadCareManagers();
  }

  loadCareManagers($event?: LazyLoadEvent | TableLazyLoadEvent) {
    console.log($event, 'Event...');
    this.loading = true;
    let pageSize = $event?.rows || 10;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    this.careManagerService
      .getUsers(pageNo, pageSize)
      .subscribe((response: CareManager[]) => {
        this.careManagers = response;
        this.loading = false;
        this.totalRecords = response.length
      });
  }

  onInput(event: any): void {
    // this.inputSubject.next(event.target.value);
    this.debouncedValue = event.target.value;
    this.onSearch(this.debouncedValue);
  }

  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.loading = true;
      this.careManagerService.searchUser(searchTerm).subscribe((response: CareManager[]) => {
        this.careManagers = response;
        this.loading = false;
        this.totalRecords = response.length;
        this.rows=response.length;
      }, err => {
        this.loading = false;
      });
    }
    else if (searchTerm !== undefined && searchTerm.length == 0) {
      this.rows=15;
      this.first=0;
      this.loadCareManagers();
    }
  }
  onViewDetails(data: any) {
    let loginResponse = JSON.parse(localStorage.getItem('user')!);
        loginResponse.userId = data.userId
      //loginResponse ;
      localStorage.setItem('user', JSON.stringify(loginResponse));
      this.router.navigate(['/dashboard']);
    // this.careManagerService.updatedUser(data.userId).subscribe((res) => {
    //   let loginResponse = JSON.parse(localStorage.getItem('user')!);
    //     loginResponse.userId = data.userId
    //   //loginResponse ;
    //   localStorage.setItem('user', JSON.stringify(res.loggedinUser));
    //   this.router.navigate(['/dashboard']);
    // })
  }
  first=0
  previousRowsPerPage=15
  onPageChange(event: any) {
    this.firstItemIndex = Math.floor(event.first / event.rows) + 1;
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }
  calculateRowNumber(rowIndex: number): number {
    return this.firstItemIndex + rowIndex + 1;
  }

}
