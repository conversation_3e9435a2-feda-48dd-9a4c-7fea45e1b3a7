import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { AdminBillingReportService } from '../../services/admin-billing-report.service';

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss',
})
export class ReportsComponent implements OnInit {
  loader: boolean = false;
  reportsList: any[] = [];
  orgsList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 10;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  date: Date = new Date();
  search: any;
  selectedOrg: any = '';
  userRole: any;
  user: any;
  constructor(
    private router: Router,
    private adminBillingReportService: AdminBillingReportService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef,
    private fb: FormBuilder
  ) { }

  userForm: FormGroup = this.fb.group({});

  ngOnInit(): void {
    if (localStorage.getItem('user')) {
      this.user = localStorage.getItem('user');
      this.userRole = this.user ? JSON.parse(this.user)?.roleType : null;
      this.selectedOrg = { groupId: JSON.parse(this.user)?.orgId }
    }
    this.cdr.detectChanges();

    if (this.userRole !== 3) {
      this.getOrgsList();
    }
    else {
      this.selectedOrg = { groupId: JSON.parse(this.user)?.orgId }
      this.loadBillingReportList(null, this.selectedOrg.groupId);
    }

  }

  getOrgsList() {
    this.loaderService.show();
    this.adminBillingReportService.getAllOrgsList().subscribe(
      (res) => {
        this.loaderService.hide();
        if (res) {
          console.log(res);
          this.orgsList = res.data!;
          this.loader = false;
          this.selectedOrg = this.orgsList[0];
          this.loadBillingReportList(null, this.orgsList[0].groupId);
          this.cdr.detectChanges();
        }
      },
      (err) => {
        this.loaderService.hide();
        this.loader = false;
      }
    );
  }

  orgChange(event: any) {
    this.selectedOrg = event.value;
    this.loadBillingReportList(null, this.selectedOrg.groupId);
  }

  loadBillingReportList(
    $event?: LazyLoadEvent | TableLazyLoadEvent | null,
    orgId?: any
  ) {
    this.loader = true;
    this.adminBillingReportService.getBillingReportList(orgId).subscribe(
      (res) => {
        if (res) {
          console.log(res);
          this.reportsList = res!;
          this.loader = false;
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  generateReport() {
    let groupId = this.selectedOrg.groupId;
    let formattedDate = this.getFormatedDate();
    this.loader = true;
    this.adminBillingReportService.generateBilling(groupId, formattedDate).subscribe(
      (res) => {
        if (res == 'Success...') {
          console.log(res);
          this.loader = false;
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Billing Generated Completed.',
            life: 3000,
          });
          this.loadBillingReportList(null, this.selectedOrg.groupId);
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  getFormatedDate() {
    const date = new Date(this.date);

    // Format the date to YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(date.getDate()).padStart(2, '0'); // Pad day with leading zero

    // Format the time to HH:MM:SS
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // Combine date and time
    const formattedDate = `${year}-${month}-${day}%20${hours}:${minutes}:${seconds}`;
    return formattedDate;
  }
  DownloadReport(fileUrl: string): void {
    const link = document.createElement('a');
    link.href = fileUrl;
    //link.download = fileName; // optional - suggest file name
    link.target = '_blank'; // open in new tab if needed
    link.click();
  }
  // DownloadReport(id: any) {
  //   this.confirmService.confirm({
  //     message: `Are you sure you want to delete ?`,
  //     header: 'Confirmation',
  //     icon: 'pi pi-info-circle',
  //     acceptButtonStyleClass: 'p-button-danger p-button-text',
  //     rejectButtonStyleClass: 'p-button-text p-button-text',
  //     acceptIcon: 'none',
  //     rejectIcon: 'none',
  //     accept: () => {
  //       this.loaderService.show();
  //       this.adminBillingReportService
  //         .deleteFeature({ roleId: id })
  //         .subscribe((res) => {
  //           this.loaderService.hide();
  //           if (res) {
  //             this.messageService.add({
  //               severity: 'success',
  //               summary: 'Confirmed',
  //               detail: 'Case Manager deleted successfully.',
  //               life: 3000,
  //             });
  //             setTimeout(() => {
  //               this.loadBillingReportList();
  //             }, 1000);
  //           } else {
  //             this.messageService.add({
  //               severity: 'error',
  //               summary: 'Rejected',
  //               detail:
  //                 'Something went wrong',
  //               life: 3000,
  //             });
  //           }
  //         }, err => {
  //           this.loaderService.hide();
  //           this.messageService.add({
  //             severity: 'error',
  //             summary: 'Rejected',
  //             detail:
  //               'Something went wrong',
  //             life: 3000,
  //           });
  //         });
  //     },
  //     reject: () => { },
  //   });
  // }

  // addCaseMaanger() {
  //  this.visible=true
  //   }

  //   onSubmit() {
  //     this.loaderService.show();
  //     this.adminBillingReportService
  //         .saveFeature(this.userForm.value)
  //         .subscribe((res) => {
  //           this.loaderService.hide();
  //           this.userForm.reset();
  //           this.visible=false;
  //           if (res?.success) {
  //             this.messageService.add({
  //               severity: 'success',
  //               summary: 'Confirmed',
  //               detail: 'Feature Added successfully.',
  //               life: 3000,
  //             });
  //             setTimeout(() => {
  //               this.loadFeaturesList();
  //             }, 1000);
  //           } else {

  //             this.messageService.add({
  //               severity: 'error',
  //               summary: 'Rejected',
  //               detail:
  //                 'Something went wrong',
  //               life: 3000,
  //             });
  //           }
  //         },err=>{
  //           this.userForm.reset();
  //           this.visible=false;
  //           this.loaderService.hide();
  //           this.messageService.add({
  //             severity: 'error',
  //             summary: 'Rejected',
  //             detail:
  //               'Something went wrong',
  //             life: 3000,
  //           });
  //         });
  //     }

  // {"roleName":"sample Role","roleDescription":"sampleRole"}
}
