export abstract class Constants {
  static readonly ALERT_COUNT_URL = 'service/patient/dashboardAlertsCount';
  static readonly PATIENT_COUNT_URL = 'service/patient/dashboardPatientCount/';
  static readonly PATIENT_GRAPH_URL = 'service/patient/dashboardPatientGraph/';
  static readonly PATIENT_BILLING_URL =
    'service/patient/patientAddressBillingInfoV2/';
  static readonly PATIENT_TASK_URL = 'service/clinician/getPatientsForTasks';
  static readonly NON_CLOSED_TASK_URL =
    'service/clinician/getDueTasksNotClosed/';
  static readonly CRITICAL_ALERT_URL =
    'service/patient/activeCriticalAlertsCurrentMonth/';
  // static readonly CALENDAR_TASK_URL =
  //   'service/clinician/getTodayCalendarTasks/';
    static readonly CALENDAR_TASK_URL =
    'service/clinician/getTodayCalendarTasksV2/';
  static readonly LATEST_CALLLED_PATIENT_URL =
    'service/patient/latestCalledPatients/';
  static readonly DASHBOARD_STATS_PATIENT_URL =
    'service/patient/dashboraReportByClinicAndCaseManager/';
  static readonly DELETE_TASK_URL = 'service/clinician/deleteCalendarTask/';
  static readonly ENCOUNTER_LIST = 'service/patient/getEncounters/';
  static readonly ADD_ENCOUNTER = 'service/patient/saveEncounter';
  static readonly ACK_ALERT = 'service/patient/updateAlertIsAcknowledged';
  static readonly SEND_MESSAGE = 'service/patient/savesPatientQuestions';
  static readonly GET_REASON_LIST='service/patient/getDisplayableEncounterReasonsAndCodes';
}
