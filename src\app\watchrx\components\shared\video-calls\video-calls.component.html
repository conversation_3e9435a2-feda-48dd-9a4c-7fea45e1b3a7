<p-dialog [(visible)]="openVideoDlg" [modal]="false" [style]="{ width: '900px' }"
  [contentStyle]="{ overflow: 'auto' }" position="top" [maximizable]="true" [header]="patientName"
  (visibleChange)="handleClose()" [header]="'Video Call'">
      <div class="row mt-2">
        <div class="col-12 md:col-12">
          <label htmlfor="state" class="text-600 font-bold m-0">Select Program(s)</label>
          <div class="flex justify-content-left gap-3 mt-2">
            <div class="flex flex-wrap gap-3">
              <div class="flex align-items-center" *ngFor="let option of programs">
                <p-radioButton name="consent" [value]="option.value" [inputId]="option.label.toLowerCase()"  [(ngModel)]="selectedProgram"
                  variant="filled" />
                <label [for]="option.label.toLowerCase()" class="ml-2 text-600 font-bold">{{ option.label }}</label>
              </div>
            </div>
          </div>
        </div>
        <div id="join-flow" class="col-12 md:col-12 mt-2">
          <p-button [rounded]="false" icon="pi pi-video" severity="info" class="mr-1" (click)="getVideoSDKJWT()"
            label="Join Session" [disabled]="this.inSession || !selectedProgram" />
          <p-button id="cancel-video-call" [rounded]="false" label="Cancel" icon="pi pi-phone" severity="danger"
            (click)="handleClose()" class="mr-1" [disabled]="!selectedProgram">
          </p-button>
        </div>
      </div>
      <main>
        <div class="row clearfix">
          <div id="sessionContainer"></div>
        </div>
      </main>
   
</p-dialog>