import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { AddPatient, AddPatientResp } from '../../../../api/addPatient';
import { Constants } from '../add-patient-constants';

@Injectable({
  providedIn: 'root',
})
export class AddPatientService {
  constructor(private http: HttpClient) {}

  addPatient(details: AddPatient): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ADD_PATIENT_URL,
      details
    );
  }

  loadPhysicians(): Observable<any> {
    return this.http.get<any>(environment.BASE_URL + Constants.PHYSICIANS);
  }
}
