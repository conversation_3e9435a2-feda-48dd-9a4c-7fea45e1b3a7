{"name": "watchrx-ui", "version": "1.0.0", "description": "WatchRX UI - Audio Transcription and Patient Management System", "author": "WatchRX Team", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:watchrx-ui": "node dist/watchrx-ui/server/server.mjs", "electron": "node_modules\\.bin\\electron electron.js --dev", "electron-dev": "electron", "electron-build": "npm run build && electron-builder", "electron-build-win": "npm run build && electron-builder --win", "electron-build-win-direct": "electron-builder --win", "electron-build-mac": "npm run build && electron-builder --mac", "electron-build-linux": "npm run build && electron-builder --linux"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^17.0.0", "@angular/animations": "^17.0.5", "@angular/cdk": "^17.0.2", "@angular/common": "^17.0.5", "@angular/compiler": "^17.0.5", "@angular/core": "^17.0.5", "@angular/forms": "^17.0.5", "@angular/platform-browser": "^17.0.5", "@angular/platform-browser-dynamic": "^17.0.5", "@angular/router": "^17.0.5", "@ffmpeg/ffmpeg": "^0.11.6", "@fortawesome/fontawesome-free": "^6.7.2", "@fullcalendar/angular": "^6.1.10", "@fullcalendar/core": "^6.1.11", "@fullcalendar/daygrid": "^6.1.11", "@fullcalendar/interaction": "^6.1.11", "@fullcalendar/list": "^6.1.11", "@fullcalendar/timegrid": "^6.1.11", "@twilio/voice-sdk": "^2.12.1", "@types/fullcalendar": "^3.8.0", "@zoom/videosdk-ui-toolkit": "^1.12.1-1", "bn-ng-idle": "^2.0.5", "chart.js": "^3.3.2", "chartjs-plugin-datalabels": "^2.2.0", "country-state-city": "^3.2.1", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "jsrsasign": "^11.1.0", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ng-idle": "^1.3.2", "primeflex": "^3.3.1", "primeicons": "6.0.1", "primeng": "17.2.0", "prismjs": "^1.29.0", "quill": "^2.0.2", "recorder-js": "^1.0.7", "rxjs": "~7.8.1", "tslib": "^2.5.0", "xlsx": "^0.18.5", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.5", "@angular/cli": "^17.0.5", "@angular/compiler-cli": "^17.0.5", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jasmine": "~4.3.1", "@types/jsrsasign": "^10.5.14", "@types/recorder-js": "^1.0.4", "concurrently": "^9.2.0", "electron": "^37.2.3", "electron-builder": "^26.0.12", "jasmine-core": "~4.6.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.2.2", "wait-on": "^8.0.3"}, "main": "electron.js", "homepage": "./", "build": {"appId": "com.watchrx.ui", "productName": "WatchRX UI", "directories": {"output": "dist-electron"}, "forceCodeSigning": false, "files": ["electron.js", "preload.js", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "signAndEditExecutable": false, "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "WatchRX UI", "runAfterFinish": true}}}