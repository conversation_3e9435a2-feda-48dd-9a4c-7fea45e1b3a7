import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { PrimeNGConfig } from 'primeng/api';
import { BnNgIdleService } from 'bn-ng-idle';
import { MenuService } from './watchrx/service/menu.service';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent implements OnInit {
  constructor(private primengConfig: PrimeNGConfig, private bnIdle: BnNgIdleService, private menuService: MenuService, private router: Router) { }
  LOGOUT_URL: string = '/service/user/logout'
  ngOnInit() {
    this.primengConfig.ripple = true;
     ['mousemove', 'keydown', 'mousedown', 'touchstart'].forEach(event =>
    window.addEventListener(event, () => this.bnIdle.resetTimer())
  );
    this.bnIdle.startWatching(1800).subscribe((isTimedOut: boolean) => {
      this.menuService.doLogout().subscribe((res) => {
        // Remove all cookies
        if (document.cookie && document.cookie !== '') {
          const cookies = document.cookie.split(';');
          for (let cookie of cookies) {
            const eqPos = cookie.indexOf('=');
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
          }
        }

        // Unregister all service workers
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(function (registrations) {
            for (let registration of registrations) {
              registration.unregister();
            }
          });
        }
        setTimeout(() => {
          localStorage.clear();
          //window.location.href = '/login';
        }, 1000)
           this.router.navigate(['/login']);
      }, err => {
        setTimeout(() => {
          localStorage.clear();
          //window.location.href = '/login';
        }, 1000)
         this.router.navigate(['/login']);
      });
    });
  }
}
