import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { CaseManagerListResp } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class AdminCaregiverService {
  constructor(private http: HttpClient) {}

  getCaregiversList(details: string): Observable<CaseManagerListResp> {
    return this.http.get<CaseManagerListResp>(
      environment.BASE_URL + Constants.ADMIN_CAREGIVERS_LIST + details
    );
  }

  deleteCaregiver(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_CAREGIVER,
      details
    );
  }

  toggleCaregiverStatus(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.CHANGE_STATUS_CAREGIVER,
      details
    );
  }

  searchCaregiver(details:any)
  {
      return this.http.get<any>(
        environment.BASE_URL + Constants.SEARCH_CAREGIVER+'4/'+details
      );
  }
}
