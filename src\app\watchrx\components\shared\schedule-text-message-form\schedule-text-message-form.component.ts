import { CommonModule } from '@angular/common';
import { Component, Input, OnInit, QueryList, ViewChildren } from '@angular/core';
import { FormsModule, NgModel } from '@angular/forms';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputMaskModule } from 'primeng/inputmask';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { Program } from '../../../api/patientEncounter';
import { ScheduleMessage } from '../../../api/scheduleTextMessageResp';
@Component({
  selector: 'app-schedule-text-message-form',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    DropdownModule,
    TableModule,
    CommonModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    AvatarModule,
    RadioButtonModule,
    InputNumberModule,
    ConfirmDialogModule,
    CheckboxModule,
    InputMaskModule,
  ],
  templateUrl: './schedule-text-message-form.component.html',
  styleUrl: './schedule-text-message-form.component.scss',
})
export class ScheduleTextMessageFormComponent implements OnInit {
  scheduledTextMessagesId: number = 0;
  selectedProgram: any = '';
  messageDescription: string = '';
  messageType: any = 'Now';
  frequencyType: any = 'One Day';
  days: string[] = [];
  isChecked: boolean = false;
  maxLength: number = 1000;
  oneDayStartDate: Date | null = new Date();
  oneDayStartTime: Date | null = new Date();
  startDate: Date | null = new Date();
  endDate: Date | null = new Date();
  time: Date | null = new Date();
  option1: string = '';
  option2: string = '';
  option3: string = '';

  @Input() programList: Program[] = [];
  @Input() isSechedule: boolean = true;
  @Input() isEnconterNeeded: boolean = true;
  @Input() phoneNumber: string | undefined;
  isEdit: boolean = false;
  selectedDays: string[] = [];
  allDaysSelected: boolean = false;
  @ViewChildren('ngModels') inputs!: QueryList<NgModel>;
  daysOfWeek = [
    { name: 'Mon', value: 'Monday' },
    { name: 'Tue', value: 'Tuesday' },
    { name: 'Wed', value: 'Wednesday' },
    { name: 'Thu', value: 'Thursday' },
    { name: 'Fri', value: 'Friday' },
    { name: 'Sat', value: 'Saturday' },
    { name: 'Sun', value: 'Sunday' },
  ];
  userRole: any;
@Input() isShowSMS=true;
  remainingChars(): number {
    return this.maxLength - (this.messageDescription?.length||0);
  }

  ngOnInit(): void {
    console.log('Sechedule:', this.isShowSMS, this.programList);
    this.days = [];
    this.allDaysSelected = false;
    this.setDefaultTime();
    this.userRole = JSON.parse(localStorage.getItem('user')!);
  }

  setDefaultTime() {
    const now = new Date();
    const minutes = now.getMinutes();
    const roundedMinutes = Math.floor(minutes / 15) * 15;
    now.setMinutes(roundedMinutes, 0, 0);
    this.oneDayStartTime = now;
    this.time = now;
  }

  resetFileds() {
    this.isEdit = false;
    this.selectedProgram = '';
    this.messageDescription = '';
    this.messageType = 'Now';
    this.frequencyType = 'One Day';
    this.days = [];
    this.isChecked = false;
    this.maxLength = 1000;
    this.oneDayStartDate = new Date();
    this.oneDayStartTime = new Date();
    this.startDate = new Date();
    this.endDate = new Date();
    this.time = new Date();
    this.option1 = '';
    this.option2 = '';
    this.option3 = '';
    this.setDefaultTime();
    this.days = [];
    this.allDaysSelected = false;
    this.inputs.forEach(input => input.reset());
    this.isSechedule = this.isSechedule
  }

  editScheduleTextMessage(item: ScheduleMessage) {
    this.days = [];
    this.allDaysSelected = false;
    this.isEdit = true;
    if (item) {
      this.selectedProgram=item?.review;
      this.scheduledTextMessagesId = item?.scheduledTextMessagesId;
      this.messageDescription = item?.question;
      this.messageType = 'Schedule';
      this.frequencyType =
        item?.recurrence === 'daily'
          ? 'Daily'
          : item?.recurrence === 'one day'
          ? 'One Day'
          : 'Weekly';
      if (item?.answer?.length > 0) {
        this.isChecked = true;
        if (item?.answer[0]) {
          this.option1 = item?.answer[0];
        }
        if (item?.answer[1]) {
          this.option2 = item?.answer[1];
        }
        if (item?.answer[2]) {
          this.option3 = item?.answer[2];
        }
      }
      if (item?.dayOfWeek) {
        this.days = this.getFullDaysOfWeek(item?.dayOfWeek);
      }
      if (item?.startDate) {
        this.startDate = this.convertDateStringToDate(item?.startDate);
      }
      if (item?.endDate) {
        this.endDate = this.convertDateStringToDate(item?.endDate);
      }

      if (item?.timeSlots) {
        if (item?.recurrence === 'one day') {
          this.oneDayStartTime = this.convertTimeStringToDateTime(
            item.timeSlots
          );
        } else {
          this.time = this.convertTimeStringToDateTime(item.timeSlots);
        }
      }
      if (item?.review) {
        this.selectedProgram = item?.review;
      }
    }
    this.updateAllDaysSelected();
  }

  convertDateStringToDate(dateString: string): Date {
    const [month, day, year] = dateString.split('-').map(Number);
    return new Date(year, month - 1, day);
  }

  convertTimeStringToDateTime(time: string): Date {
    const [hours, minutes] = time.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0); 
    return date;
  }

  getFullDaysOfWeek(shortDays: string): string[] {
    return shortDays.split('|').map((day) => {
      switch (day) {
        case 'Su':
          return 'Sunday';
        case 'Mo':
          return 'Monday';
        case 'Tu':
          return 'Tuesday';
        case 'We':
          return 'Wednesday';
        case 'Th':
          return 'Thursday';
        case 'Fr':
          return 'Friday';
        case 'Sa':
          return 'Saturday';
        default:
          return day;
      }
    });
  }
  toggleAllDays() {
    if (this.allDaysSelected) {
      this.days = this.daysOfWeek.map((day) => day.value);
    } else {
      this.days = [];
    }
  }

  updateAllDaysSelected() {
    this.allDaysSelected = this.days.length === this.daysOfWeek.length;
  }

}
