import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { AdmiAlertsResponse } from '../../../../api/adminAlerts';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class AdminAlertsService {
  constructor(private http: HttpClient) {}

  getAllOrgsList() {
    return this.http.get<any>(environment.BASE_URL + Constants.GET_ALL_ORGS);
  }

  getAdminAlerts(details: any): Observable<AdmiAlertsResponse> {
    return this.http.post<AdmiAlertsResponse>(
      environment.BASE_URL + Constants.GET_ADMIN_ALERTS,
      details
    );
  }

  getPatienstForAlerts(details: any): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + Constants.GET_PATIENTS_BY_ORG + details
    );
  }

  getAdminAlertsByFilter(details: any): Observable<AdmiAlertsResponse> {
    return this.http.post<AdmiAlertsResponse>(
      environment.BASE_URL + Constants.GET_ALERTS_BY_FILTER,
      details
    );
  }

  exportData(alertRequest: any): Observable<any[]> {
    return this.http.post<any[]>(
      environment.BASE_URL + Constants.DOWNLOAD_XLS,
      alertRequest
    );
  }
}
