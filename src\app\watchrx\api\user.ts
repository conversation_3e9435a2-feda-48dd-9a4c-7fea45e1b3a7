export interface Role {
  roleName: string;
  roleId: number;
  roleDescription: string;
  active: boolean;
}

export interface FeatureEnabled {
  groupName: string;
  groupId: number;
  groupUsrId: number | null;
  groupDescription: string;
  active: boolean;
  roles: Role[];
  roleIds: number[] | null;
  userIds: number[] | null;
  groupIds: number[] | null;
  userId: number | null;
  users: any[];
}

export interface User {
  userName: string;
  password: string | null;
  userId: number;
  roleType: number;
  firstName: string;
  lastName: string;
  picPath: string;
  createdDate: string | null;
  updatedDate: string | null;
  email: string | null;
  availStatus: string;
  featuresEnabled: FeatureEnabled[];
  orgId: number;
  otp: string | null;
  otpEnabled: boolean | null;
  passwordExpired: boolean;
}
