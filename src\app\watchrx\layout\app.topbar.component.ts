import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { User } from '../api/user';
import { MenuService } from '../service/menu.service';
import { LayoutService } from './service/app.layout.service';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { TableModule } from 'primeng/table';
import { LoaderService } from '../loader/loader/loader.service';
@Component({
  selector: 'app-topbar',
  templateUrl: './app.topbar.component.html',
  providers: [ConfirmationService, MessageService],
  styles: [`
  .layout-topbar
  {
    height:4.3rem !important;
  }
   .alerticon{
    color:#a855f7;
    color: #a855f7;
    border: 1px solid #a855f7;
    padding: 10px;
    border-radius: 34px;
    ::ng-deep .p-badge {
      background-color: red;
      top: 9px;
      right: 2px;
    }
   }
    `],

})
export class AppTopBarComponent implements OnInit {
  items!: MenuItem[];

  @ViewChild('menubutton') menuButton!: ElementRef;
  @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;
  @ViewChild('topbarmenu') menu!: ElementRef;

  orgName: string | undefined;
  alertCount: any = null;
  alertCountList: any[] = [];
  private alertInterval: any;
  chatSideVisible: boolean = false;
  user: User | undefined;
  constructor(
    public layoutService: LayoutService,
    private menuService: MenuService,
    private router: Router,
    public confirmationService: ConfirmationService,
    private messageService: MessageService,
    private loaderService:LoaderService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(localStorage.getItem('user')!);
    let features = this.user?.featuresEnabled.find(
      (feature) => feature.groupId === this.user?.orgId
    );
    this.orgName = features?.groupName;
    if (this.user?.roleType != 2) {
      this.alertInterval = setInterval(() => {
        this.getChatCount();
      }, 60000);
      this.getChatCount1();
    }
  }

  logout(event: Event) {
    this.confirmationService.confirm({
      message: 'Are you sure that you want to logout?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
     accept: () => {
        this.menuService.doLogout().subscribe({
          next: (res) => {
            console.log('Logout successful', res);
            if (document.cookie && document.cookie !== '') {
              const cookies = document.cookie.split(';');
              for (let cookie of cookies) {
                const eqPos = cookie.indexOf('=');
                const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
              }
            }
            // Unregister all service workers
            if ('serviceWorker' in navigator) {
              navigator.serviceWorker.getRegistrations().then(function (registrations) {
                for (let registration of registrations) {
                  registration.unregister();
                }
              })
            }
            setTimeout(() => {
              console.log('Logout successful');
              localStorage.clear();
              //window.location.href = '/login'
            }, 100)
              this.router.navigate(['/login']);
          }, error: (err) => {
            setTimeout(() => {
              console.log('Logout successful');
              localStorage.clear();
             // window.location.href = '/login'
            }, 100)
            this.router.navigate(['/login']);
          }
        })
      },
      reject: () => {

      }
    });

  }

  getChatCount1() {
    this.menuService.getChatCount().subscribe((res) => {
      this.alertCount = res.length > 0 ? res.length : null;
      this.alertCountList = res;
      localStorage.setItem('alertCount', res.length || 0);
    });
  }

  getChatCount() {
    this.menuService.getChatCount().subscribe((res) => {
      if (res.length > 0) {
        let getLocalValue = localStorage.getItem('alertCount') || 0;
        if (getLocalValue < res.length) {
          this.messageService.add({
            severity: 'success',
            summary: '',
            detail: 'Notification received',
          });
        }
      }
      localStorage.setItem('alertCount', res.length || 0);
      this.alertCount = res.length > 0 ? res.length : null;
      this.alertCountList = res;
    });
  }
  clearChats() {
    this.menuService.clearChatCount().subscribe((res) => {
      this.chatSideVisible = false;
      if (res == 'SUCCESS') {
        localStorage.removeItem('alertCount');
        this.alertCount = null;
        this.alertCountList = [];
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Notification cleared successfully',
        });
      }
      else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Error while clearing notification',
        });
      }
    });
  }
  ngOnDestroy() {
    if (this.alertInterval) {
      clearInterval(this.alertInterval);
    }
  }
}
