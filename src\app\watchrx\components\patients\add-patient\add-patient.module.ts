import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ChartModule } from 'primeng/chart';
import { DropdownModule } from 'primeng/dropdown';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelMenuModule } from 'primeng/panelmenu';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { AddPatientComponent } from './add-patient.component';
import { AddPatientRoutingModule } from './add-patient.routing.module';
import { SidebarModule } from 'primeng/sidebar';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ChartModule,
    MenuModule,
    TableModule,
    StyleClassModule,
    PanelMenuModule,
    ButtonModule,
    TableModule,
    InputGroupModule,
    InputGroupAddonModule,
    DropdownModule,
    CalendarModule,
    AddPatientRoutingModule,
    InputTextModule,
    MultiSelectModule,
    AutoCompleteModule,
    FormsModule,
    ReactiveFormsModule,
    ToastModule,
    SidebarModule
  ],
  declarations: [AddPatientComponent],
  providers: [ConfirmationService,MessageService],
})
export class AddPatientModule {}
