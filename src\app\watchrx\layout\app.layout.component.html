<div class="layout-wrapper" [ngClass]="containerClass">

  <div class="layout-sidebar">
    <app-sidebar style="height: calc(100vh - 40px);display: flex;"></app-sidebar>
    <!-- <div class="layout-footer" style="padding-top: 0.6rem;">App Version : {{version}}</div> -->
  </div>
  <div class="layout-main-container" style="overflow-y: hidden;">

    <div class="layout-main">
      <app-topbar></app-topbar>
      <router-outlet></router-outlet>
    </div>
  </div>
  <app-config></app-config>
  <div class="fixed-header"><app-footer></app-footer></div>

  <div class="layout-mask"></div>
</div>