import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import saveAs from 'file-saver';
import { catchError, map, Observable, throwError } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import {
  AvialableChronicConditions,
  ConsentData,
  DeviceResponse,
  GenericResponse,
  PatientConsentData,
  PatientData,
  PhysicianResponse,
} from '../../../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class PatientEditProfileService {
  constructor(private http: HttpClient) {}

  getPatientProfile(details: any): Observable<PatientData> {
    return this.http.post<PatientData>(
      environment.BASE_URL + Constants.PATIENT_INFO_URL,
      details
    );
  }

  updateBasicInfo(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_PROFILE,
      details
    );
  }

  updateBasicInfoNew(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SAVE_PROFILE1,
      details
    );
  } 
  getConsentMasterData(): Observable<ConsentData> {
    return this.http.get<ConsentData>(
      environment.BASE_URL + Constants.CONSENT_DATA
    );
  }

  getConsentPatientData(id: number): Observable<PatientConsentData> {
    return this.http.get<PatientConsentData>(
      environment.BASE_URL + Constants.PATIENT_CONSENT_DATA + id
    );
  }

  updateConsentData(req: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_CONSENT_DATA,
      req
    );
  }

  downloadConsentForm(patientId: number, fileName: string): Observable<any> {
    const url = environment.BASE_URL + Constants.DOWNLOAD_CONSENT + patientId;
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.get(url, { headers, responseType: 'arraybuffer' }).pipe(
      map((response: BlobPart) => {
        const blob = new Blob([response], {
          type: 'application/pdf',
        });
        saveAs(blob, fileName);
        return { success: true };
      }),
      catchError((error) => {
        return throwError({ success: false });
      })
    );
  }

  getEmergenyContact(details: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.EMERGENY_CONTACT,
      details
    );
  }

  addEmergenyContact(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_EMERGENCY_CONTACT,
      details
    );
  }

  deleteEmergenyContact(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_EMERGENCY_CONTACT,
      details
    );
  }

  editEmergenyContact(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.EDIT_EMERGENCY_CONTACT,
      details
    );
  }

  deleteInsurance(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_INSURANCE,
      details
    );
  }

  editInsurance(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.EDIT_INSURANCE,
      details
    );
  }

  getAvailableChronicConditions(): Observable<AvialableChronicConditions> {
    return this.http.get<AvialableChronicConditions>(
      environment.BASE_URL + Constants.AVAILABLE_CHRONIC_CONDITIONS
    );
  }

  addChronicCondition(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_CHRONIC,
      details
    );
  }

  getAllProvider(): Observable<PhysicianResponse> {
    return this.http.get<PhysicianResponse>(
      environment.BASE_URL + Constants.PROVIDER_LIST
    );
  }

  updateProvider(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_PROVIDER,
      details
    );
  }
  getAllDevices(id: number): Observable<DeviceResponse> {
    return this.http.get<DeviceResponse>(
      environment.BASE_URL + Constants.ALL_DEVICES + id
    );
  }
  validateConsent(patientId:any){
    return this.http.get<GenericResponse>(
      environment.BASE_URL + Constants.VALIDATE_CONSENT+'/'+patientId
    );
  }
}
