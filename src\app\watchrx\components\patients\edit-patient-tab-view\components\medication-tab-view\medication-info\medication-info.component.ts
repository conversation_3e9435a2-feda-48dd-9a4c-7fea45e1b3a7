import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { Prescription } from '../../../../../../api/medications';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { MedicationsService } from './service/medications.service';

@Component({
  selector: 'app-medication-info',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    RadioButtonModule,
    FileUploadModule,
    CheckboxModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    ConfirmDialogModule,
    SidebarModule,
  ],
  templateUrl: './medication-info.component.html',
  styleUrl: './medication-info.component.scss',
  providers: [ConfirmationService, MessageService],
})
export class MedicationInfoComponent implements OnInit {

  constructor(
    private medicationService: MedicationsService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) { }

  visible: boolean = false;
  prescriptionId: number | null = null;
  isEditMeds: boolean = false;
  patientId: number = 0;
  medicationList: Prescription[] = [];
  medicationName: string = '';
  dosage: string = '';
  color: string = '';
  quantity: string | number = '';
  description: string = '';
  medImage: any | undefined;
  selectedMedicationType: any = '';
  relativeInterval: any = 'Regular';
  selectedDays: string[] = [];
  allDaysSelected: boolean = false;

  fixedMedications: any[] = [];

  slotsType: any[] = [
    { name: 'Before Food' },
    { name: 'After Food' },
    { name: 'Anytime' },
  ];

  isEarlyMorningChecked: boolean = false;
  emQnty: any = '';
  emTimeSlot: any = '';

  isBreakfastChecked: boolean = false;
  bfQnty: any = '';
  bfTimeSlot: any = '';

  isLunchChecked: boolean = false;
  lunchQnty: any = '';
  lunchTimeSlot: any = '';

  isAfsChecked: boolean = false;
  afsQnty: any = '';
  afsTimeSlot: any = '';

  isDinnerChecked: boolean = false;
  dinnerQnty: any = '';
  dinnerTimeSlot: any = '';

  isBedChecked: boolean = false;
  bedQnty: any = '';
  bedTimeSlot: any = '';

  daysOfWeek = [
    { name: 'Mon', value: 'Mo' },
    { name: 'Tue', value: 'Tu' },
    { name: 'Wed', value: 'We' },
    { name: 'Thu', value: 'Th' },
    { name: 'Fri', value: 'Fr' },
    { name: 'Sat', value: 'Sa' },
    { name: 'Sun', value: 'Su' },
  ];

  medicationTypeList = [
    {
      name: 'Tablet',
      id: 1,
    },
    {
      name: 'Injection',
      id: 2,
    },
    {
      name: 'Inhaler',
      id: 3,
    },
    {
      name: 'Nasal Spray',
      id: 4,
    },
    {
      name: 'Syrup',
      id: 5,
    },
    {
      name: 'Cream',
      id: 6,
    },
    {
      name: 'Ointment',
      id: 7,
    },
    {
      name: 'Powder',
      id: 8,
    },
  ];

  SideBarHeader = "Add Medication";
  @ViewChild('fileUpload') fileUpload!: FileUpload;
  @ViewChild('fileUploader') fileUploader!: FileUpload;
  imagePreview: string = "";
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getMedicationList();
  }

  getMedicationList() {
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
    };
    this.medicationService.getMedicationInfo(req).subscribe((res) => {
      this.loaderService.hide();
      if (res?.success) {
        this.medicationList = res?.prescriptions;
      }
    });
  }

  generateErrorMessage(text: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: text,
      life: 3000,
    });
  }

  convertTo24Hours(givenTime: string): string {
    const [time, modifier] = givenTime.split(' ');
    let [hours, minutes] = time.split(':');

    if (modifier === 'PM' && hours !== '12') {
      hours = String(parseInt(hours, 10) + 12);
    }
    if (modifier === 'AM' && hours === '12') {
      hours = '00';
    }

    return `${hours.padStart(2, '0')}:${minutes}`;
  }

  addNewMedication() {
    if (this.medicationName === undefined || this.medicationName === '') {
      this.generateErrorMessage('Name of the medication is necessary');
      return;
    }
    // if (this.dosage === undefined || this.dosage === '') {
    //   this.generateErrorMessage('Dosage of the medication is necessary');
    //   return;
    // }
    // if (this.color === undefined || this.color === '') {
    //   this.generateErrorMessage('Medicine color is required.');
    //   return;
    // }


    if (
      this.selectedMedicationType === undefined ||
      this.selectedMedicationType === ''
    ) {
      this.generateErrorMessage('Medicine type is required.');
      return;
    }

    if (this.selectedDays?.length === undefined || this.selectedDays?.length === 0) {
      this.generateErrorMessage(
        'Day of the week to take medication is required.'
      );
      return;
    }

    let ts: any[] = [];
    let qt: any[] = [];
    let rlt: any[] = [];
    if (this.isEarlyMorningChecked) {
      ts.push('EarlyMorning');
      if (this.emQnty !== '') {
        qt.push(this.emQnty);
      }
      if (this.emTimeSlot?.name) rlt.push(this.emTimeSlot?.name);
    }
    if (this.isBreakfastChecked) {
      ts.push('Breakfast');
      if (this.bfQnty !== '') {
        qt.push(this.bfQnty);
      }
      if (this.bfTimeSlot?.name) rlt.push(this.bfTimeSlot?.name);
    }
    if (this.isLunchChecked) {
      ts.push('Lunch');
      if (this.lunchQnty !== '') {
        qt.push(this.lunchQnty);
      }
      if (this.lunchTimeSlot?.name) rlt.push(this.lunchTimeSlot?.name);
    }

    if (this.isAfsChecked) {
      ts.push('AfternoonSnack');
      if (this.afsQnty !== '') {
        qt.push(this.afsQnty);
      }
      if (this.afsTimeSlot?.name) rlt.push(this.afsTimeSlot?.name);
    }
    if (this.isDinnerChecked) {
      ts.push('Dinner');
      if (this.dinnerQnty !== '') {
        qt.push(this.dinnerQnty);
      }
      if (this.dinnerTimeSlot?.name) rlt.push(this.dinnerTimeSlot?.name);
    }
    if (this.isBedChecked) {
      ts.push('Bed');
      if (this.bedQnty !== '') {
        qt.push(this.bedQnty);
      }
      if (this.bedTimeSlot?.name) rlt.push(this.bedTimeSlot?.name);
    }

    let tsStr;
    let qtStr;
    let rltStr;
    if (this.relativeInterval === 'Regular') {
      if (
        this.quantity === undefined ||
        this.quantity === '' || this.quantity === '0' || this.quantity == 0
      ) {
        this.generateErrorMessage('Medicine quantity should be atleast 1.');
        return;
      }
      if (ts.length === 0) {
        this.generateErrorMessage(
          'Medication frequency for the day is necessary for every medication'
        );
        return;
      }

      /* if (qt.length === 0 || ts.length != qt.length) {
         this.generateErrorMessage(
           'Quantity of medicine to be taken is necessary for every medication'
         );
         return;
       }
 
       if (rlt.length === 0 || ts.length != rlt.length) {
         this.generateErrorMessage(
           'Time of taking medicine is necessary for every medication'
         );
         return;
       } */
      tsStr = ts.join('|');
      qtStr = qt.join('|');
      rltStr = rlt.join('|')?.replaceAll(' ', '');
    } else if (this.relativeInterval === 'Fixed') {
      console.log('fixedMedications', this.fixedMedications);
      if (this.fixedMedications?.length === 0) {
        this.generateErrorMessage('Timeslots of medication is required.');
        return;
      }
      const timesData = this.fixedMedications
        .filter((item) => item.time)
        .map((item) => this.convertTo24Hours(
          new Date(item?.time).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          }))
        );
      console.log('timesData', timesData);
      if (timesData === undefined || timesData.length === 0) {
        this.generateErrorMessage(
          'Time of taking medicine is necessary for every medication'
        );
        return;
      }
      tsStr = timesData.join('|');
      const qntData = this.fixedMedications
        .filter((item) => item.quantity && item.quantity != "0")
        .map((item) => item.quantity);
      console.log('qntData', qntData);
      if (
        qntData === undefined ||
        qntData.length === 0 ||
        timesData.length !== qntData.length
      ) {
        this.generateErrorMessage(
          'Quantity of medicine to be taken is necessary and should be atleast 1 for every medication'
        );
        return;
      }
      qtStr = qntData.join('|');

    }
    const result = this.getDaysObjectAndString(this.selectedDays);

    let payload = {
      prescriptionId: this.prescriptionId,
      imageExists: false,
      name: this.medicationName,
      dosage: this.dosage,
      color: this.color,
      type: this.selectedMedicationType,
      existingOrNewschedule: 'New',
      beforeOrAfterFood: this.relativeInterval === 'Fixed' ? 'Fixed' : 'Before',
      timeOfMedicine: {
        earlymorning: this.isEarlyMorningChecked,
        breakfast: this.isBreakfastChecked,
        lunch: this.isLunchChecked,
        afternoonsnack: this.isAfsChecked,
        dinner: this.isDinnerChecked,
        bed: this.isBedChecked,
      },
      timeSlots: tsStr,
      quantities: this.relativeInterval === 'Fixed' ? qtStr : this.quantity,
      medTimeRelativeToFood: rltStr || '',
      day: result.daysObj,
      daysOfWeek: result.daysStr,
      patientId: this.patientId,

    };
    this.loaderService.show();
    this.medicationService
      .addorUpdateMedicationInfo(payload, this.isEditMeds)
      .subscribe((res) => {
        this.loaderService.hide();
        if (res.success) {

          this.uploadMedicationImage(this.isEditMeds ? this.prescriptionId : res.createdPrescription);
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'prescription created successfully',
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to create prescription',
          });
        }
      });
  }

  onFileSelect(event: any): void {
    this.imagePreview = "";
    this.medImage = event.files[0];
    console.log(this.medImage, 'image');
    this.imagePreview = URL.createObjectURL(this.medImage);
  }

  uploadMedicationImage(medicationId: any) {
    this.visible = false;
    if (this.medImage) {
      const formData = new FormData();
      formData.append('imageFile', this.medImage);
      formData.append('medicationId', medicationId);
      this.medicationService
        .updateMedicationImage(formData)
        .subscribe((res) => {
          this.getMedicationList();
          this.loaderService.hide();
          if (res.success) {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Image uploaded successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to update image',
            });
          }
        });
    } else {
      this.getMedicationList();
      console.log('medication saved without image');
    }
  }

  deleteMeidcation(event: any, medicationId: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-primary',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.medicationService
          .deleteMedicationInfo({
            patientId: 0,
            itemId: medicationId,
            itemName: '',
          })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res.success) {
              this.getMedicationList();
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Precription deleted successfully',
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete precription',
              });
            }
          });
      },
      reject: () => { },
    });
  }

  getDaysObjectAndString(inputDays: string[]): {
    daysObj: { [key: string]: boolean };
    daysStr: string;
  } {
    const daysMapping: { [key: string]: string } = {
      Su: 'sunday',
      Mo: 'monday',
      Tu: 'tuesday',
      We: 'wednesday',
      Th: 'thursday',
      Fr: 'friday',
      Sa: 'saturday',
    };

    let daysObj: { [key: string]: boolean } = {
      sunday: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
    };

    inputDays.forEach((day) => {
      const fullName = daysMapping[day];
      if (fullName) {
        daysObj[fullName] = true;
      }
    });

    const daysStr = inputDays.join('|');

    return { daysObj, daysStr };
  }

  getDailyFrequency(item: {
    timeSlots: string;
    quantities: string;
    medTimeRelativeToFood?: string | null;
  }): string {
    const timeSlots = item.timeSlots?.split('|');
    const quantities = item.quantities?.split('|');
    let medTimeRelativeToFood: string[] = [];

    if (
      item.medTimeRelativeToFood &&
      item.medTimeRelativeToFood.trim().length !== 0
    ) {
      medTimeRelativeToFood = item.medTimeRelativeToFood?.split('|');
    }

    const frequency: string[] = [];
    console.log('timeSlots', timeSlots);
    for (let i = 0; i < timeSlots?.length; i++) {
      if (medTimeRelativeToFood.length === 0) {
        frequency.push(
          `${this.convertTo12HourFormat(timeSlots[i])} - (${quantities[i]})`
        );
      } else {
        frequency.push(
          `${timeSlots[i]} - ${medTimeRelativeToFood[i]} - (${quantities[i]})`
        );
      }
    }
    return frequency.join('<br>');
  }

  convertTo12HourFormat(time24Hour: string): string {
    return moment(time24Hour, 'HH:mm').format('hh:mm A');
  }

  showDialog() {
    this.fileUpload?.clear();
    this.fileUploader?.clear();
    this.SideBarHeader = "Add Medication"
    this.resetFields();
    this.imagePreview = '';
    this.selectedMedicationType = '';
    this.relativeInterval = 'Regular';
    this.isEditMeds = false;
    this.prescriptionId = 0;
    this.medImage = ''
    this.visible = true;
    if (this.fileUpload) {
      this.fileUpload.clear(); // Reset the file upload
    }
    if (this.fileUploader) {
      this.fileUploader.clear(); // Reset the file upload
    }
  }

  toggleAllDays() {
    if (this.allDaysSelected) {
      this.selectedDays = this.daysOfWeek.map((day) => day.value);
    } else {
      this.selectedDays = [];
    }
  }

  updateAllDaysSelected() {
    this.allDaysSelected = this.selectedDays.length === this.daysOfWeek.length;
  }

  resetFields() {
    this.isEarlyMorningChecked = false;
    this.emQnty = '';
    this.emTimeSlot = '';

    this.isBreakfastChecked = false;
    this.bfQnty = '';
    this.bfTimeSlot = '';

    this.isLunchChecked = false;
    this.lunchQnty = '';
    this.lunchTimeSlot = '';

    this.isAfsChecked = false;
    this.afsQnty = '';
    this.afsTimeSlot = '';

    this.isDinnerChecked = false;
    this.dinnerQnty = '';
    this.dinnerTimeSlot = '';

    this.isBedChecked = false;
    this.bedQnty = '';
    this.bedTimeSlot = '';

    this.medicationName = '';
    this.color = '';
    this.quantity = '';
    this.description = '';
    this.dosage = '';
    this.isEditMeds = false;
    this.prescriptionId = 0;

    this.selectedDays = [];
    this.allDaysSelected = false;

    this.fixedMedications = [];
    this.medImage = ''
  }

  addTime() {
    this.fixedMedications.push({
      time: new Date(1970, 1, 1, 6, 0, 0),
      quantity: '',
    });
  }

  removeTime(item: string): boolean {
    const indexToRemove = this.fixedMedications.indexOf(item);
    if (indexToRemove > -1) {
      this.fixedMedications.splice(indexToRemove, 1);
    }
    return false;
  }

  editMedicationView(item: Prescription) {
    this.SideBarHeader = "Edit Medication";
    this.fileUpload?.clear();
    this.fileUploader?.clear();
    this.medImage = ''
    this.resetFields();
    this.imagePreview = '';
    this.visible = true;
    this.isEditMeds = true;
    this.prescriptionId = item?.prescriptionId;
    this.medicationName = item?.medicineName;
    this.color = item?.color;
    this.quantity = item?.quantity;
    this.description = item?.description;
    this.dosage = item?.dosage;
    this.selectedMedicationType = item?.medicineForm;
    this.relativeInterval =
      item?.beforeOrAfterFood === 'Fixed' ? 'Fixed' : 'Regular';
    this.selectedDays = item?.daysOfWeek?.split('|');
    if (this.selectedDays?.length === 7) {
      this.allDaysSelected = true;
    }
    let timeSlots = item?.timeSlots?.split('|');
    let quantities = item?.quantities?.split('|');
    //console.log(timeSlots, quantities);
    if (this.relativeInterval === 'Regular') {
      let tms = item?.medTimeRelativeToFood;
      for (let i = 0; i < timeSlots.length; i++) {
        let tms = timeSlots[i];
        let bOrA = { name: this.replaceSpace(item?.medTimeRelativeToFood) };
        let qnt = quantities[i];
        if (tms === 'EarlyMorning' || tms === 'morning') {
          this.isEarlyMorningChecked = true;
          this.emTimeSlot = bOrA;
          this.emQnty = qnt;
          continue;
        }
        if (tms.toUpperCase() === 'Breakfast'.toUpperCase()) {
          this.isBreakfastChecked = true;
          this.bfTimeSlot = bOrA;
          this.bfQnty = qnt;
          continue;
        }
        if (tms.toUpperCase().toUpperCase() === 'Lunch'.toUpperCase()) {
          this.isLunchChecked = true;
          this.lunchTimeSlot = bOrA;
          this.lunchQnty = qnt;
          continue;
        }
        if (tms.toUpperCase() === 'AfternoonSnack'.toUpperCase()) {
          this.isAfsChecked = true;
          this.afsTimeSlot = bOrA;
          this.afsQnty = qnt;
          continue;
        }
        if (tms.toUpperCase() === 'Dinner'.toUpperCase()) {
          this.isDinnerChecked = true;
          this.dinnerTimeSlot = bOrA;
          this.dinnerQnty = qnt;
          continue;
        }
        if (tms.toUpperCase() === 'Bed'.toUpperCase() || tms.toUpperCase() === 'BedTime'.toUpperCase()) {
          this.isBedChecked = true;
          this.bedTimeSlot = bOrA;
          this.bedQnty = qnt;
          continue;
        }
      }
    }

    if (this.relativeInterval === 'Fixed') {
      this.fixedMedications = timeSlots.map((time, index) => {
        const date = moment(
          `1970-02-01 ${time} `, //+0530
          'YYYY-MM-DD HH:mm Z'
        ).toDate();
        return {
          time: date,
          quantity: quantities[index],
        };
      });
      console.log('this.fixedMedications', this.fixedMedications);
    }

    if (this.fileUpload) {
      this.fileUpload.clear();
    }
    this.imagePreview = item?.imageUrl;

  }

  replaceSpace(originalString: string) {
    return originalString.replace(/([a-z])([A-Z])/g, '$1 $2');
  }
  getDays(data: string) {
    let daysArray = data?.split('|');
    let fullDaysArray = daysArray?.map((day) => {
      switch (day.toLowerCase()) {
        case 'su':
          return 'Sun';
        case 'mo':
          return 'Mon';
        case 'tu':
          return 'Tue';
        case 'we':
          return 'Wed';
        case 'th':
          return 'Thu';
        case 'fr':
          return 'Fri';
        case 'sa':
          return 'Sat';
        default:
          return day;
      }
    });
    let days = fullDaysArray?.join(',');
    return days;
  }

  getTimes(item: {
    timeSlots: string;
    quantities: string;
    medTimeRelativeToFood?: string | null;
  }): number {
    const timeSlots = item.timeSlots?.split('|');
    const quantities = item.quantities?.split('|');
    let medTimeRelativeToFood: string[] = [];
    if (
      item.medTimeRelativeToFood &&
      item.medTimeRelativeToFood.trim().length !== 0
    ) {
      medTimeRelativeToFood = item.medTimeRelativeToFood.split('|');
    }
    const frequency: string[] = [];
    for (let i = 0; i < timeSlots?.length; i++) {
      if (medTimeRelativeToFood.length === 0) {
        frequency.push(`${timeSlots[i]} - ${quantities[i]}`);
      } else {
        frequency.push(
          `${timeSlots[i]} - ${medTimeRelativeToFood[i]} - ${quantities[i]}`
        );
      }
    }
    return frequency.length;
  }

  uploadPrescriptions(event: any) {
    this.loaderService.show();
    let medImage = event.files[0]
    if (medImage) {
      const formData = new FormData();
      formData.append('file', medImage);
      formData.append('patientId', this.patientId + "");
      this.medicationService
        .medicationUpload(formData)
        .subscribe((res) => {
          this.loaderService.hide();
          this.fileUpload?.clear();
          this.getMedicationList();
          if (res.success) {
            this.messageService.add({
              severity: 'success',
              summary: 'Confirmed',
              detail: 'Medication uploaded successfully.',
              life: 3000,
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Failed to upload Medication list',
              life: 3000,
            });
            return;
          }

        }, err => {
          this.fileUpload?.clear();
          this.imagePreview = '';
          this.loaderService.hide();
        });
    }
  }

  onCheckboxChange(event: any, item: Prescription, index: number) {
    //console.log('onCheckboxChange', event, item);
    let filterData = this.medicationList.filter(item => {
      if (!item.isReviewed) return true;
      if (!item.quantities || item.quantities.trim() === '') return true;
      const hasZeroOrNegative = item.quantities
        .split('|')
        .some(qty => parseInt(qty.trim(), 10) <= 0);
      if (hasZeroOrNegative) return true;
      if (!item.timeSlots || item.timeSlots.trim() === '') return true;
      return false;
    });
    if (filterData && filterData.length > 0) {

      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please update mandatory fields and review medication before setting as reminder.',
        life: 3000,
      });
      setTimeout(() => {
        item.isReminderReq = false;
        this.medicationList[index].isReminderReq = false;
        this.medicationList = [...this.medicationList];
      }, 0)
      return;
    }

    this.medicationService
      .reminderMedications(item.prescriptionId, item.isReminderReq).subscribe((res) => {
        if (res.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Medication reminder updated successfully.',
            life: 3000,
          });
        }
      });
    this.getMedicationList();
  }

  reviewMedications() {
    let filterData = this.medicationList.filter(item => {
      if (!item.quantities || item.quantities.trim() === '') return true;
      const hasZeroOrNegative = item.quantities
        .split('|')
        .some(qty => parseInt(qty.trim(), 10) <= 0);
      if (hasZeroOrNegative) return true;
      if (!item.timeSlots || item.timeSlots.trim() === '') return true;
      return false;
    });

    if (filterData && filterData.length > 0) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: `Please add mandatory fields before confirming the review.`,
        life: 3000,
      });
      return;
    }

    this.medicationService
      .reviewMedications(this.patientId).subscribe((res) => {
        if (res.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Medication reviewed successfully.',
            life: 3000,
          });
        }
      });
    this.getMedicationList();
  }

  get isAnyNotReviewed(): boolean {
    return this.medicationList.some(item => !item.isReviewed);
  }

  updateScrollBlocking() {
    const anySidebarOpen =
      this.visible

    if (anySidebarOpen) {
      document.body.classList.add('blocked-scroll');
    } else {
      document.body.classList.remove('blocked-scroll');
    }
  }
  formatTimeSlot(time: string): string {
  if (!time) return '';
  // If it's a known label, return as is
  const knownLabels = ['Morning','Breakfast','Lunch','Afternoon Snack','Dinner','Bedtime'];
  if (knownLabels.includes(time)) {
    return time;
  }
  // If it's a time string like '23:00', format it
  if (/^\d{1,2}:\d{2}$/.test(time)) {
    const [hours, minutes] = time.split(':');
    const date = new Date();
    date.setHours(+hours, +minutes, 0, 0);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', hour12: true });
  }
  // Otherwise, return as is
  return time;
}
}
