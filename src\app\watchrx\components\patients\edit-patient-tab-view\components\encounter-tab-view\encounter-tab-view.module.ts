import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { SafeHtmlPipe } from './safe-html.pipe';
import { ActiveEncountersComponent } from './encounter/components/active-encounters/active-encounters.component';
import { PendingEncountersComponent } from './encounter/components/pending-encounters/pending-encounters.component';
@NgModule({
  declarations: [SafeHtmlPipe],
  imports: [CommonModule, ActiveEncountersComponent,PendingEncountersComponent],
})
export class EncounterTabViewModule {}
