export interface AlertCount {
  Alarm?: number;
  Critical?: number;
  Info?: number;
  Warning?: number;
  status?: boolean;
}

export interface PatientCount {
  growth?: any;
  totalPatients: number;
  totalActivePatients: number;
  totalInActivePatients: number;
  newPatients?: number;
  ccmPatients?: number;
  rpmPatients?: number;
  daysReadingCaptured?: number;
  pcmPatients?: number;
  status?: boolean;
}

export interface PatientGraph {
  totalPatients?: number;
  rpm20Minless?: number;
  rpm40Minless?: number;
  rpm60Minless?: number;
  ccm20Minless?: number;
  ccm40Minless?: number;
  ccm60Minless?: number;
  pcm30Minless?: number;
  pcm60Minless?: number;
  daysMeasured?: number;
  rpm60Plus?:number;
  pcm60Plus?:number;
  ccm60Plus?:number;
  status?: boolean;
  exceeded16Days?: boolean;
}

export interface PatientBilling {
  responseCode?: number;
  responsecode?: number;
  status?: boolean;
  success?: boolean;
  messages?: [];
  patientsInfo?: PatientsInfo[];
  count?: number;
}

export interface PatientsInfo {
  patientId?: string;
  watchId?: string;
  caregiverId?: string;
  imgPath?: string;
  patientNamePhone?: string;
  patientAddress?: string;
  assignedWatch?: string;
  caregiverName?: string;
  patientName?: string;
  patientPhoneNumber?: string;
  watchPhoneNumber?: string;
  imei?: string;
  watchPlan?: string;
  caregiverRelationship?: string;
  isActive?: boolean;
  chronicConditions?: [];
  alerts?: [];
  physicianId?: number;
  physicianName?: string;
  billingStatus?: string;
  caseManagerName?: string;
  caseManagerId?: number;
  careGiverVOList?: [];
  deviceMeasures?: string;
  deviceMeasuresArray?: [];
  deviceType?: string;
  deviceName?: string;
  dob?: string;
  cptCode?: [];
  mrnNo?: string;
  noOfDays?: number;
  totalMins?: number;
  rpmCcMMins?: number;
  totalDaysLooking?: string;
  groupName?: number;
  rpmMins?: number;
  ccmMins?: number;
  pcmMins?: number;
  rtmMins?: number;
  missedDays?: number;
}

export interface PatientTask {
  responseCode?: number;
  responsecode?: number;
  status?: boolean;
  success?: boolean;
  messages?: [];
  patientMinimalVOList?: PatientsInfo[];
}

export interface PatientDueTask {
  responseCode?: number;
  responsecode?: number;
  status?: boolean;
  messages?: [];
  tasksVOList?: Task[];
}

export interface Task {
  taskId?: number;
  clinicianId?: number;
  taskTitle?: string;
  taskDesc?: string;
  taskPriority?: string;
  taskStartDate?: string;
  taskEndDate?: string;
  startDate?: string;
  endDate?: string;
  taskStatus?: string;
  patientId?: number;
  userId?: number;
  roleType?: number;
  patientName?: string;
  index?: number;
  pageSize?: number;
  userName?: string;
  taskType?: string;
  userIds?: [];
  orgId?: number;
}

export interface CriticalAlert {
  success?: boolean;
  responseCode?: number;
  responsecode?: number;
  messages?: string[];
  eList?: AlertList[];
  count?: number;
  phoneNumber?: string;
  rpmMins?: any;
  ccmMins?: any;
  pcmMins?: any;
  minsDetails?: any;
  programs?: any;
}

export interface AlertList {
  patientAlertId?: number;
  patientId?: number;
  patientName?: string;
  missedTime?: string;
  alertDescription?: string;
  missedTimeSlot?: string;
  missedBeforeOrAfterFood?: string;
  missedMedicationIds?: string;
  alertType?: string;
  alertSeverity?: string;
  createdDate?: string;
  updatedOn?: string;
  elapsedTime?: string;
  phoneNumber?: string;
  acknowledged?: boolean;
  dateTime?: string;
  patientPhoneNumber?: string;
}

export interface CalendarTask {
  id?: number;
  title?: string;
  description?: string;
  start?: string;
  end?: string;
  className?: string;
  patientId?: string;
  userId?: number;
  priority?: string;
  patinetName?: string;
  timezone?: string;
}

export interface DeleteTask {
  responseCode?: number;
  responsecode?: number;
  success?: boolean;
  messages?: [];
}

export interface PatientAlerts
{
  alerts:[];
}