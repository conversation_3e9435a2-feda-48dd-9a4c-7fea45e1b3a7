import { CommonModule, DatePipe, formatDate } from '@angular/common';
import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DataViewModule } from 'primeng/dataview';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { StyleClassModule } from 'primeng/styleclass';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import {
  Pedometer,
  Sleep,
  Vital,
  VitalReading,
  VitalSchedule,
} from '../../../../../api/patientVitals';
import { exportData, ReportRequest } from '../../../../../api/reports';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { GenericDateService } from '../../../../../service/generic-date.service';
import { ExcelService } from '../../../../reports/service/excel.service';
import { VitalService } from './service/vital.service';
import { SidebarModule } from 'primeng/sidebar';
import { LabelType, NgxSliderModule, Options } from '@angular-slider/ngx-slider';
import { AutoCompleteModule } from 'primeng/autocomplete';
@Component({
  selector: 'app-vitals',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MenuModule,
    TableModule,
    StyleClassModule,
    PanelMenuModule,
    ButtonModule,
    TableModule,
    InputGroupModule,
    InputGroupAddonModule,
    DropdownModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    RadioButtonModule,
    CheckboxModule,
    DialogModule,
    InputTextareaModule,
    DividerModule,
    CardModule,
    TooltipModule,
    ToastModule,
    ChartModule,
    TagModule,
    DataViewModule,
    AvatarModule,
    InputSwitchModule,
    ButtonModule,
    ConfirmDialogModule,
    PanelModule,
    TabViewModule,
    SidebarModule,
    NgxSliderModule,
    AutoCompleteModule,
  ],
  templateUrl: './vitals.component.html',
  styleUrl: './vitals.component.scss',
  providers: [MessageService, ConfirmationService, ExcelService],
})
export class VitalsComponent implements OnInit {


  constructor(
    private vitalService: VitalService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService,
    private dateService: GenericDateService,
    public excelService: ExcelService,
    public cdr: ChangeDetectorRef
  ) {
    this.currentWeekDate = new Date();
    this.startDate = this.dateService.getStartDateOfMonth();
    this.endDate = new Date();

    this.currentMonthDate = new Date();
    this.currentDate = new Date();
    this.currentMonth = formatDate(this.currentDate, 'MMMM-yyyy', 'en-US');
  }

  patientId: number = 0;
  vitalReadingsData: VitalReading[] = [];
  pedometerReadings: Pedometer[] = [];
  sleepReadings: Sleep[] = [];
  totalReadings: number = 0;
  singleReadingsVital: string[] = [];
  dualReadingsVital: string[] = [];
  pedometerSleepReadings: string[] = [];
  pedometerThreshold: any;
  weeklyEnabled: boolean = false;
  dailyEnabled: boolean = true;
  monthlyEnabled: boolean = false;

  tableView: boolean = true;
  gridView: boolean = false;

  currentDate: Date = new Date();
  data: any;
  options: any;
  startDate: Date;
  endDate: Date;
  vitalList: any[] = [];
  selectedVitalType: any = '';
  thresholdConfigurationResponse: any;
  vitalStatus: boolean = false;
  collectVital: any;
  manulaAddPopup: boolean = false;
  vitalValueOne: number = 0;
  vitalValueTwo: number = 0;
  vitalDate: Date = new Date();
  vitalTime: Date = new Date();
  isfastingBsChecked: boolean = false;
  israndomBsChecked: boolean = false;
  manualDataRequiredVitals: string[] = [];
  editThresholdNotReqList: string[] = [];
  editThresholdModalVisible: boolean = false;
  minValueOne: number = 0;
  maxValueOne: number = 0;
  criticalMiniValueOne: number = 0;
  criticalMaxValueOne: number = 0;

  sliderMinValueOne: number = 0;
  sliderMaxValueOne: number = 0;
  sliderCriticalMiniValueOne: number = 0;
  sliderCriticalMaxValueOne: number = 0;


  minValueTwo: number = 0;
  maxValueTwo: number = 0;
  criticalMiniValueTwo: number = 0;
  criticalMaxValueTwo: number = 0;

  sliderMinValueTwo: number = 0;
  sliderMaxValueTwo: number = 0;
  sliderCriticalMiniValueTwo: number = 0;
  sliderCriticalMaxValueTwo: number = 0;

  noOfDaysCollected: number = 0;
  vitalIdOne: number = 0;
  vitalIdTwo: number = 0;
  editVitalReadingModal: boolean = false;
  schedulePopup: boolean = false;
  scheduleType: string = 'Daily';
  scheduleStartDate: Date = new Date();
  scheduleEndDate: Date = new Date();
  scheduleMode: string = 'Frequency';
  frequencyValue: number = 15;
  pedoMeterFrequencyValue: number = 0;
  configName: string = '';
  scheduleTime: Date = new Date();
  scheduleTimeList: any[] = [];
  allDaysSelected: boolean = false;
  selectedDays: string[] = [];
  pedometerSchedulePopup: boolean = false;

  activeIndex: number = 0;
  vitalSchedules: VitalSchedule[] = [];

  currentDay: string = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
  startOfWeek!: Date;
  endOfWeek!: Date;
  currentWeekDate!: Date;

  startOfMonth: Date = new Date();
  endOfMonth: Date = new Date();
  currentMonth: string | undefined;
  currentMonthDate!: Date;
  value: any = 100;

  sbpOptions: Options = {
    floor: 0,
    ceil: 100,
    minRange: 2, // Minimum difference between handles
    pushRange: true, // Automatically adjust other handle if needed
    //step: 1,
    //showTicks: true,
    disabled: true,
    enforceStep: false,
    enforceRange: false,
    noSwitching: true, // Prevent handles from switching positions

    readOnly: true,
    showTicks: false,
    hidePointerLabels: false, // Keep labels visible
    hideLimitLabels: false,   // Keep floor/ceil labels visible
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '<b>Min:</b>' + value;
        case LabelType.High:
          return '<b>Max:</b>' + value;
        case LabelType.Floor:
          return '<b>Critical Min:</b>' + value;
        case LabelType.Ceil:
          return '<b>Critical Max:</b>' + value;
        default:
          return '' + value;
      }
    }
  };

  dbpOptions: Options = {
    floor: 0,
    ceil: 100,
    minRange: 2, // Minimum difference between handles
    pushRange: true, // Automatically adjust other handle if needed
    //step: 1,
    //showTicks: true,
    disabled: true,
    enforceStep: false,
    enforceRange: false,
    noSwitching: true, // Prevent handles from switching positions
    readOnly: true,
    showTicks: false,
    hidePointerLabels: false, // Keep labels visible
    hideLimitLabels: false,   // Keep floor/ceil labels visible
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return '<b>Min:</b>' + value;
        case LabelType.High:
          return '<b>Max:</b>' + value;
        case LabelType.Floor:
          return '<b>Critical Min:</b>' + value;
        case LabelType.Ceil:
          return '<b>Critical Max:</b>' + value;
        default:
          return '' + value;
      }
    }
  };

  userRole: any = null;

  filteredTypes = [{ name: 'Daily', value: 'd' }, { name: 'Weekly', value: 'w' }, { name: 'Monthly', value: 'm' }];
  filteredType: any[] = [];
  selectedType: any = ''
  ngOnInit() {
    this.startOfMonth.setDate(this.currentDate.getDate() - 30);
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });

    this.vitalList = [
      { name: 'Blood Pressure' },
      { name: 'Blood Sugar' },
      { name: 'Heart Rate' },
      { name: 'Oxygen Saturation' },
      { name: 'Pedometer' },
      { name: 'Weight' },
      { name: 'Temperature' },
      { name: 'Inhaler' },
      { name: 'Spirometry' },
      { name: 'Sleep Monitor' },
    ];
    this.singleReadingsVital = [
      'Heart Rate',
      'Oxygen Saturation',
      'Weight',
      'Temperature',
      'Inhaler',
      'Spirometry',
    ];
    this.pedometerSleepReadings = ['Pedometer', 'Sleep Monitor'];
    this.manualDataRequiredVitals = [
      'Blood Pressure',
      'Blood Sugar',
      'Heart Rate',
      'Oxygen Saturation',
      'Weight',
      'Temperature',
    ];
    this.editThresholdNotReqList = ['Inhaler', 'Spirometry', 'Sleep Monitor'];

    this.selectedVitalType = this.vitalList[0];
    if (this.weeklyEnabled) {
      this.setCurrentWeek();
    } else {
      this.currentDay = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
    }
    this.getVitalGraphWeekly();
    this.getVitalReadings({ first: 0, rows: 25 });
    this.getVitalStatus();
    this.getVitalThreshold();
    this.setGraphOptions()
    this.userRole = JSON.parse(localStorage.getItem('user')!);
  }


  filterType(event: any) {
    let filtered: any[] = [];
    let query = event.query;
    for (let i = 0; i < (this.filteredTypes as any[]).length; i++) {
      let country = (this.filteredTypes as any[])[i];
      if (country.name.toLowerCase().indexOf(query.toLowerCase()) == 0) {
        filtered.push(country);
      }
    }
    this.filteredType = filtered;
  }
  setGraphOptions() {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');
    const textColorSecondary = documentStyle.getPropertyValue(
      '--text-color-secondary'
    );
    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');
    this.options = {
      responsive: true,
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      spanGaps: true,
      plugins: {
        legend: {
          labels: {
            color: textColor,
          },
        },
      },
      scales: {
        x: {
          ticks: {
            color: textColorSecondary,
            font: {
              weight: 500,
            },
          },
          grid: {
            color: surfaceBorder,
            drawBorder: false,
          },
        },
        // y: {
        //   beginAtZero: true,
        //   ticks: {
        //      stepSize: 10,
        //         precision: 0, //
        //     color: textColorSecondary,
        //     callback: function (value: any) {
        //       return Number.isInteger(value) ? value : '';
        //     },
        //   },
        //   grid: {
        //     color: surfaceBorder,
        //     drawBorder: false,
        //   },
        // },
        y: {
          min: 0,
          stacked: false,
          ticks: {
            stepSize: 10,
            precision: 0,
            callback: function (value: any) {
              return Number.isInteger(value) ? value : '';
            }
          },
          grid: {
            display: true,
            color: '#000000',
            borderDash: [3, 5],
          },
        },
      },
    };
  }

  graphTypeClick(type: string) {
    if (type === 'w') {
      this.weeklyEnabled = true;
      this.dailyEnabled = false;
      this.monthlyEnabled = false;

      this.currentWeekDate = new Date();
      this.setCurrentWeek();
    }
    if (type === 'd') {
      this.monthlyEnabled = false;
      this.weeklyEnabled = false;
      this.dailyEnabled = true;
      this.currentDate = new Date();
      this.currentDay = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
    }
    if (type === 'm') {
      this.weeklyEnabled = false;
      this.dailyEnabled = false;
      this.monthlyEnabled = true;
      this.currentDate = new Date();
      this.currentDay = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
    }
    this.getVitalGraphWeekly();
  }

  viewFormat(type: string) {
    if (type === 'g') {
      this.gridView = true;
      this.tableView = false;
    }
    else if (type === 't') {
      this.tableView = true;
      this.gridView = false;
    }
  }
  vitalChanged() {
    this.dailyEnabled = true;
    this.weeklyEnabled = false;
    this.monthlyEnabled = false;
    this.tableView = true;
    this.gridView = false;
    if (this.dailyEnabled) {
      this.currentDate = new Date();
      this.currentDay = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
    }
    if (this.weeklyEnabled) {
      this.currentWeekDate = new Date();
      this.setCurrentWeek();
    }
    if (this.pedometerSleepReadings.includes(this.selectedVitalType?.name)) {
      if (this.selectedVitalType?.name === 'Pedometer') {
        this.getPedometerReadings({ first: 0, rows: 25 });
        this.getPedometerThreshold();
        this.getPedometerVitalStatus();
      } else {
        this.getSleepReadings({ first: 0, rows: 25 });
        this.getSleepMonitorVitalStatus();
        this.resetThresholdValues();
      }
      this.getVitalGraphWeekly();
    } else {
      this.getVitalReadings({ first: 0, rows: 25 });
      if (
        this.selectedVitalType?.name === 'Inhaler' ||
        this.selectedVitalType?.name === 'Spirometry'
      ) {
        this.resetThresholdValues();
      } else {
        this.getVitalThreshold();
        this.getVitalStatus();
        this.getVitalGraphWeekly();
      }
    }
  }

  getVitalReadings($event: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event.rows || 10;
    let first = $event.first || 0;
    let pageNo = first / pageSize;

    let payload = {
      vitalTypeName: this.selectedVitalType?.name
        ? this.selectedVitalType?.name
        : 'Blood Pressure',
      patientId: this.patientId,
      index: pageNo,
      pageSize: pageSize,
    };
    this.vitalService.getVitalReadings(payload).subscribe((res) => {
      if (res?.success) {
        this.vitalReadingsData = res?.eList;
        this.totalReadings = res?.count || this.vitalReadingsData?.length;
      }
    });
  }

  getPedometerReadings($event: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event.rows || 10;
    let first = $event.first || 0;
    let pageNo = first / pageSize;

    let request = this.patientId + '/' + pageNo + '/' + pageSize;
    this.vitalService.getPedometerReadings(request).subscribe((res) => {
      if (res?.success) {
        this.pedometerReadings = res?.eList;
        this.totalReadings = res?.count || this.pedometerReadings?.length;
      }
    });
  }

  getSleepReadings($event: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event.rows || 10;
    let first = $event.first || 0;
    let pageNo = first / pageSize;

    let request = this.patientId + '/' + pageNo + '/' + pageSize;
    this.vitalService.getSleepMonitorReadings(request).subscribe((res) => {
      if (res?.success) {
        this.sleepReadings = res?.eList;
        this.totalReadings = res?.count || this.pedometerReadings?.length;
      }
    });
  }

  getVitalThreshold() {
    let payload = {};
    if (this.selectedVitalType?.name === 'Blood Pressure') {
      payload = {
        eList: [
          {
            vitalTypeName: 'Systolic Blood Pressure',
            patientId: this.patientId,
          },
          {
            vitalTypeName: 'Diastolic Blood Pressure',
            patientId: this.patientId,
          },
        ],
      };
    } else {
      payload = {
        eList: [
          {
            vitalTypeName: this.selectedVitalType?.name,
            patientId: this.patientId,
          },
        ],
      };
    }

    this.vitalService.getVitalThreshold(payload).subscribe((res) => {
      if (res?.success) {
        this.thresholdConfigurationResponse = res;
        this.setThresholdConfig();
      }
    });
  }

  getPedometerThreshold() {
    this.vitalService.getPedometerThreshold(this.patientId).subscribe((res) => {
      if (res?.success) {
        this.pedometerThreshold = res;
        this.setThresholdConfig();
      }
    });
  }

  getVitalGraphWeekly() {
    if (this.weeklyEnabled) {
    } else {
      this.currentDay = formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US');
    }

    if (this.selectedVitalType?.name === 'Pedometer') {
      this.getPedometerGraph();
      return;
    }
    let typeList: any[] = [];
    if (this.selectedVitalType?.name === 'Blood Pressure') {
      typeList.push('Systolic Blood Pressure');
      typeList.push('Diastolic Blood Pressure');
    } else if (this.selectedVitalType?.name === 'Blood Sugar') {
      typeList.push('Fasting Blood Sugar');
      typeList.push('Random Blood Sugar');
    } else {
      typeList.push(this.selectedVitalType?.name);
    }

    let payload: any = {
      vitalTypeNameList: typeList,
      patientId: this.patientId,
      requestedDate: this.dailyEnabled
        ? formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US')
        : this.weeklyEnabled ? formatDate(this.currentWeekDate, 'yyyy-MM-dd', 'en-US') : formatDate(this.currentMonthDate, 'yyyy-MM-dd', 'en-US'),
    };
    if (this.weeklyEnabled) {
      payload.periodType = 'WEEKLY';
    }
    else if (this.monthlyEnabled) {
      payload.periodType = 'MONTHLY';
    }
    let weeklyEnabled = false;
    (this.weeklyEnabled || this.monthlyEnabled) ? weeklyEnabled = true : weeklyEnabled = false;
    this.vitalService
      .getVitalGraph(payload, weeklyEnabled)
      .subscribe((res) => {
        if (res?.success) {
          let firstData = res?.vitalsCountGraphVOs[0];
          let secondData = res?.vitalsCountGraphVOs[1];
          console.log(secondData, 'secondData');
          let dataSets: any[] = [];
          if (firstData?.counts) {
            dataSets.push({
              label: firstData?.vitalTypeName,
              backgroundColor: '#38bfc6',
              borderColor: '#38bfc6',
              data: firstData?.counts,
              borderWidth: 1,
              barThickness: 20,
              spanGaps: true,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              // minBarThickness:40
            });
          }
          if (secondData?.counts) {
            dataSets.push({
              label: secondData?.vitalTypeName,
              backgroundColor: '#f9a11d',
              borderColor: '#f9a11d',
              data: secondData?.counts,
              borderWidth: 1,
              barThickness: 20,
              spanGaps: true,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              // minBarThickness:40
            });
          }

          if (res?.vitalCollnStatus) {
            if (this.selectedVitalType?.name === 'Heart Rate') {
              this.noOfDaysCollected = res?.vitalCollnStatus?.heartrate;
            } else if (this.selectedVitalType?.name === 'Oxygen Saturation') {
              this.noOfDaysCollected = res?.vitalCollnStatus?.oxygensaturation;
            } else if (this.selectedVitalType?.name === 'Blood Pressure') {
              this.noOfDaysCollected =
                res?.vitalCollnStatus?.diastolicbloodpressure;
            } else if (this.selectedVitalType?.name === 'Blood Sugar') {
              this.noOfDaysCollected =
                res?.vitalCollnStatus?.fastingbloodsugar ||
                res?.vitalCollnStatus?.randombloodsugar ||
                0;
            } else if (this.selectedVitalType?.name === 'Temperature') {
              this.noOfDaysCollected = res?.vitalCollnStatus?.temperature;
            } else if (this.selectedVitalType?.name === 'Weight') {
              this.noOfDaysCollected = res?.vitalCollnStatus?.weight;
            }
          }

          this.data = {
            labels: (this.weeklyEnabled || this.monthlyEnabled) ? res?.measuredDates : res?.period,
            datasets: dataSets,
          };
        }
      });
  }

  getPedometerGraph() {
    let payload: any = {
      patientId: this.patientId,
      requestedDate: this.dailyEnabled
        ? formatDate(this.currentDate, 'yyyy-MM-dd', 'en-US')
        : formatDate(this.currentWeekDate, 'yyyy-MM-dd', 'en-US'),
    };
    if (this.weeklyEnabled) {
      payload.periodType = 'WEEKLY';
    }
    else if (this.monthlyEnabled) {
      payload.periodType = 'MONTHLY';
    }
    let weeklyEnabled = false;
    (this.weeklyEnabled || this.monthlyEnabled) ? weeklyEnabled = true : weeklyEnabled = false;
    this.vitalService
      .getPedometerGraph(payload, weeklyEnabled)
      .subscribe((res) => {
        if (res?.success) {
          let firstData = res?.vitalsCountGraphVOs[0];
          let dataSets: any[] = [];
          if (firstData?.counts) {
            dataSets.push({
              label: 'Pedometer',
              backgroundColor: '#38bfc6',
              borderColor: '#38bfc6',
              data: firstData?.counts,
              borderWidth: 1,
              barThickness: 20,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              //minBarThickness:40
            });
          }
          if (res?.vitalCollnStatus) {
            this.noOfDaysCollected = res?.vitalCollnStatus?.pedometer;
          }
          this.data = {
            labels: res?.period,
            datasets: dataSets,
          };
        }
      });
  }

  getConvertedDate(date: string) {
    const datePipe = new DatePipe('en-US');
    return datePipe.transform(new Date(date), 'EEE MM-dd-yyyy h:mm a');
  }

  getUnitName() {
    if (this.selectedVitalType?.name === 'Heart Rate') {
      return 'bpm';
    } else if (this.selectedVitalType?.name === 'Weight') {
      return 'lbs';
    } else if (this.selectedVitalType?.name === 'Temperature') {
      return '°F';
    } else if (this.selectedVitalType?.name === 'Oxygen Saturation') {
      return '%';
    }
    return '';
  }

  getVitalStatus() {
    let payload = {
      vitalTypeName: this.selectedVitalType?.name,
      patientId: this.patientId,
    };
    this.vitalService.getVitalEnableStatus(payload).subscribe((res) => {
      if (res?.success) {
        this.vitalStatus = res?.status === 'enable' ? true : false;
      }
    });
  }

  getPedometerVitalStatus() {
    this.vitalService
      .getPedometerVitalEnableStatus(this.patientId)
      .subscribe((res) => {
        if (res?.success) {
          this.vitalStatus = res?.state === 'enable' ? true : false;
          this.pedoMeterFrequencyValue = res?.timeInterval;
        }
      });
  }

  getSleepMonitorVitalStatus() {
    this.vitalService
      .getSleepVitalEnableStatus(this.patientId)
      .subscribe((res) => {
        if (res?.success) {
          this.vitalStatus = res?.state === 'enable' ? true : false;
        }
      });
  }

  updateVitalStatus(status: boolean) {
    if (this.selectedVitalType?.name === 'Pedometer') {
      this.updatePedometerStatus(status);
      return;
    }
    if (this.selectedVitalType?.name === 'Sleep Monitor') {
      this.updateSleepMonitor(status);
      return;
    }
    var data = {
      vitalTypeName: this.selectedVitalType?.name,
      status: status ? 'enable' : 'disable',
      patientId: this.patientId,
    };
    this.loaderService.show();
    this.vitalService.updatevitalenableState(data).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Vital updated successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update vital',
        });
      }
    });
  }

  updatePedometerStatus(status: boolean) {
    var state = status ? 'enable' : 'disable';
    var currentDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
    var stepsdata = {
      state: state,
      patientId: this.patientId,
      createdDate: currentDate,
      updatedDate: '',
      timeInterval: this.pedoMeterFrequencyValue,
    };
    this.loaderService.show();
    this.vitalService.updatePedometerState(stepsdata).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Vital updated successfully',
        });

        this.pedometerSchedulePopup = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update vital',
        });
      }
    });
  }

  updateSleepMonitor(status: boolean) {
    var state = status ? 'enable' : 'disable';
    var currentDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');

    var stepsdata = {
      state: state,
      patientId: this.patientId,
      createdDate: currentDate,
      updatedDate: '',
      timeInterval: '0',
    };
    this.loaderService.show();
    this.vitalService.updateSleepState(stepsdata).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Vital updated successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update vital',
        });
      }
    });
  }

  collectNow(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to collect now?',
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-info p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.selectedVitalType?.name === 'Pedometer'
          ? this.collectNowPedometer()
          : this.collectNowVital();
      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }

  collectNowVital() {
    var stepsdata = {
      vitalTypeName: this.selectedVitalType?.name,
      patientId: this.patientId,
      scheduleType: 'Now',
    };

    this.loaderService.show();
    this.vitalService
      .collectNowVitalRequest(stepsdata)
      .subscribe((response) => {
        this.loaderService.hide();
        if (response.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Request sent successfully',
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: response?.messages[0],
          });
        }
      });
  }

  collectNowPedometer() {
    var stepsdata = {
      patientId: this.patientId,
    };

    this.loaderService.show();
    this.vitalService
      .collectNowPedometerRequest(stepsdata)
      .subscribe((response) => {
        this.loaderService.hide();
        if (response.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Request sent successfully',
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: response?.messages[0],
          });
        }
      });
  }

  generateErrorMessage(message: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: message,
    });
    return;
  }
  openManulaDataPopup() {
    this.manulaAddPopup = true;
    this.vitalValueOne = 0;
    this.vitalValueTwo = 0;
    this.isfastingBsChecked = false;
    this.israndomBsChecked = false;
    this.vitalIdOne = 0;
    this.vitalIdTwo = 0;
  }

  saveManulaData() {
    let vitalTypeList: any[] = [];

    if (this.selectedVitalType?.name === 'Blood Pressure') {
      if (
        !this.validateVitalValue(
          this.vitalValueOne,
          'Please enter systolic value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.vitalValueTwo,
          'Please enter diastolic value'
        )
      )
        return;

      vitalTypeList.push(
        {
          vitalId: this.vitalIdOne,
          vitalTypeName: 'Systolic Blood Pressure',
          vitalValue: this.vitalValueOne,
        },
        {
          vitalId: this.vitalIdTwo,
          vitalTypeName: 'Diastolic Blood Pressure',
          vitalValue: this.vitalValueTwo,
        }
      );
    } else if (this.selectedVitalType?.name === 'Blood Sugar') {
      if (!this.validateBloodSugar()) return;

      if (this.isfastingBsChecked) {
        vitalTypeList.push({
          vitalId: this.vitalIdOne,
          vitalTypeName: 'Fasting Blood Sugar',
          vitalValue: this.vitalValueOne,
        });
      }
      if (this.israndomBsChecked) {
        vitalTypeList.push({
          vitalId: this.vitalIdOne,
          vitalTypeName: 'Random Blood Sugar',
          vitalValue: this.vitalValueTwo,
        });
      }
    } else {
      if (
        !this.validateVitalValue(
          this.vitalValueOne,
          `Please enter ${this.selectedVitalType?.name} value`
        )
      )
        return;

      vitalTypeList.push({
        vitalId: this.vitalIdOne,
        vitalTypeName: this.selectedVitalType?.name,
        vitalValue: this.vitalValueOne,
      });
    }

    if (!this.validateDateAndTime()) return;

    let vitalDateTime = `${moment(this.vitalDate).format(
      'YYYY-MM-DD'
    )} ${moment(this.vitalTime).format('HH:mm:00')}`;
    let payload = this.constructPayload(vitalTypeList, vitalDateTime);

    this.loaderService.show();
    this.vitalService.addManualData(payload).subscribe((response) => {
      this.loaderService.hide();
      if (response?.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Vital data saved successfully',
        });
        this.getVitalGraphWeekly();
        this.getVitalReadings({ first: 0, rows: 25 });
        this.manulaAddPopup = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0],
        });
      }
    });
  }

  validateVitalValue(value: number | null | undefined, errorMessage: string) {
    if (value === undefined || value === null || value === 0) {
      this.generateErrorMessage(errorMessage);
      return false;
    }
    return true;
  }

  validateBloodSugar() {
    if (!this.isfastingBsChecked && !this.israndomBsChecked) {
      this.generateErrorMessage('Please Select Fasting or Random');
      return false;
    }
    if (
      this.isfastingBsChecked &&
      !this.validateVitalValue(this.vitalValueOne, 'Please enter fasting value')
    )
      return false;
    if (
      this.israndomBsChecked &&
      !this.validateVitalValue(this.vitalValueTwo, 'Please enter random value')
    )
      return false;
    return true;
  }

  validateDateAndTime() {
    if (!this.vitalDate) {
      this.generateErrorMessage('Please enter vital measured date');
      return false;
    }
    if (!this.vitalTime) {
      this.generateErrorMessage('Please enter vital measured time');
      return false;
    }
    return true;
  }

  constructPayload(
    vitalTypeList: { vitalTypeName: any; vitalValue: number }[],
    vitalDateTime: string
  ) {
    return {
      vitalTypeValueVOList: vitalTypeList,
      patientId: this.patientId,
      createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      measuredDateTime: vitalDateTime,
      configuration: 'Manual',
    };
  }
  onCheckboxChange(changedCheckbox: string) {
    if (changedCheckbox === 'fasting') {
      this.israndomBsChecked = !this.isfastingBsChecked
        ? this.israndomBsChecked
        : false;
    } else if (changedCheckbox === 'random') {
      this.isfastingBsChecked = !this.israndomBsChecked
        ? this.isfastingBsChecked
        : false;
    }
  }

  openEditThresholdModal() {
    this.editThresholdModalVisible = true;
    this.setThresholdConfig();
  }

  setThresholdConfig() {
    this.resetThresholdValues();
    if (this.selectedVitalType?.name === 'Blood Pressure') {
      this.setThresholdValuesFromResponse(0);
      if (this.thresholdConfigurationResponse?.eList?.length > 1) {
        this.setThresholdValuesFromResponse(1, true);
      }
    } else if (this.selectedVitalType?.name === 'Pedometer') {
      this.setPedometerThresholdValues();
    } else if (
      this.selectedVitalType?.name !== 'Blood Pressure' &&
      this.selectedVitalType?.name !== 'Pedometer'
    ) {
      this.setThresholdValuesFromResponse(0);
    }
  }

  resetThresholdValues() {
    this.minValueOne = 0;
    this.maxValueOne = 0;
    this.criticalMiniValueOne = 0;
    this.criticalMaxValueOne = 0;

    this.minValueTwo = 0;
    this.maxValueTwo = 0;
    this.criticalMiniValueTwo = 0;
    this.criticalMaxValueTwo = 0;
  }

  setThresholdValuesFromResponse(index: number, isSecond = false) {
    const eList = this.thresholdConfigurationResponse?.eList;
    if (eList && eList.length > index) {
      if (isSecond) {
        this.dbpOptions.stepsArray = []
        this.minValueTwo = eList[index]?.vitalMin || 0;
        this.maxValueTwo = eList[index]?.vitalMax || 0;
        this.criticalMiniValueTwo = (eList[index]?.vitalCriticalMin || 0);
        this.criticalMaxValueTwo = (eList[index]?.vitalCriticalMax || 0);


        this.sliderMinValueTwo = eList[index]?.vitalMin || 0;
        this.sliderMaxValueTwo = eList[index]?.vitalMax || 0;
        this.sliderCriticalMiniValueTwo = (eList[index]?.vitalCriticalMin || 0);
        this.sliderCriticalMaxValueTwo = (eList[index]?.vitalCriticalMax || 0);
        let newStepsArray = [{ value: this.sliderCriticalMiniValueTwo }, { value: this.sliderMinValueTwo }, { value: this.sliderMaxValueTwo }, { value: this.sliderCriticalMaxValueTwo },]
        this.dbpOptions = {
          ...this.dbpOptions, // Preserve other options
          floor: this.sliderCriticalMiniValueTwo,
          ceil: this.sliderCriticalMaxValueTwo,
          stepsArray: [...newStepsArray],
        };

        // let newStepsArray = [{ value: this.criticalMiniValueTwo }, { value: this.minValueTwo }, { value: this.maxValueTwo }, { value: this.criticalMaxValueTwo },
        // ]
        // this.dbpOptions = {
        //   ...this.dbpOptions, // Preserve other options
        //   floor: this.criticalMiniValueTwo,
        //   ceil: this.criticalMaxValueTwo,
        //   stepsArray: [...newStepsArray],
        // };
        this.options.scales.y.min = (this.criticalMiniValueTwo || 0) - 30;
        this.options.scales.y.max = (this.criticalMaxValueTwo || 0) + 30;
      } else {
        this.sbpOptions.stepsArray = []
        this.minValueOne = eList[index]?.vitalMin || 0;
        this.maxValueOne = eList[index]?.vitalMax || 0;
        this.criticalMiniValueOne = (eList[index]?.vitalCriticalMin || 0);
        this.criticalMaxValueOne = (eList[index]?.vitalCriticalMax || 0);

        //sttaic
        this.sliderMinValueOne = eList[index]?.vitalMin || 0;
        this.sliderMaxValueOne = eList[index]?.vitalMax || 0;
        this.sliderCriticalMiniValueOne = (eList[index]?.vitalCriticalMin || 0);
        this.sliderCriticalMaxValueOne = (eList[index]?.vitalCriticalMax || 0);

        let newStepsArray = [{ value: this.sliderCriticalMiniValueOne }, { value: this.sliderMinValueOne }, { value: this.sliderMaxValueOne }, { value: this.sliderCriticalMaxValueOne },]
        this.sbpOptions = {
          ...this.sbpOptions, // Preserve other options
          floor: this.sliderCriticalMiniValueOne,
          ceil: this.sliderCriticalMaxValueOne,
          stepsArray: [...newStepsArray],
        };
        this.options.scales.y.min = (this.criticalMiniValueOne || 0) - 30;
        this.options.scales.y.max = (this.criticalMaxValueOne || 0) + 30;
        console.log(this.minValueOne, 'this.minValueOne');
      }
    }
    else {
      //sttaic
      this.sliderMinValueOne = 0;
      this.sliderMaxValueOne = 0;
      this.sliderCriticalMiniValueOne = 0;
      this.sliderCriticalMaxValueOne = 0;

      let newStepsArray = [{ value: this.sliderCriticalMiniValueOne }, { value: this.sliderMinValueOne }, { value: this.sliderMaxValueOne }, { value: this.sliderCriticalMaxValueOne },]
      this.sbpOptions = {
        ...this.sbpOptions, // Preserve other options
        floor: this.sliderCriticalMiniValueOne,
        ceil: this.sliderCriticalMaxValueOne,
        stepsArray: [...newStepsArray],
      };
    }
    this.cdr.detectChanges()
  }

  setPedometerThresholdValues() {
    console.log(this.minValueOne, 'this.minValueOne');
    this.sbpOptions.stepsArray = []
    this.minValueOne = this.pedometerThreshold?.pedometerStepCountMin || 0;
    this.maxValueOne = this.pedometerThreshold?.pedometerStepCountMax || 0;
    this.criticalMiniValueOne =
      (this.pedometerThreshold?.pedometerStepCountCriticalMin || 0);
    this.criticalMaxValueOne =
      (this.pedometerThreshold?.pedometerStepCountCriticalMax || 0);
    //sttaic
    this.sliderMinValueOne = this.pedometerThreshold?.pedometerStepCountMin || 0;
    this.sliderMaxValueOne = this.pedometerThreshold?.pedometerStepCountMax || 0;
    this.sliderCriticalMiniValueOne = (this.pedometerThreshold?.pedometerStepCountCriticalMin || 0);
    this.sliderCriticalMaxValueOne = (this.pedometerThreshold?.pedometerStepCountCriticalMax || 0);
    let newStepsArray = [{ value: this.sliderCriticalMiniValueOne }, { value: this.sliderMinValueOne }, { value: this.sliderMaxValueOne }, { value: this.sliderCriticalMaxValueOne },]
    this.sbpOptions = {
      ...this.sbpOptions, // Preserve other options
      floor: this.sliderCriticalMiniValueOne,
      ceil: this.sliderCriticalMaxValueOne,
      stepsArray: [...newStepsArray],
    };


    //       let newStepsArray=[ { value: this.criticalMiniValueOne},  { value: this.minValueOne }, { value: this.maxValueOne },{ value: this.criticalMaxValueOne },
    // ]

    //     this.sbpOptions = {
    //       ...this.sbpOptions, // Preserve other options
    //       floor: this.criticalMiniValueOne,
    //       ceil: this.criticalMaxValueOne,
    //        stepsArray:[...newStepsArray],
    //     };
    this.options.scales.y.min = (this.criticalMiniValueOne || 0) - 30;
    this.options.scales.y.max = (this.criticalMaxValueOne || 0) + 30;
    this.cdr.detectChanges()
  }

  saveEditedThresholdConfig() {
    let typeList: any[] = [];
    if (this.selectedVitalType?.name === 'Blood Pressure') {
      if (!this.validateVitalValue(this.minValueOne, 'Please enter minimum value'))
        return;
      if (!this.validateVitalValue(this.maxValueOne, 'Please enter maximum value'))
        return;
      if (
        !this.validateVitalValue(
          this.criticalMiniValueOne,
          'Please critical minimum value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.criticalMaxValueOne,
          'Please critical maximum value'
        )
      )
        return;

      if (
        !this.validateVitalValue(
          this.minValueTwo,
          'Please diastolic minimum value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.maxValueTwo,
          'Please diastolic maximum value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.criticalMiniValueTwo,
          'Please critical minimum value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.criticalMaxValueTwo,
          'Please critical maximum value'
        )
      )
        return;
        if (
        !(this.criticalMiniValueOne < this.minValueOne &&
          this.minValueOne < this.maxValueOne &&
          this.maxValueOne < this.criticalMaxValueOne)
      ) {
         this.generateErrorMessage("Values must follow the order criticalMin < min < max < criticalMax.");
        return;
      }
      if (
        !(this.criticalMiniValueTwo < this.minValueTwo &&
          this.minValueTwo < this.maxValueTwo &&
          this.maxValueTwo < this.criticalMaxValueTwo)
      ) {
         this.generateErrorMessage("Values must follow the order criticalMin < min < max < criticalMax.");
        return;
      }

      typeList.push({
        vitalTypeName: 'Systolic Blood Pressure',
        patientId: this.patientId,
        vitalMin: this.minValueOne,
        vitalMax: this.maxValueOne,
        vitalCriticalMin: this.criticalMiniValueOne,
        vitalCriticalMax: this.criticalMaxValueOne,
      });
      console.log(this.minValueOne, 'this.minValueOne');
      typeList.push({
        vitalTypeName: 'Diastolic Blood Pressure',
        patientId: this.patientId,
        vitalMin: this.minValueTwo,
        vitalMax: this.maxValueTwo,
        vitalCriticalMin: this.criticalMiniValueTwo,
        vitalCriticalMax: this.criticalMaxValueTwo,
      });
    } else {
      if (!this.validateVitalValue(this.minValueOne, 'Please minimum value'))
        return;
      if (!this.validateVitalValue(this.maxValueOne, 'Please maximum value'))
        return;
      if (
        !this.validateVitalValue(
          this.criticalMiniValueOne,
          'Please critical minimum value'
        )
      )
        return;
      if (
        !this.validateVitalValue(
          this.criticalMaxValueOne,
          'Please critical maximum value'
        )
      )
        return;
      if (
        !(this.criticalMiniValueOne < this.minValueOne &&
          this.minValueOne < this.maxValueOne &&
          this.maxValueOne < this.criticalMaxValueOne)
      ) {
         this.generateErrorMessage("Values must follow the order criticalMin < min < max < criticalMax.");
        return;
      }

      typeList.push({
        vitalTypeName: this.selectedVitalType?.name,
        patientId: this.patientId,
        vitalMin: this.minValueOne,
        vitalMax: this.maxValueOne,
        vitalCriticalMin: this.criticalMiniValueOne,
        vitalCriticalMax: this.criticalMaxValueOne,
      });
    }
    console.log(this.minValueOne, 'this.minValueOne');
    let payload = {
      eList: typeList,
    };
    this.loaderService.show();
    this.vitalService.updateThresholdConfig(payload).subscribe((response) => {
      this.loaderService.hide();
      if (response?.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Vital threshold updated successfully',
        });
        this.editThresholdModalVisible = false;
        this.getVitalThreshold();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0],
        });
      }
    });
  }

  saveEditedPedometerThreshold() {
    if (!this.validateVitalValue(this.minValueOne, 'Please minimum value'))
      return;
    if (!this.validateVitalValue(this.maxValueOne, 'Please maximum value'))
      return;
    if (
      !this.validateVitalValue(
        this.maxValueOne,
        'Please critical minimum value'
      )
    )
      return;
    if (
      !this.validateVitalValue(
        this.maxValueOne,
        'Please critical maximum value'
      )
    )
      return;
    let payload = {
      thresholdConfigId: this.pedometerThreshold?.thresholdConfigId || 0,
      patientId: this.patientId,
      pedometerStepCountMin: this.minValueOne,
      pedometerStepCountMax: this.maxValueOne,
      pedometerStepCountCriticalMin: this.criticalMiniValueOne,
      pedometerStepCountCriticalMax: this.criticalMaxValueOne,
      pedometerStepIsEnabled: true,
      systolicBloodPressureIsEnabled: false,
      diastolicBloodPressureIsEnabled: false,
      temperatureIsEnabled: false,
      weightIsEnabled: false,
      bloodSugarIsEnabled: false,
      heartRateIsEnabled: false,
    };

    this.loaderService.show();
    this.vitalService
      .updatePedoThresholdConfig(payload)
      .subscribe((response) => {
        this.loaderService.hide();
        if (response?.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Vital threshold updated successfully',
          });
          this.editThresholdModalVisible = false;
          //this.getVitalGraphWeekly();
          this.getPedometerThreshold()
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: response?.messages[0],
          });
        }
      });
  }

  openEditVitalReadingPopup(item: VitalReading) {
    this.manulaAddPopup = true;
    console.log(item);
    this.resetVitalFields();

    const [vital1, vital2] = item?.vitalTypeValueVOList || [];
    const vitalTypeName1 = vital1?.vitalTypeName;
    const vitalTypeName2 = vital2?.vitalTypeName;

    if (this.selectedVitalType?.name === 'Blood Pressure') {
      if (
        vitalTypeName1 === 'Systolic Blood Pressure' &&
        vitalTypeName2 === 'Diastolic Blood Pressure'
      ) {
        this.setVitalValues(vital1, vital2);
      } else {
        this.setVitalValues(vital2, vital1);
      }
    } else if (this.selectedVitalType?.name === 'Blood Sugar') {
      this.setBloodSugarValues(vital1);
    } else {
      this.setSingleVitalValue(vital1);
    }

    this.setVitalDateAndTime(item?.measuredDateTime);
  }

  resetVitalFields() {
    this.vitalValueOne = 0;
    this.vitalValueTwo = 0;
    this.vitalIdOne = 0;
    this.vitalIdTwo = 0;
    this.isfastingBsChecked = false;
    this.israndomBsChecked = false;
  }

  setVitalValues(vital1: Vital, vital2: Vital) {
    this.vitalValueOne = Number(vital1?.vitalValue);
    this.vitalValueTwo = Number(vital2?.vitalValue);
    this.vitalIdOne = vital1?.vitalId;
    this.vitalIdTwo = vital2?.vitalId;
  }

  setBloodSugarValues(vital: Vital) {
    if (vital?.vitalTypeName === 'Fasting Blood Sugar') {
      this.vitalValueOne = Number(vital.vitalValue);
      this.isfastingBsChecked = true;
    } else {
      this.vitalValueTwo = Number(vital.vitalValue);
      this.israndomBsChecked = true;
    }
    this.vitalIdOne = vital?.vitalId;
  }

  setSingleVitalValue(vital: Vital) {
    this.vitalValueOne = Number(vital?.vitalValue);
    this.vitalIdOne = vital?.vitalId;
  }

  setVitalDateAndTime(measuredDateTime: string | number | Date) {
    const date = new Date(measuredDateTime);
    this.vitalDate = date;
    this.vitalTime = date;
  }

  deleteVitalReadings(event: Event, item: VitalReading) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to delete vital?',
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-info p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.resetVitalFields();

        const [vital1, vital2] = item?.vitalTypeValueVOList || [];
        const vitalTypeName1 = vital1?.vitalTypeName;
        const vitalTypeName2 = vital2?.vitalTypeName;

        if (this.selectedVitalType?.name === 'Blood Pressure') {
          if (
            vitalTypeName1 === 'Systolic Blood Pressure' &&
            vitalTypeName2 === 'Diastolic Blood Pressure'
          ) {
            this.setVitalValues(vital1, vital2);
          } else {
            this.setVitalValues(vital2, vital1);
          }
        } else if (this.selectedVitalType?.name === 'Blood Sugar') {
          this.setBloodSugarValues(vital1);
        } else {
          this.setSingleVitalValue(vital1);
        }
        let typeList: any[] = [];
        if (this.selectedVitalType?.name === 'Blood Pressure') {
          typeList.push({ vitalId: this.vitalIdOne });
          typeList.push({ vitalId: this.vitalIdTwo });
        } else {
          typeList.push({ vitalId: this.vitalIdOne });
        }

        let payload = {
          patientId: this.patientId,
          vitalTypeValueVOList: typeList,
        };
        console.log(payload);
        this.loaderService.show();
        this.vitalService.deletVitalReadings(payload).subscribe((response) => {
          this.loaderService.hide();
          if (response?.success) {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Vital reading deleted successfully',
            });
            this.getVitalReadings({ first: 0, rows: 25 });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: response?.messages[0],
            });
          }
        });
      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }

  daysOfWeek = [
    { name: 'Monday', value: 'Mo' },
    { name: 'Tuesday', value: 'Tu' },
    { name: 'Wednesday', value: 'We' },
    { name: 'Thursday', value: 'Th' },
    { name: 'Friday', value: 'Fr' },
    { name: 'Saturday', value: 'Sa' },
    { name: 'Sunday', value: 'Su' },
  ];

  openSchedulePopup() {
    this.activeIndex = 0;
    if (this.selectedVitalType?.name === 'Pedometer') {
      this.pedometerSchedulePopup = true;
    } else {
      this.schedulePopup = true;
      this.scheduleType = 'Daily';
      this.scheduleMode = 'Frequency';
      this.frequencyValue = 15;
      this.configName = '';
      this.scheduleTimeList = [];
      this.allDaysSelected = false;
      this.selectedDays = [];
      this.scheduleTime = new Date();
    }
  }
  addTimeToList() {
    const newTimeString = this.scheduleTime.toTimeString();
    const timeExists = this.scheduleTimeList.some((item) => {
      const existingTimeString = item.time.toTimeString();
      return existingTimeString === newTimeString;
    });

    if (!timeExists) {
      this.scheduleTimeList.push({
        time: this.scheduleTime,
      });
    } else {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Same time can`t be add',
      });
      return;
    }
  }
  removeTime(item: string): boolean {
    const indexToRemove = this.scheduleTimeList.indexOf(item);
    if (indexToRemove > -1) {
      this.scheduleTimeList.splice(indexToRemove, 1);
    }
    return false;
  }

  toggleAllDays() {
    if (this.allDaysSelected) {
      this.selectedDays = this.daysOfWeek.map((day) => day.value);
    } else {
      this.selectedDays = [];
    }
  }
  updateAllDaysSelected() {
    this.allDaysSelected = this.selectedDays.length === this.daysOfWeek.length;
  }

  saveVitalSchedules() {
    if (this.scheduleStartDate > this.scheduleEndDate) {
      this.generateErrorMessage('Start must be before end date.');
      return;
    }

    if (this.scheduleMode === 'Frequency') {
      if (
        this.frequencyValue == null ||
        this.frequencyValue == undefined ||
        this.frequencyValue == 0
      ) {
        this.generateErrorMessage('Enter frequeny value');
        return;
      }
    }
    let timeSlot: null | string = null;
    if (this.scheduleMode === 'Time') {
      if (this.scheduleTimeList.length <= 0) {
        this.generateErrorMessage('Timeslots required');
        return;
      }
      let timeSlots: string[] = [];
      this.scheduleTimeList.map((time) => {
        const date = formatDate(time?.time, 'HH:mm', 'en-US');
        timeSlots.push(date);
      });
      timeSlot = timeSlots.join('|');
    }
    let daysOfWeek = 'Su|Mo|Tu|We|Th|Fr|Sa';
    if (this.scheduleType === 'Weekly') {
      if (this.selectedDays?.length === 0) {
        this.generateErrorMessage('Select atleast one day');
        return;
      }
      daysOfWeek = this.getDaysObjectAndString(this.selectedDays)?.daysStr;
    }
    if (
      this.configName === null ||
      this.configName === undefined ||
      this.configName === ''
    ) {
      this.generateErrorMessage('Configuration is mandatory');
      return;
    }

    let payload = {
      vitalTypeName: this.selectedVitalType?.name,
      patientId: this.patientId,
      collectMode: this.scheduleMode.toLowerCase(),
      frequency: this.scheduleMode === 'Frequency' ? this.frequencyValue : null,
      timeSlots: this.scheduleMode === 'Time' ? timeSlot : null,
      scheduleDayOfWeek: daysOfWeek,
      startDate: formatDate(
        this.scheduleStartDate,
        'yyyy-MM-dd 00:00:00',
        'en-US'
      ),
      endDate: formatDate(this.scheduleEndDate, 'yyyy-MM-dd 23:59:59', 'en-US'),
      createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      scheduleTitle: this.configName,
    };
    console.log(payload);
    this.loaderService.show();
    this.vitalService.collectNowVitalRequest(payload).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.schedulePopup = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Schedule created successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0],
        });
      }
    });
  }

  getDaysObjectAndString(inputDays: string[]): {
    daysObj: { [key: string]: boolean };
    daysStr: string;
  } {
    const daysMapping: { [key: string]: string } = {
      Su: 'sunday',
      Mo: 'monday',
      Tu: 'tuesday',
      We: 'wednesday',
      Th: 'thursday',
      Fr: 'friday',
      Sa: 'saturday',
    };

    let daysObj: { [key: string]: boolean } = {
      sunday: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
    };

    inputDays.forEach((day) => {
      const fullName = daysMapping[day];
      if (fullName) {
        daysObj[fullName] = true;
      }
    });

    const daysStr = inputDays.join('|');

    return { daysObj, daysStr };
  }

  onTabChange(e: any) {
    console.log(e);
    this.activeIndex = e.index;
    if (e.index === 1) {
      this.vitalSchedules = [];
      this.getVitalSchedules();
    }
  }

  getVitalSchedules() {
    let payload = {
      patientId: this.patientId,
      vitalTypeName: this.selectedVitalType?.name,
    };
    this.loaderService.show();
    this.vitalService.getVitalSchedules(payload).subscribe((response) => {
      this.loaderService.hide();
      if (response.success) {
        this.vitalSchedules = response?.eList;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: response?.messages[0],
        });
      }
    });
  }

  deleteVitalSchedule(event: Event, id: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure you want to delete?',
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-info p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = { vitalScheduleId: id };
        this.loaderService.show();
        this.vitalService.deletVitalSchedules(payload).subscribe((response) => {
          this.loaderService.hide();
          if (response?.success) {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Vital schedule deleted successfully',
            });
            this.getVitalSchedules();
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: response?.messages[0],
            });
          }
        });
      },
      reject: () => {
        console.log('Delete No');
      },
    });
  }

  prevClick() {
    if (this.dailyEnabled) {
      if (this.currentDate < new Date()) {
        this.currentDate.setDate(this.currentDate.getDate() - 1);
      } else {
        return;
      }
    } else if (this.weeklyEnabled) {
      this.currentWeekDate.setDate(this.currentWeekDate.getDate() - 7);
      this.startOfWeek.setDate(this.startOfWeek.getDate() - 7);
      this.endOfWeek.setDate(this.endOfWeek.getDate() - 7);
    }
    else if (this.monthlyEnabled) {
      this.currentMonthDate.setMonth(this.currentMonthDate.getMonth() - 1);
      this.currentMonth = formatDate(this.currentMonthDate, 'MMMM-yyyy', 'en-US');
      this.startOfMonth.setDate(this.currentDate.getDate() - 30);
      this.endOfMonth.setDate(this.currentDate.getDate());
    }
    this.getVitalGraphWeekly();
  }

  nextClick() {
    if (this.dailyEnabled) {
      this.currentDate.setDate(this.currentDate.getDate() + 1);
    } else if (this.weeklyEnabled) {
      if (this.currentWeekDate > new Date()) {
        return;
      }
      this.currentWeekDate.setDate(this.currentWeekDate.getDate() + 7);
      this.startOfWeek.setDate(this.startOfWeek.getDate() + 7);
      this.endOfWeek.setDate(this.endOfWeek.getDate() + 7);
    }
    else if (this.monthlyEnabled) {

      this.currentMonthDate.setMonth(this.currentMonthDate.getMonth() + 1);
      this.currentMonth = formatDate(this.currentMonthDate, 'MMMM-yyyy', 'en-US');
      this.startOfMonth.setDate(this.currentDate.getDate() + 30);
      this.endOfMonth.setDate(this.currentDate.getDate());
    }
    this.getVitalGraphWeekly();
  }

  setCurrentWeek() {
    const currentDate = new Date();
    this.startOfWeek = this.getStartOfWeek(currentDate);
    this.endOfWeek = this.getEndOfWeek(currentDate);
  }

  getStartOfWeek(date: Date): Date {
    const currentDay = date.getDay();
    const distanceToSunday = currentDay;
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - distanceToSunday);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek;
  }

  getEndOfWeek(date: Date): Date {
    const currentDay = date.getDay();
    const distanceToSaturday = 6 - currentDay;
    const endOfWeek = new Date(date);
    endOfWeek.setDate(date.getDate() + distanceToSaturday);
    endOfWeek.setHours(23, 59, 59, 999);
    return endOfWeek;
  }

  formatDate(date: Date): string {
    return date == undefined
      ? new Date().toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      })
      : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }

  isFutureDate(): boolean {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return this.currentDate > today;
  }

  exportToXlsx() {

    let startDate: any = this.currentDay;
    let endDate: any = this.currentDay;
    if (this.weeklyEnabled) {
      startDate = this.startOfWeek;
      endDate = this.endOfWeek
    }
    if (this.monthlyEnabled) {
      startDate = this.startOfMonth;
      endDate = this.endOfMonth
    }
    let reportRequest: ReportRequest = {
      vitalTypeNameList: [],
      patientId: this.patientId,
      endDate: formatDate(endDate, 'yyyy-MM-dd', 'en-US'),
      startDate: formatDate(startDate, 'yyyy-MM-dd', 'en-US'),
      periodType: '',
      format: '',
      requestedDate: '',
    };

    if (this.selectedVitalType?.name == 'Blood Pressure') {
      reportRequest.vitalTypeNameList = [
        'Systolic Blood Pressure',
        'Diastolic Blood Pressure',
      ];
      this.exportData(reportRequest);
    } else if (this.selectedVitalType?.name == 'Blood Sugar') {
      reportRequest.vitalTypeNameList = [
        'Fasting Blood Sugar',
        'Random Blood Sugar',
      ];
      this.exportData(reportRequest);
    } else {
      reportRequest.vitalTypeNameList = [this.selectedVitalType?.name];
      this.exportData1(reportRequest);
    }
  }

  private exportData(reportRequest: ReportRequest) {
    this.vitalService.exportData(reportRequest).subscribe((res) => {
      if (res.success) {
        let data: exportData[] = res.vitalReportsGraphVO;
        let jsonData: any = new Array();
        data.forEach((item) => {
          let xlsData: any = {};
          xlsData[res.vitalTypeName1] = item.vitalName1Count;
          xlsData[res.vitalTypeName2] = item.vitalName2Count;
          xlsData['Date'] = item.period;
          jsonData.push(xlsData);
        });
        this.excelService.exportJsonAsExcelFile(jsonData, 'report');
      }
    });
  }
  private exportData1(reportRequest: ReportRequest) {
    this.vitalService.exportData(reportRequest).subscribe((res) => {
      if (res.success) {
        let data: exportData[] = res.vitalReportsGraphVO;
        let jsonData: any = new Array();
        data.forEach((item) => {
          let xlsData: any = {};
          xlsData[res.vitalTypeName1] = item.vitalName1Count;
          xlsData['Date'] = item.period;
          jsonData.push(xlsData);
        });
        this.excelService.exportJsonAsExcelFile(jsonData, 'report');
      }
    });
  }

  // onMinValueOneChange(value: number) {
  //   console.log('minValueOne changed to:', value);
  //   this.minValueOne = value;
  // }

}
