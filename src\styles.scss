/* You can add global styles to this file, and also import other style files */

@import "assets/layout/styles/layout/layout.scss";

/* PrimeNG */
@import "../node_modules/primeng/resources/primeng.min.css";
@import "../node_modules/primeflex/primeflex.scss";
@import "../node_modules/primeicons/primeicons.css";

/* Demos */
@import "assets/watchrx/styles/flags/flags.css";
@import "assets/watchrx/styles/badges.scss";
@import "assets/watchrx/styles/code.scss";

@import url('https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap');

body {
  font-family: 'Lato', sans-serif;
  font-style: normal;
  color: #000000;
}

.red {
  color: red;
  padding: 5px;
  font-style: normal;
  font-family: 'Lato', sans-serif;
}

.p-card .p-card-content {
  padding: 0rem 0;
  border-radius: 8px;
}

.breadcrumb
{
    padding:20px;
    background-color: #ffffff;
    box-shadow: 0px 2px 4px -3px #000000;
    margin-bottom: 10px;
}
.p-text-danger
{
    color: red !important;
}
.position-relative
{
    position: relative;
}
.font-12 {
  font-size: 12px;
}
.p-text-primary
{
    color: #6366F1;
}
.font-13
{
  font-size: 13px;
}
.p-text-warning
{
    color: #d85800
}
.p-datatable .p-datatable-tbody > tr:hover {
  background:#ececed !important;
  color: #4b5563;
}
.p-dropdown-items-wrapper
{
    max-height: 210px !important;
    overflow-y: auto !important;
}
.p-datatable .p-datatable-tbody > tr > td {
  padding: 0.8rem 1rem !important;
}
.ellipsis {
  white-space: nowrap; /* Prevents text from wrapping */
  overflow: hidden; /* Hides overflowing text */
  text-overflow: ellipsis; /* Displays ellipsis when text overflows */
}

*:focus {
  outline: none !important;
  box-shadow: none !important;
}
::ng-deep .table-min-height
{
    min-height: 150px;
}