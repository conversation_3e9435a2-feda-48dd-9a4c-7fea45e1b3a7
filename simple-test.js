const { app, BrowserWindow } = require('electron');

console.log('Script started');

function createWindow() {
  console.log('Creating window...');
  
  const win = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  console.log('Loading HTML...');
  win.loadURL('data:text/html,<h1>Hello Electron!</h1><p>This is a test window</p>');
  
  win.webContents.openDevTools();
  
  console.log('Window created and loaded');
  
  return win;
}

app.whenReady().then(() => {
  console.log('App ready, creating window...');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

console.log('Script setup complete');
