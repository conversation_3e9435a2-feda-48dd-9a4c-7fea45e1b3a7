<div class="card">
  <p-toast /> 
  <div class="p-fluid p-formgrid grid">
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Early Morning</b></label>
      <p-calendar
        [(ngModel)]="earlyMorningTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Breakfast</b></label>
      <p-calendar
        [(ngModel)]="bfTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Lunch</b></label>
      <p-calendar
        [(ngModel)]="luncTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Afternoon Snacks</b></label>
      <p-calendar
        [(ngModel)]="afnsTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Dinner</b></label>
      <p-calendar
        [(ngModel)]="dinnerTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
    <div class="field col-12 md:col-4">
      <label htmlfor="earlyMorning"><b>Bed</b></label>
      <p-calendar
        [(ngModel)]="bedTime"
        [timeOnly]="true"
        hourFormat="12"
        [showIcon]="true"
        [iconDisplay]="'input'"
        [showButtonBar]="true"
      ></p-calendar>
    </div>
  </div>
  <div class="field col-12 md:col-12 flex justify-content-end">
    <p-button
      label="Cancel"
      severity="primary"
      (click)="resetSchedule()"
      [outlined]="true"
      class="mr-3"
    />
    <p-button
      label="Save"
      severity="primary"
      (click)="updateDailySchedule()"
    />
  </div>
</div>
