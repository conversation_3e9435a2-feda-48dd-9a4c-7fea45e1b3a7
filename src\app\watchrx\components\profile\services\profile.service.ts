import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { Constants } from './constants';
@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  constructor(private http: HttpClient) { }
  updatePassword(details: any): Observable<any> {
      return this.http.post<any>(
        environment.BASE_URL + Constants.CHANGE_PASSWORD,
        details
      );
    }
    getProfileDetails(): Observable<any> {
      return this.http.get<any>(
        environment.BASE_URL + Constants.USER_PROFILE_DETAILS
      );
    }
    updateProfileDetails(formData:any): Observable<any> {
      return this.http.post<any>(
        environment.BASE_URL + Constants.UPDATE_USER_DETAILS,formData
      );
    }
}
