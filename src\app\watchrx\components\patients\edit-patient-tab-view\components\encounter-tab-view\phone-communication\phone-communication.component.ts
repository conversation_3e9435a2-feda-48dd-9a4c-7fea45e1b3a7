import { CommonModule, formatDate } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { PhoneCommunication, Program } from '../../../../../../api/phoneComm';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { GenericDateService } from '../../../../../../service/generic-date.service';
import { convertToMins } from '../../../../../../utils/utils';
import { PhoneCommService } from './service/phone-comm.service';
import { SidebarModule } from 'primeng/sidebar';
import { CommonService } from '../../../../../../service/common.service';

@Component({
  selector: 'app-phone-communication',
  standalone: true,
  imports: [
    ButtonModule,
    TableModule,
    DialogModule,
    RadioButtonModule,
    FormsModule,
    CommonModule,
    CalendarModule,
    InputNumberModule,
    ToastModule,
    ConfirmDialogModule,
    SidebarModule
  ],
  templateUrl: './phone-communication.component.html',
  styleUrl: './phone-communication.component.scss',
  providers: [ConfirmationService, MessageService],
})
export class PhoneCommunicationComponent implements OnInit {
  patientId: number = -1;
  phoneCommsList: PhoneCommunication[] = [];
  totalCount: number = 0;
  programList: Program[] = [];
  visible: boolean = false;
  selectedProgram: string = '';
  noteDescription: string = '';
  maxLength: number = 100;
  callStartTime: Date = new Date();
  durationInMins: number = 0;
  phoneCommunicationId: number = 0;
  isEdit: boolean = false;
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();
title="Add Phone Record";
  constructor(
    private phoneCommService: PhoneCommService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService,
    private dateService: GenericDateService,
    private commonService:CommonService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
  }

  loadPhoneCommsList($event: LazyLoadEvent | TableLazyLoadEvent) {
    this.loaderService.show();
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;
    let payload = this.patientId + '/' + pageNo + '/' + pageSize;
    this.phoneCommService.getPhoneCommLogs(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.dataEmitter.emit(res?.minsDetails);
        this.phoneCommsList = res?.phoneCommunicationVOList;
        this.totalCount = res?.count;
        this.programList = res?.programs;
      }
    });
  }

  addPhoneComms() {
    if (this.selectedProgram === undefined || this.selectedProgram === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select program',
        life: 3000,
      });
      return;
    }

    if (this.noteDescription === undefined || this.noteDescription === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please add notes',
        life: 3000,
      });
      return;
    }

    let payload: any = {
      note: this.noteDescription,
      patientId: this.patientId,
      callStartTime: formatDate(
        this.callStartTime,
        'yyyy-MM-dd 00:00:00',
        'en-US'
      ),
      duration: this.durationInMins,
      timeStamp: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      review: this.selectedProgram,
    };
    if (this.isEdit) {
      payload.phoneCommunicationId = this.phoneCommunicationId;
    }
    this.loaderService.show();
    this.phoneCommService.addPhoneCommLogs(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.visible = false;
        this.loadPhoneCommsList({ first: 0, rows: 25 });
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Notes added successfully.',
          life: 3000,
        });
        this.commonService.updateData(true);
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to create notes',
          life: 3000,
        });
      }
    });
  }

  deletePhoneComm(event: Event, phoneCommunicationId: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = {
          phoneCommunicationId: phoneCommunicationId,
        };
        this.phoneCommService.deletePhoneCommLogs(payload).subscribe((res) => {
          if (res.success) {
            this.loadPhoneCommsList({ first: 0, rows: 25 });
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Notes deleted successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete Notes.',
            });
          }
        });
      },
      reject: () => {},
    });
  }

  resetFields() {
    this.selectedProgram = '';
    this.noteDescription = '';
    this.maxLength = 100;
    this.callStartTime = new Date();
    this.durationInMins = 0;
    this.phoneCommunicationId = 0;
  }

  editNotes(item: PhoneCommunication) {
    this.title="Edit Phone Record";
    this.resetFields();
    this.visible = true;
    this.isEdit = true;
    this.noteDescription = item?.note;
    this.callStartTime = this.convertStringToDate(item?.callStartTime);
    this.durationInMins = item?.duration;
    this.phoneCommunicationId = item?.phoneCommunicationId;
    this.selectedProgram = item?.review||''
  }

  convertStringToDate(dateTime: string): Date {
    const [datePart, timePart] = dateTime.split(' ');
    const [month, day, year] = datePart.split('-').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);

    return new Date(year, month - 1, day, hours, minutes, seconds);
  }

  formattedTime(val: number): string {
    return convertToMins(val);
  }

  showDialog(): void {
    this.resetFields();
    this.visible = !this.visible;
    this.isEdit = false;
  }

  remainingChars(): number {
    return this.maxLength - this.noteDescription.length;
  }
}
