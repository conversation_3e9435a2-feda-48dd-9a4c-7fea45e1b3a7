import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { login, loginResponse } from '../../../api/authentication';
import { Constants } from '../auth.constantant';
@Injectable({
  providedIn: 'root',
})
export class AuthenticatorService {
  constructor(private http: HttpClient) {}
  private _userDetails: any;
  doLogin(loginDetais: login): Observable<loginResponse> {
    return this.http.post<loginResponse>(
      environment.BASE_URL + Constants.LOGIN_URL,
      loginDetais
    );
  }

  get isAuthenticated(): boolean {
    if (localStorage.getItem('user')) {
      return true;
    }
    return false;
  }

  setOrgId(orgId: number, userId: number): Observable<loginResponse> {
    return this.http.get<loginResponse>(
      environment.BASE_URL + Constants.ORG_ID_URL + orgId + '/' + userId
    );
  }

  setOrgId1(data:any): Observable<loginResponse> {
    return this.http.post<loginResponse>(
      environment.BASE_URL + Constants.ORG_ID_URL, data
    );
  }

  sendResetPwdLink(userName: string): Observable<loginResponse> {
    let email = { userName: userName };
    return this.http.post<loginResponse>(
      environment.BASE_URL + Constants.FORGOT_PASSWORD_URL,
      email
    );
  }
}
