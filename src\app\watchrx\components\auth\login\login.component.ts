import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { login } from '../../../api/authentication';
import { LoaderService } from '../../../loader/loader/loader.service';
import { AuthenticatorService } from '../service/authenticator.service';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  providers: [MessageService],
  styles: [
    `
      :host ::ng-deep .pi-eye,
      :host ::ng-deep .pi-eye-slash {
        transform: scale(1.6);
        margin-right: 1rem;
        color: var(--primary-color) !important;
      };
      :host ::ng-deep .p-password{
        width:100% !important
      }
      :host ::ng-deep .selectorg{
      .p-dropdown{
          width:100% !important;
        }
      }
      `
  ],
})
export class LoginComponent {
  valCheck: string[] = ['remember'];
  username: string = "";
  password: string = "";
  submitted: boolean = false;
  roleNum!: any;
  orgs: any[] = [];
  orgNum!: any;
  display: boolean = false;
  userId!: number;

  loading: boolean = false;
  roles = [
    { name: 'Admin', code: '2' },
    { name: 'Case Manager', code: '5' },
    { name: 'Physician', code: '3' },
    { name: 'Care Giver', code: '4' },
    { name: 'Patient', code: '6' },
  ];
  userotp: string = '';
  checkOTPFromLogin: string = 'N';
  constructor(
    private authenticatorService: AuthenticatorService,
    private service: MessageService,
    private router: Router,
    private loader: LoaderService
  ) { }
  signIn() {
    this.submitted = true;
    this.loader.show();
    if (this.username != "" && this.password != "") {
      this.loading = true;
      let loginDetails: login = {
        userName: this.username,
        password: this.password,
        roleId: this.roleNum != undefined ? this.roleNum.code : null,
      };
      this.authenticatorService.doLogin(loginDetails).subscribe((res) => {
        this.loading = false;
        this.loader.hide();
        if (res.success) {
          this.checkOTPFromLogin = res.loggedinUser.otpEnabled
          localStorage.setItem('user', JSON.stringify(res.loggedinUser));
          this.userId = res.loggedinUser.userId;
          // this.idleService.startWatchingIdle();
          if (res.loggedinUser.roleType == 2) {
            this.router.navigate(['/admin/billing-reports']);
          } else if (res.loggedinUser.roleType == 1) {
            this.router.navigate(['/admin/inventory']);
          } else {
            this.display = true;
            for (let statusType of res.loggedinUser.featuresEnabled) {
              this.orgs.push({
                label: statusType.groupName,
                value: statusType.groupId,
              });
            }
          }
        } else {
          this.loading = false;
          this.loader.hide();
          this.service.add({
            key: 'tst',
            severity: 'error',
            summary: 'Error',
            detail: res.messages[0],
          });
        }
      });
    }
    else {
      this.loading = false;
      this.loader.hide();
      // this.service.add({
      //   key: 'tst',
      //   severity: 'error',
      //   summary: 'Error',
      //   detail: "Enter Username and Password",
      // });
    }
  }
  loadingPop: boolean = false;
  continue() {
    if (this.checkOTPFromLogin == 'Y') {
      if (this.orgNum == undefined || this.userotp == "") {
        this.service.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Please fill the details',
          life: 3000,
        });
        return
      }
      let obj =
      {
        orgId: this.orgNum.value,
        otp: this.userotp,
        userId: this.userId
      }
      this.loadingPop = true;
      this.authenticatorService.setOrgId1(obj).subscribe((res) => {
        if (res.success) {
          this.loadingPop = false;
          let loginResponse = JSON.parse(localStorage.getItem('user')!);
          loginResponse.orgId = this.orgNum.value;
          localStorage.setItem('user', JSON.stringify(loginResponse));
          this.router.navigate(['/dashboard']);
        }
        else {
          this.loadingPop = false;
          this.service.add({
            severity: 'error',
            summary: 'Rejected',
            detail: res['messages'][0],
            life: 3000,
          });
        }
      },err=>{
        this.loadingPop = false;
        this.service.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Something went wrong, please try again later',
          life: 3000,
        });
      })
    }
    else {
      if (this.orgNum == undefined) {
        this.loadingPop = false;
        this.service.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Please select Organization',
          life: 3000,
        });
        return
      }
      else {
        let loginResponse = JSON.parse(localStorage.getItem('user')!);
        loginResponse.orgId = this.orgNum.value;
        localStorage.setItem('user', JSON.stringify(loginResponse));
        this.router.navigate(['/dashboard']);
      }
    }
  }
}
