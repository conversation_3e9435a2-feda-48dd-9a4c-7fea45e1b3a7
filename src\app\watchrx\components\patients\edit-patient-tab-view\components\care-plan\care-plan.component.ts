import { CommonModule, formatDate } from '@angular/common';
import { Component, EventEmitter, HostListener, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { Chronic } from '../../../../../api/carePlan';
import { Program } from '../../../../../api/patientEncounter';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { CarePlanService } from './service/care-plan.service';
import { ProgressBarModule } from 'primeng/progressbar';
import { SidebarModule } from 'primeng/sidebar';
import { AccordionModule } from 'primeng/accordion';

@Component({
  selector: 'app-care-plan',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MenuModule,
    TableModule,
    StyleClassModule,
    PanelMenuModule,
    ButtonModule,
    TableModule,
    InputGroupModule,
    InputGroupAddonModule,
    DropdownModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    RadioButtonModule,
    CheckboxModule,
    DialogModule,
    InputTextareaModule,
    DividerModule,
    CardModule,
    TooltipModule,
    ToastModule,
    ProgressBarModule,
    MultiSelectModule,
    SidebarModule,
    AccordionModule
  ],
  templateUrl: './care-plan.component.html',
  styleUrl: './care-plan.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class CarePlanComponent implements OnInit {
  manditoryTotalCount: any = 0;
  commonTotalCount: any = 0;
  chronicTotalCount: any = 0;
  manditorySelectedCount: any = 0;
  commonSelectedCount: any = 0;
  chronicSelectedCount: any = 0;
  chronicCount: number = 0;
  manditoryCount: number = 0;
  commonCount: number = 0;
  fileDownloadLoading: boolean = false;
  constructor(
    private carePlanService: CarePlanService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) {
    this.currentDate = new Date();
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
  }
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();

  position: string = 'center';
  patientId: number = -1;
  templateList: Chronic[] = [];
  tabs: any[] = [];
  tabsQuestions: any = [];
  programs: Program[] = [];
  selectedProgram: any = '';
  selectedChonic: any | undefined = -1;
  chronicConditionsQnsAndAns: any = '';
  selecteTabId: number = -1;
  questions: any[] = [];
  isHistoryNeedDisplay: boolean = true;
  hstData: any = [];
  selectedAnswers: any = {};
  comments: any = {};
  selectedChonic1: any[] = [];
  selectedMenu: string = "mandatorysection";
  sideMenu: any[] = [
    { label: 'Mandatory Questions', value: 'mandatorysection', count: 0 }
  ]

  @HostListener('window:scroll', [])
  onScroll(): void {
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
      const rect = section.getBoundingClientRect();
      // Check if the section is in view
      if (rect.top <= 5 && rect.bottom > 5) {
        this.selectedMenu = section.id;
      }
    });
  }
  ngOnInit(): void {
    console.log('Selected Templated:', this.selectedChonic);
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getSelectedChronic();
    this.getTabsConfig();
    this.getTabsQuestions();
    this.getLatestCarePlans();
    // this.dataEmitter.emit({});
  }

  getSelectedChronic() {
    this.loaderService.show();
    this.carePlanService.getSelectedChronic(this.patientId).subscribe((res) => {
      this.loaderService.hide();
      if (res?.status) {
        this.templateList = res.data;
      }
    }, err => {
      this.loaderService.hide();
    });
  }
  getTabsConfig() {
    this.loaderService.show();
    this.carePlanService.getTabs().subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.tabs = res;
      }
    }, err => {
      this.loaderService.hide();
    });
  }

  getTabsQuestions(isFromSubmit = false) {
    this.manditoryTotalCount = 0;
    this.commonTotalCount = 0;
    this.chronicTotalCount = 0;
    this.manditorySelectedCount = 0;
    this.commonSelectedCount = 0;
    this.chronicSelectedCount = 0;
    this.chronicCount = 0;
    this.manditoryCount = 0;
    this.commonCount = 0;
    this.loaderService.show();

    let obj = {}
    obj = { patientId: this.patientId }
    if (isFromSubmit) {
      let chonicId: any[] = [];
      this.selectedChonic1.forEach((d: any) => {
        chonicId.push(d?.id.toString());
      })
      obj = { patientId: this.patientId, programId: this.selectedProgram.id, chronicIds: chonicId }
    }
    this.carePlanService.getTabsQuestions(obj).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.tabsQuestions = res;
        this.tabsQuestions.forEach((d: any) => {
          d.notes = "";
        })

        if (this.selectedProgram.id) {
          this.sideMenu = [
            { label: 'Mandatory Questions', value: 'mandatorysection', count: 0 },
            { label: 'Common Questions', value: 'commonsection', count: 0 },
            { label: 'Chronic Condition  Questions', value: 'ccsection', count: 0 },
          ]
          this.manditoryTotalCount = this.sideMenu[0].count = this.tabsQuestions.filter((qe: any) => qe.category == 'Mandatory').length;
          this.commonTotalCount = this.sideMenu[1].count = this.tabsQuestions.filter((qe: any) => qe.category == 'Common').length;
          this.chronicTotalCount = this.sideMenu[2].count = this.tabsQuestions.filter((qe: any) => qe.category != 'Common' && qe.category != 'Mandatory').length;
        }
      }
    }, err => {
      this.loaderService.hide();
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Failed to update.',
      });
    });
  }


  // onTemplateChange(event: any) {
  //   if (this.selectedChonic?.id > -1) {
  //     this.getTemplateDetailsById();
  //   } else {
  //     this.selectedChonic = -1;
  //   }
  // }

  getLatestCarePlans() {
    this.loaderService.show();
    this.carePlanService.getLatestCarePlan(this.patientId).subscribe((res) => {
      this.loaderService.hide();
      console.log('Programs', res);
      if (res) {
        this.programs = res?.programs;
      }
    }, err => {
      this.loaderService.hide();
    });
  }

  submitFormData(id: number) {
    console.log('this.selectedProgram', this.selectedProgram);
    if (this.selectedProgram.value === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select program',
      });
      return;
    }
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
      carePlanId: id,
      notes: this.tabsQuestions[id],
      createdDate: formatDate(new Date(), 'MM-dd-yyyy hh:mm:ss a', 'en-US'),
      encounterStartTime: formatDate(
        this.currentDate,
        'yyyy-MM-dd HH:mm:ss',
        'en-US'
      ),
      reviewType: this.selectedProgram.value,
    };
    console.log('Request:', req);
    this.carePlanService.submitCarePlanNotes(req).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Updated successfully',
        });
        this.getTabsQuestions();
      } else {
        console.log(res);
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update.',
        });
      }
    }, err => {
      this.loaderService.hide();
    });
  }

  showCarePlanQndAns(id: number, tabeName: string) {
    this.currentDate = new Date();
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
    this.isShowHistory = !this.isShowHistory;
    this.selectedTabName = tabeName;
    this.getCarePlanDetails(id);
    this.selecteTabId = id;
  }

  getCarePlanDetails(id: number) {
    this.loaderService.show();
    let details = this.currentMonthCp + '/' + id + '/' + this.patientId;
    this.carePlanService.getCarePlanDetails(details).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.chronicConditionsQnsAndAns = res;
      }
    }, err => {
      this.loaderService.hide();
    });
  }

  getTemplateDetailsById() {
    if (this.selectedChonic?.id < 0) {
      return;
    }
    this.loaderService.show();
    let payload = {
      chronicConditionId: this.selectedChonic?.id,
      patientId: this.patientId,
    };

    this.carePlanService
      .templateDetailsByChronicId(payload)
      .subscribe((res) => {
        this.loaderService.hide();
        if (res) {
          this.questions = res;
        }
      }, err => {
        this.loaderService.hide();
      });
  }

  onSubmitFormData() {
    if (this.selectedProgram.id === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select program',
      });
      return;
    }

    let data: any[] = [];
    if (this.tabsQuestions && this.tabsQuestions.length > 0) {
      this.tabsQuestions.forEach((qustn: any) => {
        let selectedAns = qustn.answer
        let addedComments = qustn.notes
        if (selectedAns || addedComments) {
          data.push({
            questionId: qustn.questionId,
            answer: selectedAns,
            comment: addedComments ? addedComments : '',
          });
        }
      });
      if (data && data.length > 0) {
        let createdDate = new Date();
        let encounterStartTime = formatDate(
          createdDate,
          'yyyy-MM-dd HH:mm:ss',
          'en-US'
        );

        let durationInMilliseconds = createdDate.getTime() - this.currentDate.getTime();
        const seconds = Math.abs(Math.floor(durationInMilliseconds / 1000));
        const minutes = Math.floor(seconds / 60);
        let payload = {
          duration: minutes,
          encounterStartTime: encounterStartTime,
          patientId: this.patientId,
          programName: this.selectedProgram.value,
          templateDetails: data,
        };
        this.loaderService.show();
        this.carePlanService
          .submitTemplateQAnsNotes(payload)
          .subscribe((res) => {
            this.loaderService.hide();
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Updated successfully',
              });
              this.getLatestCarePlans();
              this.getTabsQuestions();
              this.selectedChonic1 = [];
              this.selectedProgram = null
            } else {
              console.log(res);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to update.',
              });
            }
          }, err => {
            this.loaderService.hide();
          });
      } else {
        this.messageService.add({
          key: 'warning',
          severity: 'error',
          summary: 'Error',
          detail: 'Please select questions and answers before submit',
        });
      }
    }
  }

  isShowHistory: boolean = false;
  selectedTabName: any = '';
  currentMonthCp: string;
  currentDate: Date;
  formatCurrentMonth(date: Date): string {
    return formatDate(date, 'MMMM-yyyy', 'en-US');
  }

  onPreviousClick() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
    // if (this.selecteTabId > -1) {
    //this.getCarePlanDetails(this.selecteTabId);
    this.getHistoryData()
    // }
  }

  onNextClick() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    if (this.currentDate > new Date()) {
      this.currentDate.setMonth(this.currentDate.getMonth() - 1);
      return;
    }
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
    //if (this.selecteTabId > -1) {
    //this.getCarePlanDetails(this.selecteTabId);
    this.getHistoryData()
    // }
  }

  onPreviousMonthTemp() {
    this.currentDate.setMonth(this.currentDate.getMonth() - 1);
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
    this.isHistoryNeedDisplay = this.isSameMonthYear(this.currentMonthCp);
    if (!this.isHistoryNeedDisplay) {
      this.getHistoryData();
    } else {
      this.getTemplateDetailsById();
    }
  }

  onNextMonthTemp() {
    this.currentDate.setMonth(this.currentDate.getMonth() + 1);
    if (this.currentDate > new Date()) {
      this.currentDate.setMonth(this.currentDate.getMonth() - 1);
      return;
    }
    this.currentMonthCp = this.formatCurrentMonth(this.currentDate);
    this.isHistoryNeedDisplay = this.isSameMonthYear(this.currentMonthCp);
    if (!this.isHistoryNeedDisplay) {
      this.getHistoryData();
    } else {
      this.getTemplateDetailsById();
    }
  }

  convertText(originalText: any) {
    return originalText.replace(/\n/g, '<br>');
  }

  isSameMonthYear(dateStr: string): boolean {
    const [monthStr, yearStr] = dateStr.split('-');
    const monthNames = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December',
    ];
    const month = monthNames.indexOf(monthStr);
    const year = parseInt(yearStr, 10);
    const inputDate = new Date(year, month, 1);
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    return (
      inputDate.getMonth() === currentMonth &&
      inputDate.getFullYear() === currentYear
    );
  }

  getHistoryData() {
    this.isShowHistory = true;
    this.loaderService.show();
    let details =
      this.currentMonthCp +
      '/' +
      this.patientId;
    this.carePlanService.getTemplateHistoryData(details).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.hstData = res;
        console.log(this.hstData)
      }
    }, err => {
      this.loaderService.hide();
    });
  }

  downloadTemplateHistory() {
    this.fileDownloadLoading = true;
    let fileName =
      'PatientId:' +
      this.patientId +
      '_' +
      this.currentMonthCp +
      '_' +
      '_Template_Report' +
      '.xlsx';
    let details =
      this.currentMonthCp +
      '/' +
      this.patientId;

    this.loaderService.show();
    this.carePlanService
      .downloadTempHistory(details, fileName)
      .subscribe((res) => {
        this.fileDownloadLoading = false;
        this.loaderService.hide();
      }, err => {
        this.loaderService.hide();
        this.fileDownloadLoading = false;
      });
  }


  scrollToDiv(id: string): void {
    this.selectedMenu = id
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }
  // showHistory() {
  //   this.isShowHistory = true;
  //   let obj = {}
  //   this.carePlanService.getTemplateHistoryData(this.patientId)
  //   .subscribe((res) => {
  //     this.loaderService.hide();
  //   });
  // }
  changeProgressbarStatus(e: any, i: any) {
    if (i == 'Mandatory') {
      let filter = this.tabsQuestions.filter((d: any) => d.answer != null && d.category == 'Mandatory');
      if (filter.length > 0) {
        this.manditoryCount = ((filter.length / this.manditoryTotalCount) * 100);
        this.manditorySelectedCount = filter.length;
      }
    }
    else if (i == 'Common') {
      let filter = this.tabsQuestions.filter((d: any) => d.answer != null && d.category == 'Common');
      if (filter.length > 0) {
        this.commonCount = (filter.length / this.commonTotalCount) * 100;
        this.commonSelectedCount = filter.length;
      }
    }
    else {
      let filter = this.tabsQuestions.filter((d: any) => d.answer != null && d.category != 'Common' && d.category != 'Mandatory');
      if (filter.length > 0) {
        this.chronicCount = (filter.length / this.chronicTotalCount) * 100;
        this.chronicSelectedCount = filter.length;
      }
    }

  }


  totalDialogCount: any;
  dialogList: any;
  isShowDialog: boolean = false;
  showDialog() {
    this.loader = true;
    this.isShowDialog = true;
    this.carePlanService.getDialog(this.patientId)
      .subscribe((res) => {
        this.dialogList = res;
        this.totalDialogCount = res.length;
        this.loader = false;
      });
  }

  sendDialog(dialog: any) {
    this.carePlanService.sendDialog(this.patientId, dialog.dlgId).subscribe((res) => {
      console.log(res)
      this.isShowDialog = false;
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: 'Sent successfully',
      });
    }, err => {
      console.log(err)
      this.isShowDialog = false;
      this.messageService.add({
        key: 'warning',
        severity: 'error',
        summary: 'Error',
        detail: 'Something went wrong',
      });
    });
  }

  totalTriageCount: any;
  triageList: any;
  isShowTriage: boolean = false;
  showTriage() {
    this.loader = true;
    this.isShowTriage = true
    let obj = { "patient": this.patientId, "dialogName": "testusera_watchrxcare" }
    this.carePlanService.getTriage(obj)
      .subscribe((res) => {
        this.triageList = res;
        this.totalTriageCount = res.length;
        this.loader = false;
      }, err => {
        this.loader = false;
      });
  }

  deleteDialog(dialog: any) {
    this.carePlanService.deleteDialog(dialog).subscribe((res) => {
      this.isShowTriage = false;
      this.isShowSDOH = false;
      if (res == 'success') {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Sent successfully',
        });
      }

    }, err => {
      console.log(err)
      this.isShowTriage = false;
      this.isShowSDOH = false;
      this.messageService.add({
        key: 'warning',
        severity: 'error',
        summary: 'Error',
        detail: 'Something went wrong',
      });
    });
  }
  totalSDOHCount: any;
  sdohList: any;
  loader: boolean = false;
  isShowSDOH: boolean = false;
  showSDOH() {
    this.loader = true;
    this.isShowSDOH = true;
    let obj = { "patient": this.patientId, "dialogName": "testusera_watchrxsdoh" }
    this.carePlanService.getSDOH(obj).subscribe((res) => {
      this.sdohList = res;
      this.totalSDOHCount = res.length;
      this.loader = false;
    }, err => {
      this.loader = false;
    });
  }
  chatArray: any[] = [];
  openDialog = false;
  showConversation(sdoh: string) {
    this.openDialog = true;
    const chatData = JSON.parse(sdoh);
    this.chatArray = Object.entries(chatData).map(([question, answer]) => ({
      question,
      answer
    }));
  }

   updateScrollBlocking() {
    const anySidebarOpen =
      this.isShowSDOH ||
      this.isShowTriage ||
      this.isShowHistory ||
      this.isShowDialog
  
    if (anySidebarOpen) {
      document.body.classList.add('blocked-scroll');
    } else {
      document.body.classList.remove('blocked-scroll');
    }
  }

}
