main {
	width: 70%;
	margin: auto;
	text-align: center;
}

main #join-flow button {
	margin-top: 20px;
	background-color: #2D8CFF;
	color: #ffffff;
	text-decoration: none;
	padding-top: 10px;
	padding-bottom: 10px;
	padding-left: 40px;
	padding-right: 40px;
	display: inline-block;
	border-radius: 10px;
	cursor: pointer;
	border: none;
	outline: none;
}

main #join-flow button:hover {
	background-color: #2681F2;
}

div.volume-indicators {
	padding: 10px;
	margin-top: 20px;
	width: 300px;
}

div.volume-indicators>div {
	display: block;
	height: 20px;
	width: 0;
}

.hide {
	position: absolute !important;
	top: -9999px !important;
	left: -9999px !important;
}

.call-animation-play {
	background: rgb(130, 221, 233);
	width: 100px;
	height: 100px;
	border-radius: 100%;
	border: solid 5px rgb(252, 241, 241);
	animation: call 1.0s ease infinite;
	color: aliceblue;
	font-size: 35px;
	font-weight: bold;
	position: relative;
	align-items: center;
	justify-content: center;
}

.call-animation-pause {
	background: rgb(130, 221, 233);
	width: 100px;
	height: 100px;
	border-radius: 100%;
	border: solid 5px rgb(252, 241, 241);
	animation-play-state: paused;
	color: aliceblue;
	font-size: 35px;
	font-weight: bold;
	position: relative;
	align-items: center;
	justify-content: center;
}

.caller-img {
	position: absolute;
	height: 50px;
	width: 50px;
	top: 35px;
	left: 35px;
}

.config1 {

	h6,
	hr {
		margin: 0px !important;
	}

	::ng-deep .p-card-header {
		padding: 15px 20px;
		margin: 0px !important;
	}

	::ng-deep .p-card-body {
		padding: 0px !important
	}
}

.buttons {
	z-index: 1;
	right: 1rem;
}

::ng-deep .p-tabview-nav-content {
	padding: 0rem 1rem;
}

::ng-deep .p-tabview-nav-link {
	border: 1px solid !important;
	padding: 0.75rem 1.25rem !important;
	border-radius: 3px;
	margin: 0px !important;
	border-color: #e5e7eb !important;
}

::ng-deep .p-tabview-nav {
	border: 0px !important;
}

::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
	background: #f8f8fa !important;
	border-color: #e5e7eb !important;
	color: #6366F1;
}

.actionbuttons {
	::ng-deep .p-button.p-button-secondary.p-button-outlined {
		border-color: #DBDDE0 !important;
	}

	::ng-deep a {
		padding: 5px;
		width: 50px;
		font-size: 11px;
		text-decoration: underline;
		color: #000;
		font-weight: 500;
	}

}

::ng-deep .p-sidebar-footer {
	border-top: 1px solid #000000;
}

::ng-deep .p-sidebar-header {
	border-bottom: 1px solid #000000;
}

.footer {
	position: absolute;
	bottom: 0;
	border-top: 1px solid #52575C;
	width: 100%;
	justify-content: end;
}

.sidebarhr {
	border-top: 1px solid #52575C;
	margin: 0px !important;
}

::ng-deep .p-sidebar-content {
	padding: 0px !important;
	overflow: hidden;
}

.fileupload {
	::ng-deep .p-button {
		background-color: transparent;
		color: #6366F1;
		border: 1px solid;

	}
}

.orange {
	background-color: #ffc107;
	color: #000000;
}

.red {
	background-color: #E74F48;
	color: #FFFFFF;
}

.rounded-background {
	color: #FFFFFF;
	border-radius: 25px;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 96px;
	height: 28px;
	text-align: center;
	font-weight: 600;
}

.tabledropdown ::ng-deep .p-dropdown {
	width: 200px !important;
}

::ng-deep .footer1 {
	position: absolute;
	bottom: 23px;
	padding-left: 13px;
}