runtime: python312

env_variables:
  API_KEY: AIzaSyDdgwH8GDotEIAC768Pp8UxLiR1fPsWjyk

handlers:

# Serve static files (e.g., JS, CSS, images, and map files)
- url: /(.*\.(gif|png|jpg|jpeg|css|js|map|woff|woff2|ttf|eot|svg))$
  static_files: watchrx-ui/browser/\1
  upload: watchrx-ui/browser/.*\.(gif|png|jpg|jpeg|css|js|map|woff|woff2|ttf|eot|svg)
  http_headers:
    Cache-Control: 'no-cache, no-store, must-revalidate'
    Pragma: 'no-cache'
    Expires: '0'

# Serve assets (e.g., images from the assets folder)
- url: /assets
  static_dir: watchrx-ui/browser/assets
  http_headers:
    Cache-Control: 'no-cache, no-store, must-revalidate'
    Pragma: 'no-cache'
    Expires: '0'

# Serve PrimeIcons font files
- url: /assets/fonts
  static_dir: watchrx-ui/browser/assets/fonts
  http_headers:
    Cache-Control: 'no-cache, no-store, must-revalidate'
    Pragma: 'no-cache'
    Expires: '0'

# Catch all other routes and serve index.html for Angular routing
- url: /.*
  static_files: watchrx-ui/browser/index.html
  upload: watchrx-ui/browser/index.html
  http_headers:
    Cache-Control: 'no-cache, no-store, must-revalidate'
    Pragma: 'no-cache'
    Expires: '0'

automatic_scaling:
  target_cpu_utilization: 0.65
  min_instances: 1
  max_instances: 1
  min_pending_latency: 60ms
  max_pending_latency: automatic
  max_concurrent_requests: 100