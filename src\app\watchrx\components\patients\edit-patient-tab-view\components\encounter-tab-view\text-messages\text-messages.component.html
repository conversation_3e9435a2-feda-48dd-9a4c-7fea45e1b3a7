<div class="textmessagescreen">
  <p-confirmDialog />
  <p-toast />

  <p-tabView [(activeIndex)]="activeIndex" (activeIndexChange)="checkTab()">
    <p-tabPanel header="View Text Messages">
      <div *ngIf="activeIndex === 0" class="position-relative">
        <div class="flex md:justify-content-end mb-5 dialogbuttons">
          <p-button icon="pi pi-plus" severity="secondary" (click)="showDialog('txtmessage')" label="Send Text Message"
            [outlined]="true"></p-button>
        </div>
        <p-table [value]="viewTextMessageList" [paginator]="true" [totalRecords]="totalMessageCount" [rows]="25"
          [lazy]="true" (onLazyLoad)="loadTextMessageList($event)" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th style="width: 20%">Time Stamp</th>
              <th style="width: 35%">Message</th>
              <th style="width: 35%">Response</th>
              <th style="width: 10%">Duration</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-vqc>
            <tr>
              <td>{{ vqc.timeStamp | date : "MM-dd-yyyy h:m a" }}</td>
              <td>{{ vqc.question }}</td>
              <td>{{ vqc.answer }}</td>
              <td>{{ formattedTime(vqc.duration) }}</td>
            </tr>
          </ng-template>

        </p-table>
        <div class="footer1"> Total {{totalMessageCount}} Messages </div>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Schedule Text Messages">
      <div *ngIf="activeIndex === 1" class="position-relative">
        <div class="flex flex-column md:flex-row md:justify-content-end mb-5 dialogbuttons">
          <p-button icon="pi pi-plus" severity="secondary" (click)="showDialog('schmessage')" [outlined]="true"
            label="Schedule Text Message"></p-button>
        </div>
        <p-table [value]="scheduleTextMessageList" [paginator]="true" [totalRecords]="totalScheduleTextMessageCount"
          [rows]="25" [lazy]="true" (onLazyLoad)="loadScheduleTextMessageList($event)" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <!-- <th>S.No</th> -->
              <th>Start - End Date, Duration</th>
              <th>Time slot</th>
              <th>Recurrence</th>
              <th>Message</th>
              <th>Response Options</th>
              <th>Actions</th>

              <!-- <th>Text Message</th>
            <th>Options</th>
            <th>Start Date</th>
            <th>End Date</th>
            <th>Recurrence</th>
            <th>Time slots</th>
            <th>Date&Time</th>
            <th>Duration</th>
            <th>Actions</th> -->
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-vqc>
            <tr>
              <!-- <td>1</td> -->
              <td>{{ vqc.startDate }} - {{ vqc.endDate }} <br> {{ formattedTime(vqc.duration) }} </td>
              <td>
                <div *ngFor="let ts of vqc.timeSlotsArrary">
                  {{ ts }}
                </div>
              </td>
              <td>{{ getDays(vqc)}}</td>
              <td>{{ vqc.question }}</td>
              <td>
                <ol>
                  <li class="mr-2" *ngFor="let ans of vqc.answer">{{ ans }}</li>
                </ol>
              </td>
              <td>
                <div class="flex">
                  <button pButton icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
                    (click)="editScheduleTextMessage(vqc)"></button>
                  <button pButton icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    (click)="deleteMessage($event, vqc.scheduledTextMessagesId)" severity="danger"></button>
                </div>
              </td>


              <!-- <td>{{ vqc.question }}</td>
            <td>
              <div *ngFor="let ans of vqc.answer">
                <li class="mr-2">{{ ans }}</li>
              </div>
            </td>
            <td>{{ vqc.startDate }}</td>
            <td>{{ vqc.endDate }}</td>
            <td>{{ vqc.recurrence }}</td>
            <td>
              <div *ngFor="let ts of vqc.timeSlotsArrary">
                <li class="mr-2">
                  {{ ts }}
                </li>
              </div>
            </td>
            <td>{{ vqc.createdDate }}</td>
            <td>{{ formattedTime(vqc.duration) }}</td>
            <td>
              <div class="flex">
                <button
                  pButton
                  pRipple
                  icon="pi pi-pencil"
                  class="p-button-rounded p-button-success mr-2"
                  (click)="editScheduleTextMessage(vqc)"
                ></button>
                <button
                  pButton
                  pRipple
                  icon="pi pi-trash"
                  class="p-button-rounded p-button-warning"
                  (click)="deleteMessage($event, vqc.scheduledTextMessagesId)"
                  severity="danger"
                ></button>
              </div>
            </td> -->
            </tr>
          </ng-template>
        </p-table>
        <div class="footer1"> Total {{totalScheduleTextMessageCount}} Messages </div>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Chat">
      <div *ngIf="activeIndex === 2" class="position-relative">
        <div class="field col-12 md:col-12">
          <label htmlfor="state" class="font-bold block mb-2"
            >Select Program(s) <span class="p-text-danger">*</span></label
          >
          <div class="flex justify-content-left gap-3">
            <div class="flex flex-wrap gap-3">
              <div class="flex align-items-center" *ngFor="let option of programList">
                <p-radioButton
                  name="consent"
                  [value]="option.value"
                  [(ngModel)]="selectedChatProgram"
                  [inputId]="option.label.toLowerCase()"
                  variant="filled"
                  [required]="true"
                />
                <label [for]="option.label.toLowerCase()" class="ml-2">{{
                  option.label
                }}</label>
              </div>
            </div>
          </div>
        </div>
        <div  class="chat-container">
          <div class="chatbox" #chatContainer>
            <!-- Date Centered -->
            <ng-container *ngFor="let chat of chatHistory">
              <div class="chat-date">{{ chat.time }}</div>

              <div *ngFor="let msg of chat.data" class="chat-container1">
                <!-- Server Message (Right) -->
                <div class="chat-message server" *ngIf="msg.type === 'server'">
                  <div class="message-text">{{ msg.message }}</div>
                  <div class="message-time">{{ msg.time }}</div>
                </div>

                <!-- User Message (Left) -->
                <div class="chat-message user" *ngIf="msg.type === 'mobile'">
                  <div class="message-text">{{ msg.message }}</div>
                  <div class="message-time">{{ msg.time }}</div>
                </div>
              </div>
            </ng-container>

          </div>

          <!-- Input Box -->
          <div class="chat-input">
            <input type="text" [(ngModel)]="newMessage" placeholder="Type a message..." />
            <button (click)="sendMessage()"><i class="pi pi-send"></i></button>
          </div>
        </div>

      </div>
    </p-tabPanel>
  </p-tabView>

  <p-sidebar [(visible)]="visible" position="right" [style]="{ width: '43rem' }">
    <ng-template pTemplate="header">
      <div class="flex align-items-center gap-2">
        <span class="font-bold">
          <i class="pi pi-plus mr-3"></i>
          {{title}}
        </span>
      </div>
    </ng-template>
    <app-schedule-text-message-form #textMessageForm [programList]="programList" [isSechedule]="true"
      [isEnconterNeeded]="false" [phoneNumber]="" [isShowSMS]="isShowSMS" />
    <ng-template pTemplate="footer">
      <div class="flex justify-content-end gap-2">
        <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="onCancelModal()" />
        <p-button label="Submit" severity="primary" (click)="onSubmitData()" [loading]="loader" />
      </div>
    </ng-template>
  </p-sidebar>
</div>