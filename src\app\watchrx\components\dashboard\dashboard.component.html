<p-toast />
<p-confirmDialog />
<div class="flex flex-row align-items-between justify-content-between">
  <div class="custom-col-3">
    <div class="card alerts-card">
      <span class="xl-font">Alerts!</span>
      <ng-container *ngFor="let alert of alertTypes; let i = index">
        <p-divider />
        <div class="flex flex-row justify-content-between align-items-center">
          <div class="flex align-items-center">
            <img [src]="alert.icon" [alt]="alert.label + ' Icon'" width="24" height="24" class="mr-2" />
            <span class="alert-font" [ngClass]="i==0?'cursor-pointer p-text-danger':''"
              (click)="i==0?scrollToDiv('critical-alerts'):''">{{ alert.label }}</span>
          </div>
          <div>
            <span class="alert-font">{{ alertCountData?.[alert.key] || '0' }}</span>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
  <div class="custom-col-9">
    <div class="card patients-card">
      <div class="flex flex-row justify-content-between mb-1">
        <span class="xl-font">Patients</span>
      </div>
      <p-divider />
      <div class="flex flex-row justify-content-between mb-1">
        <div class="col-6">
          <div>
            <span class="font-medium">{{ "Total Patients " }}</span>
            <div class="mt-2"></div>
            <span class="xl-font">{{
              patientCount?.totalPatients || "0"
              }}</span>
          </div>
          <div class="mt-4"></div>
          <p-progressBar [value]="consentVsNonConsented" [showValue]="false"
            [style]="{ height: '15px' }"></p-progressBar>
          <div class="mt-4"></div>
          <div class="flex flex-column">
            <div class="flex align-items-center">
              <img src="assets/watchrx/svg/dot-consented.svg" alt="Consented Icon" width="16" height="16"
                class="mr-2" />
              <span class="font-medium">{{ "Consented - "
                }}{{ patientCount?.totalActivePatients || "0" }}</span>
            </div>
            <div class="mt-3"></div>
            <div class="flex align-items-center">
              <img src="assets/watchrx/svg/dot-non-consented.svg" alt="Consented Icon" width="16" height="16"
                class="mr-2" />
              <span class="font-medium">{{ "Non Consented - "
                }}{{ patientCount?.totalInActivePatients || "0" }}</span>
            </div>
          </div>
        </div>
        <p-divider layout="vertical" />
        <div class="col-3">
          <div>
            <span class="font-medium">{{ "New Patients Enrolled" }}</span>
          </div>
          <div class="mt-3"></div>
          <span class="xl-font">{{ patientCount?.newPatients || "0" }}</span>
        </div>
        <p-divider layout="vertical" />
        <div class="col-2">
          <div class="flex flex-column">
            <span class="font-medium">{{ "Growth " }}</span>
            <span class="font-medium">{{ "Vs Last Month" }}</span>
          </div>
          <div class="mt-3"></div>
          <span class="xl-font" style="color: #03b575">{{ growthCount }} %</span>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="flex flex-row align-items-between justify-content-between mb-3">
  <div class="col-12 table-card">
    <div class="flex flex-row justify-content-between mb-1 pad">
      <span class="xl-font">Patient Monitoring Status</span>
      <p-calendar [(ngModel)]="date" view="month" dateFormat="MM yy" [readonlyInput]="true" [monthNavigator]="true"
        [yearNavigator]="true" yearRange="2000:2090" (onSelect)="onDateChange($event)" [showIcon]="true">
      </p-calendar>
    </div>
    <p-divider />
    <div class="flex flex-row">
      <div class="col-2 flex flex-column justify-content-center" style="padding-left: 15px">
        <p class="font-medium p-0 m-0"><span class="font-bold">RPM</span></p>
        <!-- <p class="font-bold">Not Completed</p> -->
      </div>
      <div class="col-8">
        <p-chart type="bar" [data]="chartData" [options]="rpmChartOptions" />
      </div>
      <div class="col-2 flex flex-column justify-content-center">
        <div class="right-align">
          <p class="font-medium p-0 m-0">
            <span class="font-bold">{{ patientCount?.rpmPatients || 0 }}</span>
            Total
          </p>
          <p>RPM Patients</p>
          <hr class="full-width-line" />
          <span class="font-medium">{{ patientGraphRes?.rpm60Plus || "0" }} Patient(s)
          </span>
          <p>Exceeds 60 Mins</p>
        </div>
      </div>
    </div>
    <hr class="full-width-line" />
    <div class="flex flex-row mt-0">
      <div class="col-2 flex flex-column justify-content-center" style="padding-left: 15px">
        <p class="font-medium p-0 m-0"><span class="font-bold">CCM</span></p>
        <!-- <p class="font-bold">Not Completed</p> -->
      </div>
      <div class="col-8">
        <p-chart type="bar" [data]="ccmChartData" [options]="ccmChartOptions" />
      </div>
      <div class="col-2 flex flex-column justify-content-center">
        <div class="right-align">
          <p class="font-medium p-0 m-0">
            <span class="font-bold">{{ patientCount?.ccmPatients || 0 }}</span>
            Total
          </p>
          <p>CCM Patients</p>
          <hr class="full-width-line" />
          <span class="font-medium">{{ patientGraphRes?.ccm60Plus || "0" }} Patient(s)</span>
          <p>Exceeds 60 Mins</p>
        </div>
      </div>
    </div>
    <hr class="full-width-line" />
    <div class="flex flex-row mt-0">
      <div class="col-2 flex flex-column justify-content-center" style="padding-left: 15px">
        <p class="font-medium p-0 m-0"><span class="font-bold">PCM</span></p>
        <!-- <p class="font-bold">Not Completed</p> -->
      </div>
      <div class="col-8">
        <p-chart type="bar" [data]="pcmChartData" [options]="pcmChartOptions" />
      </div>
      <div class="col-2 flex flex-column justify-content-center">
        <div class="right-align">
          <p class="font-medium p-0 m-0">
            <span class="font-bold">{{ patientCount?.pcmPatients || 0 }}</span>
            Total
          </p>
          <p>PCM Patients</p>
          <hr class="full-width-line" />
          <span class="font-medium">{{ patientGraphRes?.pcm60Plus || "0" }} Patient(s)</span>
          <p>Exceeds 60 Mins</p>
        </div>
      </div>
    </div>
    <hr class="full-width-line" />
    <div class="flex flex-row mt-0">
      <div class="col-2 flex flex-column justify-content-center" style="padding-left: 15px">
        <p class="font-medium p-0 m-0">
          <span class="font-bold">16 Days measure</span>
        </p>
        <!-- <p class="font-bold">Not Completed</p> -->
      </div>
      <div class="col-8">
        <p-chart type="bar" [data]="dataCollectionChartData" [options]="dataChartOptions" />
      </div>
      <div class="col-2 flex flex-column justify-content-center">
        <div class="right-align">
          <p class="font-medium p-0 m-0">
            <span class="font-bold">{{
              patientCount?.daysReadingCaptured || 0
              }}</span>
            Total
          </p>
          <p>
            Reading Eligible <br />
            Patients
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="flex flex-row align-items-between justify-content-between mb-3">
  <div class="col-12 table-card pad">
    <div class="flex flex-row justify-content-between mb-1">
      <span class="xl-font">Patients List</span>
    </div>
    <p-divider />
    <div class="flex flex-row justify-content-between align-items-center mb-1">
      <div class="flex">
        <div class="mr-3">
          <span class="p-input-icon-left p-input-icon-right">
            <i class="pi pi-user"></i>
            <input type="text" pInputText [(ngModel)]="searchTerm" (ngModelChange)="onSearch($event)"
              placeholder="Search By Patient Name" />
            <i class="pi pi-search"></i>
          </span>
        </div>
        <div>
          <p-dropdown [(ngModel)]="selectedFilter" [options]="filterValues" inputId="dropdown"
            [autoDisplayFirst]="false" optionLabel="label" (onChange)="onFilterDropdownChange($event)"
            class="fixed-width-dropdown"></p-dropdown>
        </div>
      </div>
      <div>
        <p-button [raised]="true" severity="primary" label="Download Statistics" [outlined]="true"
          (onClick)="onDownloadStats()" [loading]="fileDownloadLoading" iconPos="left">
          <ng-template pTemplate="icon">
            <img src="assets/watchrx/svg/download_arrow.svg" alt="Download Stats" width="16" height="16" class="mr-2" />
          </ng-template>
        </p-button>
      </div>
    </div>
    <div class="grid p-fluid mt-3">
      <div class="field col-12 md:col-2">
        <p-calendar [(ngModel)]="startDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          placeholder="Start Date" [showButtonBar]="true" />
      </div>
      <div class="field col-12 md:col-2">
        <p-calendar [(ngModel)]="endDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          placeholder="End Date" [showButtonBar]="true" />
      </div>
      <div class="field col-12 md:col-2">
        <p-button label="Submit" [raised]="true" severity="primary" (onClick)="onFilterLoadPatients()" />
      </div>
    </div>
    <div class="clo-12">
      <p-table #dt [value]="patientBilling" [paginator]="true" [totalRecords]="totalBillingPatCount"
        [rows]="patientRows" [lazy]="true" [loading]="loadingBillingPat" (onLazyLoad)="loadActivePatientsList($event)"
        responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped" sortField="pcm"
        [sortOrder]="-1" [rowsPerPageOptions]="searchTerm.length==0?[15,25, 50,75, 100]:[patientRows]" [first]="first"
        (onPage)="onPageChange($event)">
        <ng-template pTemplate="header">
          <tr>
            <th style="width: 170px" pFrozenColumn>Name</th>
            <th pSortableColumn="status">Status <p-sortIcon field="status" /></th>
            <th>Date Of Birth</th>
            <th>Programs Enrolled</th>
            <th pSortableColumn="rpm">RPM Mins <p-sortIcon field="rpm" /></th>
            <th pSortableColumn="ccm">CCM Mins <p-sortIcon field="ccm" /></th>
            <th pSortableColumn="pcm">PCM Mins <p-sortIcon field="pcm" /></th>
            <th pSortableColumn="chart"> Chart Mins <p-sortIcon field="chart" /></th>
            <th pSortableColumn="dataCollection">
              Days Measured <p-sortIcon field="dataCollection" />
            </th>

          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-pat>
          <tr>
            <td style="width: 170px" pFrozenColumn class="align-left">
              <span class="cursor-pointer font-bold p-text-primary link" title="{{ pat.patientName }}"
                (click)="editPatient(pat['patientId'])">{{ pat.patientName }}</span>
            </td>
            <td>
              <div [ngClass]="checkStatus(pat)" class="rounded-background">
                {{ getClass(pat) }}
              </div>
            </td>
            <td>
              {{ getFormattedDate(pat.dob, "MM-DD-YYYY") }}
              <!-- <div class="mt-1"></div>
              {{ getTotalYears(pat.dob) }} -->
            </td>
            <td>{{ pat.programEnrolled }}</td>
            <td>{{ pat.rpmMins }}</td>
            <td>{{ pat.ccmMins }}</td>
            <td>{{ pat.pcmMins }}</td>
            <td>{{ pat.cmRevMins }}</td>
            <td>{{ pat.noOfDays }} Day(s)</td>

          </tr>
        </ng-template>
        <ng-template pTemplate="paginatorleft">
          <span class="text-900 font-bold">Total {{ totalBillingPatCount }} Patients</span>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<div class="flex flex-row align-items-between justify-content-between mb-3" id="critical-alerts">
  <div class="col-12 table-card pad">
    <div class="flex flex-row justify-content-center align-items-center mb-1">
      <div class="flex align-items-center mr-2">
        <img src="assets/watchrx/svg/critical_alrts.svg" width="24" height="24" class="mr-2" />
        <span class="xl-font">Critical Alerts &nbsp;</span>
        <p-badge value="{{ totalCriticalAlertDataCount ||0 }}" severity="danger"></p-badge>
      </div>
      <div class="ml-auto">
        <!-- <span class="last">{{ "Last 30 Days" }}</span> -->
      </div>
    </div>

    <p-divider />
    <p-table [value]="criticalAlertData" [paginator]="true" [totalRecords]="totalCriticalAlertDataCount" [rows]="15"
      [lazy]="true" [loading]="loadingCriticalAlertData" (onLazyLoad)="loadCriticalAlertsList($event)"
      responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped"
      [rowsPerPageOptions]="[15,25,50,75,100]">
      <ng-template pTemplate="header">
        <tr>
          <th>Patient Name</th>
          <th>Date and Time</th>
          <th>Description</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template let-criticalAlertData pTemplate="body">
        <tr>
          <td>
            <span class="cursor-pointer font-bold p-text-primary link" title="{{ criticalAlertData.patientName }}"
              (click)="editPatient(criticalAlertData['patientId'])">{{ criticalAlertData.patientName }}</span>
          </td>
          <td>
            {{ getAlertFormattedDate(criticalAlertData?.createdDate) }} {{
            getFormattedTime(criticalAlertData?.createdDate) }}
          </td>
          <td>
            {{ criticalAlertData?.alertDescription }}
          </td>
          <td>
            <div class="flex justify-content-around">
              <p-button icon="pi pi-align-justify" severity="secondary" [outlined]="true"
                (onClick)="openCriticalAlertModalFn(criticalAlertData)" />
                 <p-button icon="pi pi-check" severity="secondary" [outlined]="true" title="Acknowledgement"
                (onClick)="openAckAlertModalFn(criticalAlertData)" />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="paginatorleft">
        <span class="text-900 font-bold">Total {{ totalCriticalAlertDataCount }} Alerts</span>
      </ng-template>
    </p-table>
  </div>
</div>
<div class="flex flex-row align-items-between justify-content-between mb-3" *ngIf="userRole.roleType!==3">
  <div class="col-12 table-card pad">
    <div class="flex align-items-center mr-2">
      <span class="xl-font mr-2">Today's Task </span>
      <p-badge value="{{ totalTasks }}" />
    </div>
    <p-divider />
    <p-table [value]="taskVO" [paginator]="true" [totalRecords]="totalTasks" [rows]="totalTasks" [lazy]="true"
      (onLazyLoad)="todaysTask($event)" [loading]="loadingTask" responsiveLayout="scroll"
      styleClass="p-datatable-gridlines p-datatable-striped">
      <ng-template pTemplate="header">
        <tr>
          <th>Patient Name</th>
          <th>Task Title</th>
          <th>Start time - End time</th>
          <th>Description</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template let-taskVO pTemplate="body">
        <tr>
          <td>
            <span class="cursor-pointer font-bold p-text-primary link" title="{{ taskVO.patinetName}}"
              (click)="editPatient(taskVO['patientId'])">{{ taskVO.patinetName }}</span>
            <!-- <p-button label="{{ taskVO.patinetName }}" [link]="true" (onClick)="editPatient(taskVO['patientId'])" /> -->
          </td>
          <td>{{ taskVO.title }}</td>
          <td>
            {{ taskVO.start | date : "h:mm a" }}{{ " - "
            }}{{ taskVO.end | date : "h:mm a" }}
          </td>
          <td>{{ taskVO.description }}</td>
          <td>
            <div class="flex gap-2">
              <!-- <p-button icon="pi pi-pencil" severity="secondary" [outlined]="true"
                (onClick)="editTask($event, taskVO)" /> -->
              <p-button icon="pi pi-trash" severity="secondary" [outlined]="true"
                (onClick)="deleteTask($event, taskVO)" />
            </div>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="paginatorleft">
        <span class="text-900 font-bold">Total {{ totalTasks }} Tasks</span>
      </ng-template>
    </p-table>
  </div>
</div>

<div class="flex flex-row align-items-between justify-content-between mb-3">
  <div class="col-12 table-card pad" style="margin-bottom: 23px">
    <span class="xl-font">Recently contacted patients</span>
    <p-divider />
    <div class="grid p-fluid mt-3">
      <div class="field col-12 md:col-2">
        <p-calendar [(ngModel)]="rcStartDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          placeholder="Start Date" [showButtonBar]="true" />
      </div>
      <div class="field col-12 md:col-2">
        <p-calendar [(ngModel)]="rcEndDate" [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay"
          placeholder="End Date" [showButtonBar]="true" />
      </div>
      <div class="field col-2">
        <p-button label="Submit" [raised]="true" severity="primary"
          (onClick)="onFilterLoadRecentlyContactedPatients()" />
      </div>
    </div>
    <p-table #dt1 [value]="recentPatientInfo" [paginator]="true" [totalRecords]="totalRecentlyContactedPatientCount"
      [rows]="15" [lazy]="true" [loading]="loadingRecentlyContactedPatient"
      (onLazyLoad)="loadRecentlyContactedPatients($event)" responsiveLayout="scroll"
      styleClass="p-datatable-gridlines p-datatable-striped" [rowsPerPageOptions]="[15,25,50,75,100]">
      <ng-template pTemplate="header">
        <tr>
          <th>Patient Name</th>
          <th>Date Of Birth</th>
          <th>Phone Number</th>
          <th>Vital Measured (Days)</th>
          <th>RPM Mins</th>
          <th>CCM Mins</th>
          <th>PCM Mins</th>
          <th>Case Manager</th>
        </tr>
      </ng-template>
      <ng-template let-patientInfo pTemplate="body">
        <tr>
          <td>
            <span class="cursor-pointer font-bold p-text-primary link" title="{{patientInfo.patientName}}"
              (click)="editPatient( patientInfo['patientId'])">{{patientInfo.patientName }}</span>
            <!-- <p-button label="{{ patientInfo.patientName }}" [link]="true"
              (onClick)="editPatient(patientInfo['patientId'])" /> -->
          </td>
          <td>
            {{ getFormattedDate(patientInfo.dob, "MM-DD-YYYY") }}
            <!-- <div class="mt-2"></div>
            {{ getTotalYears(patientInfo.dob) }} -->
          </td>
          <td>{{ patientInfo.patientPhoneNumber }}</td>
          <td>{{ patientInfo.noOfDays }}</td>
          <td>{{ patientInfo.rpmMins }}</td>
          <td>{{ patientInfo.ccmMins }}</td>
          <td>{{ patientInfo.pcmMins }}</td>
          <td>{{ patientInfo.caseManagerName }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="paginatorleft">
        <span class="text-900 font-bold">Total {{ totalRecentlyContactedPatientCount }} Patients</span>
      </ng-template>
    </p-table>
  </div>
</div>

<!-- code need to be commented-->
<p-dialog [(visible)]="openCriticalAlertModal" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '900px' }" [contentStyle]="{ overflow: 'auto' }" position="top"
  [header]="criticalAlert?.patientName">
  <p-tabView [(activeIndex)]="activeIndex">
    <p-tabPanel header="Encounter">
      <div class="card mt-3">
        <app-encounter-form #encounterForm [programList]="programList"></app-encounter-form>
        <div class="flex gap-3 mt-2">
          <p-button label="Cancel" [outlined]="true" severity="danger" (click)="closeModalPopup()" />
          <p-button label="Save" [outlined]="true" severity="success" (click)="onSubmit()"
            [loading]="encounterLoader" />
        </div>
      </div>
      <div>
        <span class="font-bold block mb-2">Last 5 Encounters</span>
        <p-table responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped"
          [value]="encounterList">
          <ng-template pTemplate="header">
            <tr>
              <th>Reason</th>
              <th>Description</th>
              <th>Added By</th>
              <th>Date</th>
              <th>Time</th>
              <th>Duration</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-enc>
            <tr>
              <td>{{ enc.encounterReason }}</td>
              <td>{{ enc.encounterDescription }}</td>
              <td>{{ enc.addedByUser }}</td>
              <td>{{ enc.encounterDateTime | date : "MM-dd-yyyy" }}</td>
              <td>
                {{ enc.encounterDateTime | date : "h:mm a" }}-{{
                enc.encounterEndDateTime | date : "h:mm a"
                }}
              </td>
              <td>{{ convert(enc.duration) }}</td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </p-tabPanel>
    <p-tabPanel header="Message">
      <!-- <app-schedule-text-message-form #textMessageForm [programList]="programList" [isSechedule]="false"
        [isEnconterNeeded]="true" [phoneNumber]="criticalAlert?.phoneNumber" />
      <div class="flex gap-3 mt-2">
        <p-button label="Cancel" [outlined]="true" severity="danger" (click)="closeModalPopup()" />
        <p-button label="Save" [outlined]="true" severity="success" (click)="onTextSubmit()"
          [loading]="encounterLoader" />
      </div> -->
    </p-tabPanel>
    <p-tabPanel header="Task">
      <p>In Progress...</p>
    </p-tabPanel>
  </p-tabView>
</p-dialog>
<!-- code need to be commented-->

<p-sidebar [(visible)]="sidebarVisible" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '50rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/critical_alrts.svg" alt="Consented Icon" width="24" height="24" class="mr-2" />
      <span class="xl-font">Critical Alerts &nbsp;</span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
  <div class="flex flex-row justify-content-between align-items-center p-4">
    <div class="d-flex align-items-center mr-2 ellipsis" style="width:150px">
      <p class="p-0 m-0 ellipsis">
        <span class="sidebar-bold" [title]="criticalAlert?.patientName">{{ criticalAlert?.patientName }}</span>
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">
          {{ getFormattedDate(criticalAlert?.createdDate, "MM/DD/YYYY") }}
        </span>
        <!-- {{ "," }} {{ getTotalYears(criticalAlert?.createdDate) }} -->
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">{{ criticalAlert?.phoneNumber }}</span>
      </p>
    </div>
    <div class="ml-auto">
      <div class="flex gap-3">
        <p-button severity="secondary" title="Call" label="Call" [outlined]="true" iconPos="left" (click)="showCallWindow()">
          <ng-template pTemplate="icon">
            <img src="assets/watchrx/svg/call.svg" alt="Call" width="16" height="16" />
          </ng-template>
        </p-button>
        <button pButton icon="pi pi-video" title="Video Call" label="Video Call" severity="secondary" class="p-button-outlined p-button-secondary" size="small" (click)="
          showVedioCallWindow()"></button>
        <p-button severity="secondary" title="Message" label="Message" [outlined]="true" iconPos="left" (click)="showMessageSidebar()">
          <ng-template pTemplate="icon">
            <img src="assets/watchrx/svg/chat.svg" alt="Message" width="16" height="16" />
          </ng-template>
        </p-button>
        <p-button severity="secondary" title="Task" label="Task" [outlined]="true" iconPos="left" (click)="showTaskSidebar()">
          <ng-template pTemplate="icon">
            <img src="assets/watchrx/svg/calendar.svg" alt="Task" width="16" height="16" />
          </ng-template>
        </p-button>
      </div>
    </div>
  </div>
  <p-divider />
  <div class="flex flex-row justify-content-between align-items-center mb-1 p-4">
    <div class="flex align-items-center mr-2 cursor-pointer" (click)="showPreviousEncounters = !showPreviousEncounters">
      <img [src]="
          showPreviousEncounters
            ? 'assets/watchrx/svg/up_arrow.svg'
            : 'assets/watchrx/svg/down_arrow.svg'
        " width="24" height="24" class="mr-2" />
      <span class="xl-font">{{
        "Previous Encounters (" + this.encounterList?.length + ")"
        }}</span>
    </div>
    <div class="ml-auto">
      <p-button icon="pi pi-plus" severity="secondary" label="Encounter" [outlined]="true" iconPos="left"
        (click)="showEncounterSidebar()" />
    </div>
  </div>
  <p-divider />
  <div class="flex flex-column justify-content-between align-items-center mb-4 encounterlist"
    *ngIf="showPreviousEncounters">
    <div *ngFor="let event of encounterList" class="flex flex-column w-full">
      <div class="p-4">
        <div class="flex align-items-center">
          <img src="assets/watchrx/svg/time.svg" width="14" height="14" class="mr-2" />
          <h6 class="m-0 subhead font-12">
            {{ event.encounterDateTime | date : "MMMM d, h:mm a" }}
          </h6>
        </div>
        <div class="mt-3">
          <h6 class="subhead">Encounter Reason</h6>
          <p>{{ event.encounterReason }}</p>
        </div>
        <div class="mt-3">
          <h6 class="subhead">Alert Description</h6>
          <p>{{ event.encounterDescription }}</p>
        </div>
      </div>
      <p-divider />
    </div>
  </div>
</p-sidebar>
<p-sidebar [(visible)]="messageSidebarVisible" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '40rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/chat.svg" alt="Consented Icon" width="20" height="20" class="mr-2" />
      <span class="xl-font">Message &nbsp;</span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
  <div class="body" style="height: calc(100vh - 137px) !important; overflow: auto">
    <div class="flex flex-row justify-content-between align-items-center p-4">
      <div class="d-flex align-items-center mr-2">
        <p class="p-0 m-0">
          <span class="sidebar-bold">{{ criticalAlert?.patientName }}</span>
        </p>
        <p class="p-0 m-0">
          <span class="sidebar-normal">
            {{ getFormattedDate(criticalAlert?.createdDate, "DD/MM/YYYY") }}
          </span>
          <!-- {{ "," }} {{ getTotalYears(criticalAlert?.createdDate) }} -->
        </p>
        <p class="p-0 m-0">
          <span class="sidebar-normal">{{ criticalAlert?.phoneNumber }}</span>
        </p>
      </div>
    </div>
    <p-divider />
    <div class="flex flex-row justify-content-between align-items-center p-4">
      <app-schedule-text-message-form #textMessageForm [programList]="programList" [isSechedule]="false"
        [isEnconterNeeded]="true" [phoneNumber]="criticalAlert?.phoneNumber" />
    </div>
  </div>
  <div class="flex gap-3 p-3 footer">
    <p-button label="Back" [outlined]="true" severity="secondary" (click)="goBack()" />
    <p-button label="Submit" severity="primary" (click)="onTextSubmit()" [loading]="encounterLoader" />
  </div>
</p-sidebar>
<p-sidebar [(visible)]="taskSidebarVisible" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '40rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/calendar.svg" alt="Consented Icon" width="20" height="20" class="mr-2" />
      <span class="xl-font">Task &nbsp;</span>
    </div>
  </ng-template>

  <div class="flex flex-row justify-content-between align-items-center p-4 h-full overflow-auto">
    <app-watchrx-calendar [patientId]="this.criticalAlert?.patientId || 0" [usage]="'all'" [fromDashboard]="true"
      (closemodel)="closeTaskModel()" *ngIf="taskSidebarVisible"></app-watchrx-calendar>
  </div>
</p-sidebar>
<p-sidebar [(visible)]="encounterSidebarVisible" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '40rem' }" position="right" class="dashboardSidebar encountersidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/chat.svg" alt="Consented Icon" width="20" height="20" class="mr-2" />
      <span class="xl-font">Add Encounter &nbsp;</span>
    </div>
  </ng-template>
  <div class="flex flex-row justify-content-between align-items-center p-3">
    <div class="d-flex align-items-center mr-2">
      <p class="p-0 m-0">
        <span class="sidebar-bold">{{ criticalAlert?.patientName }}</span>
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">
          {{ getFormattedDate(criticalAlert?.createdDate, "DD/MM/YYYY") }}
        </span>

        <!-- {{ "," }} {{ getTotalYears(criticalAlert?.createdDate) }} -->
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">{{ criticalAlert?.phoneNumber }}</span>
      </p>
    </div>
  </div>
  <p-divider />
  <div class="flex flex-row justify-content-between align-items-center p-3">
    <app-encounter #encounterForm [programList]="programList" [reasonList]="reasonList" [patientName]="criticalAlert?.patientName"
      *ngIf="encounterSidebarVisible"></app-encounter>
  </div>
  <!-- <p-divider /> -->
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Back" [outlined]="true" severity="secondary" (click)="goBack()" />
      <p-button label="Submit" severity="primary" (click)="onSubmit()" [loading]="encounterLoader" />
    </div>
  </ng-template>
</p-sidebar>

<p-sidebar [(visible)]="acknowledgeModalVisible" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
  [style]="{ width: '50rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/critical_alrts.svg" alt="Consented Icon" width="24" height="24" class="mr-2" />
      <span class="xl-font">Acknowledgement Alert &nbsp;</span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
  <div class="flex flex-row justify-content-between align-items-center p-4">
    <div class="d-flex align-items-center mr-2 ellipsis w-full">
      <p class="p-0 m-0 ellipsis">
        <span class="sidebar-bold" [title]="criticalAlert?.patientName">{{ criticalAlert?.patientName }}</span>
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">
          {{ getFormattedDate(criticalAlert?.createdDate, "MM/DD/YYYY") }}
        </span>
      </p>
      <p class="p-0 m-0">
        <span class="sidebar-normal">{{ criticalAlert?.phoneNumber }}</span>
      </p>
    </div>
    <div class="ml-auto">
    </div>
  </div>
  <p-divider />
  <div class="flex flex-column mt-3 mb-3 p-4">
     <span class="sidebar-bold">Description</span>
    <p>{{criticalAlert?.alertDescription}}</p>
  </div>
  <p-divider/>

  <div class="flex flex-column mb-1 p-4">
     <span class="sidebar-bold">Acknowledgement reason?</span>
    <div class="p-field-checkbox ackoptions" *ngFor="let opt of acknowledgeOptions">
    <p-checkbox name="acknowledgeOptions" [value]="opt.value" [(ngModel)]="selectedAcknowledgeOptions"></p-checkbox>
    <label>{{ opt.label }}</label>
  </div>
  </div>
  <p-divider />
   <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Back" [outlined]="true" severity="secondary" (click)="acknowledgeModalVisible=false" />   
       <p-button label="Submit" (click)="submitAcknowledge()" [loading]="acknowledgeLoading" [disabled]="selectedAcknowledgeOptions.length === 0"></p-button>

    </div>
  </ng-template>
</p-sidebar>


<app-calls [openVoiceVideoDlg]="true" [selectedPatientInfo]="criticalAlert" *ngIf="openVoiceVideoDlg"
  (closeModel)="openVoiceVideoDlg=false"></app-calls>
<app-video-calls [openVideoDlg]="true" [selectedPatientInfo]="criticalAlert" *ngIf="openVideoDlg"
  (closeModel)="openVideoDlg=false"></app-video-calls>