import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RippleModule } from 'primeng/ripple';
import { SelectButtonModule } from 'primeng/selectbutton';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { ToastModule } from 'primeng/toast';
import { CarePlanComponent } from './components/care-plan/care-plan.component';
import { CustomReminderComponent } from './components/custom-reminder/custom-reminder.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { EncounterTabViewComponent } from './components/encounter-tab-view/encounter-tab-view.component';
import { GpsComponent } from './components/gps/gps.component';
import { IndividualAlertsComponent } from './components/individual-alerts/individual-alerts.component';
import { MedicationTabViewComponent } from './components/medication-tab-view/medication-tab-view.component';
import { ProfileComponent } from './components/profile/profile.component';
import { VitalsComponent } from './components/vitals/vitals.component';
import { EditPatientTabViewRoutingModule } from './edit-patient-tab-view-routing.module';
import { EditPatientTabViewComponent } from './edit-patient-tab-view.component';
import { InputSwitchModule } from 'primeng/inputswitch';
import {  SidebarModule } from 'primeng/sidebar';
import { ProgressBarModule } from 'primeng/progressbar';
import { MultiSelectModule } from 'primeng/multiselect';
import { AccordionModule } from 'primeng/accordion';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { SharedModule } from '../../shared/shared.module';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { AudioRecorderComponent } from './components/audio-recorder/audio-recorder.component';

@NgModule({
  declarations: [EditPatientTabViewComponent],
  imports: [
    CommonModule,
    EditPatientTabViewRoutingModule,
    TableModule,
    ButtonModule,
    StyleClassModule,
    PanelMenuModule,
    InputGroupAddonModule,
    InputGroupModule,
    PanelModule,
    RippleModule,
    TabViewModule,
    DashboardComponent,
    ProfileComponent,
    CarePlanComponent,
    VitalsComponent,
    MedicationTabViewComponent,
    GpsComponent,
    CustomReminderComponent,
    EncounterTabViewComponent,
    IndividualAlertsComponent,
    SelectButtonModule,
    FormsModule,
    ToastModule,
    ConfirmDialogModule,
    InputSwitchModule,
    SidebarModule,
    ProgressBarModule,
    MultiSelectModule,
    AccordionModule,
    NgxSliderModule,
    SharedModule,
    AutoCompleteModule,
    AudioRecorderComponent
  ],
  providers: [ConfirmationService, MessageService],
})
export class EditPatientTabViewModule {}
