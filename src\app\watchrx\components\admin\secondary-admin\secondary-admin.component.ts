import { Component, OnInit } from '@angular/core';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { TableLazyLoadEvent } from 'primeng/table';
import { ManagerInfo } from '../../../api/adminCaseManager';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { SecondaryAdminService } from './service/secondary-admin.service';

@Component({
  selector: 'app-secondary-admin',
  templateUrl: './secondary-admin.component.html',
  styleUrls: ['./secondary-admin.component.css'],
})
export class SecondaryAdminComponent implements OnInit {
  constructor(
    public menuService: MenuService,
    private adminService: SecondaryAdminService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService
  ) {}

  showPopup: boolean = false;
  adminList: ManagerInfo[] = [];
  totalCount: number = 0;
  itemsPerPage: number = 10;

  roleTypeList = [{ type: 'Admin' }];

  ngOnInit() {
    this.menuService.changeMenu('Admin');
  }

  addAdmin() {
    this.showPopup = true;
  }

  loadAdminsList($event?: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event?.rows || 10;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize;
    this.loaderService.show();
    this.adminService.getAdminList(url).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.adminList = res.clinicianList!;
        this.totalCount = res.count!;
      }
    });
  }

  onToggleAdmin(admin: ManagerInfo, status: string) {
    this.loaderService.show();
    this.adminService
      .toggleAdminStatus({
        clinicianId: admin.clinicianId,
        type: 'rpmAdmin',
      })
      .subscribe((res) => {
        this.loaderService.hide();
        this.loadAdminsList();
      });
  }

  onDeleteAdmin(id: number) {}

  getSeverity(status: string) {
    return status == 'Y' ? 'success' : 'danger';
  }

  getStatus(status: string) {
    return status == 'Y' ? 'ACTIVE' : 'INACTIVE';
  }
}
