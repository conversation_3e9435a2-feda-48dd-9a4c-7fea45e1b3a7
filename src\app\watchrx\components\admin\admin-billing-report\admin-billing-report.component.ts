import { Component } from '@angular/core';
import { MenuService } from '../../../service/menu.service';

@Component({
  selector: 'app-admin-billing-report',
  templateUrl: './admin-billing-report.component.html',
  styleUrl: './admin-billing-report.component.scss',
})
export class AdminBillingReportComponent {
  activeTabIndex: any = 0;

  constructor(private menuService: MenuService) {}

  ngOnInit() {
    this.menuService.changeMenu('Billing Reports');
    if (this.activeTabIndex = localStorage.getItem('adminbillingtab')) {
      this.activeTabIndex = localStorage.getItem('adminbillingtab')
    }
    else {
      this.activeTabIndex = 0
    }
  }

  tabChange(event: any) {
    console.log(event);
    localStorage.setItem('adminbillingtab', event.index)
  }
}
