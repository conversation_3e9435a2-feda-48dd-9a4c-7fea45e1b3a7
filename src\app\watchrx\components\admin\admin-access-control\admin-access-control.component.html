
<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/critical_alert.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Access Control</span
  >
</div><div class="grid">
  <div class="col-12">
    <div class="card p-0">
      <div class="grid p-fluid">
        <div class="field col-12">
          <p-tabView
            orientation="left"
            (onChange)="tabChange($event)"
            [(activeIndex)]="activeTabIndex"
          >
        
            <p-tabPanel header="Organization" class="line-height-3 m-0">
              <app-organizations
                *ngIf="activeTabIndex == 0"
              ></app-organizations>
            </p-tabPanel>
            <p-tabPanel header="Features" class="line-height-3 m-0">
              <app-features *ngIf="activeTabIndex == 1"></app-features>
            </p-tabPanel>
            <!-- <p-tabPanel
              header="Assign User to Organization"
              class="line-height-3 m-0"
            >
              <app-assing-user-organization
                *ngIf="activeTabIndex == 2"
              ></app-assing-user-organization>
            </p-tabPanel> -->
          </p-tabView>
        </div>
      </div>
    </div>
  </div>
</div>
