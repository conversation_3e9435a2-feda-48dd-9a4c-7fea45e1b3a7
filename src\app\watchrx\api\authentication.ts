export interface login {
    userName?: string;
    password?: string;
    roleId?: number;
}

export interface loginResponse {
    "success": boolean,
    "responseCode": number,
    "responsecode": number,
    "status": boolean,
    "messages": any[],
    "loggedinUser": {
        "userName": string,
        "password": string,
        "userId": number,
        "roleType": number,
        "firstName": string,
        "lastName": string,
        "picPath": string,
        "createdDate": string,
        "updatedDate": string,
        "email": string,
        "availStatus": string,
        "featuresEnabled": groupDetails[],
        "orgId": number,
        "otp": number,
        "otpEnabled": string,
        "passwordExpired": boolean
    }
}

export interface groupDetails {

    "groupName": string,
    "groupId": number,
    "groupUsrId": number,
    "groupDescription": string,
    "active": false,
    "roles": roles[],
    "roleIds": number,
    "userIds": number,
    "groupIds": number,
    "userId": number,
    "users": any[]

}

export interface roles {
    "roleName": string,
    "roleId": number,
    "roleDescription": string,
    "active": false

}