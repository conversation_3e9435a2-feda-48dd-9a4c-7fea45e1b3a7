export abstract class Constants {
  static readonly DASHBOARD_TABLE_URL =
    'service/patient/patientDashboardVitalData/';
  static readonly MEDS_GRAPH_URL = 'service/charts/missedmedication';
  static readonly ALERT_GRAPH_URL = 'service/patient/getAlertsCountReports';
  static readonly PATIENT_INFO_URL = 'service/patient/patientInfo';
  static readonly UDPATE_VITAL_STATUS = 'service/patient/enableVitalCollection';
  static readonly UPDATE_PEDOMETER =
    'service/patient/savePedometerConfiguration';
  static readonly UPDATE_PROFILE =
    'service/patient/updateEditPatientBasicInformation';
  static readonly CONSENT_DATA = 'service/patient/consentContact';
  static readonly UPDATE_CONSENT_DATA = 'service/patient/saveConsentData';
  static readonly PATIENT_CONSENT_DATA = 'service/patient/getConsentData/';
  static readonly DOWNLOAD_CONSENT = 'service/patient/downloadConsent/';
  static readonly EMERGENY_CONTACT =
    'service/patient/getPatientEmergencyContacts';
  static readonly ADD_EMERGENCY_CONTACT =
    'service/patient/saveNewEmergencyContact';
  static readonly DELETE_EMERGENCY_CONTACT = 'service/patient/deleteAnItem';
  static readonly EDIT_EMERGENCY_CONTACT =
    'service/patient/saveEditedEmergencyContact';
  static readonly DELETE_INSURANCE = 'service/patient/deleteInsuranceDetails';

  static readonly EDIT_INSURANCE = 'service/patient/updateInsuranceDetails';

  static readonly AVAILABLE_CHRONIC_CONDITIONS =
    'service/patient/getAllChronicConditions/PATIENT';

  static readonly ADD_CHRONIC = 'service/patient/saveNewChronicCondition';
  static readonly PROVIDER_LIST =
    'getAllRPMPhysicianCaseManagersForPatient/0/1000';

  static readonly UPDATE_PROVIDER = 'service/patient/updatePhysician';
  static readonly ALL_DEVICES = 'service/watch/getAllMedicalDevicesForPatient/';
  static readonly SAVE_PROFILE1 =
    'service/patient/savePatientPrimaryInformation';
    static readonly VALIDATE_CONSENT =
    'service/patient/getConsentData';
}
