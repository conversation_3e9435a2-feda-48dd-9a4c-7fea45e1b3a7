export interface ApiResponse {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string[];
  resultCount: number;
  enMins: number;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  minsDetails: string;
  programs: Program[];
  encounterNewList: Encounter[];
  draftList:Encounter[];
}

export interface Program {
  id: number;
  label: string;
  value: string;
  programStatus: boolean;
}

export interface Encounter {
  patientId: number;
  encounterId: number;
  patientAlertId: number | null;
  duration: number;
  encounterReason: string;
  encounterDescription: string;
  encounterDateTime: string;
  encounterEndDateTime: string;
  addedByUser: string;
  userId: number | null;
  enMins: number;
  review: string;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  durationInMS: number | null;
}

export interface AddEncounter {
  patientId?: number;
  encounterReason?: string;
  encounterDescription?: string;
  encounterDateTime?: string;
  patientAlertId?: number | null;
  review?: string;
  duration?: number;
  acknowledged?: boolean;
  encounterId?: number;
  reasonCode?:any;
  acknowledgeReasons?:any
  isDraft?:boolean
}
