import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map, Observable, throwError } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { PatientTask } from '../../../../api/dashboard';
import {
  AllocateDevice,
  BulkAddDeviceInventoryResp,
  Device,
  DeviceId,
} from '../../../../api/deviceAllocation';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { saveAs } from 'file-saver';
@Injectable({
  providedIn: 'root',
})
export class MedicalDeviceService {
  constructor(private http: HttpClient) {}
  static readonly DEVICE_UNALLLOCATED_URL = 'service/watch/UnallocatedWatches/';
  static readonly DEVICE_ALLLOCATED_URL = 'service/watch/AllocatedWatches/';
  static readonly DEVICE_UNASSIGN_URL = 'service/watch/unassignCaregiverWatch';
  static readonly PATIENT_BY_ORG_URL = 'service/clinician/getPatientsByOrg/';
  static readonly ALLOCATE_DEVICE_URL = 'service/watch/AllocateWatch';
  static readonly SEARCH_ALLOCATE_DEVICE_URL =
    'service/watch/AllocatedWatches/';
  static readonly INVENTORY_LIST = 'service/inventory/inventorycaregiverlist';

  static readonly ADD_DEVCIE = 'service/inventory/bulkaddwatch';
  static readonly DOWNLOAD_LIST = 'service/watch/downloadAllocatedWatches';
  static readonly GET_ALL_ORGS='service/patient/getAllOrgs';
  setUnAllocatedDevice(fromToIndex: string): Observable<Device> {
    return this.http.get<Device>(
      environment.BASE_URL +
        MedicalDeviceService.DEVICE_UNALLLOCATED_URL +
        fromToIndex
    );
  }

  setAllocatedDevice(fromToIndex: string): Observable<Device> {
    return this.http.get<Device>(
      environment.BASE_URL +
        MedicalDeviceService.DEVICE_ALLLOCATED_URL +
        fromToIndex
    );
  }

  unassignDevice(deviceId: string): Observable<Device> {
    let deviceIds: DeviceId = {
      watchId: deviceId,
    };
    return this.http.post<Device>(
      environment.BASE_URL + MedicalDeviceService.DEVICE_UNASSIGN_URL,
      deviceIds
    );
  }

  getPatientByOrg(orgId: number): Observable<PatientTask> {
    return this.http.get<PatientTask>(
      environment.BASE_URL + MedicalDeviceService.PATIENT_BY_ORG_URL + orgId
    );
  }

  allocateDevice(device: AllocateDevice): Observable<Device> {
    return this.http.post<Device>(
      environment.BASE_URL + MedicalDeviceService.ALLOCATE_DEVICE_URL,
      device
    );
  }

  searchDevice(text: string): Observable<Device> {
    return this.http.get<Device>(
      environment.BASE_URL +
        MedicalDeviceService.SEARCH_ALLOCATE_DEVICE_URL +
        text
    );
  }

  searchUnAllocatedDevice(text: string): Observable<Device> {
    return this.http.get<Device>(
      environment.BASE_URL +
        MedicalDeviceService.DEVICE_UNALLLOCATED_URL +
        text
    );
  }

  getInventoryList(): Observable<BulkAddDeviceInventoryResp> {
    return this.http.get<BulkAddDeviceInventoryResp>(
      environment.BASE_URL + MedicalDeviceService.INVENTORY_LIST
    );
  }

  bulkAddDevice(device: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + MedicalDeviceService.ADD_DEVCIE,
      device
    );
  }

  exportData(pageNo:any,fileName:string):Observable<any> {
    // return this.http.get<GenericResponse>(
    //   environment.BASE_URL + MedicalDeviceService.DOWNLOAD_LIST+'/'+pageNo,
    // );
    let url =environment.BASE_URL + MedicalDeviceService.DOWNLOAD_LIST+'/'+pageNo;
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
        return this.http.get(url, { headers, responseType: 'arraybuffer' }).pipe(
          map((response: BlobPart) => {
            const blob = new Blob([response], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });
            saveAs(blob, fileName);
            return { success: true };
          }),
          catchError((error) => {
            return throwError({ success: false });
          })
        );
  }

    getAllOrgsList() {
      return this.http.get<any>(environment.BASE_URL + MedicalDeviceService.GET_ALL_ORGS);
    }
}
