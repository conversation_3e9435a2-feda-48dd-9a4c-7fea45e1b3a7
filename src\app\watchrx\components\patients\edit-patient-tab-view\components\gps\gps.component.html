<div class="col-12">
  <p-toast />
  <p-confirmDialog />

    <form (ngSubmit)="onSubmit(form)" #form="ngForm">
      <div class="p-fluid p-formgrid grid">
        <div class="field col-12 md:col-4">
          <label for="address1">Address 1<span class="p-text-danger">*</span></label>
          <input
            id="address1"
            type="text"
            pInputText
            [(ngModel)]="formData.address1"
            name="address1"
            required
          />
          <div class="red" *ngIf="form.submitted && !formData.address1">
            Address 1 is required.
          </div>
        </div>

        <div class="field col-12 md:col-4">
          <label for="address2">Address 2</label>
          <input
            id="address2"
            type="text"
            pInputText
            [(ngModel)]="formData.address2"
            name="address2"
          />
        </div>

        <div class="field col-12 md:col-4">
          <label for="country">Country<span class="p-text-danger">*</span></label>
          <p-autoComplete
            id="country"
            [(ngModel)]="formData.country"
            name="country"
            [suggestions]="countries"
            (completeMethod)="filterCountries($event)"
            field="name"
            placeholder="Select a country"
            appendTo="body"
            [dropdown]="true"
            emptyMessage="No country found"
            [showEmptyMessage]="true"
            [required]="true"
          ></p-autoComplete>
          <div class="red" *ngIf="form.submitted && !formData.country">
            Country is required.
          </div>
        </div>

        <div class="field col-12 md:col-4">
          <label for="state">State<span class="p-text-danger">*</span></label>
          <input id="state" pInputText [(ngModel)]="formData.state" name="state" type="text"
          placeholder="state" />
          <!-- <p-autoComplete
            id="state"
            [(ngModel)]="formData.state"
            name="state"
            [suggestions]="states"
            (completeMethod)="filterStates($event)"
            field="name"
            placeholder="Select a state"
            appendTo="body"
            [dropdown]="true"
            emptyMessage="No state found"
            [showEmptyMessage]="true"
            [required]="true"
          ></p-autoComplete> -->
          <div class="red" *ngIf="form.submitted && !formData.state">
            State is required.
          </div>
        </div>

        <div class="field col-12 md:col-4">
          <label for="city">City<span class="p-text-danger">*</span></label>
          <input id="city" pInputText [(ngModel)]="formData.city" name="city" type="text"
          placeholder="city" />
          <!-- <p-autoComplete
            id="city"
            [(ngModel)]="formData.city"
            name="city"
            [suggestions]="cities"
            (completeMethod)="filterCities($event)"
            field="name"
            placeholder="Select a city"
            appendTo="body"
            [dropdown]="true"
            emptyMessage="No city found"
            [showEmptyMessage]="true"
            [required]="true"
          ></p-autoComplete> -->
          <div class="red" *ngIf="form.submitted && !formData.city">
            City is required.
          </div>
        </div>

        <div class="field col-12 md:col-4">
          <label for="zipCode">Zip Code<span class="p-text-danger">*</span></label>
          <input
            id="zipCode"
            type="text"
            pInputText
            [(ngModel)]="formData.zipCode"
            name="zipCode"
            required
          />
          <div class="red" *ngIf="form.submitted && !formData.zipCode">
            Zip Code required.
          </div>
        </div>
        <div class="field col-12 md:col-3">
          <label for="zipCode">Latitude</label>
          <input
            id="latitude"
            type="text"
            pInputText
            [(ngModel)]="formData.latitude"
            name="latitude"
            [disabled]="true"
          />
        </div>
        <div class="field col-12 md:col-3">
          <label for="zipCode">Longitude</label>
          <input
            id="longitude"
            type="text"
            pInputText
            [(ngModel)]="formData.longitude"
            name="longitude"
            [disabled]="true"
          />
        </div>
        <div class="field col-12 md:col-3">
          <label for="zipCode">Radius</label>
          <input
            id="Radius"
            type="text"
            pInputText
            [(ngModel)]="formData.radius"
            name="Radius"
            [required]="true"
          />
          <div class="red" *ngIf="form.submitted && !formData.radius">
            radius is required
          </div>
        </div>
        <div class="field col-12 md:col-3">
          <label for="unit">Unit<span class="p-text-danger">*</span></label>
          <p-dropdown
            [(ngModel)]="formData.unit"
            [options]="['Miles', 'Meters']"
            placeholder="Select a unit"
            [required]="true"
            [ngModelOptions]="{ standalone: true }"
          ></p-dropdown>
          <div class="red" *ngIf="form.submitted && !formData.unit">
            Unit is required
          </div>
        </div>

        <div class="field col-12 md:col-3">
          <div
            class="flex flex-column md:flex-row md:align-items-center justify-content-between"
          >
            <div class="mt-1 text-600 font-bold">{{ "GPS Enabled?" }}</div> 
            <div class="mt-2 md:mt-2 flex">
              <span class="text-black-500 ml-10 font-medium"
                ><p-inputSwitch [(ngModel)]="formData.gpsStatus" />
              </span>
            </div>
            <p-divider type="dotted" />
          </div>
        </div>

        <div class="field col-12 md:col-3">
          <div
            class="flex flex-column md:flex-row md:align-items-center justify-content-between"
          >
            <div class="mt-1 text-600 font-bold">{{ "Tracking Enabled?" }}</div> 
            <div class="mt-2 md:mt-2 flex">
              <span class="text-black-500 ml-10 font-medium"
                ><p-inputSwitch [(ngModel)]="formData.trackStatus" />
              </span>
            </div>
            <p-divider type="dotted" />
          </div>
        </div>
      </div>
      <div class="field col-12 flex justify-content-end ">
        <p-button
          label="Update"
          severity="primary"
          class="mr-3"
          type="submit"
        ></p-button>
      </div>
    </form>

</div>
