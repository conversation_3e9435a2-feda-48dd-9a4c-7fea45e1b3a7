image: node:18

pipelines:
  branches:
    audioSummarizer:
    - step:
        name: Build & Deploy Angular from dist/
        caches:
          - node
        script:
          - npm install -g @angular/cli
          - npm ci
          - npx ng build --configuration=production 

          # Copy app.yaml into dist/your-app/
          - cp app.yaml dist

          # Setup gcloud
          - echo "$GCLOUD_KEY_FILE" | base64 --decode > ${HOME}/gcloud-key.json
          - apt-get update && apt-get install -y curl python3 python3-pip
          - curl -sSL https://sdk.cloud.google.com | bash
          - source "$HOME/google-cloud-sdk/path.bash.inc"

          # Deploy from dist/your-app/
          - gcloud auth activate-service-account --key-file=${HOME}/gcloud-key.json
          - gcloud config set project watchrx-1007
          - cd dist
          - gcloud -q app deploy app.yaml --no-promote --version=audio-ui --quiet 