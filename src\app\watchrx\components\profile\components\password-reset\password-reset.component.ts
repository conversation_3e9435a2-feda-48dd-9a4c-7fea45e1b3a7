import { Component } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ProfileService } from '../../services/profile.service';
import { MessageService } from 'primeng/api';
import { MenuService } from '../../../../service/menu.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-password-reset',
  templateUrl: './password-reset.component.html',
  styleUrl: './password-reset.component.scss'
})
export class PasswordResetComponent {
  resetPasswordForm: FormGroup;

  constructor(private fb: FormBuilder,
    private profileService: ProfileService,
    private messageService: MessageService,
    private menuService: MenuService,
    private router: Router) {
    this.resetPasswordForm = this.fb.group({
      oldPassword: ['', Validators.required],
      newPassword: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(20),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#])[A-Za-z\d@$!%*?&#]{8,20}$/)
      ]],
      confirmPassword: ['', Validators.required]
    }, { validator: this.passwordMatchValidator });
  }

  passwordMatchValidator(form: FormGroup) {
    const newPassword = form.get('newPassword')?.value;
    const confirmPassword = form.get('confirmPassword')?.value;
    return newPassword === confirmPassword ? null : { mismatch: true };
  }

  onSubmit() {
    if (this.resetPasswordForm.valid) {
      let userName = ''
      let logUser = localStorage.getItem('user');
      if (logUser) {
        userName = JSON.parse(logUser)['userName'];
      }
      let obj = {
        currentPassword: this.resetPasswordForm.get('oldPassword')?.value,
        password: this.resetPasswordForm.get('newPassword')?.value,
        userName: userName
      }
      this.profileService.updatePassword(obj).subscribe((res) => {
        if (res.status == 'Success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'password updated successfully.',
            life: 3000,
          });
          this.logout();
        }
        else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: res.responseMessage,
            life: 3000,
          });
        }

      })
    }
  }

  logout() {
    this.menuService.doLogout().subscribe({
      next: (res) => {
        if (document.cookie && document.cookie !== '') {
          const cookies = document.cookie.split(';');
          for (let cookie of cookies) {
            const eqPos = cookie.indexOf('=');
            const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
          }
        }
        // Unregister all service workers
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistrations().then(function (registrations) {
            for (let registration of registrations) {
              registration.unregister();
            }
          })
        }
        setTimeout(() => {
          localStorage.clear();
        }, 100)
        this.router.navigate(['/login']);
      }, error: (err) => {
        setTimeout(() => {
          localStorage.clear();
        }, 100)
        this.router.navigate(['/login']);
      }
    })

  }
}
