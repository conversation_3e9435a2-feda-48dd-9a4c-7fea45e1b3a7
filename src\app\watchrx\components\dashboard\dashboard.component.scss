.gradient-card {
    background: linear-gradient(0deg, rgba(247, 218, 53, 0.5), rgba(123, 245, 238, 0.5)),
        linear-gradient(92.54deg, #FFFFFF 47.88%, #F5F5F5 100.01%);
    padding: 20px;
    border-radius: 10px;
    color: bla;
}

.info {
    background-color: #d1ecf1;
}

.alarm {
    background-color: #f8d7da;
}

:host ::ng-deep .pi-sort-alt:before {
    content: "\F0DC";
    font-family: "FontAwesome";
}

.custom-confirm-dialog {
    width: auto !important;
}

.alerts-card {
    background: #FFFFFF;
    border-radius: 8px;
    color: black;
    height: 270px;
    width: 100%;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    padding: 1rem;

    ::ng-deep .p-divider {
        margin: 1rem 0 !important;
    }
}

.alert-font {
    color: #000;
    font-family: Lato;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.1px;
}

::ng-deep .alerts-card p-divider {
    width: 100%;
    margin: 0;
}

.patients-card {
    background: #FFFFFF;
    border-radius: 8px;
    color: black;
    height: 270px;
    width: 100%;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    padding: 1rem;

    ::ng-deep .p-divider {
        margin: 1rem 0 !important;
    }
}

.xl-font {
    font-size: large;
    font-style: normal;
    font-family: 'Lato', sans-serif;
    font-weight: bold;
}

::ng-deep .p-progressbar-value {
    background-color: #1F58E7 !important;
}

::ng-deep .p-progressbar {
    background-color: #A9C1FD !important;
}

::ng-deep .p-progressbar-label {
    color: #1F58E7;
}

::ng-deep .rounded-input {
    border-radius: 20px;
}

::ng-deep .fixed-width-dropdown .p-dropdown {
    width: 220px;
}

.table-card {
    background: #FFFFFF;
    border-radius: 8px;
    color: black;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

}

.rounded-background {
    color: #FFFFFF;
    border-radius: 25px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 96px;
    height: 28px;
    text-align: center;
    font-weight: 600;
}

.green {
    background-color: #16D090;
    color: #FFFFFF;
}

.red {
    background-color: #E74F48;
    color: #FFFFFF;
}

:host ::ng-deep .p-chart canvas {
    background-color: rgba(240, 240, 240, 0.5);
    border-radius: 8px;
}

.cal {
    margin-top: -15px;
}

.full-width-line {
    width: 100%;
    border: none;
    border-top: 1px solid #ccc;
    margin: 5px 0;
}

.pad {
    padding-top: 15px;
    padding-left: 15px;
    padding-right: 15px;
}

.align-left {
    text-align: left;
}

::ng-deep p-table td {
    text-align: left;
    vertical-align: middle;
    padding: 8px;
}

.custom-col-3 {
    width: 25%;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-right: 5px;
}

.custom-col-9 {
    width: 75%;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 5px;
}

.right-align {
    text-align: end;
    align-items: center;
}

.last {
    color: var(--Neutral-Gray-dark, #52575C);
    text-align: right;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
    letter-spacing: 0.1px;
}

.sidebar-bold {
    color: var(--Neutral-Black, #25282b);
    font-family: Lato;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.1px;
}

.sidebar-normal {
    color: var(--Neutral-Black, #25282b);
    font-family: Lato;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.1px;
}

.event-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    width: 100%;
}

.event {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
}

.time {
    font-size: 0.9rem;
    color: #888888;
    margin-left: 0.5rem;
}

.event-card {
    width: 100%;
    background: #FFF;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;

    h6 {
        margin: 0;
        color: #000;
        font-size: 1rem;
    }

    p {
        margin: 0.2rem 0 0.5rem;
        font-size: 0.9rem;
        color: #333333;
    }
}

::ng-deep .p-sidebar-content {
    padding: 0px !important;
    overflow: hidden;
}

.dashboardSidebar {
    ::ng-deep .p-divider {
        margin: 0px !important;
    }

    .footer,
    ::ng-deep .p-sidebar-footer {
        ::ng-deep .p-button {
            width: 172px;
        }
    }

    ::ng-deep .bold-textarea {
        min-height: 120px;
        max-height: 120px;
    }
}

.subhead {
    margin-bottom: 8px;
    font-weight: 700;
}

.font-12 {
    font-size: 12px;
}

.footer {
    position: absolute;
    bottom: 0;
    border-top: 1px solid #52575C;
    width: 100%;
    justify-content: end;
}

.sidebarhr {
    border-top: 1px solid #52575C;
    margin: 0px !important;
}

.encounterlist {
    overflow: auto;
    height: calc(100vh - 262px);
}

.encountersidebar {
    ::ng-deep .p-sidebar-content {
        padding: 0px !important;
        overflow: auto;
    }
}

::ng-deep .p-sidebar-footer {
    border-top: 1px solid #000000;
}

::ng-deep .p-sidebar-header {
    border-bottom: 1px solid #000000;
}

::ng-deep .fc-view-harness {
    height: 33vh !important;
}

::ng-deep body.blocked-scroll {
    overflow: hidden !important;
}

::ng-deep .ackoptions {
    .p-element
    {
        margin-right: 10px;
    }
    display: flex;
    align-items: center;
    margin: 5px 0px;
}

//   .link:hover
//   {
//     text-decoration: underline;
//   }