.recentactivity {
    ::ng-deep .p-card-content {
        padding-top: 0px !important;
    }
}

.button-group {
    display: flex;
    align-items: center;
}

.left-button {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}

.right-button {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
}

.center-button {
    border-radius: 0px;
    border: 0px ;
    color: #000000;
    background-color: #ffffff;
}

.time
{
    border: 1px solid #E8E8E8;
}
.edit-button {
    height: 20px;
    width: 20px;
}
.topfilter
{
    border-bottom: 1px solid #E8E8E8;
}
.week ::ng-deep .p-button
{
    border-radius: 0px !important;
}
.daily ::ng-deep .p-button
{
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}

.month ::ng-deep .p-button
{
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}

.slider-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80%;
    margin: auto;
  }
  
  .labels {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
  }
  
  .critical-min, .critical-max {
    text-align: center;
  }
  
  .slider-bar {
    position: relative;
    width: 100%;
    height: 10px;
    margin-bottom: 20px;
    display: flex;
  }
  
  .critical-zone {
    background-color: red;
    height: 10px;
    flex: 1;
  }
  
  .healthy-zone {
    background-color: green;
    height: 10px;
    flex: 2;
  }
  
  p-slider {
    position: absolute;
    top: -15px;
    width: 100%;
  }
  
  .marker {
    position: relative;
    width: 100%;
  }
  
  .marker-icons {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
  }
  
  .marker-circle {
    width: 20px;
    height: 20px;
    background-color: #007bff;
    color: white;
    font-size: 12px;
    font-weight: bold;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .w-50
  {
    width: 50%;
  }
  ::ng-deep .w-40rem
  {
    width: 40rem;
  }

  // We need to use ::ng-deep to overcome view encapsulation
::ng-deep {
    .custom-slider
    {
        pointer-events: none;
        padding: 10px 20px;
    }
    .custom-slider .ngx-slider .ngx-slider-bar {
      background: #FF6760;
      height: 10px;
    }
    .custom-slider .ngx-slider .ngx-slider-selection {
      background: #FF6760
    }
  
    .custom-slider .ngx-slider[disabled] .ngx-slider-selection
    {
        background-color: #16D090;
    }
    .custom-slider .ngx-slider .ngx-slider-pointer {
      width: 8px;
      height: 16px;
      top: auto; /* to remove the default positioning */
      bottom: 0;
      background-color: #333;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-pointer:after {
      display: none;
    }
  
    .custom-slider .ngx-slider .ngx-slider-bubble {
      bottom: 14px;
      font-size: 12px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-bubble.ngx-slider-limit {
      //font-weight: bold;
      //color: rgb(0, 0, 0);
      font-size: 12px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-tick {
      width: 1px;
      height: 10px;
      margin-left: 4px;
      border-radius: 0;
      background: #ffe4d1;
      top: -1px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
      background: orange;
    }
  }
  ::ng-deep .chart {
  height: 100% !important;
}
::ng-deep .chart > canvas {
  height: 100% !important;
  width: 100% !important;
}

::ng-deep .ngx-slider-floor
{
  opacity: 1 !important;
}
::ng-deep .ngx-slider-ceil
{
  opacity: 1 !important;
}
::ng-deep .ngx-slider-model-value
{
  top:20px !important
}
::ng-deep .ngx-slider-model-high
{
  top:20px !important
}

