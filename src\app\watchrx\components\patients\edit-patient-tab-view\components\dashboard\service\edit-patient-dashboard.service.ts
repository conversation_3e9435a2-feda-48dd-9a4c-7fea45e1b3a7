import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import {
  GenericResponse,
  HealthData,
  MeasuredData,
  PatientData,
} from '../../../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class EditPatientDashboardService {
  constructor(private http: HttpClient) { }

  getTableData(patientId: number, date: string): Observable<HealthData> {
    return this.http.get<HealthData>(
      environment.BASE_URL +
      Constants.DASHBOARD_TABLE_URL +
      patientId +
      '/' +
      date
    );
  }

  getMedicationGraph(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.MEDS_GRAPH_URL,
      details
    );
  }

  getAlertGraph(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.ALERT_GRAPH_URL,
      details
    );
  }

  getPatientProfile(details: any): Observable<PatientData> {
    return this.http.post<PatientData>(
      environment.BASE_URL + Constants.PATIENT_INFO_URL,
      details
    );
  }

  updatevitalenableState(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.UDPATE_VITAL_STATUS,
      details
    );
  }

  updatePedometerState(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.UPDATE_PEDOMETER,
      details
    );
  }

  resendOtp(details: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.RESEND_OTP,
      details,
      { responseType: 'text' as 'json' }
    );
  }

  updatePatientStatus(details: any): Observable<GenericResponse> {
    return this.http.get<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_STATUS + details
    );
  }

  deletePatient(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_PATIENT,
      details
    );
  }
  getAttentionList(details: any) {
    return this.http.post<any>(
      environment.BASE_URL + Constants.ATTENTION_DETAILS,
      details
    );
  }
  stopTimerAPI(userId: any, patientId: any, time: any) {
    return this.http.get<GenericResponse>(
      environment.BASE_URL + Constants.TIMER + '/' + userId + '/' + patientId + '/' + time,
    );
  }

  getDashboardVitalInfo(req:any)
  {
    return this.http.get<any>(
      environment.BASE_URL + Constants.VITAL_TABLE + '/' + req.patientId + '/' + req.time,
    );
  }
}
