.card {
    padding: 1rem !important;
}



.container {
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 5px;
    color: white;
    text-transform: uppercase;
}

.list-unstyled {
    padding-left: 0;
    list-style: none;
}

.feeds_widget li {
    padding: 0px 15px;
    border: 1px solid #f0f0f0;
    margin: 4px 0;
}

.feeds_widget li:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.28), 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),
        background-color 0.2s cubic-bezier(0.8, 0, 0.8, 1),
        color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.feeds_widget li .feeds-left {
    float: left;
    width: 33px;
    font-size: 18px;
}

.feeds_widget li .feeds-body {
    width: auto;
}

.feeds_widget li .feeds-body .title {
    font-size: 16px;
}

.feeds_widget li .feeds-body>small {
    font-size: 13px;
    color: #777;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    width: 80%;
}

.careplanmenu
{
    border-left: 1px solid #E8E8E8;
    li
    {
        list-style: none;
        padding: 20px;
        cursor: pointer;
        font-size: 14px;
    }
    .active
    {
        border-left: 4px solid #336CFB;
        color:#336CFB
    }
    ul{
        position: sticky;
        top:0px;
    }
}
.searchheading
{
    padding: 15px;
    border-bottom: 1px solid #E8E8E8;
}
.disabled
{
    pointer-events: none;
}
::ng-deep .p-multiselect
{ 
    margin-top: 5px;
}
::ng-deep .p-multiselect-token{
    font-size: 10px;
    padding: 3px 12px;
}
::ng-deep .w-40rem
{
    width: 40rem;
}
.subheader
{
    font-size: 12px;
    margin-bottom: 5px;
}
::ng-deep .sdohscreen
{
     .p-sidebar-content
    {
        padding: 0px !important;
    }
}
.question {
    background-color: #f1f1f1;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 5px;
    width: 90%;
  }
  
  .answer {
    background-color: #00a9c9;
    color: white;
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 10px;
    width: 90%;
    align-self: flex-end;
    text-align: end;
  }
  ::ng-deep body.blocked-scroll {
    overflow: hidden !important;
}