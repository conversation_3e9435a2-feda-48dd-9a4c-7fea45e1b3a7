import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import {
  MeasuredData,
  ThresholdMinMax,
  ThresholdStatus,
} from '../../../../../../api/editPatientProfile';
import {
  PatientVitalReadings,
  PedometerEnableStatus,
  PedometerReadings,
  SleepMonitorReadings,
  ThresholdConfigResp,
  VitalEnableStatus,
  VitalScheduleResponse,
} from '../../../../../../api/patientVitals';
import {
  ReportExport,
  ReportRequest,
  ReportResponse,
} from '../../../../../../api/reports';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class VitalService {
  constructor(private http: HttpClient) {}

  getVitalReadings(details: any): Observable<PatientVitalReadings> {
    return this.http.post<PatientVitalReadings>(
      environment.BASE_URL + Constants.GET_VITAL_READINGS,
      details
    );
  }

  getPedometerReadings(details: any): Observable<PedometerReadings> {
    return this.http.get<PedometerReadings>(
      environment.BASE_URL + Constants.GET_PEDOMETER_READINGS + details
    );
  }

  getSleepMonitorReadings(details: any): Observable<SleepMonitorReadings> {
    return this.http.get<SleepMonitorReadings>(
      environment.BASE_URL + Constants.GET_SLEEP_READINGS + details
    );
  }

  getVitalThreshold(details: any): Observable<ThresholdConfigResp> {
    return this.http.post<ThresholdConfigResp>(
      environment.BASE_URL + Constants.GET_VITAL_THRESHOLD,
      details
    );
  }

  getPedometerThreshold(details: any): Observable<ThresholdMinMax> {
    return this.http.get<ThresholdMinMax>(
      environment.BASE_URL + Constants.GET_PEDO_THRESHOLD + details
    );
  }

  getVitalGraph(details: any, isWeekly: boolean): Observable<ReportResponse> {
    let url = isWeekly
      ? Constants.VITAL_GRAPH_WEEKLY
      : Constants.VITAL_GRAPH_DAILY;
    return this.http.post<ReportResponse>(environment.BASE_URL + url, details);
  }

  getPedometerGraph(
    details: any,
    isWeekly: boolean
  ): Observable<ReportResponse> {
    let url = isWeekly
      ? Constants.PEDO_GRAPH_WEEKLY
      : Constants.PEDO_GRAPH_DAILY;
    return this.http.post<ReportResponse>(environment.BASE_URL + url, details);
  }

  getVitalThresholdConfig(details: any): Observable<ThresholdStatus> {
    return this.http.get<ThresholdStatus>(
      environment.BASE_URL + Constants.THRESHOLD_CONFIG + details
    );
  }

  getVitalEnableStatus(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.GET_VITAL_STATUS,
      details
    );
  }

  getPedometerVitalEnableStatus(
    details: any
  ): Observable<PedometerEnableStatus> {
    return this.http.get<PedometerEnableStatus>(
      environment.BASE_URL + Constants.GET_PEDOMETER_STATUS + details
    );
  }

  getSleepVitalEnableStatus(details: any): Observable<PedometerEnableStatus> {
    return this.http.get<PedometerEnableStatus>(
      environment.BASE_URL + Constants.GET_SLEEP_STATUS + details
    );
  }

  updatevitalenableState(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.UDPATE_VITAL_STATUS,
      details
    );
  }

  updatePedometerState(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.UPDATE_PEDOMETER,
      details
    );
  }

  updateSleepState(details: any): Observable<MeasuredData> {
    return this.http.post<MeasuredData>(
      environment.BASE_URL + Constants.UPDATE_SLEEP_STATUS,
      details
    );
  }

  collectNowVitalRequest(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.COLLECT_NOW_VITAL,
      details
    );
  }

  collectNowPedometerRequest(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.COLLECT_PEDOMETER_NOW,
      details
    );
  }

  addManualData(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.ADD_MANUAL_DATA,
      details
    );
  }

  updateThresholdConfig(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.UPDATE_THRESHOLLD_CONFIG,
      details
    );
  }

  updatePedoThresholdConfig(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.UPDATE_PEDO_THRESHOLLD_CONFIG,
      details
    );
  }

  deletVitalReadings(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.DELETE_VITAL_READINGS,
      details
    );
  }

  getVitalSchedules(details: any): Observable<VitalScheduleResponse> {
    return this.http.post<VitalScheduleResponse>(
      environment.BASE_URL + Constants.VITAL_SCHEDULES,
      details
    );
  }

  deletVitalSchedules(details: any): Observable<VitalEnableStatus> {
    return this.http.post<VitalEnableStatus>(
      environment.BASE_URL + Constants.DELETE_VITAL_SCHEDULES,
      details
    );
  }

  exportData(reportReq: ReportRequest): Observable<ReportExport> {
    return this.http.post<ReportExport>(
      environment.BASE_URL + Constants.VITAL_XLS_URL,
      reportReq
    );
  }
}
