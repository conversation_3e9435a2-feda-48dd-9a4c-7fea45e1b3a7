import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AlertsComponent } from '../../../../shared/alerts/alerts.component';

@Component({
  standalone: true,
  selector: 'app-individual-alerts',
  templateUrl: './individual-alerts.component.html',
  styleUrls: ['./individual-alerts.component.css'],
  imports: [AlertsComponent],
})
export class IndividualAlertsComponent implements OnInit {
  constructor(private route: ActivatedRoute) {}
  patientId: number = 0;
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
  }
}
