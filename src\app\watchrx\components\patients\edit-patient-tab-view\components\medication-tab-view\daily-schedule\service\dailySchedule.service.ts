import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import {
  GenericResponse,
  PatientData,
} from '../../../../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class DailyScheduleService {
  constructor(private http: HttpClient) {}

  getDailySchedule(details: any): Observable<PatientData> {
    return this.http.post<PatientData>(
      environment.BASE_URL + Constants.GET_DAILY_SCHEDULE,
      details
    );
  }

  updateDailySchedule(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_DAILY_SCHEDULE,
      details
    );
  }
}
