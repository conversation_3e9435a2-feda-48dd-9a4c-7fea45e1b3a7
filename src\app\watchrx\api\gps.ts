export interface GpsInfoResponse {
  success: boolean;
  responseCode: number;
  responsecode: number;
  messages: string[];
  gpsId: string | null;
  address1: string;
  address2: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  radius: string;
  unit: string;
  gpsStatus: boolean;
  trackStatus: boolean;
  latitude: string;
  longitude: string;
  patientId: string;
  trackLatitude: string;
  trackLongitude: string;
  gpsstatus: boolean;
}
