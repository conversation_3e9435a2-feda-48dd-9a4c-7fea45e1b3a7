.fc-unthemed .fc-list-item:hover td {
    background-color: gray;
}

.fc .fc-toolbar .fc-state-default {
    background: #000000;
    color: #ffffff;
    font-size: 14px;
    padding: 8px 18px;
    height: auto;
    border: 7px;
    box-shadow: none;
    text-shadow: none;
}

.fc-button.fc-highlight {
    opacity: 0.5;
}

.fc-time-grid .fc-slats td {
    height: 3.5em;
    border-bottom: 0;
}

.fc .fc-list-table {
    table-layout: fixed;
}

.fc {
    max-width: 1100px;
    margin: 0 auto;
}

:host ::ng-deep .fc .fc-button-primary:not(:disabled).fc-button-active {
    background-color: blue !important;
    border-color: blue !important;
    color: #ffffff !important;
}

:host ::ng-deep .fc .fc-list-event:hover td {
    background-color: grey;
    color: white;
}

.fc-event.red-event {
    background-color: red !important;
    border-color: red !important;
}