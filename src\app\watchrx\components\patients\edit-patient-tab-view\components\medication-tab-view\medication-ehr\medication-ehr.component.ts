import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';
import { RadioButtonModule } from 'primeng/radiobutton';
import { SidebarModule } from 'primeng/sidebar';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { MedicationEHRService } from './service/medicationEHR.service';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { MedicationEHResp } from '../../../../../../api/medications';

@Component({
  selector: 'app-medication-ehr',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    TableModule,
    DialogModule,
    RadioButtonModule,
    FileUploadModule,
    CheckboxModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    ConfirmDialogModule,
    SidebarModule,
  ],
  templateUrl: './medication-ehr.component.html',
  styleUrl: './medication-ehr.component.scss',
  providers: [ConfirmationService, MessageService],
})
export class MedicationEhrComponent {
  patientId: number = 0;
  SideBarHeader = '';
  medicationList: MedicationEHResp[] = [];
  visible: boolean = false;
  summary: string = '';
  isEditMeds: any = null;
  constructor(
    private medicationService: MedicationEHRService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) { }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getMedicationList();
  }

  showDialog() {
    this.SideBarHeader = "Add Summary";
    this.visible = true;
     this.summary = '';
     this.isEditMeds=null;
  }
  getMedicationList() {
    this.loaderService.show();
    this.medicationService.getMedicationInfo(this.patientId).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.medicationList = res;
      }
    }, err => {
      this.loaderService.hide();
    });
  }


  editSummaryView(item: any) {
    this.SideBarHeader = "Edit Summary";
    this.visible = true;
    this.isEditMeds = item;
    console.log(item);
    this.summary = item.medicationSummary || ''
  }

  deleteSummary(event: any, medicationId: number) {
    event.stopPropagation();
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.medicationService
          .deleteMedicationInfo(medicationId)
          .subscribe((res) => {
            this.loaderService.hide();
            if (res.success) {
              this.getMedicationList();
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Precription deleted successfully',
              });
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to delete precription',
              });
            }
          },err=>{
            this.loaderService.hide();
          });
      },
      reject: () => { },
    });
  }

  addNewMedication() {
    let obj = { patientId: this.patientId, medicationSummaryId: 0, medicationSummary: this.summary }
    if (this.isEditMeds) {
      obj.medicationSummaryId = this.isEditMeds.medicationSummaryId;
    }
    this.loaderService.show();
    this.medicationService.addorUpdateMedicationInfo(obj).subscribe(res => {
      if (res.success) {
        this.loaderService.hide();
        this.isEditMeds=null;
        this.getMedicationList();
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Summary Added successfully',
        });
      } else {
        this.isEditMeds=null;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to delete summary',
        });
      }
      this.visible=false;
    }, err => {
      this.isEditMeds=null;
      this.loaderService.hide();
      this.visible=false;
    })
  }
}
