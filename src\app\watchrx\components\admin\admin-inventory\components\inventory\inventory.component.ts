import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DeviceType, InventoryItem } from '../../../../../api/inventory';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { InventoryService } from '../../service/inventory.service';

@Component({
  selector: 'app-inventory',
  templateUrl: './inventory.component.html',
  styleUrls: ['./inventory.component.css'],
})
export class InventoryComponent implements OnInit {
  constructor(
    private loaderService: LoaderService,
    private messageService: MessageService,
    private confirmService: ConfirmationService,
    private inventoryService: InventoryService,
    private fb: FormBuilder
  ) {
    this.deviceForm = this.fb.group({
      deviceType: ['', Validators.required],
      make: ['', Validators.required],
      model: ['', Validators.required],
      quantity: ['', [Validators.required, Validators.min(1)]],
      deviceName: ['', Validators.required],
      color: [''],
      deviceMeasures: ['', Validators.required],
      deviceImage: [null],
    });
  }

  inventoryList: InventoryItem[] = [];
  totalCount: number = 0;
  itemsPerPage: number = 10;

  addModal: boolean = false;
  deviceForm: FormGroup;
  selectedImage: File | null = null;

  deviceTypes: DeviceType[] = [];

  editModel: boolean = false;
  editQnty: number = 0;
  editImageFile: File | null = null;
  specUrl: File | null = null;
  editDeviceName: string = '';
  editInventoryId: number = 0;

  colors = [
    { label: 'Silver', value: 'silver' },
    { label: 'Black', value: 'black' },
    { label: 'White', value: 'white' },
    { label: 'Blue', value: 'blue' },
    { label: 'Pink', value: 'pink' },
  ];

  measurements = [
    {
      label: 'Blood Pressure',
      title: 'bloodPressure',
      value: 'Blood Pressure',
    },
    {
      label: 'Blood Sugar',
      title: 'bloodSugar',
      value: 'Blood Sugar',
    },
    {
      label: 'Weight',
      title: 'weight',
      value: 'Weight',
    },
    {
      label: 'Temperature',
      title: 'temperature',
      value: 'Temperature',
    },
    {
      label: 'Heart Rate',
      title: 'heartrate',
      value: 'Heart Rate',
    },
    {
      label: 'Oxygen Saturation',
      title: 'oxygen saturation',
      value: 'Oxygen Saturation',
    },
    {
      label: 'Inhaler',
      title: 'Inhaler',
      value: 'Inhaler',
    },
  ];

  ngOnInit() {
    this.getInventoryList();
    this.getDeviceTypes();
  }

  getDeviceTypes() {
    this.inventoryService.getDeviceTypes().subscribe((res) => {
      if (res?.success) {
        this.deviceTypes = res.eList;
      }
    });
  }

  getInventoryList() {
    this.loaderService.hide();
    this.loaderService.show();
    this.inventoryService.getInventory().subscribe((res) => {
      this.loaderService.hide();
      if (res?.success) {
        this.inventoryList = res?.inventory;
        this.totalCount = res?.inventory?.length;
        this.itemsPerPage = res?.inventory?.length;
        console.log('Size:', this.inventoryList.length);
      }
    });
  }

  showAddModalPopup() {
    this.addModal = true;
  }

  onImageSelect(event: any) {
    const file = event.files[0];
    if (file) {
      this.selectedImage = file;
    }
  }

  onSubmit() {
    if (this.deviceForm.invalid) {
      this.deviceForm.markAllAsTouched();
      return;
    }
    const formData = new FormData();
    if (this.selectedImage)
      formData.append('imageFile', this.selectedImage as File);
    else formData.append('imageFile', '');
    formData.append('imgUrl', '');
    formData.append('fileModified', 'true');
    formData.append('make', this.deviceForm.value.make);
    formData.append('model', this.deviceForm.value.model);
    formData.append('deviceType', this.deviceForm.value.deviceType);
    formData.append('deviceName', this.deviceForm.value.deviceName);
    formData.append(
      'deviceMeasures',
      this.deviceForm.value.deviceMeasures.join('|')
    );
    formData.append('color', this.deviceForm.value.color);
    formData.append('sellingPrice', '1');
    formData.append('quantity', this.deviceForm.value.quantity.toString());

    this.loaderService.show();
    this.inventoryService.addInventory(formData).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Device added to inventory successfully',
        });
        this.resetFileds();
        this.getInventoryList();
        this.addModal = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to add to inventory',
        });
      }
    });
  }

  resetFileds() {
    this.deviceForm = this.fb.group({
      deviceType: ['', Validators.required],
      make: ['', Validators.required],
      model: ['', Validators.required],
      quantity: ['', [Validators.required, Validators.min(1)]],
      deviceName: ['', Validators.required],
      color: [''],
      deviceMeasures: ['', Validators.required],
      deviceImage: [null],
    });
  }

  onEditImageSelect(event: any) {
    const file = event.files[0];
    if (file) {
      this.editImageFile = file;
    }
  }

  onEditSpecImage(event: any) {
    const file = event.files[0];
    if (file) {
      this.specUrl = file;
    }
  }

  onOpenEditModal(item: InventoryItem) {
    this.editModel = true;
    this.editQnty = item?.quantity;
    this.editDeviceName = item?.deviceName;
    this.editInventoryId = item?.inventoryId;
  }

  resetEditFileds() {
    this.editQnty = 0;
    this.editDeviceName = '';
    this.editInventoryId = 0;
  }

  updateInventory() {
    let payload = {
      inventoryId: this.editInventoryId,
      sellingPrice: '1',
      quantity: this.editQnty,
    };

    this.loaderService.show();
    this.inventoryService.updateInventory(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Inventory updated successfully',
        });
        this.resetEditFileds();
        this.getInventoryList();
        this.editModel = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update inventory',
        });
      }
    });
  }
}
