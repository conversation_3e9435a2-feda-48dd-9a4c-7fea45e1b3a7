<div class="flex md:justify-content-end mb-5 addbutton">
    <p-button label="Add Medication Summary" severity="secondary" [outlined]="true" (onClick)="showDialog()"
        icon="pi pi-plus"></p-button>
</div>
<p-toast />
<p-confirmDialog />

<p-table [value]="medicationList" styleClass="p-datatable-gridlines p-datatable-striped custom-table mg-bt">
    <ng-template pTemplate="header">
        <tr>
            <th>S.No</th>
            <th style="width: 80%">Medication Summary</th>
            <th>Action</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-med let-i="rowIndex">
        <tr class="medicationlist">
            <td>{{ i + 1 }}</td>
            <td>
                {{med.medicationSummary}}
            </td>
            <td>
                <div class="flex">
                    <button pButton pRipple icon="pi pi-pencil" class="p-button-secondary mr-2 p-button-outlined"
                        style="width: 30px; height: 30px" (click)="editSummaryView(med)"></button>
                    <button pButton pRipple icon="pi pi-trash" class="p-button-secondary p-button-outlined"
                        (click)="deleteSummary($event, med.medicationSummaryId)"
                        style="width: 30px; height: 30px"></button>
                </div>
            </td>
        </tr>
    </ng-template>
</p-table>

<p-sidebar [(visible)]="visible" position="right" styleClass="w-55rem">
    <ng-template pTemplate="header">
        <div class="flex align-items-center gap-2">
            <span class="font-bold">
                <i class="pi pi-plus mr-2" style="font-size: 1rem " *ngIf="!isEditMeds"></i>
                <i class="pi pi-pencil mr-2" style="font-size: 1rem " *ngIf="isEditMeds"></i>
                {{SideBarHeader}}
            </span>
        </div>
    </ng-template>

    <div class="p-fluid p-formgrid grid mt-2">
        <div class="field col-12 md:col-12">
            <label htmlfor="medicationName">Medication Summary</label>
            <textarea [(ngModel)]="summary" name="summary" class="w-full" rows="20" cols="30"
                placeholder="summary" pInputTextarea></textarea>
        </div>

    </div>
    <ng-template pTemplate="footer">
        <div class="flex justify-content-end gap-2">
            <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="visible = false" />
            <p-button label="Save" severity="primary" (click)="addNewMedication()" />
        </div>
    </ng-template>
</p-sidebar>