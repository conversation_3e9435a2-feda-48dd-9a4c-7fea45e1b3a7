import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import {
  MedicationResp,
  ResponseData,
} from '../../../../../../../api/medications';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class MedicationsService {
  constructor(private http: HttpClient) {}

  getMedicationInfo(details: any): Observable<ResponseData> {
    return this.http.post<ResponseData>(
      environment.BASE_URL + Constants.GET_MEDICATIONS,
      details
    );
  }

  addorUpdateMedicationInfo(
    details: any,
    isEdit: boolean
  ): Observable<MedicationResp> {
    let url = isEdit ? Constants.UPDATE_MEDS : Constants.ADD_MEDICATIONS;
    return this.http.post<MedicationResp>(environment.BASE_URL + url, details);
  }

  updateMedicationImage(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.MED_IMAGE,
      details
    );
  }

  deleteMedicationInfo(details: any): Observable<MedicationResp> {
    return this.http.post<MedicationResp>(
      environment.BASE_URL + Constants.DELETE_MEDS,
      details
    );
  }
   medicationUpload(details:any): Observable<any>
    {
      return this.http.post<any>(
        environment.BASE_URL + Constants.UPLOAD_MEDS,details
      );
    }
   reminderMedications(prescriptionId: number, isReviewed: boolean): Observable<any> {
       return this.http.get<any>(
         environment.BASE_URL + Constants.REMINDER_MEDS +"/"+ prescriptionId +"/"+isReviewed
       );
     }
   reviewMedications(patientId: number): Observable<any> {
      return this.http.get<any>(
        environment.BASE_URL + Constants.REVIEW_MEDS +"/"+ patientId
      );
    }
}
