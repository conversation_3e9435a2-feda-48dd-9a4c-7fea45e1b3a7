export abstract class Constants {
  static readonly GET_VITAL_READINGS = 'service/patient/getVitalReadings';
  static readonly GET_PEDOMETER_READINGS =
    'service/patient/getPedometerReadingList/';
  static readonly GET_SLEEP_READINGS =
    'service/patient/getSleepMonitoringReadingList/';
  static readonly GET_VITAL_THRESHOLD =
    'service/patient/getThresholdConfiguration';
  static readonly GET_PEDO_THRESHOLD = 'service/patient/getThresholdConfig/';
  static readonly VITAL_GRAPH_WEEKLY = 'service/patient/vitalGraphWeekly';
  static readonly VITAL_GRAPH_DAILY = 'service/patient/vitalGraph';

  static readonly PEDO_GRAPH_WEEKLY =
    'service/patient/vitalPedometerWeeklyGraph';
  static readonly PEDO_GRAPH_DAILY = 'service/patient/vitalPedometerDailyGraph';
  static readonly THRESHOLD_CONFIG = 'service/patient/getThresholdConfig/';
  static readonly UDPATE_VITAL_STATUS = 'service/patient/enableVitalCollection';
  static readonly UPDATE_PEDOMETER =
    'service/patient/savePedometerConfiguration';
  static readonly GET_VITAL_STATUS = 'service/patient/getEnableStatus';
  static readonly GET_PEDOMETER_STATUS =
    'service/patient/getPedometerConfiguration/';
  static readonly GET_SLEEP_STATUS =
    'service/patient/getSleepMonitoringConfiguration/';
  static readonly UPDATE_SLEEP_STATUS =
    'service/patient/enableDisableSleepMonitoring';
  static readonly COLLECT_NOW_VITAL = 'service/patient/saveVitalSchedule';
  static readonly COLLECT_PEDOMETER_NOW =
    'service/patient/getPedometerReadingCountNow';
  static readonly ADD_MANUAL_DATA = 'service/patient/saveVitalReadings';
  static readonly UPDATE_THRESHOLLD_CONFIG =
    'service/patient/saveThresholdConfiguration';
  static readonly UPDATE_PEDO_THRESHOLLD_CONFIG =
    'service/patient/saveThresholdConfig';
  static readonly DELETE_VITAL_READINGS = 'service/patient/deleteVital';
  static readonly VITAL_SCHEDULES = 'service/patient/getVitalShedules';
  static readonly DELETE_VITAL_SCHEDULES =
    'service/patient/deleteVitalSchedule';
  static readonly VITAL_XLS_URL = '/service/charts/getVitalsForPatient';
}
