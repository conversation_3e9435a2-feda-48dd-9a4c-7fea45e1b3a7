<p-dialog [(visible)]="openVoiceVideoDlg" [modal]="false" [style]="{ width: '900px' }"
  [contentStyle]="{ overflow: 'auto' }" position="top" [maximizable]="true" [header]="patientName"
  (visibleChange)="handleClose()" [header]="'Voice Call'">

  <form name="form" role="form" id="update-form">
    <div class="modal-content mt-2">
      <div class="modal-header">
        <div class="col-8">
          <div id="callStatus"></div>
        </div>
      </div>
      <div class="modal-body" id="modalBody">
        <div class="row clearfix">
          <div class="flex flex-wrap gap-3">
            <div class="flex align-items-center">
              <p-radioButton name="mode" value="mobile"  inputId="Mobile" [(ngModel)]="callMode" (onClick)="voiceToken()"/>
              <label for="Mobile" class="ml-2"> Mobile No. </label>
            </div>

            <div class="flex align-items-center">
              <p-radioButton name="mode" value="voip"  inputId="Voip"[(ngModel)]="callMode" (onClick)="voiceToken()"/>
              <label for="Voip" class="ml-2"> VOIP </label>
            </div>
          </div>
          <div class="field col-12 md:col-4">
            <label htmlfor="state" class="text-600 font-bold">Select Program(s)</label>
            <div class="flex justify-content-left gap-3">
              <div class="flex flex-wrap gap-3">
                <div class="flex align-items-center" *ngFor="let option of programs">
                  <p-radioButton name="consent" [value]="option.value" [(ngModel)]="selectedProgram"
                    [inputId]="option.label.toLowerCase()" variant="filled" class="mt-3" />
                  <label [for]="option.label.toLowerCase()" class="ml-2 mt-3 text-600 font-bold">{{ option.label
                    }}</label>
                </div>

              </div>
            </div>
          </div>
          <div class="field col-12 md:col-8" *ngIf="callMode === 'mobile'">
            <label htmlfor="state">Phone Number</label>
            <input id="phonenumber" type="text" pInputText name="phoneNumber" required /><span><small
                class="red">Note:(Example:+18001232323)</small></span>
          </div>
          <div class="field col-12 md:col-8" *ngIf="callMode === 'voip'">
            <label htmlfor="state">Patient Identity</label>
            <input id="patientname" type="text" pInputText name="patientName" required [(ngModel)]="patientname" />
          </div>
          <div id="input-volume-controller" class="col-6 hide">
            <div class="volume-indicators">
              <i class="pi pi-microphone fa-2x" aria-hidden="true"></i>
              <div id="input-volume"></div>
            </div>
          </div>
          <div id="output-volume-controller" class="col-6 hide">
            <div class="volume-indicators">
              <i class="pi pi-volume-up fa-2x" aria-hidden="true"></i>
              <div id="output-volume"></div>
            </div>
          </div>
          <div class="col-12 someth hide" id="animation">
            <div class="call-animation-play" id="palypauseanim">
              <i class="pi pi-phone caller-img"></i>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <p-button id="button-call" [disabled]="!selectedProgram" [rounded]="true" label="" icon="pi pi-phone" severity="info" class="mr-2">
        </p-button>
        <p-button id="muteUnmute" [disabled]="!selectedProgram"  [rounded]="true" label="" [icon]="iconClass" severity="info" class="mr-2">
        </p-button>
        <p-button id="button-hangup-outgoing" [disabled]="!selectedProgram"  [rounded]="true" label="" icon="pi pi-phone" severity="danger"
          class="mr-2">
        </p-button>
        <p-button id="cancel-voice-call" [disabled]="!selectedProgram"  [rounded]="true" label="Cancel" icon="pi pi-times" severity="danger"
          class="mr-1">
        </p-button>
      </div>
    </div>
  </form>
  <!-- <p-tabView [(activeIndex)]="activeIndex">
    <p-tabPanel header="Voice Call">
      <form name="form" role="form" id="update-form">
        <div class="modal-content">
          <div class="modal-header">
            <div class="col-8">
              <div id="callStatus"></div>
            </div>
          </div>
          <div class="modal-body" id="modalBody">
            <div class="row clearfix">
              <div class="field col-12 md:col-4">
                <label htmlfor="state" class="text-600 font-bold">Select Program(s)</label>
                <div class="flex justify-content-left gap-3">
                  <div class="flex flex-wrap gap-3">
                    <div class="flex align-items-center" *ngFor="let option of programs">
                      <p-radioButton name="consent" [value]="option.value" [(ngModel)]="selectedProgram"
                        [inputId]="option.label.toLowerCase()" variant="filled" class="mt-3" />
                      <label [for]="option.label.toLowerCase()" class="ml-2 mt-3 text-600 font-bold">{{ option.label
                        }}</label>
                    </div>

                  </div>
                </div>
              </div>
              <div class="field col-12 md:col-8">
                <label htmlfor="state">Phone Number</label>
                <input id="phonenumber" type="text" pInputText name="phoneNumber" required /><span><small
                    class="red">Note:(Example:+18001232323)</small></span>
              </div>
              <div id="input-volume-controller" class="col-6 hide">
                <div class="volume-indicators">
                  <i class="pi pi-microphone fa-2x" aria-hidden="true"></i>
                  <div id="input-volume"></div>
                </div>
              </div>
              <div id="output-volume-controller" class="col-6 hide">
                <div class="volume-indicators">
                  <i class="pi pi-volume-up fa-2x" aria-hidden="true"></i>
                  <div id="output-volume"></div>
                </div>
              </div>
              <div class="col-12 someth hide" id="animation">
                <div class="call-animation-play" id="palypauseanim">
                  <i class="pi pi-phone caller-img"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <p-button id="button-call" [disabled]="!selectedProgram" [rounded]="true" label="" icon="pi pi-phone" severity="info" class="mr-2">
            </p-button>
            <p-button id="muteUnmute" [disabled]="!selectedProgram"  [rounded]="true" label="" [icon]="iconClass" severity="info" class="mr-2">
            </p-button>
            <p-button id="button-hangup-outgoing" [disabled]="!selectedProgram"  [rounded]="true" label="" icon="pi pi-phone" severity="danger"
              class="mr-2">
            </p-button>
            <p-button id="cancel-voice-call" [disabled]="!selectedProgram"  [rounded]="true" label="Cancel" icon="pi pi-times" severity="danger"
              class="mr-1">
            </p-button>
          </div>
        </div>
      </form>
    </p-tabPanel>
    <p-tabPanel header="Video Call">
      <div class="flex align-items-center">
        <div class="col-12 md:col-6 flex gap-3">
          <label htmlfor="state" class="text-600 font-bold m-0">Select Program(s)</label>
          <div class="flex justify-content-left gap-3">
            <div class="flex flex-wrap gap-3">
              <div class="flex align-items-center" *ngFor="let option of programs">
                <p-radioButton name="consent" [value]="option.value" [inputId]="option.label.toLowerCase()"  [(ngModel)]="selectedProgram"
                  variant="filled" />
                <label [for]="option.label.toLowerCase()" class="ml-2 text-600 font-bold">{{ option.label }}</label>
              </div>
            </div>
          </div>
        </div>
        <div id="join-flow" class="col-12 md:col-6 flex  justify-content-end gap-3">
          <p-button [rounded]="false" icon="pi pi-video" severity="info" class="mr-1" (click)="getVideoSDKJWT()"
            label="Join Session" [disabled]="this.inSession || !selectedProgram" />
          <p-button id="cancel-video-call" [rounded]="false" label="Cancel" icon="pi pi-phone" severity="danger"
            (click)="handleClose()" class="mr-1" [disabled]="!selectedProgram">
          </p-button>
        </div>
      </div>
      <main>
        <div class="row clearfix">
          <div id="sessionContainer"></div>
        </div>
      </main>
    </p-tabPanel>
  </p-tabView> -->
</p-dialog>