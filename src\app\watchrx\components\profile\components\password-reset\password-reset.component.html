<p-toast></p-toast>
<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/chat.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Reset Password</span
  >
</div>
<div class="flex w-full card">
    <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="w-full">
        <div class="field col-12 md:col-6 flex flex-column">
            <label for="oldPassword">Old Password <span class="p-text-danger"> *</span></label>
            <p-password id="oldPassword" type="password"  formControlName="oldPassword" [feedback]="false" [toggleMask]="true" autocomplete="new-oldpassword" ></p-password>
            <small *ngIf="resetPasswordForm.controls['oldPassword'].invalid && resetPasswordForm.controls['oldPassword'].touched" class="red">
              Old password is required.
            </small>
          </div>

      
        <div class="field col-12 md:col-6 flex flex-column">
          <label for="newPassword">New Password <span class="p-text-danger"> *</span></label>
          <p-password id="newPassword" type="password" formControlName="newPassword" [feedback]="false" [toggleMask]="true" autocomplete="new-password" />
          <small *ngIf="resetPasswordForm.controls['newPassword'].invalid && resetPasswordForm.controls['newPassword'].touched" class="red">
            New password must be 8-20 characters long, contain at least one uppercase letter, one lowercase letter, one number, and one special character.
          </small>
        </div>
      
        <div class="field col-12 md:col-6 flex flex-column">
          <label for="confirmPassword">Confirm Password <span class="p-text-danger"> *</span></label>
          <p-password id="confirmPassword" type="password" formControlName="confirmPassword" [feedback]="false" [toggleMask]="true" autocomplete="new-confirmpassword"/>
          <small *ngIf="resetPasswordForm.errors?.['mismatch'] && resetPasswordForm.controls['confirmPassword'].touched" class="red">
            Passwords do not match.
          </small>
          
        </div>
      
        <div class="field flex justify-content-end actionbuttons p-3">
            <p-button  type="submit" severity="primary" label="Submit" [disabled]="resetPasswordForm.invalid"></p-button>
          </div>
      </form>
</div>