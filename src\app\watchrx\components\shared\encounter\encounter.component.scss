.bold-textarea {
    max-width: 100%;
    max-height: 200px;
    border: 1px solid #ccc;
    padding: 5px;
    word-wrap: break-word;
    transition: border-width 0.3s;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 6px;
}

// .bold-textarea.active,
// .bold-textarea:focus {
//     border-width: 0px;
// }

.bold-textarea.active:empty::before {
    content: attr(placeholder);
    color: #aaa;
}

.bold-textarea.active:focus:empty::before {
    content: '';
}

.bold-textarea[contenteditable="true"] {
    outline: none;
}

.bold {
    font-weight: bold;
}

::ng-deep .highlighted {
    background-color: yellow;
    font-weight: bold;
}
.foot
{
    border: 1px solid #ccc;
    border-bottom: 0;
    background-color: #efefef;
}
::ng-deep .start
{
    background-color: #6366F1;
    .pi
    {
        color: #ffffff;
    }
}
::ng-deep .stop
{
    background-color: red;
    .pi
    {
        color: #ffffff;
    }
}

::ng-deep .list
{
     background-color: #6366F1;
     padding:10px !important;
     color:#ffffff;
    .pi
    {
        color: #ffffff !important;
        margin-right: 10px;
    }
}
::ng-deep .disabled-textarea {
  background: #f5f5f5;
  pointer-events: none;
  color: #aaa;
}