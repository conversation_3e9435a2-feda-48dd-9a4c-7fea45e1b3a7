<ng-container *ngIf="!fromDashboard">
  <div class="card" style="margin: 10px">
    <span class="text-black-500 ml-10 mb-2 font-medium flex align-items-center">
      <p-inputSwitch [(ngModel)]="emailEnabled" (onChange)="updateEmail()" class="mr-3" />Email Notification Enabled
    </span>

    <full-calendar #calendar [options]="calendarOptions" [deepChangeDetection]="true"></full-calendar>
  </div>
  <p-toast />
  <p-dialog header="Patient Tasks" [(visible)]="openAddTaskModal" [modal]="true"
    [style]="{ width: '700px', height: 'auto' }" [contentStyle]="{ overflow: 'auto' }" [draggable]="false">
    <ng-template pTemplate="content">
      <div class="p-fluid p-formgrid grid">
        <div class="field col-12 md:col-6">
          <label for="persNumber">Task Title</label>
          <input id="taskTitle" type="text" pInputText [(ngModel)]="taskTitle" name="persNumber" [disabled]="isEdit" />
        </div>
        <div class="field col-12 md:col-6">
          <label for="persNumber">Task Priority</label>
          <p-dropdown id="connectingDevice" [(ngModel)]="taskPriority" name="taksPriority"
            [options]="['Low', 'Medium', 'High', 'Critical']" placeholder="Select a device" [required]="true"
            appendTo="body"></p-dropdown>
        </div>
        <div class="field col-12 md:col-12">
          <label for="persNumber">Task Description</label>
          <textarea rows="3" cols="30" pInputTextarea autoResize="true" class="p-inputtext p-component p-element"
            [(ngModel)]="taskDescription" [attr.maxLength]="100" placeholder="Enter your described..." required="true"
            [disabled]="isEdit">
        </textarea>
        </div>
        <div class="field col-12 md:col-3">
          <label htmlfor="description" class="font-bold block mb-2">
            Start Date
          </label>
          <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="taskStartDate" appendTo="body"
            [(ngModel)]="taskStartDate" dateFormat="mm-dd-yy"></p-calendar>
        </div>
        <div class="field col-12 md:col-3">
          <label for="calendar-timeonly" class="font-bold block mb-2">
            Start Time
          </label>
          <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay"
            appendTo="body" [showTime]="true" hourFormat="12" [(ngModel)]="taskStartTime" [stepMinute]="15">
            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
              <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
            </ng-template>
          </p-calendar>
        </div>
        <div class="field col-12 md:col-3">
          <label htmlfor="description" class="font-bold block mb-2">
            End Date
          </label>
          <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="taskEndDate" appendTo="body"
            [(ngModel)]="taskEndDate" dateFormat="mm-dd-yy"></p-calendar>
        </div>
        <div class="field col-12 md:col-3">
          <label for="calendar-timeonly" class="font-bold block mb-2">
            End Time
          </label>
          <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay"
            appendTo="body" [showTime]="true" hourFormat="12" [(ngModel)]="taskEndTime" [stepMinute]="15">
            <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
              <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
            </ng-template>
          </p-calendar>
        </div>
        <div class="field col-12 md:col-4" *ngIf="patientId === 0">
          <label htmlfor="state">Choose Patient</label>
          <p-autoComplete [suggestions]="filteredPatients" (completeMethod)="filterPatient($event)" field="patientName"
            [(ngModel)]="selectedPatient" placeholder="Select a Patient" appendTo="body" [dropdown]="true"
            emptyMessage="No patient found" [showEmptyMessage]="true" [required]="true"></p-autoComplete>
        </div>
        <div class="field col-12 md:col-6" *ngIf="isEdit">
          <label for="persNumber">Patient Name</label>
          <input id="patientName" type="text" pInputText [(ngModel)]="patientName" name="patientName"
            [disabled]="isEdit" />
        </div>
      </div>
    </ng-template>
    <ng-template pTemplate="footer">
      <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="closeModal()" />
      <p-button label="Delete" [outlined]="true" severity="danger" (click)="deleteTask()" *ngIf="isEdit" />
      <p-button label="Save" [outlined]="true" severity="success" (click)="isEdit ? editTask() : addTask()" />
    </ng-template>
  </p-dialog>
</ng-container>
<ng-container *ngIf="fromDashboard">
  <p-toast />
  <div>
    <full-calendar #calendar [options]="calendarOptions" [deepChangeDetection]="true" style="height:33vh"></full-calendar>
  </div>

  <div class="p-fluid p-formgrid grid mt-2" style="height:33vh">
    <div class="field col-12 md:col-6">
      <label for="persNumber">Task Title</label>
      <input id="taskTitle" type="text" pInputText [(ngModel)]="taskTitle" name="persNumber" [disabled]="isEdit" />
    </div>
    <div class="field col-12 md:col-6">
      <label for="persNumber">Task Priority</label>
      <p-dropdown id="connectingDevice" [(ngModel)]="taskPriority" name="taksPriority"
        [options]="['Low', 'Medium', 'High', 'Critical']" placeholder="Select a device" [required]="true"
        appendTo="body"></p-dropdown>
    </div>
    <div class="field col-12 md:col-12">
      <label for="persNumber">Task Description</label>
      <textarea rows="3" cols="30" pInputTextarea autoResize="true" class="p-inputtext p-component p-element"
        [(ngModel)]="taskDescription" [attr.maxLength]="100" placeholder="Enter your described..." required="true"
        [disabled]="isEdit">
    </textarea>
    </div>
    <div class="field col-12 md:col-3">
      <label htmlfor="description" class="font-bold block mb-2">
        Start Date
      </label>
      <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="taskStartDate" appendTo="body"
        [(ngModel)]="taskStartDate" dateFormat="mm-dd-yy"></p-calendar>
    </div>
    <div class="field col-12 md:col-3">
      <label for="calendar-timeonly" class="font-bold block mb-2">
        Start Time
      </label>
      <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay" appendTo="body"
        [showTime]="true" hourFormat="12" [(ngModel)]="taskStartTime" [stepMinute]="15">
        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
          <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
        </ng-template>
      </p-calendar>
    </div>
    <div class="field col-12 md:col-3">
      <label htmlfor="description" class="font-bold block mb-2">
        End Date
      </label>
      <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="taskEndDate" appendTo="body"
        [(ngModel)]="taskEndDate" dateFormat="mm-dd-yy"></p-calendar>
    </div>
    <div class="field col-12 md:col-3">
      <label for="calendar-timeonly" class="font-bold block mb-2">
        End Time
      </label>
      <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay" appendTo="body"
        [showTime]="true" hourFormat="12" [(ngModel)]="taskEndTime" [stepMinute]="15">
        <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
          <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
        </ng-template>
      </p-calendar>
    </div>
    <!-- <div class="field col-12 md:col-4" *ngIf="patientId === 0">
      <label htmlfor="state">Choose Patient</label>
      <p-autoComplete [suggestions]="filteredPatients" (completeMethod)="filterPatient($event)" field="patientName"
        [(ngModel)]="selectedPatient" placeholder="Select a Patient" appendTo="body" [dropdown]="true"
        emptyMessage="No patient found" [showEmptyMessage]="true" [required]="true"></p-autoComplete>
    </div> -->
    <div class="field col-12 md:col-6" *ngIf="isEdit">
      <label for="persNumber">Patient Name</label>
      <input id="patientName" type="text" pInputText [(ngModel)]="patientName" name="patientName" [disabled]="isEdit" />
    </div>
  
  <div class="footer flex gap-2 w-full justify-content-end ">
    <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="closeModal()" />
    <p-button label="Delete" [outlined]="true" severity="danger" (click)="deleteTask()" *ngIf="isEdit" />
    <p-button label="Save" severity="primary"  (click)="isEdit ? editTask() : addTask()" />
  </div>
</div>
</ng-container>