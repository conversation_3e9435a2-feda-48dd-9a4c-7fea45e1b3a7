import { CommonModule } from '@angular/common';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { GroupVo, InventoryItem } from '../../../../../api/deviceAllocation';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { MenuService } from '../../../../../service/menu.service';
import { MedicalDeviceService } from '../../service/medical-device.service';

@Component({
  standalone: true,
  selector: 'app-bulk-add-devices',
  templateUrl: './bulk-add-devices.component.html',
  styleUrls: ['./bulk-add-devices.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    ToastModule,
    ConfirmDialogModule,
    DropdownModule,
    InputTextModule,
  ],
  providers: [MessageService, ConfirmationService],
})
export class BulkAddDevicesComponent implements OnInit {
  constructor(
    private service: MedicalDeviceService,
    private loaderService: LoaderService,
    private menuService: MenuService,
    private messageService: MessageService,
    private confirmService: ConfirmationService,
    private router: Router
  ) { }

  inventoryList: InventoryItem[] = [];
  orgList: GroupVo[] = [];
  selectedOrg: any;

  deviceTypeList: any[] = [];
  selectedDeviceType: any;

  makeList: any[] = [];
  selectedMake: any;
  filtertedMakeList: any[] = [];

  modelList: any[] = [];
  selectedModel: any;
  filtertedModelList: any[] = [];

  deviceName: string = '';
  quantity: number = 0;

  loader: boolean = false;

  @Output() close: EventEmitter<any> = new EventEmitter<any>();
   @Output() stopLoader: EventEmitter<any> = new EventEmitter<any>();

  ngOnInit() {
    this.menuService.changeMenu('Medical Devices');
    this.reset();
    this.loadInventory();
  }

  loadInventory() {
    this.service.getInventoryList().subscribe((res) => {
      if (res?.success) {
        console.log(res?.inventory);
        this.inventoryList = res?.inventory;
        this.orgList = res?.groupVO;
        for (const item of res?.inventory) {
          if (!this.deviceTypeList.includes(item.deviceType)) {
            this.deviceTypeList.push(item.deviceType);
          }
        }
      }
    });
  }

  onDeviceTypeChange(deviceType: string) {
    this.selectedMake = '';
    this.makeList = [];
    this.filtertedMakeList = [];
    this.selectedMake = '';
    this.selectedModel = '';
    this.selectedOrg = '';
    this.deviceName = '';

    for (let item of this.inventoryList) {
      if (item.deviceType === deviceType) {
        if (!this.makeList.includes(item.make)) {
          this.makeList.push(item.make);
        }
      }
    }

    for (var item of this.inventoryList) {
      if (item.deviceType == deviceType) {
        this.filtertedMakeList.push(item);
      }
    }
  }

  onMakeChange(make: string) {
    this.filtertedModelList = [];
    this.selectedModel = '';
    for (var item of this.filtertedMakeList) {
      if (item.make == make) {
        this.filtertedModelList.push({
          id: item.inventoryId,
          label: item.model,
        });
      }
    }
  }

  onModelChange(model: any) {
    for (var item of this.filtertedMakeList) {
      if (item.inventoryId == model?.id) {
        this.deviceName = item.deviceName;
      }
    }
  }

  bulkAddDevice(event?: Event) {
    if (
      this.selectedModel === undefined ||
      this.selectedModel === '' ||
      this.selectedModel?.id === 0
    ) {
      this.generateErrorMessage('Please select all fields');
      this.stopLoader.emit();
      return;
    }

    if (
      this.selectedOrg === undefined ||
      this.selectedOrg === null ||
      this.selectedOrg === ''
    ) {
      this.generateErrorMessage('Please select org/clinic');
       this.stopLoader.emit();
      return;
    }

    if (
      this.quantity === undefined ||
      this.quantity === null ||
      this.quantity === 0
    ) {
      this.generateErrorMessage('Please add quantity');
       this.stopLoader.emit();
      return;
    }
    let payload = {
      inventoryId: this.selectedModel?.id,
      orgId: this.selectedOrg,
      inventoryQuantity: this.quantity,
    };

    this.loader = true;
    this.service.bulkAddDevice(payload).subscribe((res) => {
      this.loader = false;
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: res?.messages[0],
        });
        this.reset();
        setTimeout(() => {
          this.close.emit()
        }, 1000)

      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: res?.messages[0],
        });
        setTimeout(() => {
          this.close.emit()
        }, 1000)
      }
    });
  }

  generateErrorMessage(text: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: text,
      life: 3000,
    });
  }

  reset() {
    this.selectedMake = '';
    this.makeList = [];
    this.filtertedMakeList = [];
    this.selectedMake = '';
    this.selectedModel = '';
    this.selectedOrg = '';
    this.deviceName = '';
    this.quantity = 0;
    this.selectedDeviceType = '';
  }
}
