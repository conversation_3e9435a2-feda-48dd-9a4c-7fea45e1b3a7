import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnInit,
  ViewChild,
  Output,

} from '@angular/core';
import { FormsModule } from '@angular/forms';
import {
  FullCalendarComponent,
  FullCalendarModule,
} from '@fullcalendar/angular';
import { CalendarOptions, EventClickArg } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin, { DateClickArg } from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import timeGridPlugin from '@fullcalendar/timegrid';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { MinimalInfo } from '../../../api/patientTask';
import { LoaderService } from '../../../loader/loader/loader.service';
import { WatchrxCalendarService } from './service/watchrx-calendar.service';
import { InputSwitchModule } from 'primeng/inputswitch';

@Component({
  standalone: true,
  selector: 'app-watchrx-calendar',
  templateUrl: './watchrx-calendar.component.html',
  styleUrls: ['./watchrx-calendar.component.css'],
  imports: [
    CommonModule,
    FullCalendarModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    FormsModule,
    DropdownModule,
    CalendarModule,
    ToastModule,
    DialogModule,
    AutoCompleteModule,
    InputSwitchModule
  ],
  providers: [MessageService, ConfirmationService],
})
export class WatchrxCalendarComponent implements OnInit, AfterViewInit {
  @ViewChild('calendar') calendarComponent!: FullCalendarComponent;

  @Input() patientId: number = 0;
  @Input() usage: string = '';
  @Input() fromDashboard: boolean = false;
  calendarOptions!: CalendarOptions;
  calendarNeedsUpdate: boolean = false;

  events: any[] = [];
  openAddTaskModal: boolean = false;
  patientList: MinimalInfo[] | undefined;
  filteredPatients: any[] = [];
  selectedPatient: any | undefined;

  patientName: string = '';
  taskId: string = '0';
  taskTitle: string = '';
  taskPriority: string = 'Low';
  taskDescription: string = '';
  taskStartDate: Date = new Date();
  taskStartTime: Date = new Date();
  taskEndDate: Date = new Date();
  taskEndTime: Date = new Date();
  browserTimezone: string | undefined;
  isEdit: boolean = false;

  emailEnabled: boolean = false;
  @Output() closemodel = new EventEmitter()
  constructor(
    private taskService: WatchrxCalendarService,
    private messageService: MessageService,
    private loaderService: LoaderService
  ) { }

  ngOnInit(): void {
    this.browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    this.calendarOptions = {
      plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, listPlugin],
      headerToolbar: {
        right: 'prev,next',
        center: 'title',
        left: 'dayGridMonth,timeGridWeek,timeGridDay,listMonth',
      },
      buttonText: {
        today: 'Today',
        month: 'Month',
        timeGridWeek: 'Week',
        day: 'Day',
        listMonth: 'Task List',
      },
      themeSystem: 'bootstrap5',
      timeZone: 'local',
      nowIndicator: true,
      initialView: 'dayGridMonth',
      initialEvents: [],
      weekends: true,
      editable: true,
      selectable: true,
      selectMirror: true,
      dayMaxEvents: true,
      showNonCurrentDates: false,
      forceEventDuration: true,
      aspectRatio: 1.8,
      lazyFetching: true,
      handleWindowResize: true,
      windowResizeDelay: 3000,
      events: this.events,
      dateClick: this.handleDateClick.bind(this),
      eventClick: this.handleEventClick.bind(this),
      eventContent: this.renderEventContent.bind(this),
    };
    this.getEventsList();
    console.log('Patient ID:', this.patientId);
    if (this.patientId === 0 && this.usage === 'all') {
      this.getPatientList();
    }
    this.getNotification()
  }


  refreshCalendar() {
    setTimeout(() => {
      this.calendarComponent.getApi().changeView('dayGridMonth');
      if (this.calendarComponent) {
        this.calendarComponent.getApi().render();
      }
    }, 100)

  }
  renderEventContent(arg: any) {
    const event = arg.event;
    const startTime = moment(event.start).format('h:mma');
    let color = 'blue';
    if (event.extendedProps.priority === 'High') {
      color = 'tomato';
    } else if (event.extendedProps.priority === 'Low') {
      color = 'green';
    } else if (event.extendedProps.priority === 'Medium') {
      color = 'orange';
    } else if (event.extendedProps.priority === 'Critical') {
      color = 'red';
    }

    return {
      html: `<div style="background-color: ${color}; color: white; padding: 2px; display: flex; align-items: center;">
               <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; 
               border-radius: 50%; margin-right: 5px;"></span>
               ${startTime} ${event.title}
             </div>`,
    };
  }

  ngAfterViewInit(): void {
    const calendarApi = this.calendarComponent.getApi();
    if (calendarApi) {
      calendarApi.render();
      this.calendarComponent.getApi().changeView('timeGridWeek');
    }
    this.refreshCalendar()
  }

  handleWeekendsToggle() {
    const { calendarOptions } = this;
    calendarOptions.weekends = !calendarOptions.weekends;
  }

  handleDateClick(clickInfo: DateClickArg): void {
    this.resetData();
    this.isEdit = false;
    const currentDate = moment();
    const next15Minute = Math.ceil(currentDate.minute() / 15) * 15;
    const selectedDate = moment(clickInfo.date).set({
      hour: currentDate.hour(),
      minute: next15Minute,
      second: 0,
    });

    if (selectedDate.isBefore(currentDate, 'day')) {
      return;
    }
    this.taskStartDate = selectedDate.toDate();
    this.taskStartTime = selectedDate.toDate();
    const endDate = selectedDate.add(60, 'minutes');
    this.taskEndDate = endDate.toDate();
    this.taskEndTime = endDate.toDate();
    this.openAddTaskModal = true;
  }

  handleEventClick(data: EventClickArg) {
    this.resetData();
    this.isEdit = true;
    const event = data.event;
    this.taskId = event?._def?.publicId;

    let startDate = moment(event.start, 'MM-DD-YYYY').toDate();
    let startTime = moment(event.start, 'hh:mm A').toDate();

    let endDate = moment(event.end, 'MM-DD-YYYY').toDate();
    let endTime = moment(event.end, 'hh:mm A').toDate();

    let title = data?.event?._def?.title;
    let description = data?.event?.extendedProps?.['description'];
    let patientId = data?.event?.extendedProps?.['patientId'];
    let patientName = data?.event?.extendedProps?.['patinetName'];
    let priority = data?.event?.extendedProps?.['priority'];

    this.patientName = patientName;
    this.taskTitle = title;
    this.taskDescription = description;
    this.taskPriority = priority;
    this.patientId = patientId;

    this.taskStartDate = startDate;
    this.taskStartTime = startTime;

    this.taskEndDate = endDate;
    this.taskEndTime = endTime;

    this.openAddTaskModal = true;
  }

  getPatientList() {
    this.taskService.getPatientList().subscribe((res) => {
      this.loaderService.hide();
      if (res?.success) {
        this.patientList = res.patientMinimalVOList;
      }
    });
  }

  getEventsList() {
    this.taskService.getTaskList().subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        if (this.calendarComponent) {
          const calendarApi = this.calendarComponent.getApi();
          if (calendarApi) {
            calendarApi.removeAllEvents();
            calendarApi.addEventSource(res);
            calendarApi.render();
          }
        }
      }
    });
  }

  addTask() {
    if (this.taskTitle === undefined || this.taskTitle === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please add title',
        life: 3000,
      });
      return;
    }

    if (this.taskDescription === undefined || this.taskDescription === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please add description',
        life: 3000,
      });
      return;
    }
    let startDateTime =
      moment(this.taskStartDate).format('MM-DD-YYYY') +
      ' ' +
      moment(this.taskStartTime).format('hh:mm A');

    let endDateTime =
      moment(this.taskEndDate).format('MM-DD-YYYY') +
      ' ' +
      moment(this.taskEndTime).format('hh:mm A');

    let st = moment(startDateTime, 'MM-DD-YYYY hh:mm A').toDate();
    let ed = moment(endDateTime, 'MM-DD-YYYY hh:mm A').toDate();
    let diffInMinutes = moment(ed).diff(moment(st), 'minutes');
    console.log('diffInMinutes', diffInMinutes);
    if (diffInMinutes < 15) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail:
          'Warning: End date should be at least 15 minutes greater than the start date',
        life: 3000,
      });
      return;
    }
    if (this.patientId === 0 && this.usage === 'all') {
      if (this.selectedPatient === undefined) {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Please select a patient',
          life: 3000,
        });
        return;
      } else {
        this.patientId = this.selectedPatient?.patientId;
      }
    }
    let payload = {
      title: this.taskTitle,
      description: this.taskDescription,
      start: startDateTime,
      end: endDateTime,
      patientId: this.patientId,
      className: '',
      priority: this.taskPriority,
      timezone: this.browserTimezone,
    };
    this.loaderService.show();
    this.taskService.addTask(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Task added successfully.',
          life: 3000,
        });
        this.closeModal();
        this.getEventsList();
        setTimeout(() => { this.closemodel.emit() }, 500)

      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to create Task',
          life: 3000,
        });
      }

    });
  }

  editTask() {
    let startDateTime =
      moment(this.taskStartDate).format('MM-DD-YYYY') +
      ' ' +
      moment(this.taskStartTime).format('hh:mm A');
    let endDateTime =
      moment(this.taskEndDate).format('MM-DD-YYYY') +
      ' ' +
      moment(this.taskEndTime).format('hh:mm A');

    let st = moment(startDateTime, 'MM-DD-YYYY hh:mm A').toDate();
    let ed = moment(endDateTime, 'MM-DD-YYYY hh:mm A').toDate();

    let diffInMinutes = moment(ed).diff(moment(st), 'minutes');
    if (diffInMinutes < 15) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail:
          'Warning: End date should be at least 15 minutes greater than the start date',
        life: 3000,
      });
      return;
    }

    let payload = {
      id: this.taskId,
      start: startDateTime,
      end: endDateTime,
      className: '',
      priority: this.taskPriority,
      timezone: this.browserTimezone,
    };

    this.loaderService.show();
    this.taskService.updateTask(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Task updated successfully.',
          life: 3000,
        });
        this.closeModal();
        this.getEventsList();
        setTimeout(() => { this.closemodel.emit() }, 500)
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to update Task',
          life: 3000,
        });
      }
    });
  }

  deleteTask() {
    this.loaderService.show();
    this.taskService.deleteTask(this.taskId).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {

        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Task deleted successfully.',
          life: 3000,
        });
        this.closeModal();
        this.getEventsList();
        setTimeout(() => { this.closemodel.emit() }, 500)
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to delete Task',
          life: 3000,
        });
      }

    });
  }

  filterPatient(event: any) {
    const query = event.query.toLowerCase();
    if (this.patientList) {
      this.filteredPatients = this.patientList.filter((p) =>
        p.patientName?.toLowerCase().includes(query)
      );
    } else if (this.filteredPatients?.length === 0 && this.patientList) {
      this.filteredPatients = this.patientList;
    }
  }

  resetData() {
    this.taskTitle = '';
    this.taskPriority = 'Low';
    this.taskDescription = '';
    this.taskStartDate = new Date();
    this.taskStartTime = new Date();
    this.taskEndDate = new Date();
    this.taskEndTime = new Date();
    this.selectedPatient = '';
  }

  closeModal() {
    this.closemodel.emit();
    console.log('Modal closed');
    this.openAddTaskModal = false;
    if (this.usage === 'all') {
      this.patientId = 0;
    }
  }

  getNotification() {

    this.taskService.getEmailNotification().subscribe((res) => {
      this.emailEnabled = res.status || false
    });
  }
  updateEmail() {
    this.loaderService.show();
    this.taskService.sendEmailNotification(this.emailEnabled).subscribe((res) => {
      this.messageService.add({
        severity: 'success',
        summary: 'Confirmed',
        detail: 'Notification settings updated successfully.',
        life: 3000,
      });
      this.loaderService.hide();
    }, (err) => {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Failed to update notification settings.',
        life: 3000,
      });
      this.loaderService.hide();
    });
  }
}
