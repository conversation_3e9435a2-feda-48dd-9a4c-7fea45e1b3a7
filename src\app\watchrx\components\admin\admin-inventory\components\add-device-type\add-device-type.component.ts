import { Component, OnInit } from '@angular/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DeviceType } from '../../../../../api/inventory';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { InventoryService } from '../../service/inventory.service';

@Component({
  selector: 'app-add-device-type',
  templateUrl: './add-device-type.component.html',
  styleUrls: ['./add-device-type.component.css'],
})
export class AddDeviceTypeComponent implements OnInit {
  constructor(
    private loaderService: LoaderService,
    private messageService: MessageService,
    private confirmService: ConfirmationService,
    private inventoryService: InventoryService
  ) {}

  deviceTypes: DeviceType[] = [];
  totalCount: number = 0;
  itemsPerPage: number = 10;

  editDeviceType: string = '';
  editDeviceModal: boolean = false;
  editDeviceTypeId: number = 0;

  ngOnInit() {
    this.getDeviceTypes();
  }

  getDeviceTypes() {
    this.loaderService.show();
    this.inventoryService.getDeviceTypes().subscribe((res) => {
      this.loaderService.hide();
      if (res?.success) {
        this.deviceTypes = res.eList;
        this.totalCount = this.deviceTypes.length;
        this.itemsPerPage = this.deviceTypes.length;
      }
    });
  }

  openEditDeviceType(item: DeviceType) {
    this.editDeviceModal = true;
    this.editDeviceType = item.typeName;
    this.editDeviceTypeId = item.deviceTypeId;
  }

  openAddDeviceType() {
    this.editDeviceModal = true;
    this.editDeviceType = '';
    this.editDeviceTypeId = 0;
  }

  onEditDeviceType() {
    if (
      this.editDeviceType === null ||
      this.editDeviceType == undefined ||
      this.editDeviceType === ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Device type is required',
      });
      return;
    }
    let payload = {
      deviceTypeId: this.editDeviceTypeId,
      typeName: this.editDeviceType,
    };
    this.loaderService.show();
    this.inventoryService.addUpdateDeviceType(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Device type added/updated successfully',
        });
        this.editDeviceType = '';
        this.editDeviceTypeId = 0;
        this.getDeviceTypes();
        this.editDeviceModal = false;
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to add/update to device type',
        });
      }
    });
  }

  onDeleteDeviceType(id: number) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.inventoryService
          .deleteDeviceType({ deviceTypeId: id })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.getDeviceTypes();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          });
      },
      reject: () => {},
    });
  }
}
