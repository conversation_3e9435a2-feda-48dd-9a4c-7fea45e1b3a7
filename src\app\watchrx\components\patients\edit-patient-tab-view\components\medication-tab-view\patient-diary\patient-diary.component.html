<p-table
  [value]="diaryResponse"
  responsiveLayout="scroll"
  styleClass="p-datatable-gridlines p-datatable-striped"
>
  <ng-template pTemplate="header">
    <tr>
      <th>Date</th>
      <th>Medication Time</th>
      <th>Medication</th>
      <th>Meal Time</th>
    </tr>
  </ng-template>
  <ng-template pTemplate="body" let-d>
    <tr>
      <td>{{ d.createdDate }}</td>
      <td>{{ d.medicationTime }}</td>
      <td>{{ d.medication }}</td>
      <td>{{ d.mealTime }}</td>
    </tr>
  </ng-template>
</p-table>
