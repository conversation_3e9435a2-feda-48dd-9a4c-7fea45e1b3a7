import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import { PatientTask } from '../../../../../../../api/patientTask';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class PatientTaskService {
  constructor(private http: HttpClient) {}

  getTaskList(): Observable<PatientTask[]> {
    return this.http.get<PatientTask[]>(
      environment.BASE_URL + Constants.GET_TASK_LIST
    );
  }

  addTask(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_TASK,
      data
    );
  }

  updateTask(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_TASK,
      data
    );
  }

  deleteTask(data: any): Observable<GenericResponse> {
    return this.http.delete<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_TASK + data
    );
  }
}
