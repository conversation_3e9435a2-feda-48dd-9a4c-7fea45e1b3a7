<p-toast />
<div class="p-fluid p-formgrid grid">
  <div style="text-align: left" class="flex flex-row col-12">
    <div class="field col-12 md:col-6 flex-row">
      <!-- <label htmlfor="state">Search By Org/DeviceId</label> -->
      <div class="flex flex-1 flex-row">
        <input pinputtext="" id="org" type="text" placeholder="Search By Org / IMEI"
          class="p-inputtext p-component p-element" (input)="onInput($event)" [(ngModel)]="searchText" />
        <p-button class="ml-2 " label="Submit" severity="primary" (onClick)="onInput($event)"></p-button>
      </div>
    </div>
    <!-- <div class="col-12 md:col-2 flex align-items-center justify-content-end">
      <p-button class="ml-2 " icon="pi pi-download" label="Download List" severity="primary" [outlined]="true"
      (onClick)="exportToExcel('xlsx')"></p-button>
    </div> -->
  </div>
</div>
<div>
  <p-table [value]="response" [totalRecords]="totalDeviceCount" responsiveLayout="scroll"
    styleClass="p-datatable-gridlines p-datatable-striped" [paginator]="true" [rows]="rows"
    [rowsPerPageOptions]="searchText.length==0?[15,25, 50,75, 100]:[rows]" [responsiveLayout]="'scroll'" [loading]="loadingdevices"
    (onLazyLoad)="loadDevices($event)" [lazy]="true"  [first]="first"
    (onPage)="onPageChange($event)">
    <ng-template pTemplate="header">
      <tr>
        <th>Device Type</th>
        <th>Device Name</th>
        <th>Make</th>
        <th>Model</th>
        <th>Measurement</th>
        <th>Organization/Clinic</th>
        <th>Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-device>
      <tr>
        <td>{{ device?.sourceInventoryForWatchPurchase?.deviceType }}</td>
        <td>{{ device?.sourceInventoryForWatchPurchase?.deviceName }}</td>
        <td>{{ device?.sourceInventoryForWatchPurchase?.make }}</td>
        <td>{{ device?.sourceInventoryForWatchPurchase?.model }}</td>
        <td>
          <ul>
            <li *ngFor="
                let feature of device?.sourceInventoryForWatchPurchase
                  ?.deviceMeasuresArray
              ">
              {{ feature }}
            </li>
          </ul>
        </td>
        <td>{{ device?.groupVo?.groupName }}</td>
        <td>
          <div class="flex">
            <p-button [label]="'Allocate'" severity="success" [outlined]="true" (onClick)="openModal(device)"
              class="p-button-rounded p-button-success" />
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-sidebar header="Allocate Device" [(visible)]="showEdit"  [modal]="true" [blockScroll]="true" [baseZIndex]="10000" [style]="{ width: '40rem' }" position="right"
  class="dashboardSidebar" (onShow)="onOpen(patientRef, deviceRef)">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <span class="xl-font">Allocate Device &nbsp;</span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid mt-2">
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Org/Clinic Name</label>
        <input pinputtext="" id="org" type="text" placeholder="Organization/Clinic"
          class="p-inputtext p-component p-element" [(ngModel)]="orgName" [disabled]="true" />
      </div>
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Make</label>
        <input pinputtext="" id="make" type="text" placeholder="Make" class="p-inputtext p-component p-element"
          [(ngModel)]="make" [disabled]="true" />
      </div>
    </div>
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Model</label>
        <input pinputtext="" id="model" type="text" placeholder="Model" class="p-inputtext p-component p-element"
          [(ngModel)]="model" [disabled]="true" />
      </div>
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Choose Patient</label>
        <p-autoComplete [suggestions]="filteredPatients" (completeMethod)="filterPatient($event)" field="patientName"
          [(ngModel)]="selectedPatient" placeholder="Select a Patient" appendTo="body" [dropdown]="true"
          emptyMessage="No patient found" [showEmptyMessage]="true" [required]="true" #patientRef="ngModel"></p-autoComplete>
      </div>
    </div>
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Device Unique Id</label>
        <input pinputtext="" id="deviceId" type="text" placeholder="Device Unique Id"
          class="p-inputtext p-component p-element" [(ngModel)]="deviceId" required="true" #deviceRef="ngModel" />
      </div>
      <div class="field col-12 md:col-6">
        <label htmlfor="state">Phone Number</label>
        <input mask="**********" [(ngModel)]="phoneNumber" placeholder="" [required]="true"
          class="p-inputtext p-component p-element" />
        <p>
          <span><small class="red">Note:(Example:+18001232323)</small></span>
        </p>
      </div>
    </div>
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-12">
        <label htmlfor="state">Select Features</label>
        <!-- <p-autoComplete [suggestions]="filteredFeatures" (completeMethod)="filterFeatures($event)" field="" 
          [(ngModel)]="selectedFeatures" placeholder="Select a Feature" appendTo="body" [dropdown]="true" [multiple]="true"
          emptyMessage="No patient found" [showEmptyMessage]="true" [required]="true">
         
        </p-autoComplete> -->
        <div class="flex flex-wrap"><ng-container *ngFor="let item of features">
            <p-checkbox [binary]="true" [label]="item.name" [value]="item" [(ngModel)]="item.selected" class="mr-3" >
            </p-checkbox>
          </ng-container></div>

      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="showEdit = false" />
      <p-button label="Save" severity="primary" (click)="allocateDevie()" />
    </div>
  </ng-template>
</p-sidebar>