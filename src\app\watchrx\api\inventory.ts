export interface InventoryResponse {
  success: boolean;
  responseCode: number;
  messages: string[];
  inventory: InventoryItem[];
}

export interface InventoryItem {
  inventoryId: number;
  make: string;
  model: string;
  color: string;
  sellingPrice: string;
  quantity: number;
  imgUrl: string;
  specUrl: string;
  forsale: boolean;
  listPrice: string;
  deviceMeasures: string;
  deviceMeasuresArray: string[];
  deviceType: string;
  deviceName: string;
}

export interface DeviceTypeResponse {
  success: boolean;
  messages: string[];
  eList: DeviceType[];
}

export interface DeviceType {
  deviceTypeId: number;
  typeName: string;
}
