import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { ApiResponse } from '../../../../../../../api/documents';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class DocumentationService {
  constructor(private http: HttpClient) {}
  getDocumentation(details: any): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.GET_DOCUMENTATION + details
    );
  }

  addDocumentation(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_DOCUMENTATION,
      details
    );
  }

  deleteDocumentation(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_DOCUMENTATION,
      details
    );
  }
}
