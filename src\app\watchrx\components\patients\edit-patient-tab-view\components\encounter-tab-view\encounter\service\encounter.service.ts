import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { AddPatientResp } from '../../../../../../../api/addPatient';
import {
  AddEncounter,
  ApiResponse,
} from '../../../../../../../api/patientEncounter';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class EncounterService {
  constructor(private http: HttpClient) {}

  getEncounters(details: any): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.GET_ENCOUNTERS + details
    );
  }

  addEncounter(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ADD_ENCOUNTER,
      details
    );
  }

  deleteEncounter(details: any): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.DELETE_ENCOUNTER,
      details
    );
  }

  getEncountersByReason(details: any): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.GET_ECOUNTERS_BY_REASON + details
    );
  }

  downloadEncounterReport(details: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.DOWNLOAD_REPORT,
      details
    );
  }
  getReasonList(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + Constants.GET_REASON_LIST
    );  
  }


    getPatientPrograms(details:number):Observable<any>
    {
      return this.http.get<any>(
        environment.BASE_URL + Constants.PATIENT_ENROLLED_PROGRAM + details
      );
    }
  

  getPendingEncounters(details: any): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.GET_PENDING_ENCOUNTERS + details
    );
  }

  addPendingEncounter(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ADD_ENCOUNTER,
      details
    );
  }

    savePendingEncounter(details: AddEncounter): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.ADD_ENCOUNTER,
      details
    );
  }
  deletePendingEncounter(details: any): Observable<AddPatientResp> {
    return this.http.post<AddPatientResp>(
      environment.BASE_URL + Constants.DELETE_PENDING_ENCOUNTER,
      details
    );
  }
}
