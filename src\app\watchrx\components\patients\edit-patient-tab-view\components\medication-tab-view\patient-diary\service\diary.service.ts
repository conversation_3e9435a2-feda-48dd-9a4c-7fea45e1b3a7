import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { PatientDiary } from '../../../../../../../api/patientDiary';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class DiaryService {
  constructor(private http: HttpClient) {}

  getDiaries(details: any): Observable<PatientDiary> {
    return this.http.post<PatientDiary>(
      environment.BASE_URL + Constants.GET_DIARY,
      details
    );
  }
}
