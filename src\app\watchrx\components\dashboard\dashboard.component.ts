import { DatePipe, formatDate } from '@angular/common';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Chart } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import moment from 'moment';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { Subscription } from 'rxjs';
import {
  AlertList,
  CalendarTask,
  PatientCount,
  PatientGraph,
  PatientsInfo,
} from '../../api/dashboard';
import { AddEncounter, Encounter, Program } from '../../api/patientEncounter';
import { LoaderService } from '../../loader/loader/loader.service';
import { MenuService } from '../../service/menu.service';
import { EncounterComponent } from './component/encounter/encounter.component';
import { ScheduleTextMessageFormComponent } from '../shared/schedule-text-message-form/schedule-text-message-form.component';
import { DashboardService } from './service/dashboard.service';
import { DateService } from './service/date-service.service';
import { WatchrxCalendarComponent } from '../shared/watchrx-calendar/watchrx-calendar.component';

@Component({
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
})
export class DashboardComponent implements OnInit, OnDestroy {
  chartData: any;
  rpmChartOptions: any;

  ccmChartData: any;
  ccmChartOptions: any;

  pcmChartData: any;
  pcmChartOptions: any;

  dataCollectionChartData: any;
  dataChartOptions: any;

  chartDataSet: number[] = [];
  chartTotalNoOfPatients: number = 0;
  chartOptions: any;
  subscription!: Subscription;
  datePipe: DatePipe = new DatePipe('en-US');
  public patientCount!: PatientCount;
  alertCountData!: any;
  patientBilling: PatientsInfo[] = [];
  criticalAlertData!: AlertList[];

  taskVO!: CalendarTask[];
  totalTasks!: number;
  patientInfo!: PatientsInfo[];
  loadingTask: boolean = false;

  startDate: Date = this.dateService.getStartDateOfMonth();
  endDate: Date = this.dateService.getCurrentDate();

  totalBillingPatCount!: number;
  loadingBillingPat: boolean = true;
  selectedFilter: any;
  fileDownloadLoading: boolean = false;

  filterValues: any[] = [
    {
      label: 'All',
      value: 'all',
    },
    {
      label: 'RPM(0mins-20mins)',
      value: 'rpm20',
    },
    {
      label: 'RPM(21mins-40mins)',
      value: 'rpm40',
    },
    {
      label: 'RPM(41mins-60mins)',
      value: 'rpm60',
    },
    {
      label: 'CCM(0mins-20mins)',
      value: 'ccm20',
    },
    {
      label: 'CCM(21mins-40mins)',
      value: 'ccm40',
    },
    {
      label: 'CCM(41mins-60mins)',
      value: 'ccm60',
    },
    {
      label: 'PCM(0mins-30mins)',
      value: 'pcm30',
    },
    {
      label: 'PCM(31mins-60mins)',
      value: 'pcm60',
    },
    {
      label: 'RPM Patient List',
      value: 'daysMeasure',
    },
  ];

  totalCriticalAlertDataCount!: number;
  loadingCriticalAlertData: boolean = true;

  totalRecentlyContactedPatientCount!: number;
  loadingRecentlyContactedPatient: boolean = true;
  recentPatientInfo!: PatientsInfo[];
  rcStartDate: Date = this.dateService.getYesterdayDate();
  rcEndDate: Date = this.dateService.getCurrentDate();

  openCriticalAlertModal: boolean = false;
  activeIndex: number = 0;
  position: string = 'center';
  criticalAlert: AlertList | undefined;
  encounterList: Encounter[] = [];
  programList: Program[] = [];
  encounterLoader: boolean = false;

  @ViewChild(EncounterComponent) encounterForm!: EncounterComponent;
  @ViewChild(WatchrxCalendarComponent) WatchrxCalendarComponent!: WatchrxCalendarComponent;
  @ViewChild(ScheduleTextMessageFormComponent)
  textMessageForm!: ScheduleTextMessageFormComponent;

  alertTypes = [
    {
      label: 'Critical',
      key: 'Critical',
      icon: 'assets/watchrx/svg/critical_alert.svg',
    },
    { label: 'Alarm', key: 'Alarm', icon: 'assets/watchrx/svg/alarm.svg' },
    {
      label: 'Warnings',
      key: 'Warning',
      icon: 'assets/watchrx/svg/warnings.svg',
    },
    { label: 'Info', key: 'Info', icon: 'assets/watchrx/svg/info.svg' },
  ];

  date: Date = new Date();
  consentVsNonConsented: number = 0;
  showPreviousEncounters: boolean = true;
  growthCount: any;
  openVoiceVideoDlg: boolean = false;
  openVideoDlg:boolean=false;
  patientGraphRes: PatientGraph | undefined;
  userRole: any = null;

  @ViewChild('dt') table: Table | undefined;
  @ViewChild('dt1') table1: Table | undefined;
  selectedPatienListDropdown: string = '';
  patientRows:number=15;
  reasonCodeMap = {
    "Critical Alert": "EC_001",
    "Data Review": "EC_002",
    "Phone Call": "EC_003",
    "RPM Review": "EC_004",
    "CCM Review": "EC_005",
    "PCM Review": "EC_006",
    "EMR/Chart review": "EC_007",
    "BPM Device Documentation/Training": "EC_008",
    "Glucometer Device education Template": "EC_009",
    "Weigh scale device education template for CHF": "EC_010",
    "Weigh scale device education template for ESRD, volume overload": "EC_011",
    "Introduction to RPM": "EC_012",
    "Introduction to CCM": "EC_013",
    "Introduction to PCM": "EC_014",
    "Introduction to RTM": "EC_015",
    "Introduction to PCM & RPM": "EC_016",
    "Introduction to PCM & RTM": "EC_017",
    "Introduction to CCM & RPM": "EC_018",
    "Other": "EC_019",
    "Text Message": "EC_020",
    "Appointment": "EC_021",
    "Home Visits": "EC_022",

};
  constructor(
    private dashboardService: DashboardService,
    public menuService: MenuService,
    private dateService: DateService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private router: Router,
    private loaderService: LoaderService,

  ) { }

  onDateChange(selectedDate: Date) {
    const startDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth(),
      1
    );
    const endDate = new Date(
      selectedDate.getFullYear(),
      selectedDate.getMonth() + 1,
      0
    );
    this.initChart(startDate, endDate);
    this.dashboardCount(startDate, endDate);
  }

  ngOnInit() {
    Chart.register(ChartDataLabels);
    Chart.register(this.backgroundPlugin);
    this.startDate = this.dateService.getStartDateOfMonth();
    this.endDate = this.dateService.getCurrentDate();
    this.menuService.changeMenu('Dashboard');
    this.dashboardCount(this.startDate, this.endDate);
    this.initChart(this.startDate, this.endDate);
    this.alertCount();
    this.getReasonList();
    //this.todaysTask();

    this.selectedFilter = this.filterValues[0];
    this.userRole = JSON.parse(localStorage.getItem('user')!);
  }

  loadActivePatientsList($event?: LazyLoadEvent | TableLazyLoadEvent) {

    this.loadingBillingPat = true;
    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    let sortField = $event?.sortField || 'pcm';
    let sortOrder = $event?.sortOrder == 1 ? 'ASC' : 'DESC';
    let url = pageNo + '/' + pageSize + '/' + fromToDetails + '/sort/' + sortField + '/' + sortOrder;
    if (this.selectedPatienListDropdown != "") {
      url = pageNo + '/' + pageSize + '/' + fromToDetails + '/' + this.selectedPatienListDropdown + '/all/' + sortOrder;
    }

    this.dashboardService.setPatientBilling(url).subscribe((res) => {
      if (res.success) {
        this.patientBilling = res.patientsInfo!;
        this.totalBillingPatCount = res.count!;
        this.loadingBillingPat = false;
      }
    });
  }

  onFilterLoadPatients() {
    if(this.searchTerm.length!=0)
    {
      this.searchTerm="";
      this.patientRows=15;
    }
    this.loadingBillingPat = true;
    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let pageNo = 0;
    let pageSize = this.patientRows;
    let sortField = 'pcm';
    let sortOrder = 'DESC';
    let url =
      pageNo +
      '/' +
      pageSize +
      '/' +
      fromToDetails +
      '/sort/' +
      sortField +
      '/' +
      sortOrder;
    if (this.selectedPatienListDropdown != "") {
      url = pageNo + '/' + pageSize + '/' + fromToDetails + '/' + this.selectedPatienListDropdown + '/all/' + sortOrder;
    }
    this.dashboardService.setPatientBilling(url).subscribe((res) => {
      if (res.success) {
        this.patientBilling = res.patientsInfo!;
        this.totalBillingPatCount = res.count!;
        this.loadingBillingPat = false;
      }
    });
    if (this.table) {
      this.table.first = 0;
    }
  }

  onFilterDropdownChange($event: any) {
    this.patientRows=15;
    let val = $event.value?.value;
    this.selectedPatienListDropdown = $event.value.value;
    this.onFilterGraphOrDropdown(val);
    if (this.table) {
      this.table.first = 0;
    }
  }

  onFilterGraphOrDropdown(val: string) {
    this.loadingBillingPat = true;
    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let pageNo = 0;
    let pageSize = 15;
    let sortOrder = 'DESC';
    let url =
      pageNo +
      '/' +
      pageSize +
      '/' +
      fromToDetails +
      '/' +
      val +
      '/all/' +
      sortOrder;
    this.loaderService.show();
    this.dashboardService.setPatientBilling(url).subscribe((res) => {
      if (res.success) {
        this.patientBilling = res.patientsInfo!;
        this.totalBillingPatCount = res.count!;
        this.loadingBillingPat = false;
        this.loaderService.hide();
      }
    });
  }

  searchTerm: string = '';

  onSearch(term: string): void {
    if (term != null && term != undefined && term.length > 2) {
      let fromToDetails =
        formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
        '/' +
        formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');

      let url = fromToDetails + '/' + term;
      this.dashboardService.setPatientBilling(url).subscribe((res) => {
        if (res.success) {
          this.patientBilling = res.patientsInfo!;
          this.totalBillingPatCount = res.count!;
          this.loadingBillingPat = false;
          this.patientRows=res.count!;
        }
      });
    } else {
      if (term != null && term != undefined && term.length == 0) {
        this.patientRows=15
        this.onFilterLoadPatients();
      }
    }
  }

  onDownloadStats() {
    this.fileDownloadLoading = true;
    const startDate = formatDate(this.startDate, 'yyyy-MM-dd', 'en-US');
    const endDate = formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    const fileName =
      'Stats_' +
      formatDate(this.startDate, 'yyyyMMdd', 'en-US') +
      '_' +
      formatDate(this.endDate, 'yyyyMMdd', 'en-US') +
      '.xlsx';
    this.dashboardService
      .downloadStatistics(startDate, endDate, fileName)
      .subscribe({
        next: (response) => {
          this.fileDownloadLoading = false;
          if (response.success) {
            console.log('File downloaded successfully');
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'File downloaded successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Error downloading file',
            });
          }
        },
        error: (error) => {
          this.fileDownloadLoading = false;
          console.error('Error downloading file', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Error downloading file',
          });
        },
      });
  }

  loadCriticalAlertsList($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loadingCriticalAlertData = true;
    let pageSize = $event?.rows || 25;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    let startDate: Date = this.dateService.getStartDateOfMonth();
    let endDate: Date = this.dateService.getCurrentDate();
    const startDate1 = formatDate(startDate, 'yyyy-MM-dd', 'en-US');
    const endDate1 = formatDate(endDate, 'yyyy-MM-dd', 'en-US');
    //let url = pageNo + '/' + pageSize+'/'+startDate1+'/'+endDate1;
    let url = pageNo + '/' + pageSize;

    this.dashboardService.setPatientCriticalAlert(url).subscribe((res) => {
      this.loadingCriticalAlertData = false;
      if (res.success) {
        this.criticalAlertData = res.eList!;
        this.totalCriticalAlertDataCount = res.count!;
        console.log('Critical Data:', this.criticalAlertData);
      }
    },err=>{
      this.criticalAlertData = [];
      this.loadingCriticalAlertData = false;
    });
  }

  loadRecentlyContactedPatients($event: LazyLoadEvent | TableLazyLoadEvent) {
    this.selectedFilter = this.filterValues[0];
    this.loadingRecentlyContactedPatient = true;
    let fromToDetails =
      formatDate(this.rcStartDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.rcEndDate, 'yyyy-MM-dd', 'en-US');
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;
    let url = pageNo + '/' + pageSize + '/' + fromToDetails;
    this.dashboardService.setLastCallPatients(url).subscribe((res) => {
      if (res.success) {
        this.recentPatientInfo = res.patientsInfo!;
        this.totalRecentlyContactedPatientCount = res.count!;
        this.loadingRecentlyContactedPatient = false;
      }
    });
  }

  onFilterLoadRecentlyContactedPatients() {
    this.loadingRecentlyContactedPatient = true;
    let fromToDetails =
      formatDate(this.rcStartDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.rcEndDate, 'yyyy-MM-dd', 'en-US');
    let pageNo = 0;
    let pageSize = 15;
    let url = pageNo + '/' + pageSize + '/' + fromToDetails;
    this.dashboardService.setLastCallPatients(url).subscribe((res) => {
      if (res.success) {
        this.recentPatientInfo = res.patientsInfo!;
        this.totalRecentlyContactedPatientCount = res.count!;
        this.loadingRecentlyContactedPatient = false;
        if (this.table1) {
          this.table1.first = 0;
        }
      }

    });

  }

  todaysTask(event?:any) {
    this.loadingTask = true;
    let todaydate = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
    this.dashboardService.setTodayCalendarTasks(todaydate!).subscribe((res) => {
      this.loadingTask = false;
      if (res) {
        this.taskVO = res;
        this.totalTasks = res?.length;
      }
    });
  }

  alertCount() {
    let startDate: Date = this.dateService.getStartDateOfMonth();
    let endDate: Date = this.dateService.getCurrentDate();
    const startDate1 = formatDate(startDate, 'yyyy-MM-dd', 'en-US');
    const endDate1 = formatDate(endDate, 'yyyy-MM-dd', 'en-US');
    //let formToDetails = startDate1+'/'+endDate1
    this.dashboardService.setAlertCount().subscribe((res) => {
      if (res.status) {
        this.alertCountData = res;
      }
    });
  }

  dashboardCount(startDate: Date, endDate: Date) {
    let fromToDetails =
      formatDate(startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(endDate, 'yyyy-MM-dd', 'en-US');
    this.dashboardService.setPatientCount(fromToDetails).subscribe((res) => {
      if (res.status) {
        this.patientCount = res;
        this.consentVsNonConsented =
          (res?.totalActivePatients / res?.totalPatients) * 100;
        this.growthCount = res.growth
      }
    });
  }
  initChart(startDate: Date, endDate: Date) {
    let fromToDetails =
      formatDate(startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(endDate, 'yyyy-MM-dd', 'en-US');
    this.dashboardService.setPatientGraph(fromToDetails).subscribe((res) => {
      if (res.status) {
        this.patientGraphRes = res
        this.chartTotalNoOfPatients = res.totalPatients!;
        this.chartData = {
          labels: ['< 20 min', '< 40 min', '< 60 min'],
          datasets: [
            {
              label: '',
              data: [res.rpm20Minless!, res.rpm40Minless!, res.rpm60Minless!],
              maxBarThickness: 30,
              fill: true,
              backgroundColor: [
                'rgba(250, 192, 50, 1)',
                'rgba(51, 108, 251, 1)',
                'rgba(239, 93, 168, 1)',
              ],
            },
          ],
        };

        this.ccmChartData = {
          labels: ['< 20 min', '< 40 min', '< 60 min'],
          datasets: [
            {
              label: '',
              data: [res.ccm20Minless!, res.ccm40Minless!, res.ccm60Minless!],
              maxBarThickness: 30,
              fill: true,
              backgroundColor: [
                'rgba(250, 192, 50, 1)',
                'rgba(51, 108, 251, 1)',
                'rgba(239, 93, 168, 1)',
              ],
            },
          ],
        };

        this.pcmChartData = {
          labels: ['< 30 min', '< 60 min'],
          datasets: [
            {
              label: '',
              data: [res.pcm30Minless!, res.pcm60Minless!],
              maxBarThickness: 30,
              fill: true,
              backgroundColor: [
                'rgba(250, 192, 50, 1)',
                'rgba(51, 108, 251, 1)',
                'rgba(239, 93, 168, 1)',
              ],
            },
          ],
        };

        this.dataCollectionChartData = {
          labels: ['< 16 days','>= 16 days'],
          datasets: [
            {
              label: '',
              data: [res.daysMeasured!,res.exceeded16Days!],
              maxBarThickness: 30,
              fill: true,
              backgroundColor: [
                'rgba(250, 192, 50, 1)',
                'rgba(51, 108, 251, 1)',
                'rgba(239, 93, 168, 1)',
              ],
            },
          ],
        };
        this.setRpmChartOptions();
        this.setCcmChartOptions();
        this.setPcmChartOptions();
        this.setDataChartOptions();
      }
    });
  }

  setRpmChartOptions() {
    const maxValue = this.patientCount?.rpmPatients;
    const onClickHandler = (event: any, elements: any) => {
      if (elements.length > 0) {
        const label = this.chartData.labels[elements[0].index];
        const flVal =
          label === '< 20 min'
            ? 'rpm20'
            : label === '< 40 min'
              ? 'rpm40'
              : label === '< 60 min'
                ? 'rpm60'
                : 'all';
        this.selectedFilter = this.filterValues.find(
          (item) => item.value === flVal
        );
        this.onFilterGraphOrDropdown(flVal);
      }
    };
    this.rpmChartOptions = this.setCommonChartOptions(
      5,
      maxValue,
      onClickHandler
    );
  }

  setCcmChartOptions() {
    const maxValue = this.patientCount?.ccmPatients;
    const onClickHandler = (event: any, elements: any) => {
      if (elements.length > 0) {
        const label = this.ccmChartData.labels[elements[0].index];
        const flVal =
          label === '< 20 min'
            ? 'ccm20'
            : label === '< 40 min'
              ? 'ccm40'
              : label === '< 60 min'
                ? 'ccm60'
                : 'all';
        this.selectedFilter = this.filterValues.find(
          (item) => item.value === flVal
        );
        this.onFilterGraphOrDropdown(flVal);
      }
    };
    this.ccmChartOptions = this.setCommonChartOptions(
      5,
      maxValue,
      onClickHandler
    );
  }

  setPcmChartOptions() {
    const maxValue = this.patientCount?.pcmPatients;
    const onClickHandler = (event: any, elements: any) => {
      if (elements.length > 0) {
        const label = this.pcmChartData.labels[elements[0].index];
        const flVal =
          label === '< 30 min'
            ? 'pcm30'
            : label === '< 60 min'
              ? 'pcm60'
              : 'all';
        this.selectedFilter = this.filterValues.find(
          (item) => item.value === flVal
        );
        this.onFilterGraphOrDropdown(flVal);
      }
    };
    this.pcmChartOptions = this.setCommonChartOptions(
      6,
      maxValue,
      onClickHandler
    );
  }

  setDataChartOptions() {
    const maxValue = this.patientCount?.daysReadingCaptured;
    const onClickHandler = (event: any, elements: any) => {
      if (elements.length > 0) {
        this.onFilterGraphOrDropdown('daysMeasure');
        this.selectedFilter = this.filterValues[9];
      }
    };
    this.dataChartOptions = this.setCommonChartOptions(
      6,
      maxValue,
      onClickHandler
    );
  }

  setCommonChartOptions(aspectRatio: any, maxValue: any, onClickHandler: any) {
    return {
      aspectRatio: aspectRatio,
      indexAxis: 'y',
      plugins: {
        legend: { display: false },
        tooltip: {
          callbacks: { label: (context: any) => ` ${context.raw}` },
        },
        datalabels: {
          anchor: 'center',
          align: 'center',
          color: '#000',
          font: { size: 14, weight: 'bold' },
          display: (context: any) =>
            context.dataset.data[context.dataIndex] > 0,
        },
      },
      scales: {
        x: {
          beginAtZero: true,
          min: 0,
          max: maxValue,
          ticks: { color: '#495057', stepSize: 10 },
          grid: { display: false },
        },
        y: {
          ticks: { color: '#495057', display: true },
          grid: { display: false },
        },
      },
      onClick: onClickHandler,
    };
  }

  backgroundPlugin = {
    id: 'dottedBorderPlugin',
    beforeDraw: (chart: any) => {
      const ctx = chart.ctx;
      const chartArea = chart.chartArea;
      ctx.setLineDash([5, 5]);
      ctx.beginPath();
      ctx.moveTo(chartArea.left, chartArea.top);
      ctx.lineTo(chartArea.right, chartArea.top);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(chartArea.right, chartArea.top);
      ctx.lineTo(chartArea.right, chartArea.bottom);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(chartArea.left, chartArea.bottom);
      ctx.lineTo(chartArea.right, chartArea.bottom);
      ctx.stroke();

      ctx.beginPath();
      ctx.moveTo(chartArea.left, chartArea.top);
      ctx.lineTo(chartArea.left, chartArea.bottom);
      ctx.stroke();

      ctx.setLineDash([]);

      ctx.save();
      ctx.fillStyle = '#FFF9ED';
      ctx.fillRect(
        chartArea.left,
        chartArea.top,
        chartArea.right - chartArea.left,
        chartArea.bottom - chartArea.top
      );
      ctx.restore();
    },
  };

  ngOnDestroy() {
    Chart.unregister(ChartDataLabels);
    Chart.unregister(this.backgroundPlugin);
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  private fromToDate() {
    return (
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US')
    );
  }

  getClass(x: any) {
    const [rpmMinutesStr] = x?.rpmMins?.split(':');
    const rpmMinutes = parseInt(rpmMinutesStr, 10);

    const [ccmMinutesStr] = x?.ccmMins?.split(':');
    const ccmMinutes = parseInt(ccmMinutesStr, 10);

    const [pcmMinutesStr] = x?.pcmMins?.split(':');
    const pcmMinutes = parseInt(pcmMinutesStr, 10);

    if (
      rpmMinutes >= 20 ||
      ccmMinutes >= 20 ||
      pcmMinutes >= 30 ||
      x.noOfDays >= 16
    ) {
      return 'Completed';
    } else {
      return 'Pending';
    }
  }

  checkStatus(x: any) {
    const [rpmMinutesStr] = x?.rpmMins?.split(':');
    const rpmMinutes = parseInt(rpmMinutesStr, 10);

    const [ccmMinutesStr] = x?.ccmMins?.split(':');
    const ccmMinutes = parseInt(ccmMinutesStr, 10);

    const [pcmMinutesStr] = x?.pcmMins?.split(':');
    const pcmMinutes = parseInt(pcmMinutesStr, 10);

    if (
      rpmMinutes >= 20 ||
      ccmMinutes >= 20 ||
      pcmMinutes >= 30 ||
      x.noOfDays >= 16
    ) {
      return 'green';
    } else {
      return 'red';
    }
  }

  getCriticalAlertClass() {
    return 'alarm';
  }

  deleteTask(event: Event, task: any) {
    console.log('task,', task);
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',

      accept: () => {
        this.dashboardService.deleteTask(task?.id).subscribe((res) => {
          if (res.success) {
            this.todaysTask();
            this.messageService.add({
              severity: 'success',
              summary: 'Confirmed',
              detail: 'Task deleted successfully.',
              life: 3000,
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Failed to delete task.',
              life: 3000,
            });
          }
        });
      },
      reject: () => { },
    });
  }

  openCriticalAlertModalFn(item: AlertList) {
    console.log(item);
    this.sidebarVisible = !this.sidebarVisible;
    //this.openCriticalAlertModal = true;
    this.criticalAlert = item;
    let fromToDetails = item.patientId + '/0/5/' + this.fromToDate();
    this.dashboardService
      .getPatientEncounter(fromToDetails)
      .subscribe((res) => {
        if (res.success) {
          this.encounterList = res.encounterNewList;
          this.programList = res.programs;
        }
      });
  }

  convert(val: number): string {
    const minutes: number = Math.floor(val);
    const seconds: number = Math.round((val - minutes) * 60);
    return `${minutes}m:${seconds}s`;
  }

  onSubmit() {
    if (this.criticalAlert) {
         if (
      this.encounterForm.selectedProgram === undefined ||
      this.encounterForm.selectedProgram === ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select program',
        life: 3000,
      });
      return;
    }

    if (
      this.encounterForm.selectedEReason?.reason === undefined ||
      this.encounterForm.selectedEReason?.reason === ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select encounter reason',
        life: 3000,
      });
      return;
    }
    if (
      this.encounterForm?.description === undefined ||
      this.encounterForm?.description === ''||
      this.stripHtmlTags(this.encounterForm.description)==''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please enter a description',
        life: 3000,
      });
      return;
    }

      this.encounterForm.stopSpeechToText()
      this.encounterForm.getData();
      console.log(this.encounterForm.getData());
      let req: AddEncounter = {
        patientId: this.criticalAlert.patientId,
        encounterReason: this.encounterForm.selectedEReason?.reason || '',
        encounterDescription: this.stripHtmlTags(this.encounterForm.description) || '' || '',
        encounterDateTime:
          formatDate(this.encounterForm.encounterDate, 'yyyy-MM-dd', 'en-US') +
          ' ' +
          formatDate(this.encounterForm.encounterTime, 'HH:mm:ss', 'en-US'),
        patientAlertId: this.criticalAlert.patientAlertId,
        review: this.encounterForm.selectedProgram || 'rpm',
        duration: this.encounterForm.value4 || '',
       // reasonCode:this.reasonCodeMap[this.encounterForm.selectedEReason.reason as keyof typeof this.reasonCodeMap]||'EC_019',
      reasonCode: this.encounterForm.selectedEReason?.reasonCode || 'EC_019',
      };
      console.log(req);
      this.encounterLoader = true;
      this.dashboardService.addEncounter(req).subscribe((res) => {
        this.encounterLoader = false;
        if (res.success) {
          this.acknowledgeAlert(this.criticalAlert?.patientAlertId,'Encounter');
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Encounter createad successfully.',
            life: 3000,
          });
          this.openCriticalAlertModal = false;
          this.closeSidebar()
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: res.messages &&res.messages.length>0?res.messages[0]:'Failed to create encounter',
            life: 3000,
          });
        }
      });
    }
  }

  stripHtmlTags(html: any) {
    if (html && html.changingThisBreaksApplicationSecurity) {
      var xx = html?.changingThisBreaksApplicationSecurity.replace(/<p>/g, '\n');
      return xx.replace(/<\/?[^>]+(>|$)|&nbsp;/g, '');
    }
    else {
      var xx = html?.replace(/<p>/g, '\n');
      return xx.replace(/<\/?[^>]+(>|$)|&nbsp;/g, '');
    }

  }

  acknowledgeAlert(alertId: number | undefined, acknowledgmentReason='') {
    let req: AddEncounter = {
      acknowledged: true,
      patientAlertId: alertId,
      acknowledgeReasons: [acknowledgmentReason]
    };
    this.dashboardService.updateAlert(req).subscribe((res) => {
      this.encounterLoader = false;
      if (res.success) {
        this.alertCount();
        let data: LazyLoadEvent = {
          first: 0,
          rows: 15,
        };
        this.loadCriticalAlertsList(data);
      } else {
        console.log('Failed to acknowledge');
      }
    });
  }

  onTextSubmit() {
    console.log(this.criticalAlert);
    this.encounterLoader = true;
    let selectedProgram = this.textMessageForm.selectedProgram;
    if (!selectedProgram) {
      this.generateErrorMessage('One program should be selected');
      this.encounterLoader = false;
      return;
    }
    let messageDescription = this.textMessageForm.messageDescription;
    if (!messageDescription) {
      this.generateErrorMessage('Please enter message');
      this.encounterLoader = false;
      return;
    }
    let messageType = this.textMessageForm.messageType;
    let ansSelected = this.textMessageForm.isChecked;

    let opt1 = this.textMessageForm.option1;
    let opt2 = this.textMessageForm.option2;
    let opt3 = this.textMessageForm.option3;
    let phoneNumber = this.textMessageForm.phoneNumber;
    let isEncounterNeeded = this.textMessageForm.isEnconterNeeded;

    let ans: any[] = [];

    if (ansSelected) {
      if (opt1 && opt1 !== '') {
        ans.push(opt1);
      } else {
        this.generateErrorMessage('Option 1 needed');
        return;
      }

      if (opt2 && opt2 !== '') {
        ans.push(opt2);
      } else {
        this.generateErrorMessage('Option 2 needed');
        return;
      }

      if (opt3 && opt3 !== '') {
        ans.push(opt3);
      }
    }
    let userName = '';
    let logUser = localStorage.getItem('user');
    if (logUser) {
      userName = JSON.parse(logUser)['firstName'];
    }

    let req: any = {};
    if (messageType === 'Now') {
      req = {
        patientIds: [this.criticalAlert?.patientId],
        deliveryTime: '',
        question: messageDescription,
        senderName: userName,
        timeZone: '',
        nowOrSchedule: 'Now',
        answers: ans,
        phoneNumber: phoneNumber,
        review: selectedProgram,

      };
    }
    else {
      req = {
        patientIds: [this.criticalAlert?.patientId],
        deliveryTime: '',
        question: messageDescription,
        senderName: userName,
        timeZone: '',
        nowOrSchedule: 'Now',
        answers: ans,
        phoneNumber: phoneNumber,
        review: selectedProgram,
        sms:
          messageType === 'SMS'
            ? '**DO NOT REPLY To this message, For emergency please dial 911'
            : '',
      };
    }

    this.dashboardService.sendMessage(req).subscribe((res) => {
      this.encounterLoader = false;

      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Message sent successfully.',
          life: 3000,
        });
        this.closeModalPopup();
        this.closeSidebar();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to send message',
          life: 3000,
        });
      }
    });

    if (isEncounterNeeded) {
      var encounterdata: AddEncounter = {
        patientId: this.criticalAlert?.patientId,
        encounterReason: 'Critical Alert',
        encounterDescription: messageDescription,
        encounterDateTime: formatDate(
          new Date(),
          'yyyy-MM-dd HH:mm:ss',
          'en-US'
        ),
        patientAlertId: this.criticalAlert?.patientAlertId,
        duration: 1,
      };
      this.dashboardService.addEncounter(encounterdata).subscribe((res) => {
        this.encounterLoader = false;
        if (res.success) {
          this.acknowledgeAlert(this.criticalAlert?.patientAlertId,'Message');
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Encounter createad successfully.',
            life: 3000,
          });
          this.closeModalPopup();
          this.closeSidebar()
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'Failed to create encounter',
            life: 3000,
          });
        }
      });
    } else {
      this.acknowledgeAlert(this.criticalAlert?.patientAlertId,'Message');
      this.closeModalPopup();
      this.closeSidebar();
      this.encounterLoader = false;
    }

  }

  closeModalPopup() {
    this.openCriticalAlertModal = false;
    this.activeIndex = 0;
  }

  generateErrorMessage(text: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: text,
      life: 3000,
    });
  }

  editPatient(patientId: string) {
    localStorage.setItem('mainTab', '0');
    localStorage.removeItem('subTab');
    this.router.navigate(['/editpatient'], { queryParams: { id: patientId } });
  }

  getFormattedDate(
    dateOfBirth: string | undefined,
    format: string | 'YYYY-MMM-DD'
  ): string {
    if (dateOfBirth === null || dateOfBirth === undefined) {
      return '';
    }
    return moment(dateOfBirth).format(format);
  }

  getTotalYears(dateOfBirth: string | undefined): string {
    if (dateOfBirth === null || dateOfBirth === undefined) {
      return '';
    }
    return moment().diff(moment(dateOfBirth), 'years') + ' Years';
  }

  getAlertFormattedDate(datetime: string): string {
    return moment(datetime, 'MM-DD-YYYY HH:mm:ss').format('MM-DD-YY');
  }

  getFormattedTime(datetime: string): string {
    return moment(datetime, 'MM-DD-YYYY HH:mm:ss').format('hh:mm:ss A');
  }

  sidebarVisible: boolean = false;
  messageSidebarVisible: boolean = false;
  taskSidebarVisible: boolean = false;
  encounterSidebarVisible: boolean = false;
  showMessageSidebar() {
    this.sidebarVisible = false;
    this.messageSidebarVisible = true;
    this.taskSidebarVisible = false;
    this.encounterSidebarVisible = false;
    setTimeout(() => { 
      this.textMessageForm.resetFileds()
     },100)
  }
  showTaskSidebar() {
    this.sidebarVisible = false;
    this.messageSidebarVisible = false;
    this.taskSidebarVisible = true;
    this.encounterSidebarVisible = false;
    setTimeout(() => {
      this.WatchrxCalendarComponent.refreshCalendar();
    }, 100)
  }

  showEncounterSidebar() {
    this.sidebarVisible = false;
    this.messageSidebarVisible = false;
    this.taskSidebarVisible = false;
    this.encounterSidebarVisible = true;
  }
  goBack() {
    if(this.encounterForm)
    {
      this.encounterForm.stopSpeechToText()
    }
    this.sidebarVisible = true;
    this.messageSidebarVisible = false;
    this.taskSidebarVisible = false;
    this.encounterSidebarVisible = false;
    this.encounterLoader = false
  }
  closeSidebar() {
    this.sidebarVisible = false;
    this.messageSidebarVisible = false;
    this.taskSidebarVisible = false;
    this.encounterSidebarVisible = false;
    this.encounterLoader = false
  }

  showCallWindow() {
    this.closeSidebar();
    this.openVoiceVideoDlg = true;
  }
  showVedioCallWindow() {
    this.closeSidebar();
    this.openVideoDlg = true;
  }
  closeTaskModel() {
    this.taskSidebarVisible = false;
    this.todaysTask();
  }

  updateScrollBlocking() {
    const anySidebarOpen =
      this.sidebarVisible ||
      this.messageSidebarVisible ||
      this.taskSidebarVisible ||
      this.encounterSidebarVisible
  
    if (anySidebarOpen) {
      document.body.classList.add('blocked-scroll');
    } else {
      document.body.classList.remove('blocked-scroll');
    }
  }

  scrollToDiv(id: string): void {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }

  previousRowsPerPage = 15;
  first = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }

  reasonList:any=[];
   getReasonList()
  {
    this.dashboardService.getReasonList().subscribe((res: any) => {
      if (res) {
        console.log('reasonList', res);
        this.reasonList = this.convertObjectToArray(res);
      }
    }, err => {
      this.reasonList = [];
    });
  }

  convertObjectToArray(obj: any): { reason: string, reasonCode: string }[] {
  return Object.keys(obj).map(key => ({
    reason: key,
    reasonCode: obj[key]
  }));
}


acknowledgeModalVisible: boolean = false;
acknowledgeOptions = [
  { label: 'Encounter created', value: 'Encounter created' },
  { label: 'Patient called', value: 'Patient called' },
  { label: 'Duplicate alerts', value: 'Duplicate alerts' }
];

selectedAcknowledgeOptions: string[] = [];
acknowledgeLoading: boolean = false;

openAckAlertModalFn(item: AlertList) {
  this.selectedAcknowledgeOptions = [];
  this.acknowledgeModalVisible = true;
   this.criticalAlert = item;
}

submitAcknowledge() {
  if (!this.criticalAlert?.patientAlertId) return;
  this.acknowledgeLoading = true;
  const payload = {
    acknowledged: true,
    patientAlertId: this.criticalAlert?.patientAlertId,
    acknowledgeReasons: this.selectedAcknowledgeOptions
  };
  this.dashboardService.acknowledgeAlert(payload).subscribe({
    next: () => {
      this.acknowledgeLoading = false;
      this.acknowledgeModalVisible = false;
      this.messageService.add({ severity: 'success', summary: 'Acknowledged', detail: 'Alert acknowledged successfully.' });
      this.loadCriticalAlertsList()
      // Optionally refresh alerts here
    },
    error: (err) => {
      this.acknowledgeLoading = false;
      this.messageService.add({ severity: 'error', summary: 'Error', detail: 'Failed to acknowledge alert.' });
    }
  });
}
}
