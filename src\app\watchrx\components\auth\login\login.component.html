<p-toast />
<div class="surface-ground flex min-h-screen overflow-hidden p-hidden-sm p-hidden-xs"
  style="background-color: #f7f8fc !important">
  <div class="flex-1 position-relative flex align-items-center">
    <div class="flex align-items-center">
      <img src="assets/layout/images/login-logo.png" alt="Image" height="400" class="mr-3" />
      <div>
        <p class="m-0" style="
            color: #0047cc;
            font-size: 40px !important;
            font-weight: 400;
            line-height: 46px;
          ">
          Simplify Digital Healthcare​
        </p>
        <p style="
            font-size: 18px !important;
            font-style: italic;
            padding-right: 59px;
            text-align: justify;
            margin-top: 15px !important;
          " class="m-0">
          WatchRx is at the forefront of digital healthcare transformation and
          virtual care.With our smartwatch and AI/ML enabled platform, you can
          increase patient engagement and care coordination to improve patient
          outcomes, personalize care, and reduce healthcare costs.
        </p>
      </div>
    </div>
  </div>
  <div class="flex flex-column align-items-center justify-content-center" style="height: 100vh; width: 30%">
    <div class="w-full surface-card py-5 px-5 sm:px-5 h-full" style="
        background-color: #ffffff !important;
        padding: 20px;
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 15px;
        margin-bottom: 15px;
        box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.25);
        box-shadow: 1px 0px 18px -9px rgba(0, 0, 0, 0.25);
      ">
      <div class="text-center mb-5">
        <img src="assets/layout/images/watchrx-full.jpg" alt="Image" height="100" class="mb-3" />
        <div class="text-900 text-3xl font-medium mb-3">
          <span class="text-5xl" style="color: var(--primary-color)">Login</span>

        </div>
      </div>

      <div>
        <div class="mb-5">
          <label for="email1" class="block text-900 text-xl font-medium mb-2">Username <span
              class="p-text-danger">*</span>
          </label>
          <input id="email1" [(ngModel)]="username" type="text" placeholder="UserName" pInputText class="w-full mb-2"
            style="padding: 1rem" required autofocus [ngClass]="{ 'ng-invalid ng-dirty': submitted && !username }" />
          <small class="ng-dirty ng-invalid p-text-danger" *ngIf="submitted && !username">User Name is required.</small>
        </div>
        <div class="mb-5">
          <label for="password1" class="block text-900 font-medium text-xl mb-2">Password <span
              class="p-text-danger">*</span>
          </label>
          <p-password id="password1" [(ngModel)]="password" placeholder="Password" [feedback]="false"
            [toggleMask]="true" styleClass="mb-2" inputStyleClass="w-full" required autofocus class="w-full"
            [ngClass]="{ 'ng-invalid ng-dirty': submitted && !username }"></p-password>
          <small class="ng-dirty ng-invalid p-text-danger" *ngIf="submitted && !password">Password is required.</small>
        </div>
        <div class="flex align-items-center justify-content-between mb-5 gap-5">
          <label for="block text-900 font-medium text-xl mb-2">Select Role</label>
          <p-dropdown inputId="dropdown" [autoDisplayFirst]="true" placeholder="Select a Role" [options]="roles"
            [(ngModel)]="roleNum" optionLabel="name"></p-dropdown>
        </div>

        <div class="flex align-items-center justify-content-between mb-5 gap-5">
          <div class="flex align-items-center"></div>
          <a class="font-medium no-underline ml-2 text-right cursor-pointer" [routerLink]="['/forgotPassword']"
            style="color: var(--primary-color)">Forgot password?</a>
        </div>
        <button pButton pRipple label="Sign In" class="w-full p-3 text-xl p-button-warning" (click)="signIn()"
          [loading]="loading"></button>
      </div>
    </div>
  </div>
</div>
<p-toast key="tst"></p-toast>
<p-dialog [header]="checkOTPFromLogin=='Y'?'Enter OTP & Choose Organization':'Choose Organization'" [(visible)]="display" [modal]="true" showEffect="fade"
  [style]="{ width: '25vw' }">
  <div class="flex align-items-center justify-content-between mb-3 gap-5 col-12" *ngIf="checkOTPFromLogin=='Y'">
    <input id="otp" [(ngModel)]="userotp" type="text" placeholder="Enter OTP" pInputText style="padding: 0.6rem; width:100% ;"
      required />
  </div>
  <div class="flex align-items-center justify-content-between mb-2 gap-5 col-12 selectorg">
    <p-dropdown inputId="dropdown" [autoDisplayFirst]="true" placeholder="Select Organization" [options]="orgs"
      [(ngModel)]="orgNum" optionLabel="label" appendTo="body" class="w-full"></p-dropdown>
  </div>
  <ng-template pTemplate="footer">
    <!--icon="pi pi-check"-->
    <button pButton [loading]="loadingPop" (click)="continue()" label="Continue" class="p-button" class="w-full"></button>
  </ng-template>
</p-dialog>