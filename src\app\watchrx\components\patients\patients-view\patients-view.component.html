<p-toast />
<p-confirmDialog />
<div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb">
  <span class="font-bold font-16 flex align-items-center">
    <img src="assets/watchrx/svg/nurse.svg" alt="patients" width="16" height="16" class="mr-2" />
    Patients</span>
</div>
<div class="grid">
  <div class="col-12">
    <p-card class="config1">
      <ng-template pTemplate="header">
        <h6 class="font-bold">Patients List</h6>
      </ng-template>
      <hr />
      <div class="flex flex-row p-3 md:justify-content-start ">
        <div class="flex-1 mr-3">
          <span class="p-input-icon-left p-input-icon-right w-full">
            <!-- <i class="pi pi-user"></i> -->
            <i class="pi pi-search" style="width: 20px;"></i>
            <!-- <input
              type="text"
              pInputText
              placeholder="Search"
              (input)="onSearch($event)"
            /> -->
            <input type="text" pInputText (input)="onInput($event)" placeholder="Search" class="w-full"
              style="border-radius: 20px;" [(ngModel)]="searchText" />
            <!-- <i class="pi pi-search"></i> -->
          </span>
        </div>
        <div class="flex">
          <p-button label="Search" severity="primary" class="mr-1" (onClick)="onSearch(debouncedValue)" />
        </div>
      </div>



      <div class="grid p-fluid mt-3 relative">
        <div class="flex flex-row md:justify-content-end mb-2 absolute buttons" *ngIf="userRole.roleType!==3">
          <p-button label="Add Patient" severity="secondary" class="mr-2" [outlined]="true" icon="pi pi-plus"
            [routerLink]="['/patients/addpatient']" />
          <p-fileUpload mode="basic" chooseLabel="Bulk Upload" chooseIcon="pi pi-upload" #fileUpload
            [maxFileSize]="1000000" (onSelect)="uploadPatients($event)" name="medImage"
            class="upload mr-2 fileupload"></p-fileUpload>
          <!-- <p-button
            label="Bulk Upload"
            severity="primary"
            class="mr-2"
            [outlined]="true"
            icon="pi pi-upload"
          /> -->
          <p-button label="Template" severity="primary" class="mr-2" [outlined]="true" icon="pi pi-download"
            (onClick)="downloadFile()" />
        </div>
        <div class="field col-12">
          <p-tabView orientation="left" (onChange)="tabChange()" [(activeIndex)]="activeTabIndex">
            <p-tabPanel [header]="'Active('+(patientCount?.totalActivePatients??0)+')'" class="line-height-3 m-0 position-relative">
              <p-table #dt [value]="patientList" [paginator]="true" [totalRecords]="totalActivePatients"
                [rows]="itemsPerPage" [lazy]="true" [loading]="loader" (onLazyLoad)="loadActivePatients($event)"
                responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped"
                [rowsPerPageOptions]="searchText.length==0?[15,25, 50, 75,100]:[itemsPerPage]"
                (onPage)="onPageChange($event)" [first]="first">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Patient Name</th>
                    <th>Phone Number</th>
                    <th>MD/DO/NP/PA
                      <p-dropdown [options]="providerList" optionLabel="label" [showClear]="true" *ngIf="userRole.roleType==3"
                        placeholder="Select Provider" [appendTo]="'body'" [(ngModel)]="selectedProvider" (onChange)="onProviderChange($event)" class="tabledropdown">
                      </p-dropdown>
                    </th>
                    <th>Case Manager
                      <p-dropdown [options]="casemanagerList" optionLabel="label" [showClear]="true" *ngIf="userRole.roleType==3"
                        placeholder="Select CaseManager" [appendTo]="'body'" [(ngModel)]="selectedCaseManager"  (onChange)="onCaseManagerChange($event)" class="tabledropdown"></p-dropdown>
                    </th>
                    <th>Chronic Condition</th>
                    <th>MRN</th>
                    <th>Action</th>
                  </tr>
                </ng-template>
                <ng-template let-patientInfo pTemplate="body">
                  <tr>
                    <td>
                      <span class="text-primary font-bold cursor-pointer" (click)="
                          editPatient(
                            patientInfo['patientId'],
                            patientInfo.patientName,
                            'active'
                          )
                        ">{{ patientInfo.patientName }}</span>
                    </td>
                    <td>{{ patientInfo.patientPhoneNumber }}</td>
                    <td>{{ patientInfo.physicianName }}</td>
                    <td>{{ patientInfo.caseManagerName }}</td>
                    <td>{{ getNames(patientInfo.chronicConditions) }}</td>
                    <td>{{ patientInfo.mrnNo }}</td>
                    <td>
                      <div class="flex actionbuttons">
                        <a class="cursor-pointer" (click)="
                          viewAlerts(
                            patientInfo['patientId'],
                            patientInfo.patientName
                          )
                        ">view Alerts</a>
                        <button pButton icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
                          size="small" (click)="
                            editPatient(
                              patientInfo['patientId'],
                              patientInfo.patientName,'active'
                            )
                          "></button>
                        <button pButton icon="pi pi-phone" class="p-button-outlined p-button-secondary mr-2"
                          size="small" (click)="
                            makeCall(
                              patientInfo['patientId'],
                              patientInfo.patientName,
                              patientInfo
                            )
                          "></button>
                        <button pButton icon="pi pi-video" class="p-button-outlined p-button-secondary mr-2"
                          size="small" (click)="
                          makeVideoCall(
                            patientInfo['patientId'],
                            patientInfo.patientName,
                            patientInfo
                          )
                        "></button>
                        <button pButton icon="pi pi-envelope" class="p-button-outlined p-button-secondary" size="small"
                          (click)="
                            makeMessage(
                              patientInfo['patientId'],
                              patientInfo.patientName,
                              patientInfo
                            )
                          "></button>
                      </div>
                    </td>
                  </tr>
                </ng-template>
              </p-table>
               <div class="footer1 text-900 font-bold" > Total {{totalActivePatients}} Active Patients </div>
              <!-- <p>Total {{totalActivePatients+inActiveTotalPatients}} Patients</p> -->
            </p-tabPanel>
            <p-tabPanel [header]="'Inactive('+(patientCount?.totalInActivePatients??0)+')'" class="line-height-3 m-0 position-relative">
              <p-table [value]="inActivePatientList" [paginator]="true" [totalRecords]="inActiveTotalPatients"
                [rows]="itemsPerPage1" [lazy]="true" [loading]="inActiveLoader"
                (onLazyLoad)="loadInActivePatients($event)" responsiveLayout="scroll"
                styleClass="p-datatable-gridlines p-datatable-striped"
                [rowsPerPageOptions]="searchText.length==0?[15,25, 50,75, 100]:[itemsPerPage1]"
                (onPage)="onPageChange1($event)" [first]="first1">
                <ng-template pTemplate="header">
                  <tr>
                    <th>Patient Name</th>
                    <th>Phone Number</th>
                    <th>MD/DO/NP/PA
                      <p-dropdown [options]="providerList" optionLabel="label" [showClear]="true" *ngIf="userRole.roleType==3"
                        placeholder="Select a Provider" [appendTo]="'body'" [(ngModel)]="selectedProvider" (onChange)="onProviderChange($event)" class="tabledropdown">
                      </p-dropdown>
                    </th>
                    <th>Case Manager
                      <p-dropdown [options]="casemanagerList" optionLabel="label" [showClear]="true" *ngIf="userRole.roleType==3"
                        placeholder="Select a CaseManager" [appendTo]="'body'" [(ngModel)]="selectedCaseManager" (onChange)="onCaseManagerChange($event)" class="tabledropdown"></p-dropdown>
                    </th>
                    <th>Chronic Condition</th>
                    <th>MRN</th>
                    <th>Consent Status</th>
                    <th>Action</th>
                  </tr>
                </ng-template>
                <ng-template let-patientInfo pTemplate="body">
                  <tr>
                    <td>
                      <!-- <p-button
                        label="{{ patientInfo.patientName }}"
                        [link]="true"
                        (onClick)="
                          editPatient(
                            patientInfo['patientId'],
                            patientInfo.patientName
                          )
                        "
                      /> -->
                      <span class="text-primary font-bold cursor-pointer" (click)="
                          editPatient(
                            patientInfo['patientId'],
                            patientInfo.patientName,
                            'inactive'
                          )
                        ">{{ patientInfo.patientName }}</span>
                    </td>
                    <td>{{ patientInfo.patientPhoneNumber }}</td>
                    <td>{{ patientInfo.physicianName }}</td>
                    <td>{{ patientInfo.caseManagerName }}</td>
                    <td>{{ getNames(patientInfo.chronicConditions) }}</td>
                    <td>{{ patientInfo.mrnNo }}</td>
                    <td>
                      <div [ngClass]="checkStatus(patientInfo.consentStatus)" class="rounded-background">
                        {{ getClass(patientInfo.consentStatus) }}
                      </div>
                    </td>
                    <td>
                      <div class="flex actionbuttons">
                        <p-button [outlined]="true" severity="secondary" icon="pi pi-pencil" size="small" class="mr-1"
                          (click)="
                            editPatient(
                              patientInfo['patientId'],
                              patientInfo.patientName,
                              'inactive'
                            )
                          " />
                      </div>
                    </td>
                  </tr>
                </ng-template>
              </p-table>
               <div class="footer1 text-900 font-bold" > Total {{inActiveTotalPatients}} Inactive Patients </div>
            </p-tabPanel>
          </p-tabView>
        </div>
      </div>
    </p-card>
  </div>
</div>
<!-- <p-dialog
  [(visible)]="openVoiceVideoDlg"
  [modal]="false"
  [style]="{ width: '900px' }"
  [contentStyle]="{ overflow: 'auto' }"
  position="top"
  [maximizable]="true"
  [header]="patientName"
  (visibleChange)="handleClose()"
>
  <p-tabView [(activeIndex)]="activeIndex">
    <p-tabPanel header="Voice Call">
      <form name="form" role="form" id="update-form">
        <div class="modal-content">
          <div class="modal-header">
            <div class="col-8">
              <div id="callStatus"></div>
            </div>
          </div>
          <div class="modal-body" id="modalBody">
            <div class="row clearfix">
              <div class="field col-12 md:col-4">
                <label htmlfor="state" class="text-600 font-bold"
                  >Select Program(s)</label
                >
                <div class="flex justify-content-left gap-3">
                  <div class="flex flex-wrap gap-3">
                    <div
                      class="flex align-items-center"
                      *ngFor="let option of programs"
                    >
                      <p-radioButton
                        name="consent"
                        [value]="option.value"
                        [inputId]="option.label.toLowerCase()"
                        variant="filled"
                        class="mt-3"
                      />
                      <label
                        [for]="option.label.toLowerCase()"
                        class="ml-2 mt-3 text-600 font-bold"
                        >{{ option.label }}</label
                      >
                    </div>
                  </div>
                </div>
              </div>
              <div class="field col-12 md:col-8">
                <label htmlfor="state">Phone Number</label>
                <input
                  id="phonenumber"
                  type="text"
                  pInputText
                  name="phoneNumber"
                  required
                /><span
                  ><small class="red">Note:(Example:+18001232323)</small></span
                >
              </div>
              <div id="input-volume-controller" class="col-6 hide">
                <div class="volume-indicators">
                  <i class="pi pi-microphone fa-2x" aria-hidden="true"></i>
                  <div id="input-volume"></div>
                </div>
              </div>
              <div id="output-volume-controller" class="col-6 hide">
                <div class="volume-indicators">
                  <i class="pi pi-volume-up fa-2x" aria-hidden="true"></i>
                  <div id="output-volume"></div>
                </div>
              </div>
              <div class="col-12 someth hide" id="animation">
                <div class="call-animation-play" id="palypauseanim">
                  <i class="pi pi-phone caller-img"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <p-button
              id="button-call"
              [rounded]="true"
              label="Initiate Call"
              icon="pi pi-phone"
              severity="info"
              class="mr-1"
            >
            </p-button>
            <p-button
              id="muteUnmute"
              [rounded]="true"
              label="Mute/UnMute"
              icon="pi pi-microphone"
              severity="info"
              class="mr-1"
            >
            </p-button>
            <p-button
              id="button-hangup-outgoing"
              [rounded]="true"
              label="Disconnect"
              icon="pi pi-phone"
              severity="danger"
              class="mr-1"
            >
            </p-button>
            <p-button
              id="cancel-voice-call"
              [rounded]="true"
              label="Cancel"
              icon="pi pi-phone"
              severity="danger"
              class="mr-1"
            >
            </p-button>
          </div>
        </div>
      </form>
    </p-tabPanel>
    <p-tabPanel header="Video Call">
      <div class="field col-12 md:col-4">
        <label htmlfor="state" class="text-600 font-bold"
          >Select Program(s)</label
        >
        <div class="flex justify-content-left gap-3">
          <div class="flex flex-wrap gap-3">
            <div
              class="flex align-items-center"
              *ngFor="let option of programs"
            >
              <p-radioButton
                name="consent"
                [value]="option.value"
                [inputId]="option.label.toLowerCase()"
                variant="filled"
                class="mt-3"
              />
              <label
                [for]="option.label.toLowerCase()"
                class="ml-2 mt-3 text-600 font-bold"
                >{{ option.label }}</label
              >
            </div>
          </div>
        </div>
      </div>
      <main>
        <div class="row clearfix">
          <div id="join-flow">
            <p-button
              [rounded]="false"
              icon="pi pi-video"
              severity="info"
              class="mr-1"
              (click)="getVideoSDKJWT()"
              label="Join Session"
              [disabled]="this.inSession"
            />
            <p-button
              id="cancel-video-call"
              [rounded]="true"
              label="Cancel"
              icon="pi pi-phone"
              severity="danger"
              (click)="handleClose()"
              class="mr-1"
            >
            </p-button>
          </div>

          <div id="sessionContainer"></div>
        </div>
      </main>
    </p-tabPanel>
  </p-tabView>
</p-dialog> -->
<app-calls [openVoiceVideoDlg]="openVoiceVideoDlg" [selectedPatientInfo]="selectedPatientInfo" *ngIf="openVoiceVideoDlg"
  (closeModel)="openVoiceVideoDlg=false"></app-calls>
<app-video-calls [openVideoDlg]="openVideoDlg" [selectedPatientInfo]="selectedPatientInfo" *ngIf="openVideoDlg"
  (closeModel)="openVideoDlg=false"></app-video-calls>
<p-sidebar [(visible)]="messageSidebarVisible" [modal]="true" [blockScroll]="true" [baseZIndex]="10000"
  [style]="{ width: '40rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/chat.svg" alt="Consented Icon" width="20" height="20" class="mr-2" />
      <span class="xl-font">Message &nbsp;</span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
  <div class="flex flex-row justify-content-between align-items-center p-4">
    <app-schedule-text-message-form #textMessageForm [programList]="programList" [isSechedule]="true"
      [isEnconterNeeded]="true" [phoneNumber]="selectedPatientInfo?.phoneNumber" />
  </div>
  <div class="flex gap-3 p-3 footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Back" [outlined]="true" severity="secondary" (click)="messageSidebarVisible=false" />
      <p-button label="Submit" severity="primary" (click)="onTextSubmit()" [loading]="encounterLoader" />
    </div>
  </div>
</p-sidebar>
<p-sidebar [(visible)]="openAlertsDilog" [modal]="true" [blockScroll]="true" [baseZIndex]="10000"
  [style]="{ width: '40rem' }" position="right" class="dashboardSidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <img src="assets/watchrx/svg/chat.svg" alt="Consented Icon" width="20" height="20" class="mr-2" />
      <span class="xl-font">Alerts &nbsp;</span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
  <div class="col-12 flex" *ngFor="let alert of alerts">
    <div class="col-8">{{alert.alertDescription}}</div>
    <div class="col-4">{{alert.createdDateTime}}</div>
  </div>
  <div class="flex gap-3 p-3 footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Close" severity="secondary" (click)="openAlertsDilog=false" />
    </div>
  </div>
</p-sidebar>