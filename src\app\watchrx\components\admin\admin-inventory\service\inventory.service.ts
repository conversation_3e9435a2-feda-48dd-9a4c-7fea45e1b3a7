import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import {
  DeviceTypeResponse,
  InventoryResponse,
} from '../../../../api/inventory';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class InventoryService {
  constructor(private http: HttpClient) {}

  getInventory(): Observable<InventoryResponse> {
    return this.http.get<InventoryResponse>(
      environment.BASE_URL + Constants.INVENTORY_LIST
    );
  }

  getDeviceTypes(): Observable<DeviceTypeResponse> {
    return this.http.get<DeviceTypeResponse>(
      environment.BASE_URL + Constants.DEVICE_TYPES
    );
  }

  addInventory(data: any) {
    return this.http.post<InventoryResponse>(
      environment.BASE_URL + Constants.ADD_INVENTORY,
      data
    );
  }

  updateInventory(data: any) {
    return this.http.post<InventoryResponse>(
      environment.BASE_URL + Constants.UPDATE_INVENTORY,
      data
    );
  }

  addUpdateDeviceType(data: any) {
    return this.http.post<InventoryResponse>(
      environment.BASE_URL + Constants.ADD_UPDATE_DEVICE_TYPE,
      data
    );
  }

  deleteDeviceType(data: any) {
    return this.http.post<InventoryResponse>(
      environment.BASE_URL + Constants.DELETE_DEVICE_TYPE,
      data
    );
  }
}
