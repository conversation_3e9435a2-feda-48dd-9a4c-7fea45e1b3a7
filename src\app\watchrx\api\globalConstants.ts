export const encounterReasonList = [
  // {
  //   reason: 'Critical Alert',
  // },
  // { reason: 'Data Review' },
  // { reason: 'Text Message' },
  // { reason: 'Appointment' },
  // { reason: 'Phone Call' },
  // { reason: 'Home Visits' },
  // { reason: 'RPM Review' },
  // { reason: 'CCM Review' },
  // { reason: 'PCM Review' },
  // { reason: 'Device Documentation/Training' },
  // { reason: 'BPM Device Documentation/Training' },
  // { reason: 'Introduction to RPM' },
  // { reason: 'Introduction to CCM' },
  // { reason: 'Introduction to PCM' },
  // { reason: 'Introduction to RTM' },
  // { reason: 'Introduction to PCM & RPM' },
  // { reason: 'Introduction to PCM & RTM' },
  // { reason: 'Introduction to CCM & RPM' },
  // { reason: 'Other' },
  { reason: "Critical Alert" },
  { reason: "Data Review" },
  { reason: "Phone Call" },
  { reason: "RPM Review" },
  { reason: "CCM Review" },
  { reason: "PCM Review" },
  { reason: "EMR/Chart review" },
  { reason: "BPM Device Documentation/Training" },
  { reason: "Glucometer Device education Template" },
  { reason: "Weigh scale device education template for CHF" },
  { reason: "Weigh scale device education template for ESRD, volume overload" },
  { reason: "Introduction to RPM" },
  { reason: "Introduction to CCM" },
  { reason: "Introduction to PCM" },
  { reason: "Introduction to RTM" },
  { reason: "Introduction to PCM & RPM" },
  { reason: "Introduction to PCM & RTM" },
  { reason: "Introduction to CCM & RPM" },
  { reason: "Other" },
  {reason:'Medication Review & Reconciliation'},
  {reason:'Vitals Review (BP, Weight, Glucose)'},
  {reason:'Chart Review Before Call'},
  {reason:'Call After Abnormal RPM Reading'},
  {reason:'Coordination With Clinical Staff'},
  {reason:'Discussion With Medical Director'},
  {reason:'Care Coordination With PCP/Specialist'},
  {reason:'Care Plan Review and Goal Setting'},
  {reason:'Support for SDOH'},
  {reason:'Review of Asynchronous Messages'},
  {reason:'Device Setup or Tech Support'},
  {reason:'Monthly CCM Check-In'},
  {reason:'Lifestyle Coaching'}
];

export const deviceTypes  = [
  { type: "BP monitor" },
  { type: "Glucometer" }, 
  { type: "Weigh Scale" }, 
  { type: "Pulse Oximeter" },
   { type: "Smart inhaler" }
];