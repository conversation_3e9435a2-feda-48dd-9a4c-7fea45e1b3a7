import { Component, OnInit } from '@angular/core';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { TableLazyLoadEvent } from 'primeng/table';
import { debounceTime, Subject } from 'rxjs';
import { PatientInfo } from '../../../api/allPatients';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { AdminPatientsService } from './service/admin-patients.service';

@Component({
  selector: 'app-admin-patients',
  templateUrl: './admin-patients.component.html',
  styleUrls: ['./admin-patients.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminPatientsComponent implements OnInit {
  isLoading: boolean = false;
  constructor(
    public menuService: MenuService,
    private loaderService: LoaderService,
    private service: AdminPatientsService,
    public messageService: MessageService,
    public confirmService: ConfirmationService
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      this.debouncedValue = value;
      this.onSearch(this.debouncedValue);
    });
  }
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: string = '';

  //itemsPerPage: number = 15;
  patientList: PatientInfo[] = [];
  totalActivePatients: number = 0;
  loader: boolean = false;

  orgsList: any[] = [];
  clinicianList: any[] = [];
  filteredClinicians: any[] = [];
  filteredToClinicians: any[] = [];
  selectedOrg: any = null;

  selectedOrgId: number = 0;
  selectedClinicianId: number = 0;

  selectedRows: any[] = [];
  selectAll: boolean = false;
  selectionMode: string = 'multiple';

  fromClinican: any;
  fromClinicans: any;
  toClinican: any;
  globalSelectAll: boolean = false;
  rows: number = 15;
  ngOnInit() {
    this.menuService.changeMenu('Patients');
    this.getOrgsList();
  }

  loadActivePatients($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loader = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    let orgId = this.selectedOrgId ? this.selectedOrgId : '0';
    let cId = this.selectedClinicianId ? this.selectedClinicianId : '0';
    let url = pageNo + '/' + pageSize + '/' + cId + '/' + '0/' + orgId + '/' + 'Y';
    this.patientList = [];
    this.service.setActivePatientList(url).subscribe((res) => {
      this.loader = false;
      if (res.success) {
        this.patientList = res.patientsInfo!;
        this.itemsPerPage = $event?.rows || 15;
        this.patientList.forEach((patient) => {
          if (
            this.selectedRows.some(
              (selected) => selected.patientId === patient.patientId
            )
          ) {
            patient.selected = true;
          } else {
            patient.selected = false;
          }
        });
        if (res.patientsInfo.length!=-1) {
          this.updateSelectAllState();
          this.totalActivePatients = res.count!;
        }
        this.loader = false;
      }
      else {
          this.totalActivePatients = 0;
        this.patientList = [];
        this.itemsPerPage = $event?.rows || 15;
        this.updateSelectAllState();
        this.loader = false;
        this.selectAll=false;
      }
    }, err => {
        this.totalActivePatients = 0;
      this.patientList = [];
      this.itemsPerPage = $event?.rows || 15;
      this.updateSelectAllState();
      this.loader = false;
        this.selectAll=false;
    });
  }

  getOrgsList() {
    this.service.getAllOrgsClinicList().subscribe((res) => {
      if (res) {
        this.orgsList = res.orgList!;
        this.clinicianList = res.clinicianList!;
      }
    });
  }

  onInput(event: any): void {
    this.inputSubject.next(event.target.value);
  }

  searchTerm: string = '';
  onSearch(term: string): void {
    this.selectedOrg = null;
    this.fromClinican = null;
    this.selectedOrgId = 0;
    this.selectedClinicianId = 0;
    this.filteredClinicians = [];

    if (term != null && term != undefined && term.length > 2) {
      this.loader = true;
      this.service
        .setSearchPatientList(term + '?ptstatus=Y')
        .subscribe((res) => {
          this.loader = false;
          if (res.success) {
            this.patientList = res.patientsInfo!;
            this.totalActivePatients = res.count!;
            this.itemsPerPage = res.patientsInfo?.length || 15;
            this.loader = false;
          }
        });
    } else {

      this.searchTerm = term;
      this.debouncedValue = term;
      if (term != null && term != undefined && term.length == 0) {
        this.loadActivePatients({ first: 0, rows: this.itemsPerPage });
      }
    }
  }

  onClinicSelect() {
    if (this.searchText && this.searchText.length > 2) {
      this.searchText = '';
      this.itemsPerPage = 15;
    }
    console.log('Selected Org:', this.selectedOrg);
    this.debouncedValue = '';
    if (this.selectedOrg === null || this.selectedOrg === undefined) {
      this.filteredClinicians = [];
      this.selectedClinicianId = 0;
      this.selectedOrgId = 0;
      this.loadActivePatients({ first: 0, rows: this.itemsPerPage });
      return;
    }

    this.filteredClinicians = [];
    const orgId = this.selectedOrg?.id;
    this.selectedOrgId = orgId;
    for (let i = 0; i < this.clinicianList.length; i++) {
      if (this.clinicianList[i].orgId === orgId && this.clinicianList[i].status == 'Y') {
        const exists = this.filteredClinicians.find(
          (cm) => cm.id === this.clinicianList[i].id
        );
        if (!exists) {
          this.filteredClinicians.push(this.clinicianList[i]);
        }
      }
    }
    this.selectedClinicianId = 0;
    this.first = 0;
    this.fromClinican = null;
    this.loadActivePatients({ first: 0, rows: this.itemsPerPage });
  }

  onClinicianSelect(clinician: any) {
    this.fromClinicans = clinician;
    this.first = 0;
    this.selectedClinicianId = clinician?.id;
    this.loadActivePatients({ first: 0, rows: this.itemsPerPage });
  }

  onCheckboxSelect(patient: any) {
    if (patient.selected) {
      this.onRowSelect({ data: patient });
      this.updateSelectAllState();
    } else {
      this.onRowUnselect({ data: patient });
      this.selectAll = false;
      this.globalSelectAll = false;
    }
    // this.updateSelectAllState();
  }

  onEditCaseManager(patient: any) {
    this.selectAll = false;
    this.selectedRows = [];
    this.patientList.forEach((patient) => {
      patient.selected = false;
    });
    patient.selected = true;
    //this.onCheckboxSelect(patient);
    this.onRowSelect({ data: patient });
    this.onOpen()

  }
  onRowSelect(event: any) {
    const selectedPatient = event.data;
    selectedPatient.selected = true;
    if (
      !this.selectedRows.some(
        (patient) => patient.patientId === selectedPatient.patientId
      )
    ) {
      this.selectedRows.push(selectedPatient);
    }

    console.log('Row selected:', selectedPatient);
    console.log('Selected rows list:', this.selectedRows);
  }

  onRowUnselect(event: any) {
    const unselectedPatient = event.data;
    unselectedPatient.selected = false;
    this.selectedRows = this.selectedRows.filter(
      (patient) => patient.patientId !== unselectedPatient.patientId
    );

    console.log('Row unselected:', unselectedPatient);
    console.log('Updated selected rows list:', this.selectedRows);
  }

  selectAllRows(event: any) {
    if (event.checked) {
      this.globalSelectAll = true;
      this.patientList.forEach((patient) => {
        if (
          !this.selectedRows.some(
            (selected) => selected.patientId === patient.patientId
          )
        ) {
          this.selectedRows.push(patient);
          patient.selected = true;
        }
      });
    } else {
      this.globalSelectAll = false;
      this.patientList.forEach((patient) => {
        this.selectedRows = this.selectedRows.filter(
          (selected) => {
            selected.selected = false;
            selected.patientId !== patient.patientId

          }
        );
        patient.selected = false;
      });
    }

    console.log('Select all rows:', event.checked);
    console.log('Selected rows list:', this.selectedRows);
  }

  updateSelectAllState() {
    // this.selectAll = this.patientList.every((patient) => patient.selected);
    if (this.selectAll == true) {
      this.globalSelectAll = true
    }
    else {
      this.globalSelectAll = false
    }
    this.patientList?.every((patient) => this.globalSelectAll ? patient.selected = true : patient.selected = false);
    this.globalSelectAll ? this.selectAll = true : false
  }

  canTransfer(): boolean {
    return this.globalSelectAll || this.selectedRows.length > 0;
  }

  transeferPatients() {
    if (this.canTransfer()) {
      this.confirmService.confirm({
        message: `Are you sure you want to transfer patients \n FROM: ${this.fromClinicans.label} \nTO: ${this.toClinican.label} ?`,
        header: 'Confirmation',
        icon: 'pi pi-info-circle',
        acceptButtonStyleClass: 'p-button p-button-primary',
        rejectButtonStyleClass: 'p-button p-button-outlined p-button-primary',
        acceptIcon: 'none',
        rejectIcon: 'none',

        accept: () => {
          this.loaderService.show();
          const selectedPatientIds: number[] = this.selectedRows.map(
            (patient) => patient.patientId
          );
          let payload = {
            patientIds: this.globalSelectAll ? [] : selectedPatientIds,
            fromUserId: this.fromClinicans?.id,
            orgId: this.selectedOrgId,
            toUserId: this.toClinican?.id,
          };

          this.service.transferPatients(payload).subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Patient(s) transfered successfully.',
                life: 3000,
              });
              // this.loadActivePatients({ first: 0, rows: 5 });
              this.clearSelection();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          });
        },
        reject: () => { },
      });
    } else {
      console.error('No patients selected for transfer.');
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'No patients selected for transfer.',
      });
    }
  }

  clearSelection() {
    this.selectAll = false;
    this.selectedRows = [];
    this.loadActivePatients({ first: 0, rows: this.itemsPerPage });
  }
  onOpen() {
    this.toClinican = null;
    let filterData = this.filteredClinicians.filter((list: any) => list.id != this.fromClinicans.id);
    this.filteredToClinicians = filterData
  }
  faxDialogVisible: boolean = false;
  faxFromDate: Date | null = null;
  faxToDate: Date | null = null;

  onFax() {
    // Corrected logic: show error if NO clinic is selected OR no patients are selected
   if (!this.selectedOrg || (this.selectedRows.length === 0 && !this.globalSelectAll)) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Select patients to continue.',
        life: 2000
      });
      return;
    }
    this.faxDialogVisible = true;
    this.faxFromDate = null;
    this.faxToDate = null;
  }

  onFaxDialogSubmit() {
    if (!this.faxFromDate || !this.faxToDate) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select both From and To dates.',
        life: 2000
      });
      return;
    }
    const orgId = this.selectedOrg?.id;
    const patientIds = (this.selectedRows.length > 0 || this.globalSelectAll)
      ? this.selectedRows.map((p: any) => parseInt(p.patientId))
      : [];
    const payload = {
      orgId: orgId,
      patientId: patientIds,
      alertSeverity: null,
      startDate: this.faxFromDate ? this.faxFromDate.toISOString().split('T')[0] + 'T00:00:00.000' : null,
      endDate: this.faxToDate ? this.faxToDate.toISOString().split('T')[0] + 'T00:00:00.000' : null
    };
    this.isLoading = true;
    this.service.sendFax(payload).subscribe({
      next: (res) => {
        this.isLoading = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Fax',
          detail: 'Fax request sent successfully.',
          life: 2000
        });
        this.faxDialogVisible = false;
        this.faxFromDate = null;
        this.faxToDate = null;
      },
      error: () => {
        this.isLoading = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to send fax.',
          life: 2000
        });
      }
    });
  }

  onSubmit() {
    // Implement your submit logic here
    this.messageService.add({
      severity: 'info',
      summary: 'Submit',
      detail: 'Submit button clicked.',
      life: 2000
    });
  }
  previousRowsPerPage = 15;
  first = 0;
  itemsPerPage: number = 15;
  searchText: string = '';
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
      this.itemsPerPage = event.rows
    } else {
      this.first = event.first;
    }
  }
}
