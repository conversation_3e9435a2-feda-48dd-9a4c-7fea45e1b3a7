import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import {
  MedicationEHResp,
  MedicationResp,
  ResponseData,
} from '../../../../../../../api/medications';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class MedicationEHRService {
  constructor(private http: HttpClient) { }

  getMedicationInfo(details: any): Observable<MedicationEHResp[]> {
    return this.http.get<MedicationEHResp[]>(
      environment.BASE_URL + Constants.GET_MEDICATIONS + '/' + details,
    );
  }

  addorUpdateMedicationInfo(details: any): Observable<GenericResponse> {
    let url = Constants.ADD_MEDICATIONS;
    return this.http.post<GenericResponse>(environment.BASE_URL + url, details);
  }

  deleteMedicationInfo(id: any): Observable<GenericResponse> {
    return this.http.delete<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_MEDS+'/'+id
       );
  }
}
