export interface PatientListResp {
  success: boolean;
  responseCode: string | null;
  responsecode: string | null;
  messages: string[];
  patientsInfo: PatientInfo[];
  count: number;
}

export interface PatientInfo {
  patientId: string;
  imgPath: string | null;
  patientNamePhone: string;
  patientName: string;
  patientPhoneNumber: string;
  chronicConditions: ChronicCondition[];
  physicianName: string;
  caseManagerName: string;
  caseManagerId: number;
  mrnNo: string;
  groupName: string;
  selected: boolean;
}

export interface ChronicCondition {
  chronicConditionName: string;
}

export interface voice {
  identity: string;
  token: string;
  phoneNumber: string;
  programs: Program[];
  status: true;
}

export interface video {
  zoomToken: string;
  sessionName: string;
  userName: string;
  sessionPasscode:string;
  status: true;
}
export interface Program {
  id: number;
  label: string;
  value: string;
}
