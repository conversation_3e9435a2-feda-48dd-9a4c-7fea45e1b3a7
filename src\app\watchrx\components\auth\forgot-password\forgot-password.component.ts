import { Component } from '@angular/core';
import { AuthenticatorService } from '../service/authenticator.service';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
@Component({
    selector: 'app-access',
    templateUrl: './forgot-password.component.html',
    providers: [MessageService],
    
})
export class ForgotPwdComponent {

    constructor(private authenticatorService: AuthenticatorService, private router: Router, private service: MessageService) {
    }

    userName!: string;
    submitted: boolean = false;
    sendLink() {
        this.submitted = true;
        this.authenticatorService.sendResetPwdLink(this.userName).subscribe(
            res=>{
                console.log(res)
              if (res.status) {
                this.service.add({ key: 'tst', severity: 'info', summary: 'Success', detail: "Reset Password Link Send Successfully." });
              } else {
                this.service.add({ key: 'tst', severity: 'error', summary: 'Error', detail: "Failed to Send Reset Password Link." });
      
              }
            }
          )
    }
}
