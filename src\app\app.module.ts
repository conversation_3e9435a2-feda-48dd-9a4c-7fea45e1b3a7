import { LocationStrategy, PathLocationStrategy } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HttpRequestInterceptor } from './credential.interceptor';
import { AppLayoutModule } from './watchrx/layout/app.layout.module';
import { LoaderComponent } from "./watchrx/loader/loader/loader.component";
import { CountryService } from './watchrx/service/country.service';
import { CustomerService } from './watchrx/service/customer.service';
import { EventService } from './watchrx/service/event.service';
import { IconService } from './watchrx/service/icon.service';
import { NodeService } from './watchrx/service/node.service';
import { PhotoService } from './watchrx/service/photo.service';
import { ProductService } from './watchrx/service/product.service';
import { BnNgIdleService } from 'bn-ng-idle';
import { NoCacheHeadersInterceptor } from './no-cache-interceptor';
@NgModule({
  declarations: [AppComponent],
  imports: [AppRoutingModule, BrowserModule, AppLayoutModule, HttpClientModule, LoaderComponent],
  providers: [
    { provide: LocationStrategy, useClass: PathLocationStrategy },
    CountryService,
    CustomerService,
    EventService,
    IconService,
    NodeService,
    PhotoService,
    ProductService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpRequestInterceptor,
      multi: true,
    }, {
      provide: HTTP_INTERCEPTORS,
      useClass: NoCacheHeadersInterceptor,
      multi: true,
    },
    BnNgIdleService
  ],
  bootstrap: [AppComponent],
})
export class AppModule { }
