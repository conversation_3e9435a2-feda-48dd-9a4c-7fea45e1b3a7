import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { MenuService } from '../../service/menu.service';

@Component({
  selector: 'app-taskboard',
  templateUrl: './taskboard.component.html',
  styleUrl: './taskboard.component.scss',
})
export class TaskboardComponent implements OnInit {
  usage: string = 'all';
  @Input() fromSidebar = false;
  
  constructor(
    public menuService: MenuService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.menuService.changeMenu('Task Board');
    this.cdr.detectChanges();
  }
  
}
