import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { LoaderService } from '../../../loader/loader/loader.service';
import { CreateCasemanagersService } from './service/create-casemanagers.service';
import { CheckboxModule } from 'primeng/checkbox';
@Component({
  selector: 'app-create-case-manager',
  standalone: true,
  templateUrl: './create-case-manager.component.html',
  styleUrl: './create-case-manager.component.scss',
  imports: [
    ReactiveFormsModule,
    FormsModule,
    CalendarModule,
    DropdownModule,
    TableModule,
    CommonModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    AvatarModule,
    RadioButtonModule,
    InputNumberModule,
    ConfirmDialogModule,
    MultiSelectModule,
    ToastModule,
    ConfirmDialogModule,
    CheckboxModule
  ],
  providers: [LoaderService, MessageService],
})
export class CreateCaseManagerComponent implements OnInit {
  userForm: FormGroup = this.fb.group({});

  organizationList: any[] = [];
  @Output() submit = new EventEmitter<any>();
  @Output() cancel = new EventEmitter<any>();

  @Input() roleTypeList: any[] = [];
  @Input() isOrgNeeded: boolean = true;
  //enabledMultiFactor: boolean = true;
  constructor(
    private fb: FormBuilder,
    public createManagerService: CreateCasemanagersService,
    public loaderService: LoaderService,
    private messageService: MessageService
  ) { }

  ngOnInit(): void {
    this.userForm = this.fb.group(
      {
        firstname: ['', Validators.required],
        lastname: ['', Validators.required],
        email: ['', [Validators.required, Validators.email]],
        phoneNumber: [''],
        password: [
          '',
          [
            Validators.required,
            Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d|\W).+$/),
          ],
        ],
        confirmpassword: ['', Validators.required],
        roleType: ['',Validators.required],
        organization: ['',Validators.required],
        enabledMultiFactor: [true]
      },
      {
        validators: this.passwordMatchValidator,
      }
    );

    this.getGroups();
    //setTimeout(() => { this.enabledMultiFactor = true }, 30000)

  }
  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmpassword = form.get('confirmpassword');
    return password &&
      confirmpassword &&
      password.value === confirmpassword.value
      ? null
      : { mismatch: true };
  }

  getGroups() {
    this.createManagerService.getGroupsandRolesList().subscribe((res) => {
      this.organizationList = res.groupsAndAssignedRoles;
    });
  }
  loading: boolean = false;
  onSubmit() {
    this.loading = true;
    const roleType = this.userForm.get('roleType')?.value;
    if (roleType?.value === null || roleType?.value === undefined) {
      this.loading = false;
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Role is required',
      });
      return;
    }
    const orgList: any[] = this.userForm.get('organization')?.value;

    if (this.isOrgNeeded && (orgList === undefined || orgList?.length === 0)) {
      this.loading = false;
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Select organization',
      });
      return;
    }
    let idsList: any[] = [];
    if (this.isOrgNeeded) {
      idsList = orgList.map((object) => object.groupId);
    }

    let payload = {
      firstName: this.userForm.get('firstname')?.value,
      lastName: this.userForm.get('lastname')?.value,
      userName: this.userForm.get('email')?.value,
      phoneNumber: this.userForm.get('phoneNumber')?.value,
      password: this.userForm.get('password')?.value,
      userRole:
        roleType?.value === 'Case Manager'
          ? 'caseManager'
          : roleType?.value === 'Care Giver'
            ? 'caregiver'
            : roleType?.value === 'Provider'
              ? 'provider'
              : roleType?.value === 'Admin'
                ? 'admin'
                : roleType?.value === 'Physician'
                  ? 'physician'
                  : roleType?.value,
      groups: idsList,
      otpEnabled: this.userForm.get('enabledMultiFactor')?.value == true ? 'Y' : 'N',
    };

    this.loaderService.show();
    this.createManagerService.createCaseManager(payload).subscribe((res) => {
      this.loading = false;
      this.userForm.reset();
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Account created successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Error while account creation',
        });
      }
      setTimeout(() => { this.submit.emit(); }, 1000);
    }, err => {
      console.log(err)
       this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Error while account creation',
        });
      this.loading = false;
    });
  }

  reasonChange() { }

  onCancel() {
    this.resetForm();
    this.cancel.emit();
  }

  resetForm() {
    this.userForm.reset();
    this.userForm.get('enabledMultiFactor')?.setValue(true)
  }

  onMultiFactorChange(event: any) {
    this.userForm.get('enabledMultiFactor')?.setValue(event.checked);
  }
}
