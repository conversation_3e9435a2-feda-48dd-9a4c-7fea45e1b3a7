import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../../environments/environment';
import { GenericResponse } from '../../../../../../../api/editPatientProfile';
import { ApiResponse } from '../../../../../../../api/phoneComm';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class PhoneCommService {
  constructor(private http: HttpClient) {}

  getPhoneCommLogs(details: any): Observable<ApiResponse> {
    return this.http.get<ApiResponse>(
      environment.BASE_URL + Constants.GET_GET_PHONE_COMM + details
    );
  }

  addPhoneCommLogs(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_PHONE_COMMS,
      details
    );
  }

  deletePhoneCommLogs(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_PHONE_COMMS,
      details
    );
  }
}
