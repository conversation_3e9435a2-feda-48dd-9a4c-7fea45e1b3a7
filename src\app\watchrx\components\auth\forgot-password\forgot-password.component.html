<div
  class="surface-ground flex min-h-screen overflow-hidden p-hidden-sm p-hidden-xs"
  style="background-color: #f7f8fc !important"
>
  <div class="flex-1 position-relative flex align-items-center">
    <div class="flex align-items-center">
      <img
        src="assets/layout/images/login-logo.png"
        alt="Image"
        height="400"
        class="mr-3"
      />
      <div>
        <p
          class="m-0"
          style="
            color: #0047cc;
            font-size: 40px !important;
            font-weight: 400;
            line-height: 46px;
          "
        >
          Simplify Digital Healthcare​
        </p>
        <p
          style="
            font-size: 18px !important;
            font-style: italic;
            padding-right: 59px;
            text-align: justify;
            margin-top: 15px !important;
          "
          class="m-0"
        >
          WatchRx is at the forefront of digital healthcare transformation and
          virtual care.With our smartwatch and AI/ML enabled platform, you can
          increase patient engagement and care coordination to improve patient
          outcomes, personalize care, and reduce healthcare costs.
        </p>
      </div>
    </div>
  </div>
  <div
    class="flex flex-column align-items-center justify-content-center"
    style="height: 100vh; width: 30%"
  >
    <div
      class="w-full surface-card py-5 px-5 sm:px-5 h-full"
      style="
        background-color: #ffffff !important;
        padding: 20px;
        border-radius: 10px;
        margin-right: 20px;
        margin-top: 15px;
        margin-bottom: 15px;
        box-shadow: 4px 4px 4px 4px rgba(0, 0, 0, 0.25);
        box-shadow: 1px 0px 18px -9px rgba(0, 0, 0, 0.25);
      "
    >
      <div class="text-center mb-5">
        <img
          src="assets/layout/images/watchrx-full.jpg"
          alt="Image"
          height="100"
          class="mb-3"
        />
      </div>

      <div>
        <label for="email1" class="block text-900 text-xl font-medium mb-2"
          >Enter your registered email to send reset password link.</label
        >
        <input
          id="email1"
          [(ngModel)]="userName"
          type="text"
          placeholder="Username"
          pInputText
          class="w-full mb-5"
          style="padding: 1rem"
        />
        <small class="ng-dirty ng-invalid" *ngIf="submitted && !userName"
          >Email is required.</small
        >
        <div class="flex align-items-center justify-content-between mb-5 gap-5">
          <div class="flex align-items-center"></div>
          <a
            class="font-medium no-underline ml-2 text-right cursor-pointer"
            style="color: var(--primary-color)"
            [routerLink]="['/login']"
            >Do you want login?</a
          >
        </div>
        <button
          pButton
          pRipple
          label="Send Link"
          class="w-full p-3 text-xl"
          (click)="sendLink()"
        ></button>
      </div>
    </div>
  </div>
</div>
<p-toast key="tst"></p-toast>
