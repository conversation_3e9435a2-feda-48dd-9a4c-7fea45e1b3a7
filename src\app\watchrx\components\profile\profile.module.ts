import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ProfileRoutingModule } from './profile-routing.module';
import { ChartModule } from 'primeng/chart';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelMenuModule } from 'primeng/panelmenu';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { DialogRoutingModule } from '../dialog/dialog-routing.module';
import { PasswordResetComponent } from './components/password-reset/password-reset.component';
import { ProfileComponent } from './components/profile/profile.component';
import { ConfirmationService, MessageService } from 'primeng/api';
import { FileUploadModule } from 'primeng/fileupload';
import { PasswordModule } from 'primeng/password';


@NgModule({
  declarations: [PasswordResetComponent,ProfileComponent],
  imports: [
    CommonModule,
    ProfileRoutingModule,
        FormsModule,
        ChartModule,
        MenuModule,
        TableModule,
        StyleClassModule,
        PanelMenuModule,
        ButtonModule,
        TableModule,
        InputGroupModule,
        InputGroupAddonModule,
        DropdownModule,
        CalendarModule,
        DialogRoutingModule,
        InputTextModule,
        MultiSelectModule,
        AutoCompleteModule,
        FormsModule,
        ReactiveFormsModule,
        ToastModule,
        FileUploadModule,
        PasswordModule

  ],
  providers: [ConfirmationService, MessageService],
})
export class ProfileModule { }
