import { CommonModule, formatDate } from '@angular/common';
import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { PatientDocument, Program } from '../../../../../../api/documents';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { convertToMins } from '../../../../../../utils/utils';
import { DocumentationService } from './service/documentation.service';
import { SidebarModule } from 'primeng/sidebar';
import { Editor, EditorModule } from 'primeng/editor';
import { InputTextModule } from 'primeng/inputtext';

@Component({
  selector: 'app-documentation',
  standalone: true,
  imports: [
    ButtonModule,
    TableModule,
    DialogModule,
    RadioButtonModule,
    FormsModule,
    CommonModule,
    CalendarModule,
    InputNumberModule,
    ToastModule,
    ConfirmDialogModule,
    SidebarModule,
    EditorModule,
    InputTextModule
  ],
  templateUrl: './documentation.component.html',
  styleUrl: './documentation.component.scss',
  providers: [ConfirmationService, MessageService],
})
export class DocumentationComponent implements OnInit {
  documentationId: number = 0;
  patientId: number = -1;
  visible: boolean = false;

  documentationList: PatientDocument[] = [];
  totalCount: number = 0;
  programList: Program[] = [];

  selectedProgram: string = '';
  noteDescription: string = '';
  maxLength: number = 1000;
  callStartTime: Date = new Date();
  durationInMins: number = 0;
  isEdit: boolean = false;
  title="Add New Record"
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    private documentService: DocumentationService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
  }

  loadDocumentationList($event: LazyLoadEvent | TableLazyLoadEvent) {
    this.loaderService.show();
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;
    let payload = this.patientId + '/' + pageNo + '/' + pageSize;
    this.documentService.getDocumentation(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.dataEmitter.emit(res?.minsDetails);
        this.documentationList = res?.patientDocumentationVOList;
        this.totalCount = res?.count;
        this.programList = res?.programs;
      }
    });
  }

  addDocumentation() {
    if (this.selectedProgram === undefined || this.selectedProgram === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select program',
        life: 3000,
      });
      return;
    }

    if (this.noteDescription === undefined || this.noteDescription === '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please add notes',
        life: 3000,
      });
      return;
    }

    let payload: any = {
      note: this.noteDescription,
      patientId: this.patientId,
      documentedDate: formatDate(
        this.callStartTime,
        'yyyy-MM-dd 00:00:00',
        'en-US'
      ),
      duration: this.durationInMins,
      createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
      review: this.selectedProgram,
    };
    if (this.isEdit) {
      payload.documentationId = this.documentationId;
    }
    this.loaderService.show();
    this.documentService.addDocumentation(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.visible = false;
        this.loadDocumentationList({ first: 0, rows: 25 });
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Notes added successfully.',
          life: 3000,
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to create notes',
          life: 3000,
        });
      }
    });
  }

  deleteDocument(event: Event, documentationId: number) {
    console.log('Delete clicked....', documentationId);
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = {
          documentationId: documentationId,
        };
        this.documentService.deleteDocumentation(payload).subscribe((res) => {
          if (res.success) {
            this.loadDocumentationList({ first: 0, rows: 25 });
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Notes deleted successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete Notes.',
            });
          }
        });
      },
      reject: () => {},
    });
  }

  resetFields() {
    this.selectedProgram = '';
    this.noteDescription = '';
    this.maxLength = 1000;
    this.callStartTime = new Date();
    this.durationInMins = 0;
    this.documentationId = 0;
  }

  editNotes(item: PatientDocument) {
      this.title="Edit Record"
    this.resetFields();
    this.visible = true;
    this.isEdit = true;
    this.noteDescription = item?.note;
    this.callStartTime = this.convertStringToDate(item?.documentedDate);
    this.durationInMins = item?.duration;
    this.documentationId = item?.documentationId;
    this.selectedProgram=item?.review||'';
  }

  convertStringToDate(dateTime: string): Date {
    const [datePart, timePart] = dateTime.split(' ');
    const [month, day, year] = datePart.split('-').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);

    return new Date(year, month - 1, day, hours, minutes, seconds);
  }

  formattedTime(val: number): string {
    return convertToMins(val);
  }

  showDialog(): void {
     this.title="Add New Record"
    this.resetFields();
    this.visible = !this.visible;
    this.isEdit = false;
  }
  // checkCharacterLimit() {
  //   // Create a temporary div to parse the HTML and get plain text
  //   const tempDiv = document.createElement('div');
  //   tempDiv.innerHTML = this.noteDescription;
  //   const plainText = tempDiv.textContent || tempDiv.innerText;
    
  //   // Calculate remaining characters based on plain text
  //   this.remainingCharacters = this.maxCharacters - plainText.length;
    
  //   // If we've exceeded the limit, revert to the previous valid content
  //   if (plainText.length > this.maxCharacters) {
  //     this.noteDescription = this.previousNoteDescription;
      
  //     // Recalculate after reverting
  //     tempDiv.innerHTML = this.noteDescription;
  //     const updatedPlainText = tempDiv.textContent || tempDiv.innerText;
  //     this.remainingCharacters = this.maxCharacters - updatedPlainText.length;
  //   } else {
  //     // Store the current valid content for potential future reversion
  //     this.previousNoteDescription = this.noteDescription;
  //   }
  // }
  
  maxCharacters: number = 1000;
  remainingCharacters: number = this.maxCharacters;
  
// Use this configuration option for p-editor
editorConfig = {
  modules: {
    keyboard: {
      bindings: {}
    }
  }
}

// Add this to your component
@ViewChild('editor') editor: any;
 checkCharacterLimit1(event:any) { 
  console.log(event,"event") 
  const tempDiv = document.createElement('div');  
  tempDiv.innerHTML = event.textValue;
  const plainText = tempDiv.textContent || tempDiv.innerText || '';  
  this.remainingCharacters = Math.max(0, this.maxCharacters - plainText.length);  
  if (plainText.length > this.maxCharacters) {  
    if (this.editor && this.editor.getQuill) {  
      const quill = this.editor.getQuill();  
      const range = quill.getSelection(); 
      const currentIndex = range ? range.index : 0; 
      const truncatedText = plainText.slice(0, this.maxCharacters);  
      quill.setText(truncatedText); 
      this.noteDescription = quill.root.innerHTML; 
      const newLength = truncatedText.length;  
      if (range) {  
        quill.setSelection(Math.min(currentIndex, newLength), 0);   
      }  
      this.remainingCharacters = Math.max(0, this.maxCharacters - truncatedText.length);  
    }  
  }  
}  
}
