const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getDesktopSources: () => ipcRenderer.invoke('get-desktop-sources'),
  getSystemAudioStream: (sourceId) => ipcRenderer.invoke('get-system-audio-stream', sourceId),
  platform: process.platform,
  versions: {
    node: process.versions.node,
    chrome: process.versions.chrome,
    electron: process.versions.electron
  },
  getEnhancedMediaStream: async (constraints) => {
    try {
      if (constraints.video && constraints.video.mandatory && constraints.video.mandatory.chromeMediaSource === 'desktop') {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        return stream;
      }
      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      throw error;
    }
  }
});

window.addEventListener('DOMContentLoaded', () => {
  if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
    const originalGetDisplayMedia = navigator.mediaDevices.getDisplayMedia.bind(navigator.mediaDevices);
    
    navigator.mediaDevices.getDisplayMedia = async function(constraints) {
      try {
        console.log('🎯 getDisplayMedia called with constraints:', constraints);
        const sources = await window.electronAPI.getDesktopSources();
        console.log('📺 Available sources:', sources.length);

        if (sources && sources.length > 0) {
          const screenSource = sources.find(source => source.id.startsWith('screen:')) || sources[0];
          console.log('🖥️ Selected source:', screenSource?.name, screenSource?.id);

          if (screenSource) {
            const electronConstraints = {
              audio: constraints.audio ? {
                mandatory: {
                  chromeMediaSource: 'desktop',
                  chromeMediaSourceId: screenSource.id,
                  echoCancellation: false,
                  noiseSuppression: false,
                  autoGainControl: false
                }
              } : false,
              video: constraints.video ? {
                mandatory: {
                  chromeMediaSource: 'desktop',
                  chromeMediaSourceId: screenSource.id,
                  minWidth: constraints.video.width || 1,
                  maxWidth: constraints.video.width || 1920,
                  minHeight: constraints.video.height || 1,
                  maxHeight: constraints.video.height || 1080
                }
              } : false
            };

            console.log('🔧 Using electron constraints:', electronConstraints);
            const stream = await window.electronAPI.getEnhancedMediaStream(electronConstraints);
            console.log('🎵 Got stream with tracks:', stream.getTracks().map(t => `${t.kind}: ${t.label}`));
            return stream;
          }
        }

        console.log('⚠️ Falling back to original getDisplayMedia');
        return await originalGetDisplayMedia(constraints);

      } catch (error) {
        console.error('❌ getDisplayMedia error:', error);
        console.log('⚠️ Falling back to original getDisplayMedia due to error');
        return await originalGetDisplayMedia(constraints);
      }
    };
  }
});