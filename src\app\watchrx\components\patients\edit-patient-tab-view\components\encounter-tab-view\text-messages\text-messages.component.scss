.custom-textarea {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

.dialogbuttons {
    position: absolute;
    top: -59px;
    right: 10px;
}

::ng-deep .textmessagescreen {
    .p-sidebar-content {
        padding: 1.25rem;
    }
}
::ng-deep .footer1
{
    position: absolute;
    bottom: 23px;
    padding-left: 13px;
}
.chat-container {
    width: 100%;
    max-width: 100%;
    margin: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    background: #f9f9f9;
    display: flex;
    flex-direction: column;
  }
  
  .chat-date {
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    margin: 10px 0;
    color: #666;
  }
  
  .chat-message {
    max-width: 100%;
    padding: 8px;
    border-radius: 8px;
    margin: 5px;
    display: flex;
    flex-direction: column;
  }
  
  .chat-message.server {
    background: #e0e0e0;
    color: #000000;
    align-self: flex-end;
    text-align: left;
    max-width: 50%;
    word-wrap: break-word;
  }
  
  .chat-message.user {
    background: #e0e0e0;
    color: #000000;
    align-self: flex-start;
    text-align: left;
    max-width: 50%;
    word-wrap: break-word;
  }
  
  .message-text {
    font-size: 14px;
  }
  
  .message-time {
    font-size: 10px;
    margin-top: 4px;
    opacity: 0.7;
  }
  
  .chat-input {
    display: flex;
    margin-top: 10px;
    padding: 5px;
    background: white;
    border-top: 1px solid #ddd;
  }
  
  .chat-input input {
    flex-grow: 1;
    border: none;
    padding: 8px;
    outline: none;
  }
  
  .chat-input button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
  }
  
  .chat-input button i {
    font-size: 16px;
  }
  .chatbox
  {
    min-height: 200px;
    max-height:400px;
    overflow: auto;
    height: 400px;
  }
  .chat-container1
  {
    display: flex;
        flex-direction: column-reverse;
  }