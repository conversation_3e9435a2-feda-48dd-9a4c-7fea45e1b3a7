import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { CaseManagerListResp } from '../../../../api/adminCaseManager';
import { Constants } from './constants';
import { GenericResponse } from '../../../../api/editPatientProfile';

@Injectable({
  providedIn: 'root',
})
export class SecondaryAdminService {
  constructor(private http: HttpClient) {}

  getAdminList(details: string): Observable<CaseManagerListResp> {
    return this.http.get<CaseManagerListResp>(
      environment.BASE_URL + Constants.SECONDARY_ADMIN_LIST + details
    );
  }

  toggleAdminStatus(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.CHANGE_STATUS_CASEMANAGER,
      details
    );
  }
}
