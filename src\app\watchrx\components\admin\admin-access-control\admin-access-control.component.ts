import { Component, OnInit } from '@angular/core';
import { MenuService } from '../../../service/menu.service';

@Component({
  selector: 'app-admin-access-control',
  templateUrl: './admin-access-control.component.html',
  styleUrl: './admin-access-control.component.scss',
})
export class AdminAccessControlComponent implements OnInit {
  activeTabIndex: any = 0;

  constructor(public menuService: MenuService) { }

  ngOnInit() {
    this.menuService.changeMenu('Access Control');
    if (this.activeTabIndex = localStorage.getItem('adminaccesstab')) {
      this.activeTabIndex = localStorage.getItem('adminaccesstab')
    }
    else {
      this.activeTabIndex = 0
    }
  }

  tabChange(event: any) {
    console.log(event);
    localStorage.setItem('adminaccesstab', event.index)
  }
}
