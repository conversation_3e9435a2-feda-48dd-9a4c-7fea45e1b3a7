import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SafeHtml } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { Role, User } from '../../../api/user';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { EditPatientDashboardService } from './components/dashboard/service/edit-patient-dashboard.service';
import { CommonService } from '../../../service/common.service';
import { interval, Subscription } from 'rxjs';
import { ProfileComponent } from './components/profile/profile.component';

@Component({
  selector: 'app-edit-patient-tab-view',
  templateUrl: './edit-patient-tab-view.component.html',
  styleUrl: './edit-patient-tab-view.component.scss',
})
export class EditPatientTabViewComponent implements OnInit {
  activeIndex: any = -1;
  patientId: number = -1;
  patientName: string = '';
  showMins: boolean = false;
  safeHtml: SafeHtml | undefined;
  patientStatus: string = 'Active';
  pStatus: boolean = true;
  connectingDevice: string = '';
  roles: Role[] | undefined = [];
  patientInformation: any;
  private timerSubscription: Subscription = new Subscription();
  public elapsedSeconds: number = 0;
  public displayTime: string = '00h:00m:00s';
  public unFormetedTime: string = '00:00:00';
  openVoiceVideoDlg: boolean = false;
  openVideoDlg: boolean = false;
  userRole: any;
  user: any;
  @ViewChild(ProfileComponent) profileComponent!: ProfileComponent;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    public menuService: MenuService,
    private sanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
    private patientService: EditPatientDashboardService,
    private loaderService: LoaderService,
    private confirmService: ConfirmationService,
    private messageService: MessageService,
    private commonService: CommonService
  ) { }

  ngOnInit(): void {
    this.commonService.currentData.subscribe(async (message) => {
      if (message) {
        await this.getPatientProfileInfo();
      }

    });
    this.activeIndex = localStorage.getItem('mainTab')
      ? parseInt(localStorage.getItem('mainTab') as string, 10)
      : 0;
    this.menuService.changeMenu('Patients');
    this.route.queryParams.subscribe(async (params) => {
      this.patientId = params['id'];
      await this.getPatientProfileInfo();
    });
    this.cdr.detectChanges();
    let user: User = JSON.parse(localStorage.getItem('user')!);

    let features = user.featuresEnabled.find(
      (feature) => feature.groupId === user.orgId
    );
    this.roles = features?.roles;

    this.timerSubscription = interval(1000).subscribe(() => {
      this.elapsedSeconds++;
      this.updateDisplayTime();
    });
    if (localStorage.getItem('user')) {
      this.user = localStorage.getItem('user');
      this.userRole = JSON.parse(this.user)?.roleType;
    }
  }

  updateDisplayTime() {
    const hours = Math.floor(this.elapsedSeconds / 3600);
    const minutes = Math.floor((this.elapsedSeconds % 3600) / 60);
    const seconds = this.elapsedSeconds % 60;
    this.displayTime = `${this.formatTime(hours)}h:${this.formatTime(minutes)}m:${this.formatTime(seconds)}s`;
    this.unFormetedTime = `${this.formatTime(hours)}:${this.formatTime(minutes)}:${this.formatTime(seconds)}`;
  }

  formatTime(value: number): string {
    return value < 10 ? `0${value}` : `${value}`;
  }
  isNameFound = (nameToFind: string) => {
    return this.roles?.some((role) => role.roleName === nameToFind) || true;
  };

  // pendingIndex: number | null = null; // tab user wants to switch to
  // onMainTabClick(e: any) {   
  //   let newIndex1:any = e;
  //   if (newIndex1 !== this.activeIndex) {
  //     this.pendingIndex = newIndex1;
  //     // Revert to current tab temporarily
  //     this.activeIndex = this.activeIndex; // to keep current tab visible
  //     // Show confirmation dialog if the tabs are different
  //     this.confirmService.confirm({
  //       message: 'Are you sure you want to leave this page?',
  //       header: 'Confirmation',
  //       icon: 'pi pi-info-circle',
  //       acceptButtonStyleClass: 'p-button-danger p-button-text',
  //       rejectButtonStyleClass: 'p-button-text p-button-text',
  //       acceptIcon: 'none',
  //       rejectIcon: 'none',
  //       accept: () => {
  //         this.activeIndex = e.index;
  //         localStorage.setItem('mainTab', this.activeIndex);
  //         localStorage.removeItem('subTab');
  //       },
  //       reject: () => {
  //         this.activeIndex = 1; 
  //       },
  //     })
  //   }
  //  // const newIndex = e.index;
  //   //if (newIndex === this.activeIndex) return;

  //   if (this.activeIndex != 1) {
  //     if (this.profileComponent.unloadScreen() == false) {

  //       this.confirmService.confirm({
  //         message: 'Are you sure you want to leave this page?',
  //         header: 'Confirmation',
  //         icon: 'pi pi-info-circle',
  //         acceptButtonStyleClass: 'p-button-danger p-button-text',
  //         rejectButtonStyleClass: 'p-button-text p-button-text',
  //         acceptIcon: 'none',
  //         rejectIcon: 'none',
  //         accept: () => {
  //           this.activeIndex = e.index;
  //           localStorage.setItem('mainTab', this.activeIndex);
  //           localStorage.removeItem('subTab');
  //         },
  //         reject: () => {
  //           this.activeIndex = 1; 
  //         },
  //       })
  //     }
  //     else
  //     {
  //       this.activeIndex = e.index;
  //       localStorage.setItem('mainTab', this.activeIndex);
  //       localStorage.removeItem('subTab');
  //     }
  //   }
  //   else {
  //     this.activeIndex = e.index;
  //     localStorage.setItem('mainTab', this.activeIndex);
  //     localStorage.removeItem('subTab');
  //   }
  // }
  onMainTabClick(e: any) {
    this.activeIndex = e.index;
    localStorage.setItem('mainTab', this.activeIndex);
    localStorage.removeItem('subTab');
  }

  handleDataFromChild(data: any) {
    console.log('Received data from child:', data);
    if (data && data?.length > 0) {
      this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(data);
      this.cdr.detectChanges();
      if (!this.showMins) {
        this.showMins = true;
      }
    } else {
      this.showMins = false;
      this.cdr.detectChanges();
    }
  }
  async getPatientProfileInfo() {
    let req = {
      patientId: this.patientId,
    };
    this.loaderService.show();
    this.patientService.getPatientProfile(req).subscribe((res) => {
      this.loaderService.hide();
      this.patientName = res?.firstName + ' ' + res?.lastName;
      this.patientStatus = res?.status;
      this.connectingDevice = res?.connectingDevice;
      this.patientInformation = res;
      if (this.patientStatus === 'Active') {
        this.pStatus = true;
      } else if ((this.patientStatus = 'InActive')) {
        this.pStatus = false;
      }
    });
  }

  resendOtp(event: Event) {
    console.log('Clicked for OTP');
    this.confirmService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to send OTP?',
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',

      accept: () => {
        this.loaderService.show();
        this.patientService.resendOtp({ patientId: this.patientId }).subscribe(
          (response: string) => {
            this.loaderService.hide();
            console.log('API Response:', response);
            this.messageService.add({
              severity: 'success',
              summary: 'Confirmed',
              detail: 'OTP sent successfully.',
              life: 3000,
            });
          },
          (error) => {
            this.loaderService.hide();
            console.error('API Error:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Failed to send OTP.',
              life: 3000,
            });
          }
        );
      },
      reject: () => { },
    });
  }

  onChangeStatus(status: boolean) {
    console.log(status, 'ssssssssssss');
    let updateStatus = '';
    if ('Active' === this.patientStatus) {
      updateStatus = 'InActive';
    } else {
      updateStatus = 'Active';
    }
    this.confirmService.confirm({
      message: `Are you sure you want to change the patient status to ${updateStatus}?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.patientService
          .updatePatientStatus(
            `patientId=${this.patientId}&status=${updateStatus}`
          )
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Status updated successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.router.navigate(['/patients']);
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to update status.',
                life: 3000,
              });
            }
          });
      },
      reject: () => {
        if (this.patientStatus === 'Active') {
          this.pStatus = true;
        } else {
          this.pStatus = false;
        }
      },
    });
  }

  onDeletePatient() {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.patientService
          .deletePatient({ patientId: this.patientId })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Patient deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.router.navigate(['/patients']);
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail:
                  'PatientAlerts/PatientVitals/Device Allocation details exists for the patient',
                life: 3000,
              });
            }
          });
      },
      reject: () => { },
    });
  }
  getFormattedDate(
    dateOfBirth: string | undefined,
    format: string | 'YYYY-MMM-DD'
  ): string {
    if (dateOfBirth === null || dateOfBirth === undefined) {
      return '';
    }
    return moment(dateOfBirth).format(format);
  }

  getTotalYears(dateOfBirth: string | undefined): string {
    if (dateOfBirth === null || dateOfBirth === undefined) {
      return '';
    }
    return moment().diff(moment(dateOfBirth), 'years') + ' Years';
  }
  convert(val: number): string {
    const minutes: number = Math.floor(val);
    const seconds: number = Math.round((val - minutes) * 60);

    return `${minutes}m:${seconds}s`;
  }

  ngOnDestroy() {
    // Stop the timer
    if (this.timerSubscription) {
      this.timerSubscription.unsubscribe();
    }
    let user = JSON.parse(this.user);
    console.log('Stopping timer for user:', user?.userId);
    this.patientService?.stopTimerAPI(user?.userId, this.patientId, this.unFormetedTime).subscribe(response => {
      console.log('Timer stopped, API response:', response);
    });
  }

  showCallWindow() {
    this.openVoiceVideoDlg = true;
  }
  showVideoCallWindow() {
    this.openVideoDlg = true;
  }
}
