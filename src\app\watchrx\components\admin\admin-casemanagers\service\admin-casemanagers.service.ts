import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { CaseManagerListResp } from '../../../../api/adminCaseManager';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { APIConfig } from './APIConfig';
@Injectable({
  providedIn: 'root',
})
export class AdminCasemanagersService {
  constructor(private http: HttpClient) {}

  getManagersList(details: string): Observable<CaseManagerListResp> {
    return this.http.get<CaseManagerListResp>(
      environment.BASE_URL + APIConfig.ADMIN_CASEMANAGER_LIST + details
    );
  }
  deletePatient(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.DELETE_CASEMANAGER,
      details
    );
  }
  toggleCaseManagerStatus(details: any) {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + APIConfig.CHANGE_STATUS_CASEMANAGER,
      details
    );
  }

   searchCasemanager(details:any)
    {
        return this.http.get<any>(
          environment.BASE_URL + APIConfig.SEARCH_CASEMANAGER+details
        );
    }
}
