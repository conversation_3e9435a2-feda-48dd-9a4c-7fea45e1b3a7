import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, NgModel } from '@angular/forms';
import { LazyLoadEvent, MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { debounceTime, Subject } from 'rxjs';
import { PatientsInfo } from '../../../../../api/dashboard';
import { AllocateDevice } from '../../../../../api/deviceAllocation';
import { MenuService } from '../../../../../service/menu.service';
import { MedicalDeviceService } from '../../service/medical-device.service';
import { SidebarModule } from 'primeng/sidebar';
import { CheckboxModule } from 'primeng/checkbox';

@Component({
  standalone: true,
  selector: 'app-unallocated-medical-devices',
  templateUrl: './unallocated-medical-devices.component.html',
  styleUrls: ['./unallocated-medical-devices.component.css'],
  imports: [
    CommonModule,
    FormsModule,
    AutoCompleteModule,
    TableModule,
    ToastModule,
    ConfirmDialogModule,
    DialogModule,
    SidebarModule,
    CheckboxModule
  ],
})
export class UnallocatedMedicalDevicesComponent implements OnInit {
  response: any = {};
  showEdit: boolean = false;
  orgName: string = '';
  orgId: number = 0;
  allocationId: number = 0;
  make: string = '';
  model: string = '';
  deviceId: string = '';
  phoneNumber: string = '';

  selectedPatient: any | undefined;
  patientList: PatientsInfo[] = [];
  filteredPatients: any[] = [];
  loadingdevices: boolean = true;
  totalDeviceCount: number = 0;
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: string = '';
  features: any[] = [{ name: 'BP', selected: false },
  { name: 'SPO2', selected: false },
  { name: 'Pedometer', selected: false },
  { name: 'Sleep Monitor', selected: false },
  { name: 'Heart Rate', selected: false }]
  filteredFeatures: any[] = []
  selectedFeatures: any[] = [];
  searchText: string = '';
  rows:number=15;
  constructor(
    private deviceService: MedicalDeviceService,
    private messageService: MessageService,
    private menuService: MenuService
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      this.debouncedValue = value;
      console.log('Search Value:', this.debouncedValue);
      this.onSearch(this.debouncedValue);
    });
  }

  ngOnInit() {

    this.menuService.changeMenu('Medical Devices');
  }

  filterPatient(event: any) {
    const query = event.query.toLowerCase();
    this.filteredPatients = this.patientList.filter((p) =>
      p.patientName?.toLowerCase().includes(query)
    );
  }

  filterFeatures(event: any) {
    const query = event.query.toLowerCase();
    this.filteredFeatures = this.features.filter(feature =>
      feature.toLowerCase().includes(query)
    );
  }

  loadDevices($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loadingdevices = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize;
    this.deviceService.setUnAllocatedDevice(url).subscribe((res) => {
      if (res.success) {
        this.response = res.purchasedWatchList;
        this.totalDeviceCount = res.resultCount;
        this.loadingdevices = false;
      }
    });
  }

  openModal(device: any) {

    this.showEdit = !this.showEdit;
    this.deviceService
      .getPatientByOrg(device?.groupVo?.groupId)
      .subscribe((res) => {
        if (res.success) {
          this.patientList = res.patientMinimalVOList!;
        }
      });
    this.orgId = device?.groupVo?.groupId;
    this.allocationId = device?.allocationId;
    this.orgName = device?.groupVo?.groupName;
    this.make = device?.sourceInventoryForWatchPurchase?.make;
    this.model = device?.sourceInventoryForWatchPurchase?.model;
    this.deviceId = device?.watchAllocatedByAdmin?.imei;
    

  }

  onOpen(patientref:NgModel, deviceref:NgModel)
  {
    this.selectedPatient= undefined;
    patientref.control.markAsUntouched();
    patientref.control.markAsPristine();
    this.deviceId= '';
    deviceref.control.markAsUntouched();
    deviceref.control.markAsPristine();
    this.features.forEach((feature) => {
      feature.selected = false;
    })
  }
  allocateDevie() {
    let selectedFeatures = this.features.filter((feature) => feature.selected).map((feature) => feature.name);
    if (this.selectedPatient === undefined || this.deviceId === undefined) {
      if (this.selectedPatient === undefined) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Please select patient',
        });
      }
      else if (this.deviceId === undefined) {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Please add device unique id',
        });
      }
      return;
    }
    let device: AllocateDevice = {
      allocationId: this.allocationId,
      imei: this.deviceId,
      orgId: this.orgId,
      patientId: this.selectedPatient.patientId,
      phoneNumber: this.phoneNumber,
      selectedFeatures: selectedFeatures
    };
    this.deviceService.allocateDevice(device).subscribe((res) => {
      if (res.success) {
        this.messageService.add({
          severity: 'info',
          summary: 'Sucess',
          detail: 'Device allocated successfully.',
        });
        this.loadDevices();
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Failed',
          detail: res.messages[0],
        });
      }
    });
    this.showEdit = false;
  }

  onInput(event: any): void {
    this.inputSubject.next(this.searchText);
  }

  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.deviceService.searchUnAllocatedDevice(searchTerm).subscribe((res) => {
        if (res.success) {
          this.response = res.purchasedWatchList;
          this.totalDeviceCount = res.purchasedWatchList.length;
          this.loadingdevices = false;
          this.rows=res.purchasedWatchList.length;
        }
      });
    } else {
      if (searchTerm !== undefined && searchTerm.length === 0) {
        this.rows=15;
        this.first=0;
        this.deviceService.setUnAllocatedDevice('0/15').subscribe((res) => {
          if (res.success) {
            this.response = res.purchasedWatchList;
            this.totalDeviceCount = res.resultCount;
            this.loadingdevices = false;
          }
        });
      }
    }
  }
  exportToExcel(type: string) {
    let fileName = 'All_Clinic_Unallocated_Devices' + '.xlsx';
    this.deviceService.exportData(0, fileName).subscribe((response) => {
      if (response.success) {
        console.log('File downloaded successfully');
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'File downloaded successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Error downloading file',
        });
      }
      // if (res) {
      //   let finalArray = new Array();
      //   res.forEach((x) => {
      //     finalArray.push({
      //       DATE_TIME: x.dateTime,
      //       SEVERITY: x.alertType,
      //       PATIENT_NAME: x.patientName,
      //       PHONE_NUMBER: x.patientPhoneNumber,
      //       ALERT_DESCRIPTION: x.alertDescription,
      //     });
      //   });
      //   const worksheet = XLSX.utils.json_to_sheet(finalArray, {
      //     header: this.columns,
      //   });
      //   const workbook = XLSX.utils.book_new();
      //   XLSX.utils.book_append_sheet(workbook, worksheet, 'Alerts');
      //   XLSX.writeFile(workbook, 'Alert.' + type);
      // }
    });
  }

  onCheckboxChange(event: any, item: any) {
    item.selected = !item.selected;
  }

  first=0
  previousRowsPerPage=15
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }
}
