import { CommonModule, formatDate } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import moment from 'moment';
import { MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ChartModule } from 'primeng/chart';
import { ChipModule } from 'primeng/chip';
import { ImageModule } from 'primeng/image';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import {
  PatientData,
  SelectedProgram,
} from '../../../../../api/editPatientProfile';
import { VitalService } from '../vitals/service/vital.service';
import { EditPatientDashboardService } from './service/edit-patient-dashboard.service';
@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [
    BreadcrumbModule,
    AvatarModule,
    InputTextModule,
    CardModule,
    ButtonModule,
    ImageModule,
    CommonModule,
    TableModule,
    ChartModule,
    InputSwitchModule,
    FormsModule,
    ToastModule,
    ChipModule,
  ],
  templateUrl: './dashboard.component.html',
  styleUrl: './dashboard.component.scss',
  providers: [MessageService],
})
export class DashboardComponent implements OnInit {
  @Input() id: any = -1;
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();

  patientName: string = '';
  location: string = '';
  profileImage: string = 'assets/watchrx/images/avatar/user-icon.png';
  phoneNumber: string = '';
  providerName: string = '';
  daysMeasured: number = 0;
  programs: SelectedProgram[] = [];

  heartRateStatus: boolean = false;
  stepsStatus: boolean = false;
  spo2Status: boolean = false;
  weightStatus: boolean = false;
  tempStatus: boolean = false;
  bpStatus: boolean = false;
  bsStatus: boolean = false;
  sleepStatus: boolean = false;

  headers: string[] = [];
  tabledData: any[] = [];
  threshold: any = {};
  thresholdStatus: any = {};
  graphOptions: any;
  medicationData: any;
  alertsData: any;
  patientData: PatientData | undefined;
  totalEnabled: number = 0;
  data: any[] = [];
  chartOptions: any;
  graphOptions1: any;
  chartOptions1: any;
  chartOptions2: any;
  chartOptions3: any;
  chartOptions4: any;
  chartOptions5: any;
  chartOptions6: any;
  chartOptions7: any;
  attentionList: any[] = [];
  thresholdValues: any = {};
  constructor(
    private editPatientDashboardService: EditPatientDashboardService,
    private messageService: MessageService,
    private vitalService: VitalService
  ) {}

  getPatientProfileInfo() {
    let req = {
      patientId: this.id,
    };
    this.editPatientDashboardService.getPatientProfile(req).subscribe((res) => {
      this.patientData = res;
      this.patientName = res?.firstName + ' ' + res?.lastName;
      if (res.picPath) {
        this.profileImage = res.picPath;
      }
      this.phoneNumber = res.phoneNumber;
      this.location =
        res.address?.address1 +
        ',' +
        res.address?.address2 +
        ' ' +
        res.address?.city +
        ' ' +
        res.address?.state +
        ' ' +
        res.address?.country +
        ' ' +
        res.address?.zip;
      this.providerName = res.physicianName;
      this.daysMeasured = res.daysMeasured;
      this.programs = res.programs?.selectedPrograms;

      this.heartRateStatus = res.thresholdData?.heartRateIsEnabled;
      this.stepsStatus = res.thresholdData?.pedometerStepIsEnabled;
      this.spo2Status = res.thresholdData?.spo2IsEnabled;
      this.weightStatus = res.thresholdData?.weightIsEnabled;
      this.tempStatus = res.thresholdData?.temperatureIsEnabled;
      this.bpStatus = res.thresholdData?.diastolicBloodPressureIsEnabled;
      this.bsStatus = res.thresholdData?.bloodSugarIsEnabled;
      this.sleepStatus = res.thresholdData?.sleepMonitorIsEnabled;
      this.totalEnabled =
        (this.heartRateStatus == true ? 1 : 0) +
        (this.stepsStatus == true ? 1 : 0) +
        (this.spo2Status == true ? 1 : 0) +
        (this.weightStatus == true ? 1 : 0) +
        (this.tempStatus == true ? 1 : 0) +
        (this.bpStatus == true ? 1 : 0) +
        (this.bsStatus == true ? 1 : 0) +
        (this.sleepStatus == true ? 1 : 0);
      this.thresholdValues = res.thresholdMinMax;
      this.dataEmitter.emit({});
      this.getVitalGraphWeekly();
    });
  }

  getTableData() {
    let todaydate = formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
    this.editPatientDashboardService
      .getTableData(this.id, todaydate)
      .subscribe((res) => {
        if (res.status) {
          this.headers = res.dates;
          this.tabledData = res.data;
          this.threshold = res.threshold;
          this.thresholdStatus = res.thresholdStatus;
        }
      });
  }

  getMedicationMissedGraph() {
    let req = {
      patientId: this.id,
      periodType: 'WEEKLY',
      endDate: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
    };

    this.editPatientDashboardService
      .getMedicationGraph(req)
      .subscribe((res) => {
        if (res.success) {
          const documentStyle = getComputedStyle(document.documentElement);
          let vitalsCountGraphVOs = res.vitalsCountGraphVOs;
          this.medicationData = {
            labels: this.formatDatesWithNewline(res.measuredDates),
            datasets: [
              {
                type: 'bar',
                label: 'Missed Medication',
                backgroundColor: documentStyle.getPropertyValue('--red-500'),
                data: vitalsCountGraphVOs[0].counts,
              },
              {
                type: 'bar',
                label: 'Medication To Take',
                backgroundColor: documentStyle.getPropertyValue('--green-500'),
                data: vitalsCountGraphVOs[1].counts,
              },
            ],
          };
        }
      });
  }

  formatDatesWithNewline(dates: string[]): string[] {
    return dates.map((date) => {
      const day = moment(date).format('ddd');
      const datePart = moment(date).format('MM/DD');
      return `${day}\n${datePart}`;
    });
  }

  getAlertGraph() {
    let req = {
      patientId: Number(this.id),
      format: 'WEEKLY',
      endDate: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
    };

    this.editPatientDashboardService.getAlertGraph(req).subscribe((res) => {
      if (res.success) {
        const documentStyle = getComputedStyle(document.documentElement);
        let vitalsCountGraphVOs = res.vitalsCountGraphVOs;
        let lables = this.formatDatesWithNewline(res.measuredDates);
        this.alertsData = {
          labels: lables,
          datasets: [
            {
              type: 'line',
              label: 'Alarm',
              backgroundColor: documentStyle.getPropertyValue('--orange-500'),
              borderColor: documentStyle.getPropertyValue('--orange-500'),
              data: vitalsCountGraphVOs[0].counts,
              spanGaps: true,
            },
            {
              type: 'line',
              label: 'Critical',
              backgroundColor: documentStyle.getPropertyValue('--red-500'),
              borderColor: documentStyle.getPropertyValue('--red-500'),
              data: vitalsCountGraphVOs[1].counts,
              spanGaps: true,
            },
            {
              type: 'line',
              label: 'Info',
              backgroundColor: documentStyle.getPropertyValue('--primary-500'),
              borderColor: documentStyle.getPropertyValue('--primary-500'),
              data: vitalsCountGraphVOs[2].counts,
              spanGaps: true,
            },
            {
              type: 'line',
              label: 'Warning',
              backgroundColor: documentStyle.getPropertyValue('--yellow-500'),
              borderColor: documentStyle.getPropertyValue('--yellow-500'),
              data: vitalsCountGraphVOs[3].counts,
              spanGaps: true,
            },
          ],
        };
        this.setGraphOptions(lables);
      }
    });
  }

  updateVitalStatus(status: boolean, eventType: string) {
    this.updateTotalCount();

    let vitalTypeName = '';
    if (eventType === 'BP') {
      vitalTypeName = 'Blood Pressure';
    } else if (eventType === 'BS') {
      vitalTypeName = 'Blood Sugar';
    } else if (eventType === 'TEMP') {
      vitalTypeName = 'Temperature';
    } else if (eventType === 'SPO2') {
      vitalTypeName = 'Oxygen Saturation';
    } else if (eventType === 'Heart Rate') {
      vitalTypeName = 'Heart Rate';
    } else if (eventType === 'Weight') {
      vitalTypeName = 'Weight';
    }
    var data = {
      vitalTypeName: vitalTypeName,
      status: status ? 'enable' : 'disable',
      patientId: this.id,
    };
    console.log(data);

    this.editPatientDashboardService
      .updatevitalenableState(data)
      .subscribe((response) => {
        if (response.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Vital updated successfully',
          });
          this.getVitalGraphWeekly();
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update vital',
          });
        }
      });
  }

  updatePedometer(status: boolean) {
    this.updateTotalCount();
    var state = status ? 'enable' : 'disable';
    var currentDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
    var stepsdata = {
      state: state,
      patientId: this.id,
      createdDate: currentDate,
      updatedDate: '',
      timeInterval: '180',
    };
    this.editPatientDashboardService
      .updatePedometerState(stepsdata)
      .subscribe((response) => {
        if (response.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Vital updated successfully',
          });
          this.getVitalGraphWeekly();
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update vital',
          });
        }
      });
  }

  updateSleepMonitor(status: boolean) {
    this.updateTotalCount();
    var state = status ? 'enable' : 'disable';
    var currentDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');

    var stepsdata = {
      state: state,
      patientId: this.id,
      createdDate: currentDate,
      updatedDate: '',
      timeInterval: '0',
    };
    this.editPatientDashboardService
      .updatePedometerState(stepsdata)
      .subscribe((response) => {
        if (response.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Vital updated successfully',
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update vital',
          });
        }
      });
  }

  updateTotalCount() {
    this.totalEnabled =
      (this.heartRateStatus == true ? 1 : 0) +
      (this.stepsStatus == true ? 1 : 0) +
      (this.spo2Status == true ? 1 : 0) +
      (this.weightStatus == true ? 1 : 0) +
      (this.tempStatus == true ? 1 : 0) +
      (this.bpStatus == true ? 1 : 0) +
      (this.bsStatus == true ? 1 : 0) +
      (this.sleepStatus == true ? 1 : 0);
  }

  convert(val: number): string {
    const minutes: number = Math.floor(val);
    const seconds: number = Math.round((val - minutes) * 60);

    return `${minutes}m:${seconds}s`;
  }

  getHeaderValue(val: any): string {
    return formatDate(val, 'MM/dd', 'en-US');
  }

  getTextColor(value: any, vitalType: string): string {
    if (value === undefined || value === '-') {
      return 'black';
    }
    if (this.threshold === undefined || this.threshold === null) {
      return 'black';
    }

    switch (vitalType) {
      case 'Heart Rate': {
        if (this.threshold.heartRateCriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.heartRateCriticalMax ||
          value < this.threshold.heartRateCriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.heartRateMax ||
          value < this.threshold.heartRateMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }
      case 'Fasting BS':
      case 'Random BS': {
        if (this.threshold.bloodSugarCriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.bloodSugarCriticalMax ||
          value < this.threshold.bloodSugarCriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.bloodSugarMax ||
          value < this.threshold.bloodSugarMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }
      case 'Blood Pressure': {
        if (this.threshold.systolicBloodPressureCriticalMax === null) {
          return 'black';
        }
        var splitValue = value.split('/');
        var sysV = splitValue[0];
        var diaV = splitValue[1];

        if (
          sysV > this.threshold.systolicBloodPressureCriticalMax ||
          sysV < this.threshold.systolicBloodPressureCriticalMin
        ) {
          return 'red';
        } else if (
          sysV > this.threshold.systolicBloodPressureMax ||
          sysV < this.threshold.systolicBloodPressureMin
        ) {
          return 'orange';
        } else if (
          diaV > this.threshold.diastolicBloodPressureCriticalMax ||
          diaV < this.threshold.diastolicBloodPressureCriticalMin
        ) {
          return 'red';
        } else if (
          diaV > this.threshold.diastolicBloodPressureMax ||
          diaV < this.threshold.diastolicBloodPressureMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }

      case 'Weight': {
        if (this.threshold.weightCriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.weightCriticalMax ||
          value < this.threshold.weightCriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.weightMax ||
          value < this.threshold.weightMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }

      case 'Temprature': {
        if (this.threshold.temperatureCriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.temperatureCriticalMax ||
          value < this.threshold.temperatureCriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.temperatureMax ||
          value < this.threshold.temperatureMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }

      case 'SpO2': {
        if (this.threshold.spo2CriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.spo2CriticalMax ||
          value < this.threshold.spo2CriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.spo2Max ||
          value < this.threshold.spo2Min
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }
      case 'Steps Count': {
        if (this.threshold.pedometerStepCountCriticalMax === null) {
          return 'black';
        }
        if (
          value > this.threshold.pedometerStepCountCriticalMax ||
          value < this.threshold.pedometerStepCountCriticalMin
        ) {
          return 'red';
        } else if (
          value > this.threshold.pedometerStepCountMax ||
          value < this.threshold.pedometerStepCountMin
        ) {
          return 'orange';
        } else {
          return 'green';
        }
      }

      default:
        return 'black';
    }
  }

  getClass(index: number, vitalType: string): string {
    if (vitalType === undefined || vitalType === null) {
      return index % 2 === 0 ? '#ADD8E6' : '#CBC3E3';
    }

    let vitalStatus = false;
    switch (vitalType) {
      case 'Heart Rate':
        vitalStatus = this.thresholdStatus.heartRateIsEnabled;
        break;
      case 'Fasting BS':
      case 'Random BS':
        vitalStatus = this.thresholdStatus.bloodSugarIsEnabled;
        break;
      case 'Blood Pressure':
        vitalStatus = this.thresholdStatus.systolicBloodPressureIsEnabled;
        break;
      case 'Weight':
        vitalStatus = this.thresholdStatus.weightIsEnabled;
        break;
      case 'Temperature':
        vitalStatus = this.thresholdStatus.temperatureIsEnabled;
        break;
      case 'SpO2':
        vitalStatus = this.thresholdStatus.spo2IsEnabled;
        break;
      case 'Steps Count':
        vitalStatus = this.thresholdStatus.pedometerStepIsEnabled;
        break;
      case 'Inhaler':
      case 'Spirometry':
        vitalStatus = true;
        break;
    }

    return index % 2 === 0
      ? vitalStatus
        ? '#FFB6C1'
        : 'lightgray'
      : vitalStatus
      ? '#87CEEB'
      : 'lightgray';
  }

  getNeedAttention() {
    const currentDate = new Date();
    const sevenDaysBefore = new Date();
    sevenDaysBefore.setDate(currentDate.getDate() - 7);
    let req = {
      patientId: Number(this.id),
      startDate: formatDate(sevenDaysBefore, 'yyyy-MM-dd hh:mm:ss', 'en-US'),
      endDate: formatDate(new Date(), 'yyyy-MM-dd hh:mm:ss', 'en-US'),
    };

    this.editPatientDashboardService.getAttentionList(req).subscribe((res) => {
      if (res.length > 0) {
        this.attentionList = res;
      }
    });
  }
  ngOnInit(): void {
    this.getPatientProfileInfo();
    this.getTableData();
    this.getMedicationMissedGraph();
    this.getAlertGraph();
    this.getNeedAttention();
    this.getDashboardVital();
  }

  tableData: any;
  columnList: any;

  getDashboardVital() {
    let req = {
      patientId: Number(this.id),
      time: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
    };
    this.editPatientDashboardService
      .getDashboardVitalInfo(req)
      .subscribe((res) => {
        if (res) {
          this.columnList = ['VitalType', ...res.dates];
          this.tableData = res.data.map((item: any) => {
            const row: any = { VitalType: item.vitalType };
            res.dates.forEach((date: any, index: any) => {
              row[date] = item.value[index];
            });
            return row;
          });
        }
        //console.log(res)
      });
  }

  setGraphOptions(lables: any) {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');

    this.graphOptions = {
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
        },
        legend: {
          display: false,
          labels: {
            color: textColor,
          },
        },
      },
      scales: {
        x: {
          ticks: {
            callback: (value: any) => {
              return lables[value];
            },
          },
          grid: {
            display: false,
          },
        },
        y: {
          min: 0,
          stacked: false,
          ticks: {
            stepSize: 10,
          },
          grid: {
            display: true,
            color: '#000000',
            borderDash: [3, 5],
          },
        },
      },
    };
  }
  async getVitalGraphWeekly() {
    this.graphOptions1 = {
      maintainAspectRatio: false,
      aspectRatio: 1.2,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
        },
        legend: {
          display: false,
        },
      },
      scales: {
        x: {
          ticks: {},
          grid: {
            display: false,
          },
        },
        y: {
          min: 0,
          stacked: false,
          ticks: {
            stepSize: 10,
          },
          grid: {
            display: true,
            color: '#000000',
            borderDash: [3, 5],
          },
        },
      },
    };

    this.data = [];
    let payload: any = {
      patientId: this.patientData?.patientId,
      requestedDate: formatDate(new Date(), 'yyyy-MM-dd', 'en-US'),
      periodType: 'WEEKLY',
    };
    if (this.bsStatus) {
      payload.vitalTypeNameList = ['Fasting Blood Sugar', 'Random Blood Sugar'];
      await this.getVitalData(payload, 'Blood Sugar');
      this.chartOptions6 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions6.scales.y.min =
        (this.patientData?.thresholdMinMax?.bloodSugarCriticalMin || 0) - 30;
      this.chartOptions6.scales.y.max =
        (this.patientData?.thresholdMinMax?.bloodSugarCriticalMax || 0) + 30;
    }
    if (this.bpStatus) {
      payload.vitalTypeNameList = [
        'Systolic Blood Pressure',
        'Diastolic Blood Pressure',
      ];
      await this.getVitalData(payload, 'Blood Pressure');
      this.chartOptions1 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions1.scales.y.min =
        (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin ||
          0) - 30;
      this.chartOptions1.scales.y.max =
        (this.patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax ||
          0) + 30;
    }
    if (this.heartRateStatus) {
      payload.vitalTypeNameList = ['Heart Rate'];
      await this.getVitalData(payload, 'Heart Rate');
      this.chartOptions = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions.scales.y.min =
        (this.patientData?.thresholdMinMax?.heartRateCriticalMin || 0) - 30;
      this.chartOptions.scales.y.max =
        (this.patientData?.thresholdMinMax?.heartRateCriticalMax || 0) + 30;
    }
    if (this.stepsStatus) {
      //payload.vitalTypeNameList = ['Pedometer'];
      await this.getVitalDataForPedometer(payload, 'Steps Count');
      this.chartOptions2 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions2.scales.y.min =
        (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMin ||
          0) - 30;
      this.chartOptions2.scales.y.max =
        (this.patientData?.thresholdMinMax?.pedometerStepCountCriticalMax ||
          0) + 30;
    }
    if (this.spo2Status) {
      payload.vitalTypeNameList = ['Oxygen Saturation'];
      await this.getVitalData(payload, 'SpO2');
      this.chartOptions3 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions3.scales.y.min =
        (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) - 30;
      this.chartOptions3.scales.y.max =
        (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) + 30;
    }
    if (this.sleepStatus) {
      payload.vitalTypeNameList = ['Sleep Monitor'];
      await this.getVitalData(payload, 'Sleep Monitor');
      this.chartOptions7 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions7.scales.y.min =
        (this.patientData?.thresholdMinMax?.spo2CriticalMin || 0) - 30;
      this.chartOptions7.scales.y.max =
        (this.patientData?.thresholdMinMax?.spo2CriticalMax || 0) + 30;
    }
    if (this.weightStatus) {
      payload.vitalTypeNameList = ['Weight'];
      await this.getVitalData(payload, 'Weight');
      this.chartOptions4 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions4.scales.y.min =
        (this.patientData?.thresholdMinMax?.weightCriticalMin || 0) - 30;
      this.chartOptions4.scales.y.max =
        (this.patientData?.thresholdMinMax?.weightCriticalMax || 0) + 30;
    }
    if (this.tempStatus) {
      payload.vitalTypeNameList = ['Temperature'];
      await this.getVitalData(payload, 'Temperature');
      this.chartOptions5 = JSON.parse(JSON.stringify(this.graphOptions1));
      this.chartOptions5.scales.y.min =
        (this.patientData?.thresholdMinMax?.temperatureCriticalMin || 0) - 30;
      this.chartOptions5.scales.y.max =
        (this.patientData?.thresholdMinMax?.temperatureCriticalMax || 0) + 30;
    }

    // if (this.selectedVitalType?.name === 'Pedometer') {
    //   this.getPedometerGraph();
    //   return;
    // }
  }

  async getVitalData(payload: any, name: string) {
    this.vitalService.getVitalGraph(payload, true).subscribe((res) => {
      if (res?.success) {
        let firstData = res?.vitalsCountGraphVOs[0];
        let secondData = res?.vitalsCountGraphVOs[1];
        console.log(secondData, 'secondData');
        let dataSets: any[] = [];
        if (firstData?.counts) {
          dataSets.push({
            label: firstData?.vitalTypeName,
            backgroundColor: '#38bfc6',
            borderColor: '#38bfc6',
            data: firstData?.counts,
            borderWidth: 3,
            barThickness: 20,
            spanGaps: true,
          });
        }
        if (secondData?.counts) {
          dataSets.push({
            label: secondData?.vitalTypeName,
            backgroundColor: '#f9a11d',
            borderColor: '#f9a11d',
            data: secondData?.counts,
            borderWidth: 3,
            barThickness: 20,
            spanGaps: true,
          });
        }

        let lables = this.formatDatesWithNewline(res?.measuredDates);
        let obj = {
          labels: lables,
          datasets: dataSets,
        };
        this.data.push({ obj, name });
        this.data = [...this.data];
        this.setGraphOptions(lables);
        console.log(this.data);
        this.data.sort((a, b) => a.name.localeCompare(b.name));
      }
    });
  }
  async getVitalDataForPedometer(payload: any, name: string) {
    this.vitalService.getPedometerGraph(payload, true).subscribe((res) => {
      if (res?.success) {
        let firstData = res?.vitalsCountGraphVOs[0];
        let dataSets: any[] = [];
        if (firstData?.counts) {
          dataSets.push({
            label: 'Pedometer',
            backgroundColor: '#38bfc6',
            borderColor: '#38bfc6',
            data: firstData?.counts,
            borderWidth: 3,
            barThickness: 20,
            spanGaps: true,
          });
        }
        let obj = {
          labels: res?.period,
          datasets: dataSets,
        };

        this.data.push({ obj, name });
        this.data = [...this.data];
        this.data.sort((a, b) => a.name.localeCompare(b.name));
      }
    });
  }

  gridView: boolean = false;
  tableView: boolean = true;
  viewFormat(type: string) {
    if (type === 'g') {
      this.gridView = true;
      this.tableView = false;
    } else if (type === 't') {
      this.tableView = true;
      this.gridView = false;
    }
  }

  getClassName(index: any, vitalType: any) {
    if (vitalType === undefined || vitalType === null) {
      if (index % 2 === 0) {
        return 'xl-khaki';
      } else {
        return 'l-blue';
      }
    }
    var vitalStatus = false;
    if (vitalType === 'Heart Rate') {
      vitalStatus = this.heartRateStatus;
    }
    if (vitalType === 'Fasting BS' || vitalType === 'Random BS') {
      vitalStatus = this.bsStatus;
    }
    if (vitalType === 'Blood Pressure') {
      vitalStatus = this.bpStatus;
    }

    if (vitalType === 'Weight') {
      vitalStatus = this.weightStatus;
    }
    if (vitalType === 'Temperature') {
      vitalStatus = this.tempStatus;
    }
    if (vitalType === 'SpO2') {
      vitalStatus = this.spo2Status;
    }
    if (vitalType === 'Steps Count') {
      vitalStatus = this.stepsStatus;
    }
    if (vitalType === 'Inhaler' || vitalType === 'Spirometry') {
      vitalStatus = true;
    }
    if (index % 2 === 0) {
      return vitalStatus ? 'xl-khaki' : 'xl-parpl';
    } else {
      return vitalStatus ? 'l-blue' : 'xl-parpl';
    }
  }

  getTextColorValue(value: any, vitalType: any) {
    let threshold = this.thresholdValues;
    if (
      value === undefined ||
      value === '-' ||
      value === 'Heart Rate' ||
      value === 'Fasting BS' ||
      value === 'Random BS' ||
      value === 'Blood Pressure' ||
      value === 'Weight' ||
      value === 'Temperature' ||
      value === 'SpO2' ||
      value === 'Steps Count'
    ) {
      return 'black';
    }
    if (vitalType === 'Heart Rate') {
      if (threshold.heartRateCriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.heartRateCriticalMax ||
        value < threshold.heartRateCriticalMin
      ) {
        return 'red';
      } else if (
        value > threshold.heartRateMax ||
        value < threshold.heartRateMin
      ) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'Fasting BS' || vitalType === 'Random BS') {
      if (threshold.bloodSugarCriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.bloodSugarCriticalMax ||
        value < threshold.bloodSugarCriticalMin
      ) {
        return 'red';
      } else if (
        value > threshold.bloodSugarMax ||
        value < threshold.bloodSugarMin
      ) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'Blood Pressure') {
      if (threshold.systolicBloodPressureCriticalMax === null) {
        return 'black';
      }
      var splitValue = value.split('/');
      var sysV = splitValue[0];
      var diaV = splitValue[1];

      if (
        sysV > threshold.systolicBloodPressureCriticalMax ||
        sysV < threshold.systolicBloodPressureCriticalMin
      ) {
        return 'red';
      } else if (
        sysV > threshold.systolicBloodPressureMax ||
        sysV < threshold.systolicBloodPressureMin
      ) {
        return 'orange';
      } else if (
        diaV > threshold.diastolicBloodPressureCriticalMax ||
        diaV < threshold.diastolicBloodPressureCriticalMin
      ) {
        return 'red';
      } else if (
        diaV > threshold.diastolicBloodPressureMax ||
        diaV < threshold.diastolicBloodPressureMin
      ) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'Weight') {
      if (threshold.weightCriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.weightCriticalMax ||
        value < threshold.weightCriticalMin
      ) {
        return 'red';
      } else if (value > threshold.weightMax || value < threshold.weightMin) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'Temperature') {
      if (threshold.temperatureCriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.temperatureCriticalMax ||
        value < threshold.temperatureCriticalMin
      ) {
        return 'red';
      } else if (
        value > threshold.temperatureMax ||
        value < threshold.temperatureMin
      ) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'SpO2') {
      if (threshold.spo2CriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.spo2CriticalMax ||
        value < threshold.spo2CriticalMin
      ) {
        return 'red';
      } else if (value > threshold.spo2Max || value < threshold.spo2Min) {
        return 'orange';
      } else {
        return 'green';
      }
    }

    if (vitalType === 'Steps Count') {
      if (threshold.pedometerStepCountCriticalMax === null) {
        return 'black';
      }
      if (
        value > threshold.pedometerStepCountCriticalMax ||
        value < threshold.pedometerStepCountCriticalMin
      ) {
        return 'red';
      } else if (
        value > threshold.pedometerStepCountMax ||
        value < threshold.pedometerStepCountMin
      ) {
        return 'orange';
      } else {
        return 'green';
      }
    } else {
      return 'black';
    }
  }
}
