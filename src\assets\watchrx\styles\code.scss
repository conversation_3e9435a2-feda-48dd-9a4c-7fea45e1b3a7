pre.app-code {
    background-color: var(--surface-ground);
    margin: 0 0 1rem 0;
    padding: 0;
    border-radius: var(--border-radius);
    overflow: auto;

    code {
        color: var(--surface-900);
        padding: 1rem;
        line-height: 1.5;
        display: block;
        font-family: monaco, Consolas, monospace;
    }
}

.p-datatable {
    border-radius: 10px;
}

.p-datatable th {
    background: #FFFFFF;
    color: #25282B;
}

.p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #25282B;
}

.p-datatable .p-sortable-column .p-sortable-column-icon {
    color: #25282B;
    margin-left: 0.5rem;
}

.p-datatable th:first-child {
    // border-top-left-radius: 10px;
}

.p-datatable th:last-child {
    // border-top-right-radius: 10px;
}

.p-paginator {
    justify-content: flex-end;
}

.p-paginator .p-paginator-pages {
    display: flex;
    justify-content: flex-end;
}

.p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: #336CFB;
    border: 2px solid #336CFB;
    color: #FFFFFF;
}

.p-paginator .p-paginator-pages .p-paginator-page {
    background-color: transparent;
    border: 1px solid #E8E8E8;
    color: #25282B;
    min-width: 2rem;
    height: 2rem;
    margin: 0.143rem;
    transition: box-shadow 0.2s;
    border-radius: 5px;
}

.menu-font {
    color: var(--Neutral-Gray-dark, #52575C);
    font-family: 'Lato';
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.1px;
}