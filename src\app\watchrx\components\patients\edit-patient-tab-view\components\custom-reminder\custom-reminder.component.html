<div class="customremainder">
  <p-toast />
  <p-confirmDialog />
  <div class="flex md:justify-content-end p-3">
    <p-button icon="pi pi-plus" label="Add Reminder" severity="secondary" [outlined]="true"
      (onClick)="showDialog(false)"></p-button>
  </div>
  <p-table [value]="reminderList" responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped">
    <ng-template pTemplate="header">
      <tr>
        <th>Alert Type</th>
        <th>Alert Time</th>
        <th>Description</th>
        <th>Start Date</th>
        <th>End Date</th>
        <th>Type</th>
        <th>Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-cust>
      <tr>
        <td>{{ cust.alertType }}</td>
        <td>{{ getFormatedHours(cust.alertTime) }}</td>
        <td>{{ cust.detail }}</td>
        <td>{{ cust.startDate }}</td>
        <td>{{ cust.endDate }}</td>
        <td>{{ cust.type }}</td>
        <td>
          <div class="flex flex-wrap">
            <button pButton icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
              (click)="editRecord(cust)"></button>
            <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
              (click)="deleteRecord($event, cust?.alertId)"></button>
          </div>
        </td>
      </tr>
    </ng-template>
  </p-table>

<p-sidebar header="Custom Reminder" [(visible)]="visible" position="right" [style]="{ width: '40rem' }">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isEdit"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isEdit"></i>
        {{title}}
      </span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid">
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-4">
        <label htmlfor="state">Alert Type</label>
        <p-dropdown inputId="dropdown" optionLabel="alertType" [options]="alertTypes" appendTo="body"
          [(ngModel)]="selectedAlertType" [disabled]="isEdit"></p-dropdown>
      </div>
      <div class="field col-12 md:col-4" *ngIf="selectedAlertType?.value === 'Other'">
        <label htmlfor="state">Custom Alert type</label>
        <input pinputtext="" id="firstname" type="text" placeholder="Enter Alert type"
          class="p-inputtext p-component p-element" [(ngModel)]="otherAlertType" />
      </div>
      <div class="field col-12 md:col-4">
        <label htmlfor="state">Time</label>
        <p-calendar [(ngModel)]="selectedTime" [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true"
          inputId="templatedisplay" appendTo="body" hourFormat="12" [stepMinute]="5" [showButtonBar]="true">
          <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
            <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
          </ng-template>
        </p-calendar>
      </div>
    </div>
    <div class="flex field col-12 md:col-12">
      <div class="flex flex-wrap gap-3" style="margin-left: 5px">
        <div class="flex align-items-center">
          <p-radioButton name="sch" value="daily" [(ngModel)]="scheduleType" inputId="daily" />
          <label for="daily" class="ml-2"> Daily </label>
        </div>

        <div class="flex align-items-center">
          <p-radioButton name="sch" value="schedule" [(ngModel)]="scheduleType" inputId="schedule" />
          <label for="schedule" class="ml-2"> Schedule </label>
        </div>
      </div>
    </div>
    <div class="flex field col-12 md:col-12" *ngIf="scheduleType === 'schedule'">
      <div class="field col-12 md:col-6">
        <label htmlfor="description" class="font-bold block mb-2">
          Start Date
        </label>
        <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay" appendTo="body"
          [(ngModel)]="startDate" />
      </div>
      <div class="field col-12 md:col-6">
        <label htmlfor="description" class="font-bold block mb-2">
          End Date
        </label>
        <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay" appendTo="body"
          [(ngModel)]="endDate" />
      </div>
    </div>
    <div class="flex field col-12 md:col-12">
      <textarea rows="3" cols="30" pInputTextarea autoResize="true" class="p-inputtext p-component p-element"
        [(ngModel)]="description" [attr.maxLength]="maxLength" placeholder="Enter your message here...">
      </textarea>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="primary" (click)="visible = false" />
      <p-button label="Save" severity="primary" (click)="isEdit ? submitEditRecord() : submitRecord()" />
    </div>
  </ng-template>
</p-sidebar>
</div>