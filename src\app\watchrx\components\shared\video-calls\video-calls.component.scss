main {
    width: 90%;
    margin: auto;
    text-align: center;
  }
    
  main #join-flow button {
    margin-top: 20px;
    background-color: #2D8CFF;
    color: #ffffff;
    text-decoration: none;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 40px;
    padding-right: 40px;
    display: inline-block;
    border-radius: 10px;
    cursor: pointer;
    border: none;
    outline: none;
  }
  
  main #join-flow button:hover {
    background-color: #2681F2;
  }

  div.volume-indicators {
		padding: 10px;
		margin-top: 20px;
		width: 300px;
	}

	div.volume-indicators>div {
		display: block;
		height: 20px;
		width: 0;
	}

	.hide {
		position: absolute !important;
		top: -9999px !important;
		left: -9999px !important;
	}

	.call-animation-play {
		background: rgb(130, 221, 233);
		width: 100px;
		height: 100px;
		border-radius: 100%;
		border: solid 5px rgb(252, 241, 241);
		animation: call 1.0s ease infinite;
		color: aliceblue;
		font-size: 35px;
		font-weight: bold;
		position: relative;
		align-items: center;
		justify-content: center;
	}

	.call-animation-pause {
		background: rgb(130, 221, 233);
		width: 100px;
		height: 100px;
		border-radius: 100%;
		border: solid 5px rgb(252, 241, 241);
		animation-play-state: paused;
		color: aliceblue;
		font-size: 35px;
		font-weight: bold;
		position: relative;
		align-items: center;
		justify-content: center;
	}

	.caller-img {
		position: absolute;
		height: 50px;
		width: 50px;
		top: 35px;
		left: 35px;
	}

	::ng-deep .p-dialog-mask
	{
		z-index: 999 !important;
	}
	.videocall
	{
		::ng-deep .p-tabview-panel 
		{
			display: flex
			;
		}
	}
	::ng-deep .videokit 
	{
		flex-direction: row !important;
		.self-view
		{
			max-width: 200px;
			top: 0px;
			height: 313px;
			margin-right: 14px !important;
		}	
	}
    ::ng-deep .p-dialog-header
    {
        border-bottom: 1px solid #d9d7d7;
        padding:12px 20px !important;
    }