import { Component, OnInit } from '@angular/core';
import { LazyLoadEvent } from 'primeng/api';
import { TableLazyLoadEvent } from 'primeng/table';
import { physicians, physicianVO } from '../../api/physician';
import { MenuService } from '../../service/menu.service';
import { PhysicianService } from '../physicians/service/physicians.service';

@Component({
  selector: 'app-physicians',
  // standalone: true,
  // imports: [],
  templateUrl: './physicians.component.html',
  styleUrl: './physicians.component.scss',
})
export class PhysiciansComponent implements OnInit {
  physicians: physicianVO[] = [];
  totalRecords: number = 0;
  loading: boolean = true;
  debouncedValue: string = "";
  searchText:string="";
  rows:number=15;

  constructor(
    public menuService: MenuService,
    private phyService: PhysicianService
  ) { }

  ngOnInit(): void {
    this.menuService.changeMenu('Physicians');
  }

  loadPhysicians($event?: LazyLoadEvent | TableLazyLoadEvent) {
    let pageSize = $event?.rows || 25;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    this.loading = true;
    this.phyService
      .getUsers(pageNo, pageSize)
      .subscribe((response: physicians) => {
        this.physicians = response.physicianVOList;
        this.totalRecords = response.resultCount;
        this.loading = false;
      });
  }

  onInput(event: any): void {
    // this.inputSubject.next(event.target.value);
    this.debouncedValue = event.target.value;
    this.onSearch(this.debouncedValue)
  }

  onSearch(searchTerm: string): void {
    this.loading = true;
    if (searchTerm && searchTerm !== undefined && searchTerm.length>2) {
      this.phyService.searchUser(searchTerm).subscribe((response: physicians) => {
        this.physicians = response.physicianVOList;
        this.totalRecords = response.resultCount;
        this.loading = false;
        this.rows=response.resultCount;
      }, err => {
        this.loading = false;
      });
    }
    else {
      if ( searchTerm !== undefined && searchTerm.length==0)
      {
        this.rows=15;
        this.first=0;
        this.loadPhysicians();
      }
    
    }
  }
  onViewDetails(data: any) {

  }
  first=0
  previousRowsPerPage=15
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
    } else {
      this.first = event.first;
    }
  }
}
