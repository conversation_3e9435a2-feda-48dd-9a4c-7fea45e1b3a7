<p-confirmDialog />
<p-toast />
<div class="layout-topbar">
  <div #topbarmenu class="layout-topbar-menu" [ngClass]="{
      'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible
    }">
    <div class="flex flex-row justify-content-between gap-3 align-items-center">
      <p-button icon="pi pi-building" [rounded]="true" severity="help" [outlined]="true" [disabled]="false"
        label="{{orgName}}" class="non-clickable"></p-button>
      <i class="pi pi-bell text-2xl alerticon cursor-pointer" *ngIf="!alertCount && user?.roleType!=2"></i>
      <i class="pi pi-bell text-2xl alerticon cursor-pointer" *ngIf="alertCount&& user?.roleType!=2" pBadge [value]="alertCount"
        (click)="chatSideVisible=true"></i>
      <p-button icon="pi pi-sign-out" [rounded]="true" severity="danger" [outlined]="true" (onClick)="logout($event)" />
    </div>
  </div>
</div>
<p-sidebar [(visible)]="chatSideVisible" [style]="{ width: '40rem' }" position="right"
  class="dashboardSidebar encountersidebar">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <span class="xl-font">Chat Notification</span>
    </div>
  </ng-template>
  <div class="flex w-full">
    <p-table #dt [value]="alertCountList" responsiveLayout="scroll" class="w-full"
      styleClass="p-datatable-gridlines p-datatable-striped w-full" [scrollable]="true" scrollHeight="400px" w>
      <ng-template pTemplate="header">
        <tr>
          <th style="width: 170px" pFrozenColumn>Patient Names</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-pat>
        <tr>
          <td style="width: 170px" pFrozenColumn class="align-left">
            <p [title]="pat.patientName" class="font-bold" style="color: #4338CA;">{{pat.patientName}} </p>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
  <!-- <p-divider /> -->
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Clear" [outlined]="true" severity="secondary" (click)="clearChats()" />
      <p-button label="Close" severity="primary" (click)="chatSideVisible=false" />
    </div>
  </ng-template>
</p-sidebar>