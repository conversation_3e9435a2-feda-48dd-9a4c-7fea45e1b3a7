<div style="margin-bottom: 50px">
  <p-tabView
    [(activeIndex)]="activeIndex"
    class="custom-tab-view position-relative medication"
  >
    <p-tabPanel header="Medication Information">
      <hr />
      <app-medication-info
        *ngIf="activeIndex == 0"
        [id]="patientId"
      ></app-medication-info>
    </p-tabPanel>
    <p-tabPanel header="Medication EHR">
      <hr />
      <app-medication-ehr
        *ngIf="activeIndex == 1"
        [id]="patientId"
      ></app-medication-ehr>
    </p-tabPanel>
    <p-tabPanel header="Patient Diary">
      <app-patient-diary
        *ngIf="activeIndex == 2"
        [id]="patientId"
      ></app-patient-diary>
    </p-tabPanel>
    <p-tabPanel header="Daily Schedule">
      <app-daily-schedule
        *ngIf="activeIndex == 3"
        [id]="patientId"
      ></app-daily-schedule>
    </p-tabPanel>
  </p-tabView>
</div>
