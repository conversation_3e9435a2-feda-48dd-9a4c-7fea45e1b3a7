import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FullCalendarModule } from '@fullcalendar/angular';
import { SharedModule } from '../shared/shared.module';
import { TaskboardRoutingModule } from './taskboard-routing.module';
import { TaskboardComponent } from './taskboard.component';
import { InputSwitchModule } from 'primeng/inputswitch';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [TaskboardComponent],
  imports: [
    CommonModule,
    TaskboardRoutingModule,
    FullCalendarModule,
    SharedModule,
    InputSwitchModule,
    FormsModule
  ],
  exports:[
    TaskboardComponent
  ]
})
export class TaskboardModule {}
