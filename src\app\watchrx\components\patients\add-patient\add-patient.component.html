
<div class="card">
  <h5>Basic Information</h5>
  <p-toast />
  <form (ngSubmit)="onSubmit(form)" #form="ngForm" autocomplete="off">
    <div class="p-fluid p-formgrid grid">
      <div class="field col-12 md:col-4">
        <label for="firstName">First Name <span class="p-text-danger"> *</span></label>
        <input id="firstName" type="text" pInputText [(ngModel)]="formData.firstName" name="firstName" required autocomplete="new-firstname"/>
        <div class="red" *ngIf="form.submitted && !formData.firstName">
          First Name is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="lastName">Last Name <span class="p-text-danger"> *</span></label>
        <input id="lastName" type="text" pInputText [(ngModel)]="formData.lastName" name="lastName" required autocomplete="new-lastname" />
        <div class="red" *ngIf="form.submitted && !formData.lastName">
          Last Name is required.
        </div>
      </div>


      <div class="field col-12 md:col-4">
        <label for="dateOfBirth">Date Of Birth <span class="p-text-danger"> *</span></label>
        <p-calendar id="dateOfBirth" [(ngModel)]="formData.dateOfBirth" name="dateOfBirth" dateFormat="mm/dd/yy"
          [iconDisplay]="'input'" [showIcon]="true" [required]="true"></p-calendar>
        <div class="red" *ngIf="form.submitted && !formData.dateOfBirth">
          Date of Birth is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="country">Gender<span class="p-text-danger"> *</span></label>
        <p-autoComplete id="gender" [(ngModel)]="formData.gender" name="gender" [suggestions]="filteredGenders"
          (completeMethod)="filterGender($event)" placeholder="Select a gender" [dropdown]="true"
          [required]="true"></p-autoComplete>
        <div class="red" *ngIf="form.submitted && !formData.gender">
          Gender is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="address1">Address 1 <span class="p-text-danger"> *</span></label>
        <input id="address1" type="text" pInputText [(ngModel)]="formData.address1" name="address1" required autocomplete="new-address" />
        <div class="red" *ngIf="form.submitted && !formData.address1">
          Address 1 is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="address2">Address 2</label>
        <input id="address2" type="text" pInputText [(ngModel)]="formData.address2" name="address2" />
      </div>

      <div class="field col-12 md:col-4">
        <label for="country">Country <span class="p-text-danger"> *</span></label>
        <p-autoComplete id="country" [(ngModel)]="formData.country" name="country" [suggestions]="countries"
          (completeMethod)="filterCountries($event)" field="name" placeholder="Select a country" appendTo="body"
          [dropdown]="true" emptyMessage="No country found" [showEmptyMessage]="true" autocomplete="new-country"
          [required]="true"></p-autoComplete>
        <div class="red" *ngIf="form.submitted && !formData.country">
          Country is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="state">State <span class="p-text-danger"> *</span></label>
         <input id="state" type="text" pInputText [(ngModel)]="formData.state" name="state" autocomplete="new-state" required />
        <!-- <p-autoComplete id="state" [(ngModel)]="formData.state" name="state" [suggestions]="states"
          (completeMethod)="filterStates($event)" field="name" placeholder="Select a state" appendTo="body"
          [dropdown]="true" emptyMessage="No state found" [showEmptyMessage]="true" [required]="true"></p-autoComplete> -->
        <div class="red" *ngIf="form.submitted && !formData.state">
          State is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="city">City <span class="p-text-danger"> *</span></label>
        <input id="address1" type="text" pInputText [(ngModel)]="formData.city" name="city" autocomplete="new-city" required />
        <!-- <p-autoComplete id="city" [(ngModel)]="formData.city" name="city" [suggestions]="cities"
          (completeMethod)="filterCities($event)" field="name" placeholder="Select a city" appendTo="body"
          [dropdown]="true" emptyMessage="No city found" [showEmptyMessage]="true" [required]="true"></p-autoComplete> -->
        <div class="red" *ngIf="form.submitted && !formData.city">
          City is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="zipCode">Zip Code <span class="p-text-danger"> *</span></label>
        <input id="zipCode" type="text" pInputText [(ngModel)]="formData.zipCode" name="zipCode" autocomplete="new-zipcode" required />
        <div class="red" *ngIf="form.submitted && !formData.zipCode">
          Zip Code required.
        </div>
      </div>
       <div class="field col-12 md:col-4">
            <label for="timezone">Timezone<span class="p-text-danger"> *</span></label>
            <p-autoComplete id="timezone" [(ngModel)]="formData.timezone" name="timezone" [suggestions]="timeZoneList"
              (completeMethod)="filterTimeZones($event)" field="" placeholder="Select a timezone" appendTo="body"
              [dropdown]="true" emptyMessage="No timezone found" [showEmptyMessage]="true"
              [required]="true"></p-autoComplete>
            <div class="red" *ngIf="form.submitted && !formData.timezone">
              Timezone is required.
            </div>
          </div>

      <div class="field col-12 md:col-4">
        <label for="provider">MD/DO/NP/PA	 <span class="p-text-danger"> *</span></label>
        <p-dropdown [(ngModel)]="formData.provider" name="provider" [options]="providerList" optionLabel="name"
          placeholder="Select a provider" [required]="true"></p-dropdown>
        <div class="red" *ngIf="form.submitted && !formData.provider">
          Provider is required.
        </div>
      </div>

      <div class="field col-12 md:col-4">
        <label for="mrn">MRN</label>
        <input id="mrn" type="text" pInputText [(ngModel)]="formData.mrn" name="mrn" />
      </div>

      <div class="field col-12 md:col-4">
        <label for="connectingDevice">Connecting Device <span class="p-text-danger"> *</span></label>
        <p-dropdown id="connectingDevice" [(ngModel)]="formData.connectingDevice" name="connectingDevice"
          [options]="['Watch', 'Mobile', 'Wireless MD']" placeholder="Select a device" [required]="true"></p-dropdown>
        <div class="red" *ngIf="form.submitted && !formData.connectingDevice">
          Connecting Device is required.
        </div>
      </div>

       <!-- *ngIf="formData.connectingDevice !== 'Watch'"-->

       <div class="field col-12 md:col-4" *ngIf="formData.connectingDevice == 'Mobile'">
        <label for="email">Email <span class="p-text-danger">*</span></label>
        <input id="email" type="email" pInputText [(ngModel)]="formData.email" name="email" required #email="ngModel" autocomplete="new-email"   />
        <div>{{email.errors?.['email']}}</div>
        <div class="red" *ngIf="(email.touched || form.submitted) && email.invalid">
          <div *ngIf="email.errors?.['required']">Email is required.</div>
          <!-- pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$" <div *ngIf="email.errors?.['pattern']">Must be a valid email address.</div> -->
        </div>
      </div>
      <div class="field col-12 md:col-4" *ngIf="formData.connectingDevice != 'Mobile'">
        <label for="email">Email</label>
        <input id="email" type="email" pInputText  #email="ngModel" [(ngModel)]="formData.email" name="email"  autocomplete="new-email"/>
        <!-- <div class="red" *ngIf="(email.touched || form.submitted) && email.errors?.['pattern']">
          pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,4}$" <div *ngIf="email.errors?.['pattern']">Must be a valid email address.</div>
        </div> -->
      </div>
      
      <div class="field col-12 md:col-4">
        <label for="phoneNumber">Phone Number</label>
        <input id="phoneNumber" type="text" pInputText [(ngModel)]="formData.phoneNumber" name="phoneNumber"
          pattern="^\+[1-9]{1}[0-9]{3,14}$" #phoneModel="ngModel" minlength="10" autocomplete="new-phonenumber" />
        <div *ngIf="phoneModel.invalid && (phoneModel.dirty || phoneModel.touched)">
          <small class="p-error" *ngIf="phoneModel.errors?.['pattern']">
            This value length is invalid. It should be between 10 and 17 characters long.
          </small>
        </div>
        <span><small>Note:(Example:+18001232323) Country code is manditory and space is not allowed in between the
            number</small></span>
      </div>

      <div class="field col-12 md:col-4">
        <label for="persNumber">PERS Number</label>
        <input id="persNumber" type="text" pInputText [(ngModel)]="formData.persNumber" name="persNumber"
          pattern="^\+[1-9]{1}[0-9]{3,14}$" #psphoneModel="ngModel" minlength="10" />
        <div *ngIf="psphoneModel.invalid && (psphoneModel.dirty || psphoneModel.touched)">
          <small class="p-error" *ngIf="psphoneModel.errors?.['pattern']">
            This value length is invalid. It should be between 10 and 17 characters long.
          </small>
        </div>
        <span><small>Note:(Example:+18001232323) Country code is manditory and space is not allowed in between the
            number</small></span>
      </div>

      <div  class="col-12 lg:col-12" id="cptcodes">
        <div class="p-card-header flex justify-content-between section align-items-center" id="emergencycontact"
          style="padding:8px 10px !important; background-color:#efefef">
          <h6 class="p-0 m-0">Chronic Conditions</h6>
          <p-button label="Add Chronic Conditions" severity="secondary" [outlined]="true"  class="mr-3"
            (onClick)="openChronicPopup()" icon="pi pi-plus"></p-button>
        </div>
        <p-table [value]="chronicCondition" responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped">
          <ng-template pTemplate="header">
            <tr>
              <th>Condition Name</th>
              <th>ICD Code</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-cc let-i="rowIndex">
            <tr>
              <td style="min-width: 7rem">{{ cc.chronicConditionName }}</td>
              <td style="min-width: 8rem">
                {{ cc.icdCode }}
              </td>
              <td>
                <div class="flex">
                  <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                    style="height: 30px; width: 30px" (click)="deleteItem($event, 'chronic', cc.chonicId,i)"></button>
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
        <!-- </div> -->
      </div>
    </div>
    <!-- <div class="field col-12 md:col-3">
      <p-button
        label="Save Info"
        type="submit"
        [raised]="true"
        severity="success"
        [loading]="loader"
      ></p-button>
    </div> -->
    <div class="field flex justify-content-end actionbuttons p-3 gap-3">
      <p-button label="Cancel" type="buton" severity="secondary" [outlined]="true" class="mr-3" (onClick)="resetForm()"></p-button>
      <p-button label="Save Patient" type="submit" severity="primary" class="mr-3" [loading]="loader"></p-button>
    </div>
  </form>
</div>
<p-sidebar [(visible)]="showChronicPopup" position="right" styleClass="w-30rem" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()">
  <ng-template pTemplate="header"  style="border-bottom:1px solid #000000">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Add Chronic Conditions
      </span>
    </div>
  </ng-template>
  <div class="flex align-items-left flex-column mb-5 mt-3">
    <p-multiSelect [options]="availableChronicCondition" [(ngModel)]="selectedChronic"
      placeholder="Select Chronic Conditions" optionLabel="chonicConditionName" display="chip" [showClear]="true"
      appendTo="body" class="mb-2 w-full"></p-multiSelect>
    <div *ngIf="selectedChronic && selectedChronic.length > 0">
      <label><strong>Selected Chronic Conditions:</strong></label>
      <div *ngFor="let condition of selectedChronic">
        <p>
          {{ condition?.chonicConditionName }} {{ " - "
          }}{{ condition?.icdCode }}
        </p>
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="showChronicPopup = false" />
      <p-button label="Save"  (click)="addNewChroniCondition()" />
    </div>
  </ng-template>
</p-sidebar>