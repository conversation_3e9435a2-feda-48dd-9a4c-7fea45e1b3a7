<p-toast />
<div class="grid p-fluid mt-3">
  <div class="field col-12">
    <p-table
      [value]="inventoryList"
      [paginator]="true"
      [totalRecords]="totalCount"
      [rows]="itemsPerPage"
      [lazy]="true"
      responsiveLayout="scroll"
      styleClass="p-datatable-gridlines p-datatable-striped"
    >
      <ng-template pTemplate="caption">
        <div class="flex align-items-center justify-content-between">
          <div>
            <p-button
              icon="pi pi-cart-plus"
              severity="contrast"
              label="Total Inventory:
          {{ inventoryList.length }}"
            />
          </div>
          <div>
            <button
              pButton
              pRipple
              severity="secondary"
              icon="pi pi-plus"
              class="p-button-rounded p-button-success mr-2"
              style="height: 30px; width: 30px"
              (click)="showAddModalPopup()"
            ></button>
            <button
              pButton
              pRipple
              icon="pi pi-refresh"
              class="p-button-rounded p-button-primary mr-2"
              style="height: 30px; width: 30px"
              (click)="getInventoryList()"
            ></button>
          </div>
        </div>
      </ng-template>
      <ng-template pTemplate="header">
        <tr>
          <th>Image</th>
          <th>Device Name</th>
          <th>Device Type</th>
          <th>Make & Model</th>
          <th>Measurement</th>
          <th>Quantity</th>
          <th>Action</th>
        </tr>
      </ng-template>
      <ng-template let-inv pTemplate="body">
        <tr>
          <td>
            <img
              [src]="
                inv.imgUrl ? inv.imgUrl : 'assets/watchrx/images/no_image.jpg'
              "
              [alt]="inv.deviceName"
              width="50"
              height="50"
              class="shadow-4"
            />
          </td>
          <td>{{ inv.deviceName }}</td>
          <td>{{ inv.deviceType }}</td>
          <td>{{ inv.make }} {{ "\n" }} {{ inv.model }}</td>
          <td>
            <ul>
              <li *ngFor="let feature of inv.deviceMeasuresArray">
                {{ feature }}
              </li>
            </ul>
          </td>
          <td>{{ inv.quantity }}</td>
          <td>
            <div class="flex">
              <p-button
                icon="pi pi-ellipsis-v"
                severity="primary"
                [raised]="true"
                (click)="onOpenEditModal(inv)"
              />
            </div>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-dialog
  header="Add to inventory"
  [(visible)]="addModal"
  [modal]="true"
  [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }"
  [style]="{ width: '50vw' }"
  [draggable]="false"
  [resizable]="false"
>
  <form [formGroup]="deviceForm" (ngSubmit)="onSubmit()">
    <div class="flex flex-row">
      <div class="p-field col-6">
        <label for="deviceType">Device Type</label>
        <p-dropdown
          [options]="deviceTypes"
          formControlName="deviceType"
          placeholder="Select Device Type"
          optionLabel="typeName"
          optionValue="typeName"
          appendTo="body"
        ></p-dropdown>
        <div
          *ngIf="
            deviceForm.get('deviceType')?.invalid &&
            deviceForm.get('deviceType')?.touched
          "
          class="red"
        >
          Device Type is required.
        </div>
      </div>
      <div class="p-field col-6">
        <label for="deviceName">Device Name</label>
        <input
          id="deviceName"
          type="text"
          pInputText
          formControlName="deviceName"
        />
        <div
          *ngIf="
            deviceForm.get('deviceName')?.invalid &&
            deviceForm.get('deviceName')?.touched
          "
          class="red"
        >
          Device Name is required.
        </div>
      </div>
    </div>
    <div class="flex flex-row">
      <div class="p-field col-6">
        <label for="make">Make</label>
        <input id="make" type="text" pInputText formControlName="make" />
        <div
          *ngIf="
            deviceForm.get('make')?.invalid && deviceForm.get('make')?.touched
          "
          class="red"
        >
          Make is required.
        </div>
      </div>

      <div class="p-field col-6">
        <label for="model">Model</label>
        <input id="model" type="text" pInputText formControlName="model" />
        <div
          *ngIf="
            deviceForm.get('model')?.invalid && deviceForm.get('model')?.touched
          "
          class="red"
        >
          Model is required.
        </div>
      </div>
    </div>
    <div class="flex flex-row">
      <div class="p-field col-6">
        <label for="quantity">Quantity</label>
        <input
          id="quantity"
          type="number"
          pInputText
          formControlName="quantity"
        />
        <div
          *ngIf="
            deviceForm.get('quantity')?.invalid &&
            deviceForm.get('quantity')?.touched
          "
          class="red"
        >
          Quantity is required and must be greater than 0.
        </div>
      </div>
      <div class="p-field col-6">
        <label for="color">Color</label>
        <p-dropdown
          [options]="colors"
          formControlName="color"
          placeholder="Select Color"
          appendTo="body"
        ></p-dropdown>
      </div>
    </div>
    <div class="flex flex-row">
      <div class="p-field col-6">
        <label for="deviceMeasures">Measurement</label>
        <p-multiSelect
          [options]="measurements"
          formControlName="deviceMeasures"
          placeholder="Select Measurements"
          display="chip"
          appendTo="body"
        >
        </p-multiSelect>
        <div
          *ngIf="
            deviceForm.get('deviceMeasures')?.invalid &&
            deviceForm.get('deviceMeasures')?.touched
          "
          class="red"
        >
          Measurement is required.
        </div>
      </div>

      <div class="p-field col-6">
        <label for="deviceImage">Device Image</label>
        <p-fileUpload
          name="deviceImage"
          mode="basic"
          chooseLabel="Choose Device Image"
          chooseIcon="pi pi-upload"
          accept="image/*"
          [maxFileSize]="1000000"
          (onSelect)="onImageSelect($event)"
        ></p-fileUpload>
      </div>
    </div>
    <p-footer>
      <div class="flex col-4">
        <button pButton type="submit" label="Submit"></button>
      </div>
    </p-footer>
  </form>
</p-dialog>

<p-dialog
  header="Update {{ editDeviceName }} inventory"
  [(visible)]="editModel"
  [modal]="true"
  [style]="{ width: '480px' }"
  [draggable]="false"
  [resizable]="false"
>
  <div class="flex flex-column">
    <div class="p-field col-12">
      <label for="quantity">Quantity</label>
      <input id="quantity" type="number" pInputText [(ngModel)]="editQnty" />
    </div>

    <div class="p-field col-12">
      <label for="deviceImage">Device Image</label>
      <p-fileUpload
        name="deviceImage"
        mode="basic"
        chooseLabel="Choose Device Image"
        chooseIcon="pi pi-upload"
        accept="image/*"
        [maxFileSize]="1000000"
        (onSelect)="onEditImageSelect($event)"
      ></p-fileUpload>
    </div>

    <div class="p-field col-12">
      <label for="specUrl">Device Image</label>
      <p-fileUpload
        name="specUrl"
        mode="basic"
        chooseLabel="Choose Specification"
        chooseIcon="pi pi-upload"
        accept="image/*"
        [maxFileSize]="1000000"
        (onSelect)="onEditSpecImage($event)"
      ></p-fileUpload>
    </div>
  </div>
  <p-footer>
    <div class="flex col-4">
      <p-button
        icon="pi pi-pencil"
        severity="success"
        [raised]="true"
        label="Update"
        (click)="updateInventory()"
      />
    </div>
  </p-footer>
</p-dialog>
