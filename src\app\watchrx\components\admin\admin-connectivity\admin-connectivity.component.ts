import { Component, OnInit } from '@angular/core';
import moment from 'moment';
import { MessageService } from 'primeng/api';
import { DeviceConnectivity } from '../../../api/connectivity';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { ConnectivityService } from './service/connectivity.service';

@Component({
  selector: 'app-admin-connectivity',
  templateUrl: './admin-connectivity.component.html',
  styleUrls: ['./admin-connectivity.component.css'],
  providers: [MessageService],
})
export class AdminConnectivityComponent implements OnInit {
  constructor(
    private connectivityService: ConnectivityService,
    private loaderService: LoaderService,
    public menuService: MenuService,
    public messageService: MessageService
  ) {}

  deviceList: DeviceConnectivity[] = [];
  totalCount: number = 0;
  itemsPerPage: number = 10;
  visible: boolean = false;

  editDeviceId: string = '';
  editDeviceStatus: boolean = false;
  loader: boolean = false;

  ngOnInit() {
    this.menuService.changeMenu('Connectivity');
    this.loaderService.show();
    this.connectivityService.getConnectedDeviceList().subscribe((res) => {
      this.loaderService.hide();
      if (res?.success) {
        this.deviceList = res?.monitoredIMEIList;
        console.log('Device List:', this.deviceList);
        this.totalCount = res?.monitoredIMEIList?.length;
      }
    });
  }

  getName(status: boolean) {
    return status ? 'ONLINE' : 'OFFLINE';
  }

  getColor(status: boolean) {
    return status ? 'success' : 'danger';
  }

  formatDate = (dateString: string): string => {
    const date = moment(dateString, 'DD-MM-YYYY HH:mm:ss');
    return date.format('h:mm A, ddd D, MMM YYYY');
  };

  openModal(item: DeviceConnectivity) {
    this.visible = true;
    this.editDeviceId = item.imei;
    this.editDeviceStatus = item.status;
  }

  takeAction(type: string) {
    this.loader = true;
    let payload = { monitoredIMEIList: [this.editDeviceId], type: type };
    this.connectivityService.heartBeatOrLogs(payload).subscribe((response) => {
      this.loader = false;
      if (response.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Request sent successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to send request',
        });
      }
    });
  }
}
