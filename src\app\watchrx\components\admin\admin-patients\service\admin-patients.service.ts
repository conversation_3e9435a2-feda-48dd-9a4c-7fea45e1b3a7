import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { PatientListResp } from '../../../../api/allPatients';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class AdminPatientsService {
  constructor(private http: HttpClient) {}

  setActivePatientList(details: string): Observable<PatientListResp> {
    return this.http.get<PatientListResp>(
      environment.BASE_URL + Constants.PATIENT_LIST + details
    );
  }

  getAllOrgsClinicList(): Observable<any> {
    return this.http.get<any>(
      environment.BASE_URL + Constants.CLINIC_ORG_DETAILS
    );
  }

  setSearchPatientList(details: string): Observable<PatientListResp> {
    return this.http.get<PatientListResp>(
      environment.BASE_URL + Constants.SEARCH_PATIENT + details
    );
  }

  transferPatients(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.TRANSFER_PATIENTS,
      details
    );
  }

  sendFax(payload: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.SEND_FAX,
      payload,
    );
  }
}
