<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/gear.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Device Management</span
  >
</div>
<div class="position-relative">
  <p-button [label]="'Add Bulk Devices'" severity="secondary" [outlined]="true"
  (onClick)="openBulkAdd()" class="p-button-rounded p-button-secondary bulkadd" *ngIf="userRole.roleType==2"/>

  <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)" >
    <p-tabPanel header="Allocated Device">
      <app-allocated-medical-devices
        *ngIf="activeIndex === 0"
      ></app-allocated-medical-devices>
    </p-tabPanel>
    <p-tabPanel header="Unallocated Device">
      <app-unallocated-medical-devices
        *ngIf="activeIndex === 1"
      ></app-unallocated-medical-devices>
    </p-tabPanel>
     <!-- <p-tabPanel header="Bulk Add Devices" *ngIf="userRole.roleType==2">
      <!-- <app-bulk-add-devices *ngIf="activeIndex === 2"></app-bulk-add-devices>--
    </p-tabPanel>  -->
  </p-tabView>
</div>

<p-sidebar header="Allocate Device" [(visible)]="visible" [style]="{ width: '40rem' }" position="right"
  class="dashboardSidebar" (onHide)="activeIndex=0">
  <ng-template pTemplate="header">
    <div class="flex align-items-center">
      <span class="xl-font"> <i class="pi pi-plus mr-3"></i>Bulk Add Device &nbsp;</span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid mt-2">
    <app-bulk-add-devices (close)="closeTab()" (stopLoader)="loading=false"></app-bulk-add-devices>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
       <p-button label="Cancel" severity="secondary" (click)="visible = false;activeIndex=0" />
      <p-button label="Save" severity="primary" [loading]="loading" (click)="allocateDevie($event)"  /> 
    </div>
  </ng-template>
</p-sidebar>