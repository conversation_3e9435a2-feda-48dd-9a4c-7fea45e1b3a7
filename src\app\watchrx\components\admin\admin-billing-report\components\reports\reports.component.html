<p-toast />
<p-confirmDialog />
<div class="grid">
  <div class="col-12">
    <div class="flex flex-row mb-2 mt-3">
      <div class="col-3" *ngIf="userRole!=3">
        <span class="p-float-label">
          <p-dropdown
            [options]="orgsList"
            checkmark="true"
            optionLabel="groupName"
            [filter]="true"
            filterBy="groupName"
            [(ngModel)]="selectedOrg"
            [showClear]="false"
            placeholder="Select Organization"
            (onChange)="orgChange($event)"
            class="mr-2"
          /><label for="calendar">Org/Clinic</label></span
        >
      </div>

      <div class="flex col-3">
        <p-calendar
          [(ngModel)]="date"
          [showIcon]="true"
          [showOnFocus]="false"
          [placeholder]="'dd-mm-yyyy'"
          inputId="buttondisplay"
        />
      </div>
      <div class="flex col-3">
        <p-button
          [rounded]="false"
          icon="pi pi-refresh"
          severity="primary"
          class="ml-1"
          title="delete"
          label="Generate Report"
          (click)="generateReport()"
        />
      </div>
      <!-- <div class="col-2">
        <p-inputGroup>
          <input
            type="Search"
            pInputText
            placeholder="Search"
            [(ngModel)]="search"
          />
        </p-inputGroup>
      </div>
      <div class="col-1">
        <button
          type="button"
          pButton
          icon="pi pi-search"
          class="p-button-primary"
          (click)="dt.filterGlobal(search, 'contains')"
        ></button>
      </div> -->
    </div>

    <div class="grid p-fluid">
      <div class="field col-12">
        <p-table
          #dt
          [value]="reportsList"
          [paginator]="false"
          [totalRecords]="totalCaseManagers"
          [rows]="itemsPerPage"
          [lazy]="true"
          [loading]="loader"
          [globalFilterFields]="['orgName', 'physicianName', 'createdDate']"
          responsiveLayout="scroll"
          styleClass="p-datatable-gridlines p-datatable-striped"
        >
          <ng-template pTemplate="header">
            <tr>
              <th>Clinic</th>
              <th>Generated By</th>
              <th>Billing Date</th>
              <th>Download</th>
            </tr>
          </ng-template>
          <ng-template let-featureInfo pTemplate="body">
            <tr>
              <td>
                {{ featureInfo.orgName }}
              </td>
              <td>{{ featureInfo.physicianName }}</td>
              <td>{{ featureInfo.createdDate }}</td>
              <td>
                <div class="flex">
                  <p-button
                    [rounded]="false"
                    icon="pi pi-download"
                    severity="success"
                    size="small"
                    class="mr-1"
                    title="delete"
                    (click)="DownloadReport(featureInfo['fileURL'])"
                  />
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>
