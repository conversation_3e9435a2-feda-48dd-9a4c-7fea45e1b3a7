import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { MedicalDevicesTabComponent } from '../shared/medical-devices-tab/medical-devices-tab.component';
import { MedicalDevicesTabViewRoutingModule } from './medical-devices-tab-view-routing.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    MedicalDevicesTabViewRoutingModule,
    ButtonModule,
    TableModule,
    TabViewModule,
    FormsModule,
    InputTextModule,
    MedicalDevicesTabComponent,
  ],
})
export class MedicalDevicesTabViewModule {}
