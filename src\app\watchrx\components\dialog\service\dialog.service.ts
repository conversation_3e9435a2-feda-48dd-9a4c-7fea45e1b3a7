import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';

import {
  Dialog, AssignDialog
} from '../../../api/dialog';

@Injectable({
  providedIn: 'root'
})
export class DialogService {

  constructor(private http: HttpClient) {}
 
  DIALOGS_URL = '/service/patient/dialogsByUser';
  Assign_URL = '/service/patient/assignDialogToPatient';
  
  dialogData(): Observable<Dialog[]> {
    return this.http.post<Dialog[]>(environment.BASE_URL + this.DIALOGS_URL,null);
  }

  assign(request: AssignDialog): Observable<string> {
    return this.http.post<string>(environment.BASE_URL + this.Assign_URL, request);
  }
}
