import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { DropdownModule } from 'primeng/dropdown';
import { FileUploadModule } from 'primeng/fileupload';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputSwitchModule } from 'primeng/inputswitch';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RadioButtonModule } from 'primeng/radiobutton';
import { RippleModule } from 'primeng/ripple';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { CreateCaseManagerComponent } from '../shared/create-case-manager/create-case-manager.component';
import { SharedModule } from '../shared/shared.module';
import { AdminAccessControlComponent } from './admin-access-control/admin-access-control.component';
import { AssingUserOrganizationComponent } from './admin-access-control/components/assing-user-organization/assing-user-organization.component';
import { FeaturesComponent } from './admin-access-control/components/features/features.component';
import { OrganizationsComponent } from './admin-access-control/components/organizations/organizations.component';
import { AdminAlertsComponent } from './admin-alerts/admin-alerts.component';
import { AdminBillingReportComponent } from './admin-billing-report/admin-billing-report.component';
import { ReportsComponent } from './admin-billing-report/components/reports/reports.component';
import { StatisticsComponent } from './admin-billing-report/components/statistics/statistics.component';
import { AdminCaregiversComponent } from './admin-caregivers/admin-caregivers.component';
import { AdminCasemanagersComponent } from './admin-casemanagers/admin-casemanagers.component';
import { AdminConnectivityComponent } from './admin-connectivity/admin-connectivity.component';
import { AdminInventoryComponent } from './admin-inventory/admin-inventory.component';
import { AddDeviceTypeComponent } from './admin-inventory/components/add-device-type/add-device-type.component';
import { InventoryComponent } from './admin-inventory/components/inventory/inventory.component';
import { AdminMedicalDevicesComponent } from './admin-medical-devices/admin-medical-devices.component';
import { AdminPatientsComponent } from './admin-patients/admin-patients.component';
import { AdminPhysiciansComponent } from './admin-physicians/admin-physicians.component';
import { AdminRoutingModule } from './admin-routing.module';
import { AdminTemplatesComponent } from './admin-templates/admin-templates.component';
import { SecondaryAdminComponent } from './secondary-admin/secondary-admin.component';
import { SidebarModule } from 'primeng/sidebar';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ChartModule } from 'primeng/chart';
import { ProgressBarModule } from 'primeng/progressbar';
@NgModule({
  declarations: [
    AdminCasemanagersComponent,
    AdminPhysiciansComponent,
    AdminAccessControlComponent,
    FeaturesComponent,
    OrganizationsComponent,
    AssingUserOrganizationComponent,
    AdminCaregiversComponent,
    AdminMedicalDevicesComponent,
    AdminBillingReportComponent,
    StatisticsComponent,
    ReportsComponent,
    AdminConnectivityComponent,
    AdminTemplatesComponent,
    AdminPatientsComponent,
    AdminInventoryComponent,
    InventoryComponent,
    AddDeviceTypeComponent,
    SecondaryAdminComponent,
    AdminAlertsComponent,
  ],
  imports: [
    CommonModule,
    AdminRoutingModule,
    TableModule,
    ButtonModule,
    StyleClassModule,
    PanelMenuModule,
    InputGroupAddonModule,
    InputGroupModule,
    PanelModule,
    RippleModule,
    TabViewModule,
    FileUploadModule,
    InputTextModule,
    DialogModule,
    RadioButtonModule,
    ToastModule,
    ConfirmDialogModule,
    SharedModule,
    CreateCaseManagerComponent,
    DropdownModule,
    FormsModule,
    ReactiveFormsModule,
    MultiSelectModule,
    CalendarModule,
    CardModule,
    TagModule,
    AvatarModule,
    DialogModule,
    InputSwitchModule,
    FileUploadModule,
    CheckboxModule,
    DividerModule,
    SidebarModule,
    OverlayPanelModule,
    ChartModule,
    ProgressBarModule
  ],
  providers: [ConfirmationService, MessageService],
})
export class AdminModule {}
