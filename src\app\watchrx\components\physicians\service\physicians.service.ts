import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { physicians } from '../../../api/physician';
import { environment } from '../../../../../environments/environment';
import { DatePipe, formatDate } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class PhysicianService {
  static readonly PHYSICAN_URL = 'service/clinician/getPhysiciansByCaseMangerByUserIndex';
  static readonly SEARCH_URL = 'service/clinician/getPhysicianByOrgId';
  datePipe: DatePipe = new DatePipe('en-US');
  constructor(private http: HttpClient) {}

  getUsers(skip: number, rows: number): Observable<physicians> {

    return this.http.get<physicians>(
      environment.BASE_URL + PhysicianService.PHYSICAN_URL + '/' + skip + '/' + rows
    );
  } 

  searchUser(user:string)
  {
    return this.http.get<physicians>(
      environment.BASE_URL + PhysicianService.SEARCH_URL + '/'+user
    );
  }
}
