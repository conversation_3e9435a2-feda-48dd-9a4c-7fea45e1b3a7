.gray-box {
    background-color: rgb(0, 0, 0, 0.5);
    padding: 5px;
    display: flex;
    width: auto;
    border-radius: 10px;
}

.white-text {
    color: #ffffff;
}

.button-background {
    transition: background-color 0.3s ease;
}

.button-background:hover {
    background-color: #c8c8c8;
}

.font-16 {
    font-size: 16px;
}

.breadcrumb {
    padding: 20px;
    background-color: #ffffff;
    box-shadow: 0px 2px 4px -3px #000000;
    margin-bottom: 10px;
}

.editpatientscreen {
    margin-bottom: 40px;
    box-shadow: 0px 2px 4px -3px #000000;

    ::ng-deep .p-tabview-panels {
        padding: 0px !important;
    }

    ::ng-deep .p-tabview-panel {
        padding: 1.25rem !important
    }

    .profile,
    .medi,
    .encounter,
    .alerts,
    .customremainder {
        ::ng-deep .p-tabview-panel {
            padding: 0rem !important
        }
    }

}

.button-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-right: 12px;
    
    audio-recorder {
        display: flex;
        align-items: center;
    }
}