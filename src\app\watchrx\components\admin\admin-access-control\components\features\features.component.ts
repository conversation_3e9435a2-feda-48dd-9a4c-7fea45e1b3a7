import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { AdminAccessControlService } from '../../service/admin-access-control.service';
@Component({
  selector: 'app-features',
  templateUrl: './features.component.html',
  styleUrl: './features.component.scss',
})
export class FeaturesComponent implements OnInit {
  loader: boolean = false;
  caseManagersList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 10;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  constructor(
    private router: Router,
    private adminAccessControlService: AdminAccessControlService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef,
    private fb: FormBuilder
  ) {}

  userForm: FormGroup = this.fb.group({});

  ngOnInit(): void {
    this.userForm = this.fb.group({
      roleName: ['', Validators.required],
      roleDescription: ['', Validators.required],
    });
    this.cdr.detectChanges();
  }

  loadFeaturesList($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loader = true;
    let pageSize = $event?.rows || 10;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    let url = pageNo + '/' + pageSize;
    this.adminAccessControlService.getFeaturesList().subscribe((res) => {
      if (res.status) {
        console.log(res);
        this.caseManagersList = res.roles!;
        this.loader = false;
      }
    });
  }

  onDeleteFeature(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete this feature? Upon deleting the feature will be lost from all the Orgs and database`,
      header: 'Delete Feature',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-secondary p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      accept: () => {
        this.loaderService.show();
        this.adminAccessControlService.deleteFeature({ roleId: id }).subscribe(
          (res) => {
            this.loaderService.hide();
            if (res) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Feature deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadFeaturesList();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          },
          (err) => {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        );
      },
      reject: () => {},
    });
  }

  addCaseMaanger() {
    this.visible = true;
  }

  onSubmit() {
    this.loaderService.show();
    this.adminAccessControlService.saveFeature(this.userForm.value).subscribe(
      (res) => {
        this.loaderService.hide();
        this.userForm.reset();
        this.visible = false;
        if (res?.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Feature Added successfully.',
            life: 3000,
          });
          setTimeout(() => {
            this.loadFeaturesList();
          }, 1000);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'Something went wrong',
            life: 3000,
          });
        }
      },
      (err) => {
        this.userForm.reset();
        this.visible = false;
        this.loaderService.hide();
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Something went wrong',
          life: 3000,
        });
      }
    );
  }

  // {"roleName":"sample Role","roleDescription":"sampleRole"}
}
