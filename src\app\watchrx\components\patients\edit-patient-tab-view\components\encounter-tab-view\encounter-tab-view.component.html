<p-tabView
  [(activeIndex)]="activeIndex"
  class="custom-tab-view encountertab"
  (onChange)="onEncounterTabClick($event)"
>
  <p-tabPanel header="Encounter">
    <app-encounter
      *ngIf="activeIndex == 0"
      [id]="patientId"
      (dataEmitter)="handleDataFromChild($event)"
      [patientData]="patientData"
    ></app-encounter>
  </p-tabPanel>
  <p-tabPanel header="Text Message">
    <app-text-messages
      *ngIf="activeIndex == 1"
      [id]="patientId"
      (dataEmitter)="handleDataFromChild($event)"
      class="position-relative"
    ></app-text-messages>
  </p-tabPanel>
  <p-tabPanel header="Phone Communication">
    <app-phone-communication
      *ngIf="activeIndex == 2"
      [id]="patientId"
      (dataEmitter)="handleDataFromChild($event)"
    ></app-phone-communication>
  </p-tabPanel>
  <p-tabPanel header="Documentation">
    <app-documentation
      *ngIf="activeIndex == 3"
      [id]="patientId"
      (dataEmitter)="handleDataFromChild($event)"
    ></app-documentation>
  </p-tabPanel>
  <p-tabPanel header="Tasks">
    <app-tasks
      *ngIf="activeIndex == 4"
      [id]="patientId"
      (dataEmitter)="handleDataFromChild($event)"
    ></app-tasks>
  </p-tabPanel>
</p-tabView>

