.rounded-image {
    border-radius: 50%;
    width: 115px;
    height: 115px;
}

.image-container {
    position: relative;
    border-radius: 50px 0 0 50px;
}

.image-text {
    position: absolute;
    top: 25%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    padding: 5px;
    font-size: large;
    font-family: 'Courier New', Courier, monospace;
    border-radius: 10px;
    width: 90%;
}

.image-topLeftRoundedBackground {
    background-size: cover;
    width: 100%;
    height: 125px;
    border-radius: 10px 10px 0 0;
}

.left-grid,
.right-grid {
    border: 1px solid #E8E8E8;

    hr {
        margin: 0px !important;
    }

    .item {
        padding: 15px 10px !important;
    }

}

.config {

    h6 {
        margin: 0px !important;
    }

    ::ng-deep .p-chip {
        background-color: #336CFB;
        padding: 0px 10px;
        color: #ffffff;
    }

    ::ng-deep .p-card-header {
        padding: 15px 10px;
        margin: 0px !important;
    }

    ::ng-deep .p-card-body {
        padding: 0px !important
    }

    ::ng-deep .p-card {
        box-shadow: 0px 0px 0px 0px !important;
        border: 1px solid #E8E8E8 !important;
    }
}

.config1 {

    h6,
    hr {
        margin: 0px !important;
    }

    ::ng-deep .p-card-header {
        padding: 15px 10px;
        margin: 0px !important;
    }

    ::ng-deep .p-card-body {
        padding: 0px !important
    }

    ::ng-deep .p-card {
        box-shadow: 0px 0px 0px 0px !important;
        border: 1px solid #E8E8E8 !important;
    }

}

.daily ::ng-deep .p-button {
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}

.month ::ng-deep .p-button {
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}
.gray
{
    background-color: #E8E8E8;
}
.l-blue {
    background: linear-gradient(45deg, #72c2ff, #86f0ff) !important;
}
.xl-khaki {
    background: #f9f1d8 !important;
}
.xl-parpl {
    background: #efebf4 !important;
}