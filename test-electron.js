const { app, BrowserWindow, ipcMain, desktopCapturer } = require('electron');
const path = require('path');

const createWindow = () => {
  console.log('Creating Electron window...');

  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    backgroundColor: '#ffffff'
  });

  console.log('Window created, loading URL...');

  // Load the URL immediately
  mainWindow.loadURL('http://localhost:4200/login');

  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show, displaying...');
    mainWindow.show();
    mainWindow.focus();
    mainWindow.webContents.openDevTools();
  });

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Page finished loading');
    if (!mainWindow.isVisible()) {
      mainWindow.show();
    }
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log(`Failed to load: ${errorCode} - ${errorDescription} - ${validatedURL}`);
    if (errorCode === -102) {
      console.log('Connection refused, retrying with 127.0.0.1...');
      mainWindow.loadURL('http://localhost:4200/login');
    } else if (errorCode === -105) {
      console.log('Name not resolved, retrying with 127.0.0.1...');
      mainWindow.loadURL('http://localhost:4200/login');
    }
  });

  // Force show window after 3 seconds if it hasn't shown yet
  setTimeout(() => {
    if (!mainWindow.isVisible()) {
      console.log('Force showing window after timeout...');
      mainWindow.show();
    }
  }, 3000);

  return mainWindow;
};

const setupIPC = () => {
  ipcMain.handle('get-desktop-sources', async () => {
    try {
      const sources = await desktopCapturer.getSources({
        types: ['window', 'screen'],
        thumbnailSize: { width: 150, height: 150 },
        fetchWindowIcons: true
      });
      
      return sources.map(source => ({
        id: source.id,
        name: source.name,
        thumbnail: source.thumbnail.toDataURL(),
        display_id: source.display_id,
        appIcon: source.appIcon ? source.appIcon.toDataURL() : null
      }));
    } catch (error) {
      return [];
    }
  });

  ipcMain.handle('get-system-audio-stream', async (event, sourceId) => {
    try {
      console.log('Getting system audio stream for source:', sourceId);
      return { success: true, streamId: sourceId };
    } catch (error) {
      console.error('System audio stream error:', error);
      return { success: false, error: error.message };
    }
  });
};

console.log('Starting Electron app...');

app.whenReady().then(() => {
  console.log('Electron app is ready');
  setupIPC();
  const window = createWindow();

  // Keep a reference to prevent garbage collection
  global.mainWindow = window;

  app.on('activate', () => {
    console.log('App activated');
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  console.log('App is about to quit');
});