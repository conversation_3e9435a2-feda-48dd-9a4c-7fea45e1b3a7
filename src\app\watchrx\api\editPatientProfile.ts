export interface HealthData {
  data: VitalData[];
  thresholdStatus: ThresholdStatus;
  dates: string[];
  threshold: Threshold;
  message: string[];
  status: boolean;
}

export interface VitalData {
  vitalType: string;
  value: (number | string)[];
}

export interface ThresholdStatus {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string | null;
  thresholdConfigId: number | null;
  heartRateIsEnabled: boolean;
  heartRateMin: number | null;
  heartRateMax: number | null;
  heartRateCriticalMin: number | null;
  heartRateCriticalMax: number | null;
  bloodSugarIsEnabled: boolean;
  bloodSugarMin: number | null;
  bloodSugarMax: number | null;
  bloodSugarCriticalMin: number | null;
  bloodSugarCriticalMax: number | null;
  systolicBloodPressureIsEnabled: boolean;
  systolicBloodPressureMin: number | null;
  systolicBloodPressureMax: number | null;
  systolicBloodPressureCriticalMin: number | null;
  systolicBloodPressureCriticalMax: number | null;
  diastolicBloodPressureIsEnabled: boolean;
  diastolicBloodPressureMin: number | null;
  diastolicBloodPressureMax: number | null;
  diastolicBloodPressureCriticalMin: number | null;
  diastolicBloodPressureCriticalMax: number | null;
  weightIsEnabled: boolean;
  weightMin: number | null;
  weightMax: number | null;
  weightCriticalMin: number | null;
  weightCriticalMax: number | null;
  temperatureIsEnabled: boolean;
  temperatureMax: number | null;
  temperatureMin: number | null;
  temperatureCriticalMin: number | null;
  temperatureCriticalMax: number | null;
  pedometerStepIsEnabled: boolean;
  pedometerStepCountMin: number | null;
  pedometerStepCountMax: number | null;
  pedometerStepCountCriticalMin: number | null;
  pedometerStepCountCriticalMax: number | null;
  spo2IsEnabled: boolean;
  spo2Min: number | null;
  spo2Max: number | null;
  spo2CriticalMin: number | null;
  spo2CriticalMax: number | null;
  sleepMonitorIsEnabled: boolean;
  patientId: number | null;
}

export interface Threshold {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string | null;
  thresholdConfigId: number;
  heartRateIsEnabled: boolean;
  heartRateMin: number;
  heartRateMax: number;
  heartRateCriticalMin: number;
  heartRateCriticalMax: number;
  bloodSugarIsEnabled: boolean;
  bloodSugarMin: number;
  bloodSugarMax: number;
  bloodSugarCriticalMin: number;
  bloodSugarCriticalMax: number;
  systolicBloodPressureIsEnabled: boolean;
  systolicBloodPressureMin: number;
  systolicBloodPressureMax: number;
  systolicBloodPressureCriticalMin: number;
  systolicBloodPressureCriticalMax: number;
  diastolicBloodPressureIsEnabled: boolean;
  diastolicBloodPressureMin: number;
  diastolicBloodPressureMax: number;
  diastolicBloodPressureCriticalMin: number;
  diastolicBloodPressureCriticalMax: number;
  weightIsEnabled: boolean;
  weightMin: number;
  weightMax: number;
  weightCriticalMin: number;
  weightCriticalMax: number;
  temperatureIsEnabled: boolean;
  temperatureMax: number;
  temperatureMin: number;
  temperatureCriticalMin: number;
  temperatureCriticalMax: number;
  pedometerStepIsEnabled: boolean;
  pedometerStepCountMin: number;
  pedometerStepCountMax: number;
  pedometerStepCountCriticalMin: number;
  pedometerStepCountCriticalMax: number;
  spo2IsEnabled: boolean;
  spo2Min: number;
  spo2Max: number;
  spo2CriticalMin: number;
  spo2CriticalMax: number;
  sleepMonitorIsEnabled: boolean;
  patientId: number | null;
}

export interface MeasuredData {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string | null;
  measuredDates: string[];
  vitalsCountGraphVOs: VitalCountGraphVO[];
  thresholdConfig: null;
}

export interface VitalCountGraphVO {
  vitalTypeName: string | null;
  vitalTypeId: number | null;
  counts: number[];
  counts1: number[] | null;
  alerts: string | null;
}

export interface PatientData {
  contactDetails: any;
  isConsentStatus: string | undefined;
  consentStatus: string | undefined;
  gender?: any;
  patientId: number;
  patientName: string | null;
  patientCreatedDate: string;
  orgId: number | null;
  casemanagerId: number | null;
  priDoctor: string | null;
  clinician: Clinician;
  morningClinician: string | null;
  noonClinician: string | null;
  nightClinician: string | null;
  nameSos: string | null;
  phoneNumbersos: string | null;
  primaryCaseManager: string | null;
  mrn: string;
  gcmId: string | null;
  vitalVO: VitalVO;
  connectingDevice: string;
  timezone: string;
  phoneNumber: string;
  persPhoneNumber: string | null;
  altPhoneNumber: string | null;
  screeningRequestVOList: ScreeningRequestVO[] | null;
  address: Address;
  maritalStatus: string | null;
  language: string | null;
  dob: number;
  visitReason: string | null;
  revisit: boolean;
  watch: Watch;
  specialist: string | null;
  employment: string | null;
  ssn: string | null;
  userName: string | null;
  password: string | null;
  roleType: number;
  caregiverRelationship: string | null;
  firstName: string;
  lastName: string;
  picPath: string;
  createdDate: number;
  updatedDate: number;
  status: string;
  imageFile: string | null;
  fileModified: boolean;
  earlyMorningHour: string;
  breakFastHour: string;
  lunchHour: string;
  noonSnackHour: string;
  dinnerHour: string;
  bedHour: string;
  earlyMorningMin: string;
  breakFastMin: string;
  lunchMin: string;
  noonSnackMin: string;
  dinnerMin: string;
  bedMin: string;
  timeSlots: string;
  mobilenoSoS: string | null;
  physicianId: number;
  physicianName: string;
  physicianEmail: string;
  physicianPhone: string;
  chronicConditions: ChronicCondition[];
  insuranceVOList: InsuranceVO[];
  cptVOList: CptVO[];
  alerts: Alert[];
  billingStatus: string;
  thresholdData: ThresholdData;
  thresholdMinMax: ThresholdMinMax;
  email: string;
  enMins: number | null;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  dateOfBirth: string;
  daysMeasured: number;
  groupName: string;
  isConsentTaken: boolean;
  programs: Programs;
  consentData: string | null;
  dueScreening: string[];
  patientAddress: string;
}

export interface Clinician {
  address1: string | null;
  address2: string | null;
  city: string | null;
  state: string | null;
  zip: string | null;
  country: string | null;
  platformType: string | null;
  phoneNumber: string;
  altPhoneNumber: string | null;
  hospital: string | null;
  address: string | null;
  speciality: string | null;
  specialityName: string | null;
  shift: string | null;
  shiftName: string | null;
  available: boolean;
  userName: string | null;
  password: string | null;
  roleType: string | null;
  firstName: string;
  lastName: string;
  addressView: string | null;
  email: string | null;
  picPath: string | null;
  createdDate: string | null;
  updatedDate: string | null;
  status: string | null;
  name: string | null;
  imageFile: string | null;
  fileModified: boolean;
  patientCount: number | null;
  isDisplayReminder: boolean | null;
  userStatus: string | null;
  passwordExpired: boolean;
  clinicianId: number;
  userID?: string | null;
}

export interface VitalVO {
  vitalId: number | null;
  userId: number | null;
  patientId: number | null;
  createdDate: string | null;
  systolicBloodPressure: number;
  systolicBloodPressureDate: string;
  diastolicBloodPressure: number;
  diastolicBloodPressureDate: string;
  weight: number;
  weightDate: string;
  temperature: number;
  temperatureDate: string;
  fasting: number;
  fastingDate: string;
  normal: number | null;
  normalDate: string | null;
  userName: string | null;
  source: string | null;
  stepCount: number;
  stepCountDate: string;
  spo2: number;
  spo2Date: string;
  heartRate: number;
  heartRateDate: string;
  deepSleep: string;
  shallowSleep: string;
  sleepTime: string;
}

export interface ScreeningRequestVO {
  // Add properties if there are any specific to ScreeningRequestVO
}

export interface Address {
  name: string | null;
  address1: string;
  address2: string;
  state: string;
  city: string;
  zip: string;
  country: string;
  addressId: number;
  status: string | null;
  phoneNumber: string | null;
}

export interface Watch {
  watchId: number;
  imeiNumber: string;
  phoneNumber: string | null;
  watchMake: string | null;
  watchModel: string | null;
  watchColor: string | null;
  modelNumber: string | null;
  assignStatus: string | null;
  status: string | null;
  imageFile: string | null;
  picPath: string | null;
  fileModified: boolean;
  appColor: string | null;
  appLanguage: string | null;
  isActive: boolean | null;
  isCustom: boolean | null;
  deviceMeasures: string | null;
  deviceMeasuresArray: string | null;
  deviceType: string;
  deviceName: string | null;
  orgId: string | null;
  patient: string | null;
  clinician: string | null;
  patientWatchAssignmntVO: string | null;
}

export interface ChronicCondition {
  patientId?: number | null;
  chonicId: number;
  chronicConditionName: string;
  chonicConditionName?: string;
  icdCode: string | null;
}

export interface InsuranceVO {
  insuranceId: number;
  insuranceCompany: string;
  insuranceNumber?: string;
  insuranceMemberNumber: string;
  patientId?: number | null;
  status?:string;
}

export interface CptVO {
  cptId: number;
  cptCode: string;
  patientId: number | null;
}

export interface Alert {
  alertDescription: string;
  createdDateTime: string;
}

export interface ThresholdData {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string | null;
  thresholdConfigId: number | null;
  heartRateIsEnabled: boolean;
  heartRateMin: number | null;
  heartRateMax: number | null;
  heartRateCriticalMin: number | null;
  heartRateCriticalMax: number | null;
  bloodSugarIsEnabled: boolean;
  bloodSugarMin: number | null;
  bloodSugarMax: number | null;
  bloodSugarCriticalMin: number | null;
  bloodSugarCriticalMax: number | null;
  systolicBloodPressureIsEnabled: boolean;
  systolicBloodPressureMin: number | null;
  systolicBloodPressureMax: number | null;
  systolicBloodPressureCriticalMin: number | null;
  systolicBloodPressureCriticalMax: number | null;
  diastolicBloodPressureIsEnabled: boolean;
  diastolicBloodPressureMin: number | null;
  diastolicBloodPressureMax: number | null;
  diastolicBloodPressureCriticalMin: number | null;
  diastolicBloodPressureCriticalMax: number | null;
  weightIsEnabled: boolean;
  weightMin: number | null;
  weightMax: number | null;
  weightCriticalMin: number | null;
  weightCriticalMax: number | null;
  temperatureIsEnabled: boolean;
  temperatureMax: number | null;
  temperatureMin: number | null;
  temperatureCriticalMin: number | null;
  temperatureCriticalMax: number | null;
  pedometerStepIsEnabled: boolean;
  pedometerStepCountMin: number | null;
  pedometerStepCountMax: number | null;
  pedometerStepCountCriticalMin: number | null;
  pedometerStepCountCriticalMax: number | null;
  spo2IsEnabled: boolean;
  spo2Min: number | null;
  spo2Max: number | null;
  spo2CriticalMin: number | null;
  spo2CriticalMax: number | null;
  sleepMonitorIsEnabled: boolean;
  patientId: number | null;
}

export interface ThresholdMinMax {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string | null;
  thresholdConfigId: number;
  heartRateIsEnabled: boolean;
  heartRateMin: number;
  heartRateMax: number;
  heartRateCriticalMin: number;
  heartRateCriticalMax: number;
  bloodSugarIsEnabled: boolean;
  bloodSugarMin: number;
  bloodSugarMax: number;
  bloodSugarCriticalMin: number;
  bloodSugarCriticalMax: number;
  systolicBloodPressureIsEnabled: boolean;
  systolicBloodPressureMin: number;
  systolicBloodPressureMax: number;
  systolicBloodPressureCriticalMin: number;
  systolicBloodPressureCriticalMax: number;
  diastolicBloodPressureIsEnabled: boolean;
  diastolicBloodPressureMin: number;
  diastolicBloodPressureMax: number;
  diastolicBloodPressureCriticalMin: number;
  diastolicBloodPressureCriticalMax: number;
  weightIsEnabled: boolean;
  weightMin: number;
  weightMax: number;
  weightCriticalMin: number;
  weightCriticalMax: number;
  temperatureIsEnabled: boolean;
  temperatureMax: number;
  temperatureMin: number;
  temperatureCriticalMin: number;
  temperatureCriticalMax: number;
  pedometerStepIsEnabled: boolean;
  pedometerStepCountMin: number;
  pedometerStepCountMax: number;
  pedometerStepCountCriticalMin: number;
  pedometerStepCountCriticalMax: number;
  spo2IsEnabled: boolean;
  spo2Min: number;
  spo2Max: number;
  spo2CriticalMin: number;
  spo2CriticalMax: number;
  sleepMonitorIsEnabled: boolean;
  patientId: number;
}

export interface Programs {
  selectedPrograms: SelectedProgram[];
  availablePrograms: SelectedProgram[];
}

export interface SelectedProgram {
  mins: number;
  programName: string;
  patientProgramId: number;
  programActivated: boolean;
  programId: number;
}

export interface GenericResponse {
  messages: string[];
  status: boolean;
  responseCode: any;
  success: boolean;
}

export interface ConsentData {
  consentEmail: string;
  consentPhone: string;
}

export interface PatientConsentData {
  dateTime: string;
  patientName: string;
  patientId: number;
  patientEmail: string;
  contactPhone: string;
  status: boolean;
}

export interface EmergengenyContact {
  contactId: number;
  name: string;
  number: string;
  selection: boolean;
  peers: boolean;
  status?:string;
}
export interface AvialableChronicConditions {
  success: boolean;
  responseCode: string;
  messages: [];
  eList: ChronicCondition[];
}

export interface PhysicianResponse {
  success: boolean;
  responseCode: string;
  messages: [];
  physicianVOList: Physician[];
  clinicianVOList: Clinician[];
}
export interface Physician {
  physicianId: number;
  firstName: string;
  lastName: string;
  email: string;
  watchrxUserID?:string
}

export interface DeviceResponse {
  success: boolean;
  responseCode: string;
  messages: [];
  patientsInfo: DeviceInfo[];
}

export interface DeviceInfo {
  patientId: string;
  watchId: string;
  caregiverId?: string | null;
  imgPath?: string | null;
  patientNamePhone: string;
  patientAddress?: string | null;
  assignedWatch?: string | null;
  caregiverName?: string | null;
  patientName?: string | null;
  patientPhoneNumber?: string | null;
  watchPhoneNumber?: string | null;
  imei: string;
  watchPlan: string;
  caregiverRelationship?: string | null;
  isActive: boolean;
  chronicConditions?: string | null;
  alerts?: string | null;
  physicianId?: string | null;
  physicianName?: string | null;
  billingStatus?: string | null;
  caseManagerName?: string | null;
  caseManagerId?: string | null;
  careGiverVOList?: any[] | null;
  deviceMeasures?: any | null;
  deviceMeasuresArray?: any[] | null;
  deviceType: string;
  deviceName?: string | null;
  dob?: string | null;
  cptCode?: string | null;
  mrnNo?: string | null;
  noOfDays?: number | null;
  totalMins?: number | null;
  rpmCcMMins?: number | null;
  totalDaysLooking?: number | null;
  groupName?: string | null;
  rpmMins?: number | null;
  ccmMins?: number | null;
  pcmMins?: number | null;
  rtmMins?: number | null;
  missedDays?: number | null;
}

export interface contactInfo {
  id?: number;
  contactDays: string;
  contactFromTime: string |null;
  contactToTime: string|null;
  patientId?: number | null;
  status?:string;
}
