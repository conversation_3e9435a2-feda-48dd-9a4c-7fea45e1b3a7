import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { PatientList, PatientTask } from '../../../../api/patientTask';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class WatchrxCalendarService {
  constructor(private http: HttpClient) {}

  getTaskList(): Observable<PatientTask[]> {
    return this.http.get<PatientTask[]>(
      environment.BASE_URL + Constants.GET_TASK_LIST
    );
  }

  addTask(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.ADD_TASK,
      data
    );
  }

  updateTask(data: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_TASK,
      data
    );
  }

  deleteTask(data: any): Observable<GenericResponse> {
    return this.http.delete<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_TASK + data
    );
  }

  getPatientList(): Observable<PatientList> {
    return this.http.get<PatientList>(
      environment.BASE_URL + Constants.PATIENT_LIST
    );
  }
  getEmailNotification():Observable<GenericResponse> {
    return this.http.get<GenericResponse>(
      environment.BASE_URL + Constants.NOTIFICATION
    );
  }
  sendEmailNotification(status:boolean):Observable<GenericResponse> {
    return this.http.get<GenericResponse>(
      environment.BASE_URL + Constants.SEND_NOTIFICATION+`?status=${status}`
    );
  }
}
