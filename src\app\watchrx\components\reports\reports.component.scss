.custom-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.p-button-sm {
    //padding: 0.1rem 0.3rem !important;
    font-size: 0.65rem !important;
    line-height: 1rem !important;
    height: auto !important;
}

.gradient-card {
    background: linear-gradient(0deg, rgba(247, 218, 53, 0.5), rgba(123, 245, 238, 0.5)),
        linear-gradient(92.54deg, #FFFFFF 47.88%, #F5F5F5 100.01%);
    padding: 20px;
    border-radius: 10px;
    color: bla;
}
.border
{
    border-bottom: 1px solid #E8E8E8;
}
hr{
    margin: 0;
}


 // We need to use ::ng-deep to overcome view encapsulation
 ::ng-deep {
    .custom-slider
    {
        pointer-events: none;
        padding: 10px 20px;
    }
    .custom-slider .ngx-slider .ngx-slider-bar {
      background: #FF6760;
      height: 10px;
    }
    .custom-slider .ngx-slider .ngx-slider-selection {
      background: #FF6760
    }
  
    .custom-slider .ngx-slider[disabled] .ngx-slider-selection
    {
        background-color: #16D090;
    }
    .custom-slider .ngx-slider .ngx-slider-pointer {
      width: 8px;
      height: 16px;
      top: auto; /* to remove the default positioning */
      bottom: 0;
      background-color: #333;
      border-top-left-radius: 3px;
      border-top-right-radius: 3px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-pointer:after {
      display: none;
    }
  
    .custom-slider .ngx-slider .ngx-slider-bubble {
      bottom: 14px;
      font-size: 12px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-bubble.ngx-slider-limit {
      //font-weight: bold;
      //color: rgb(0, 0, 0);
      font-size: 12px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-tick {
      width: 1px;
      height: 10px;
      margin-left: 4px;
      border-radius: 0;
      background: #ffe4d1;
      top: -1px;
    }
  
    .custom-slider .ngx-slider .ngx-slider-tick.ngx-slider-selected {
      background: orange;
    }
  }
  .w-50
  {
    width: 50%;
  }
  .week ::ng-deep .p-button
{
    border-radius: 0px !important;
}
.daily ::ng-deep .p-button
{
    border-top-right-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}

.month ::ng-deep .p-button
{
    border-top-left-radius: 0px !important;
    border-bottom-left-radius: 0px !important;
}
::ng-deep .left-button, ::ng-deep .right-button, ::ng-deep .center-button
{
  background-color: #FFFFFF;
  color: #25282B;
  border: 0px;
}
::ng-deep .right-button
{
  border-top: 1px solid #A0A4A8 !important;;
    border-bottom: 1px solid #A0A4A8 !important;
    border-right: 1px solid #A0A4A8 !important;;
}
::ng-deep .left-button
{
  border-top: 1px solid #A0A4A8 !important;;
    border-bottom: 1px solid #A0A4A8 !important;
    border-left: 1px solid #A0A4A8 !important;;
}
::ng-deep .center-button
{
  border-top: 1px solid #A0A4A8 !important;;
    border-bottom: 1px solid #A0A4A8 !important;
}
::ng-deep .ngx-slider-floor
{
  opacity: 1 !important;
}
::ng-deep .ngx-slider-ceil
{
  opacity: 1 !important;
}
::ng-deep .ngx-slider-model-value
{
  top:20px !important
}
::ng-deep .ngx-slider-model-high
{
  top:20px !important
}