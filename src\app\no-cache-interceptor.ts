import {
    <PERSON>ttp<PERSON><PERSON>,
    <PERSON>ttp<PERSON><PERSON><PERSON>,
    HttpInterceptor,
    HttpRequest,
  } from '@angular/common/http';
  import { Injectable } from '@angular/core';
  import { Observable } from 'rxjs';
  
  @Injectable()
  export class NoCacheHeadersInterceptor implements HttpInterceptor {
    constructor() {}
  
    intercept(
      request: HttpRequest<unknown>,
      next: <PERSON>ttpHandler
    ): Observable<HttpEvent<unknown>> {
      const authReq = request.clone({
        setHeaders: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache',
        },
      });
      return next.handle(authReq);
    }
  }