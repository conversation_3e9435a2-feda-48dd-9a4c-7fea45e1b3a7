<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/chat.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Dialog</span
  >
</div>
<div class="p-3"  style="background-color: #ffffff;">
  <div class="flex flex-row align-items-between justify-content-between mb-3">
    <div>
      <p-multiSelect [options]="patientList" [(ngModel)]="selectedPatient" optionLabel="patientName"
        placeholder="Select Patients" display="chip"></p-multiSelect>
    </div>
    <p-button [raised]="true" label="Assign Dialogs" severity="primary" [outlined]="true"
      (onClick)="assignDialog()"></p-button>
  </div>
  <!--scrollHeight="400px"-->
  <p-table [value]="response" [loading]="loadingDialogs"  [tableStyle]="{'min-width': '50rem'}"
    [scrollable]="true" selectionMode="multiple"  styleClass="p-datatable-gridlines p-datatable-striped">
    >
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 20%">Choose Dialog</th>
        <th style="width: 20%">Dialog Name</th>
        <th style="width: 40%">
          Dialog Description
        </th>
        <th style="width: 20%">
          Assigned Patients
        </th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-dialog>
      <tr>
        <td>
          <p-tableCheckbox [value]="dialog" (click)="checkValue(dialog)"></p-tableCheckbox>
        </td>
        <td>{{ dialog.dialogName }}</td>
        <td> {{ dialog.dialogDescription }}
        </td>
        <td><span class="cursor-pointer" (click)="openSidebar(dialog)">{{ dialog?.patientDialogs?.length }}</span></td>
      </tr>
    </ng-template>
  </p-table>
</div>
<p-toast key="tst"></p-toast>
<p-sidebar [(visible)]="PatientsListPopup" position="right" styleClass="w-40rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Assigned Patients
      </span>
    </div>
  </ng-template>
  <hr class="full-width-line sidebarhr" />
 <ul class="custom-list">
  <li *ngFor="let name of selectedDialog?.patientDialogs"> {{name}}</li>
 </ul>
</p-sidebar>