import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GenericDateService {
  constructor() {}

  getStartDateOfMonth(): Date {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), 1);
  }

  getCurrentDate(): Date {
    return new Date();
  }

  getYesterdayDate(): Date {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    return yesterday;
  }

  getThreeMonthOldDate(): Date {
    const currentDate = new Date();
    return new Date(currentDate.setMonth(currentDate.getMonth() - 3));
  }
}
