.gray-box {
    background-color: rgb(0, 0, 0, 0.5);
    padding: 15px;
    display: flex;
    width: auto;
    border-radius: 10px;
}

.white-text {
    color: #ffffff;
}
::ng-deep .encountertab
{
::ng-deep .p-tabview-nav-link
{
    border: 1px solid !important;
    padding: 0.95rem 1.25rem !important;
    border-radius: 3px;
    margin: 0px !important;
    border-color:#e5e7eb !important;
}
::ng-deep .p-tabview-nav
{
    border: 0px !important;
}
::ng-deep .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link
{
    background: #f8f8fa !important;
    border-color:#e5e7eb !important;
    color: #6366F1;
}
}
.encountertab
{
    ::ng-deep .p-tabview-nav-container{
        padding: 1.25rem !important;
        border-bottom: 1px solid #E8E8E8;
    }
   
} 
.position-relative{
    position: relative;
}
