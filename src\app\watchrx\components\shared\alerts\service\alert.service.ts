import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { AlertRequest, AlertResponse } from '../../../../api/alert';
import { AlertList } from '../../../../api/dashboard';

@Injectable({
  providedIn: 'root',
})
export class AlertService {
  constructor(private http: HttpClient) {}

  ALERT_URL = 'service/patient/getAlertsByClinicianForFilter';
  EXPORT_URL = 'service/patient/downloadXls';
  DELETE_URL='service/patient/deleteAlert/';
  alertData(alertRequest: AlertRequest): Observable<AlertResponse> {
    return this.http.post<AlertResponse>(
      environment.BASE_URL + this.ALERT_URL,
      alertRequest
    );
  }

  exportData(alertRequest: AlertRequest): Observable<AlertList[]> {
    return this.http.post<AlertList[]>(
      environment.BASE_URL + this.EXPORT_URL,
      alertRequest
    );
  }
  deleteData(alertId:number): Observable<AlertList[]> {
    return this.http.get<AlertList[]>(
      environment.BASE_URL + this.DELETE_URL+alertId,
    );
  }
}
