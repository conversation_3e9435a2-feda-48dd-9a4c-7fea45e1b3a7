import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminAccessControlComponent } from './admin-access-control/admin-access-control.component';
import { AdminAlertsComponent } from './admin-alerts/admin-alerts.component';
import { AdminBillingReportComponent } from './admin-billing-report/admin-billing-report.component';
import { AdminCaregiversComponent } from './admin-caregivers/admin-caregivers.component';
import { AdminCasemanagersComponent } from './admin-casemanagers/admin-casemanagers.component';
import { AdminConnectivityComponent } from './admin-connectivity/admin-connectivity.component';
import { AdminInventoryComponent } from './admin-inventory/admin-inventory.component';
import { AdminMedicalDevicesComponent } from './admin-medical-devices/admin-medical-devices.component';
import { AdminPatientsComponent } from './admin-patients/admin-patients.component';
import { AdminPhysiciansComponent } from './admin-physicians/admin-physicians.component';
import { AdminTemplatesComponent } from './admin-templates/admin-templates.component';
import { SecondaryAdminComponent } from './secondary-admin/secondary-admin.component';

const routes: Routes = [];

@NgModule({
  imports: [
    RouterModule.forChild([
      { path: 'casemanagers', component: AdminCasemanagersComponent },
      { path: 'physicians', component: AdminPhysiciansComponent },
      { path: 'access-control', component: AdminAccessControlComponent },
      { path: 'caregivers', component: AdminCaregiversComponent },
      { path: 'medical-devices', component: AdminMedicalDevicesComponent },
      { path: 'billing-reports', component: AdminBillingReportComponent },
      { path: 'connectivity', component: AdminConnectivityComponent },
      { path: 'templates', component: AdminTemplatesComponent },
      { path: 'patients', component: AdminPatientsComponent },
      { path: 'inventory', component: AdminInventoryComponent },
      { path: 'admins', component: SecondaryAdminComponent },
      { path: 'alerts', component: AdminAlertsComponent },
    ]),
  ],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
