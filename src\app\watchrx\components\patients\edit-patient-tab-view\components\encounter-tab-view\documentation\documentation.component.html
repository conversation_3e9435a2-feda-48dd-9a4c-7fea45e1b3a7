<div class="position-relative">
  <p-confirmDialog />
  <p-toast />
  <div class="flex md:justify-content-end mb-2 mt-2">
    <p-button label="Add New Record" severity="secondary" [outlined]="true" (onClick)="showDialog()"
      icon="pi pi-plus"></p-button>
  </div>
  <p-table [value]="documentationList" [paginator]="true" [totalRecords]="totalCount" [rows]="25" [lazy]="true"
    (onLazyLoad)="loadDocumentationList($event)" responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped">
    <ng-template pTemplate="header">
      <tr>
        <th>Document Date and Time</th>
        <th>Duration</th>
        <th>Timestamp</th>
        <th>Notes</th>
        <th>Action</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-enc>
      <tr>

        <td>{{ enc.documentedDate | date : "MM-dd-YYYY hh:mm:ss a"}}</td>
        <td>{{ formattedTime(enc.duration) }}</td>
        <td>{{ enc.createdDate }}</td>
        <td [innerHTML]="enc.note"></td>

        <td>
          <div class="flex">
            <button pButton icon="pi pi-pencil" class="p-button-outlined p-button-secondary mr-2"
              (click)="editNotes(enc)" style="height: 30px; width: 30px"></button>
            <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
              (click)="deleteDocument($event, enc?.documentationId)" style="height: 30px; width: 30px"></button>
          </div>
        </td>
      </tr>
    </ng-template>

  </p-table>
  <div class="footer1"> Total {{totalCount}} Documentations </div>
</div>
<p-sidebar position="right" [style]="{ width: '40rem' }" class="addencountersidebar" [(visible)]="visible">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isEdit"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isEdit"></i>
        {{title}}
      </span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid">
    <hr />
    <div class="field col-12 md:col-12">
      <label htmlfor="state" class="font-bold block mb-2">Select Program(s)</label>
      <div class="flex justify-content-left gap-3">
        <div class="flex flex-wrap gap-3">
          <div class="flex align-items-center" *ngFor="let option of programList">
            <p-radioButton name="consent" [value]="option.label" [(ngModel)]="selectedProgram"
              [inputId]="option.label.toLowerCase()" variant="filled" />
            <label [for]="option.label.toLowerCase()" class="ml-2">{{
              option.label
              }}</label>
          </div>
        </div>
      </div>
    </div>

    <div class="field col-12 md:col-6">
      <label htmlfor="description" class="font-bold block mb-2">Date <span class="p-text-danger">*</span> </label>
      <p-calendar inputId="calendar-12h" [(ngModel)]="callStartTime" [showTime]="false" hourFormat="12" appendTo="body"
        [showIcon]="true">
      </p-calendar>
    </div>
    <div class="field col-12 md:col-6">
      <label htmlfor="description" class="font-bold block mb-2">Duration <small>(In Minutes)</small> <span
          class="p-text-danger">*</span></label>
          <input pInputText class="flex-auto" [(ngModel)]="durationInMins" inputId="minmax" type="number" [min]="1" [max]="100"/>
    </div>
    <div class="field col-12 md:col-12">
      <div class="flex flex-wrap justify-content-between">
        <label htmlfor="description" class="font-bold block mb-2">Note(s) <span class="p-text-danger">*</span></label>
        <label htmlfor="description" class="font-bold block mb-2">Limit ({{ remainingCharacters }})</label>
      </div>

      <p-editor #editor [(ngModel)]="noteDescription" [style]="{ height: '200px' }" (onTextChange)="checkCharacterLimit1($event)">
      </p-editor>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="primary" (click)="visible = false" />
      <p-button label="Submit" severity="primary" (click)="addDocumentation()" />
    </div>
  </ng-template>
</p-sidebar>