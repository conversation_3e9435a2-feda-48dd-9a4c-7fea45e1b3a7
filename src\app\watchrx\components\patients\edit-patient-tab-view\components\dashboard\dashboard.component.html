<div class="flex flex-row md:justify-content-end">
  <p-button label="Grid View" severity="secondary" [outlined]="!gridView" (onClick)="viewFormat('g')"
    class="p-button-sm daily" pTooltip="Grid View" tooltipPosition="top" />
  <p-button label="Table View" severity="secondary" [outlined]="!tableView" (onClick)="viewFormat('t')"
    class="p-button-sm month" pTooltip="Table View" tooltipPosition="top" />
</div>
<div class="grid" *ngIf="tableView">
  <div class="col-12 lg:col-12 config1 pb-0 pt-3">
    <div class="card p-0">
      <div class="flex p-card-header justify-content-between">
        <h6 class="m-0 font-bold">Vital Data</h6>
        <h6 class="m-0">Last 7 Days</h6>
      </div>
      <hr />
      <div class="flex p-card-header justify-content-around pt-2 pb-2">
        <div class="flex mr-3 col-3 justify-content-center "><img src="assets/watchrx/svg/red-circle.svg"
            alt="Critical Stats" width="16" height="16" style="width:30px" />
          Critical</div>
        <div class="flex mr-3 col-3 justify-content-center"><img src="assets/watchrx/svg/orange1-circle.svg"
            alt="Alarm Stats" width="16" height="16" style="width:30px" />
          Min & Max</div>
        <div class="flex mr-3 col-3 justify-content-center"><img src="assets/watchrx/svg/blue1-circle.svg"
            alt="Warning Stats" width="16" height="16" style="width:30px" />
          Normal</div>
        <div class="flex mr-3 col-3 justify-content-center"><img src="assets/watchrx/svg/gray-circle.svg"
            alt="Information Stats" width="16" height="16" style="width:30px" />
          Not Enabled</div>
      </div>
      <hr />
      <p-table [value]="tableData" [scrollable]="true">
        <ng-template pTemplate="header">
          <tr>
            <th *ngFor="let col of columnList">{{ col }}</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-row let-i="rowIndex">
          <tr [ngClass]="getClassName(i, row.VitalType)">
            <td *ngFor="let col of columnList" [ngStyle]="{'color': getTextColorValue(row[col], row.VitalType)}">{{
              row[col] }}</td>
          </tr>
        </ng-template>
      </p-table>
    </div>
  </div>
</div>
<div class="grid" style="margin-top:10px" *ngIf="gridView">
  <div class="col-12 lg:col-12 xl:col-12">
    <p-card class="config1">
      <ng-template pTemplate="header">
        <h6 class="font-bold">Vitals</h6>
      </ng-template>
      <hr />
      <div class="grid m-0">

        <ng-container *ngFor="let datas of data; let i=index">
          <div class="left-grid p-0 lg:col-6 sm:col-12" style="height:450px;"
            [ngClass]="{'lg:col-12': data.length === 1}">
            <div class="flex p-card-header justify-content-between">
              <h6 class="font-bold">{{datas.name}}</h6>
              <h6 class="font-bold">Last 7 Days</h6>
            </div>
            <hr />
            <ng-container *ngIf="datas.name=='Blood Pressure'">
              <div class="flex p-card-header justify-content-around">
                <div class="flex justify-content-between align-items-center">
                  <div class="flex mr-3"><img src="assets/watchrx/svg/blue-circle.svg" alt="Download Stats" width="16"
                      height="16" />
                    Systolic</div>
                  <div>
                    <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax
                      ||0)}}</p>
                    <p>Critical Min - {{(patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin ||0)}} </p>
                  </div>
                </div>
                <div class="flex justify-content-between align-items-center">
                  <div class="flex mr-3"> <img src="assets/watchrx/svg/orange-circle.svg" alt="Download Stats"
                      width="16" height="16" />
                    Diastolic</div>
                  <div>
                    <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMax
                      ||0)}}</p>
                    <p>Critical Min - {{(patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMin ||0)}} </p>
                  </div>
                </div>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions1" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Blood Sugar'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.bloodSugarCriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.bloodSugarCriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions6" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Heart Rate'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.heartRateCriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.heartRateCriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='SpO2'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.spo2CriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.spo2CriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions3" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Sleep Monitor'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.spo2CriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.spo2CriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions7" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Steps Count'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.pedometerStepCountCriticalMax ||0)}}
                </p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.pedometerStepCountCriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions2" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Temperature'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.temperatureCriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.temperatureCriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions5" />
              </div>
            </ng-container>
            <ng-container *ngIf="datas.name=='Weight'">
              <div class="flex p-card-header justify-content-center align-items-center flex-column">
                <p class="mb-1">Critical Max - {{(patientData?.thresholdMinMax?.weightCriticalMax ||0)}}</p>
                <p>Critical Min - {{(patientData?.thresholdMinMax?.weightCriticalMin ||0)}} </p>
              </div>
              <hr />
              <div class="p-3">
                <p-chart type="line" [data]="datas.obj" [options]="chartOptions4" />
              </div>
            </ng-container>


            <!-- <div class="p-3">
              <p-chart type="line" [data]="datas.obj" [options]="chartOptions" />
            </div> -->
          </div>
        </ng-container>

        <div class="left-grid p-0 lg:col-6 sm:col-12" style="height:450px;">
          <div class="flex p-card-header justify-content-between">
            <h6 class="m-0 font-bold">Medication Report</h6>
            <h6 class="m-0 font-bold">Last 7 Days</h6>
          </div>
          <hr />
          <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="flex mr-3 mb-1"><img src="assets/watchrx/svg/red-circle.svg" alt="Missed Medication"
                style="width:20px; height:16px" />
              Missed Medication</p>
            <p class="flex mr-3"><img src="assets/watchrx/svg/blue1-circle.svg" alt="Medication to Take"
                style="width:20px; height:16px" />
              Medication to Take</p>
          </div>
          <hr />
          <div class="p-3">
            <p-chart type="bar" [data]="medicationData" [options]="graphOptions" />
          </div>
        </div>
      </div>
    </p-card>
  </div>

</div>
<div class="grid" style="margin-top:10px">
  <p-toast />
  <div class="col-12 lg:col-6 config">
    <p-card>
      <ng-template pTemplate="header">
        <div class="flex justify-content-between">
          <h6 class="font-bold">Needs Attention <p-chip class="bg-danger">
              {{attentionList.length}}
            </p-chip></h6>
          <h6>Last 7 Days</h6>
        </div>

      </ng-template>
      <hr style="margin:0px !important">
      <div class="flex p-card-header flex-column p-0 pt-1" style="height:320px; overflow-y: auto; overflow-x: hidden;">
        <ng-container *ngIf="attentionList.length>0; else noData">
          <div class="grid" *ngFor="let att of attentionList" style="border-bottom:1px solid #E8E8E8">
            <div class="col-1 mt-2"> <img src="assets/watchrx/svg/heart.svg" alt="Download Stats" width="16"
                height="16" />
            </div>
            <div class="col-11 mt-2">
              <h6 style="margin-bottom: 8px !important">
                {{att.attentionSource=="Vital"?att.attentionType:att.attentionSource}}</h6>
              <div class="grid font-13">
                <div class="col-9">{{att.attentionDescription}}</div>
                <div class="col-3">{{att.dateTime}}</div>
              </div>
            </div>
          </div>
        </ng-container>
        <ng-template #noData>
          <div class="flex h-full justify-content-center align-items-center">No data found</div>
        </ng-template>
      </div>
    </p-card>
  </div>
  <div class="col-12 lg:col-6 config1">
    <div class="card p-0" style="height:370px;">
      <div class="flex p-card-header justify-content-between">
        <h6 class="m-0 font-bold">Alerts</h6>
        <h6 class="m-0">Last 7 Days</h6>
      </div>
      <hr />
      <div class="flex p-card-header justify-content-around">
        <div class="flex mr-3"><img src="assets/watchrx/svg/red-circle.svg" alt="Critical Stats" width="16"
            height="16" />
          Critical</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/orange1-circle.svg" alt="Alarm Stats" width="16"
            height="16" />
          Alarm</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/yellow-circle.svg" alt="Warning Stats" width="16"
            height="16" />
          Warning</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/blue1-circle.svg" alt="Information Stats" width="16"
            height="16" />
          Information</div>
      </div>
      <hr />
      <p-chart type="bar" [data]="alertsData" [options]="graphOptions" />
    </div>
  </div>
</div>
<div class="grid" style="margin-top:10px">
  <div class="col-12 lg:col-12 xl:col-12">
    <p-card class="config">
      <ng-template pTemplate="header">
        <h6 class="font-bold">Configuration <p-chip>
            {{totalEnabled}} Enabled
          </p-chip></h6>
      </ng-template>
      <div class="grid m-0">
        <div class="col-6 left-grid p-0">
          <div class="flex justify-content-between  align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="heartRateStatus" (onChange)="updateVitalStatus(heartRateStatus, 'Heart Rate')"
                class="mr-3" /> Heart Rate
            </span>
            <span class="text-green-500 font-medium" *ngIf="heartRateStatus">{{
              heartRateStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!heartRateStatus">{{
              heartRateStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="bpStatus" (onChange)="updateVitalStatus(bpStatus, 'BP')" class="mr-3" />Blood
              Pressure
            </span>
            <span class="text-green-500 font-medium" *ngIf="bpStatus">{{
              bpStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!bpStatus">{{
              bpStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="bsStatus" (onChange)="updateVitalStatus(bpStatus, 'BS')" class="mr-3" /> Blood
              Sugar
            </span>
            <span class="text-green-500 font-medium" *ngIf="bsStatus">{{
              bsStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!bsStatus">{{
              bsStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="weightStatus" (onChange)="updateVitalStatus(weightStatus, 'Weight')"
                class="mr-3" /> Weight
            </span>
            <span class="text-green-500 font-medium" *ngIf="weightStatus">{{
              weightStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!weightStatus">{{
              weightStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
        </div>


        <div class="col-6 right-grid p-0">
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="tempStatus" (onChange)="updateVitalStatus(tempStatus, 'TEMP')" class="mr-3" />
              Temperature
            </span>
            <span class="text-green-500 font-medium" *ngIf="tempStatus">{{
              tempStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!tempStatus">{{
              tempStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="spo2Status" (onChange)="updateVitalStatus(spo2Status, 'SPO2')"
                class="mr-3" />SpO2
            </span>
            <span class="text-green-500 font-medium" *ngIf="spo2Status">{{
              spo2Status ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!spo2Status">{{
              spo2Status ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="stepsStatus" (onChange)="updatePedometer(stepsStatus)" class="mr-3" /> Steps
              Count
            </span>
            <span class="text-green-500 font-medium" *ngIf="stepsStatus">{{
              stepsStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!stepsStatus">{{
              stepsStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
          <hr />
          <div class="flex justify-content-between align-items-center item">
            <span class="text-black-500 ml-10 font-medium flex align-items-center">
              <p-inputSwitch [(ngModel)]="sleepStatus" (onChange)="updateSleepMonitor(sleepStatus)" class="mr-3" />Sleep
              Monitor
            </span>
            <span class="text-green-500 font-medium" *ngIf="sleepStatus">{{
              sleepStatus ? "Enabled" : "Disabled"
              }}</span>
            <span class="text-red-500 font-medium" *ngIf="!sleepStatus">{{
              sleepStatus ? "Enabled" : "Disabled"
              }}</span>
          </div>
        </div>
      </div>
    </p-card>
  </div>
  <!-- <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Blood Pressure" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="bpStatus"
              (onChange)="updateVitalStatus(bpStatus, 'BP')" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="bpStatus">{{
        bpStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!bpStatus">{{
        bpStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Blood Sugar" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="bsStatus"
              (onChange)="updateVitalStatus(bpStatus, 'BS')" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="bsStatus">{{
        bsStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!bsStatus">{{
        bsStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Weight" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="weightStatus"
              (onChange)="updateVitalStatus(weightStatus, 'Weight')" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="weightStatus">{{
        weightStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!weightStatus">{{
        weightStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Temperature" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="tempStatus"
              (onChange)="updateVitalStatus(tempStatus, 'TEMP')" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="tempStatus">{{
        tempStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!tempStatus">{{
        tempStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "SpO2" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="spo2Status"
              (onChange)="updateVitalStatus(spo2Status, 'SPO2')" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="spo2Status">{{
        spo2Status ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!spo2Status">{{
        spo2Status ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Steps Count" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="stepsStatus"
              (onChange)="updatePedometer(stepsStatus)" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="stepsStatus">{{
        stepsStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!stepsStatus">{{
        stepsStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div>
  <div class="col-12 lg:col-6 xl:col-3">
    <div class="card mb-0">
      <div class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
        <div class="mt-1 text-600 font-bold">{{ "Sleep Monitor" }}</div>
        <div class="mt-2 md:mt-2 flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="sleepStatus"
              (onChange)="updateSleepMonitor(sleepStatus)" />
          </span>
        </div>
      </div>
      <hr />
      <span class="text-green-500 font-medium" *ngIf="sleepStatus">{{
        sleepStatus ? "Enabled" : "Disabled"
        }}</span>
      <span class="text-red-500 font-medium" *ngIf="!sleepStatus">{{
        sleepStatus ? "Enabled" : "Disabled"
        }}</span>
    </div>
  </div> -->
</div>