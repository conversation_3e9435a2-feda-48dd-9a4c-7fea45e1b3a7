import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { authGuard } from './watchrx/components/auth/gaurd/auth.guard';
import { AppLayoutComponent } from './watchrx/layout/app.layout.component';
@NgModule({
  imports: [
    RouterModule.forRoot(
      [
        {
          path: 'dashboard',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/dashboard/dashboard.module').then(
                  (m) => m.DashboardModule
                ),
                canActivate: [authGuard],
                data: {
                  roles: [5],
                }
            },
          ],
        },
        {
          path: 'patients',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/patients/patients-view/patient-view.module'
                ).then((m) => m.PatientViewModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'patients',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/patients/add-patient/add-patient.module'
                ).then((m) => m.AddPatientModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'editpatient',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/patients/edit-patient-tab-view/edit-patient-tab-view.module'
                ).then((m) => m.EditPatientTabViewModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'care-managers',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/care-managers/care-managers.module'
                ).then((m) => m.CareManagersModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'physcians',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/physicians/physicians.module'
                ).then((m) => m.PhysiciansModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'patient-alerts',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/patient-alerts/patient-alerts.module'
                ).then((m) => m.PatientAlertsModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'medical-devices',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import(
                  './watchrx/components/medical-devices-tab-view/medical-devices-tab-view.module'
                ).then((m) => m.MedicalDevicesTabViewModule),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'taskboard',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/taskboard/taskboard.module').then(
                  (m) => m.TaskboardModule
                ),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'reports',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/reports/reports.module').then(
                  (m) => m.ReportsModule
                ),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'dialog',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/dialog/dialog.module').then(
                  (m) => m.DialogModule
                ),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: 'admin',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/admin/admin.module').then(
                  (m) => m.AdminModule
                ),
                canActivate: [authGuard],
                data: {
                  roles: [2],
                }
            },
          ],
        },
        {
          path: 'profile',
          component: AppLayoutComponent,
          children: [
            {
              path: '',
              loadChildren: () =>
                import('./watchrx/components/profile/profile.module').then(
                  (m) => m.ProfileModule
                ),
                canActivate: [authGuard],
            },
          ],
        },
        {
          path: '',
          loadChildren: () =>
            import('./watchrx/components/auth/auth.module').then(
              (m) => m.AuthModule
            ),
         
        },
        { path: '**', redirectTo: '/notfound' },
      ],
      {
        scrollPositionRestoration: 'enabled',
        anchorScrolling: 'enabled',
        onSameUrlNavigation: 'reload',
      }
    ),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
