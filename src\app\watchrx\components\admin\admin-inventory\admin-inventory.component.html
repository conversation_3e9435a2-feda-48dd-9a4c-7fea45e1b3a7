<div class="grid">
  <div class="col-12">
    <div class="card">
      <div class="grid p-fluid mt-3">
        <div class="field col-12">
          <p-tabView
            orientation="left"
            (onChange)="tabChange($event)"
            [(activeIndex)]="activeTabIndex"
          >
            <p-tabPanel header="Inventory" class="line-height-3 m-0">
              <app-inventory *ngIf="activeTabIndex == 0"> </app-inventory>
            </p-tabPanel>
            <p-tabPanel header="Device Type" class="line-height-3 m-0">
              <app-add-device-type
                *ngIf="activeTabIndex == 1"
              ></app-add-device-type>
            </p-tabPanel>
          </p-tabView>
        </div>
      </div>
    </div>
  </div>
</div>
