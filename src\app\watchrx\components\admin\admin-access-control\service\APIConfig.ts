export abstract class APIConfig {
  static readonly ADMIN_FEATURES_LIST = 'service/access/allRoles';
  static readonly DELETE_FEATURE = 'service/access/deleteRole';
  static readonly ADD_FEATURE = 'service/access/saveRole';

  static readonly GROUPS_AND_ROLES = 'service/access/allGroupsAndAssignedRoles';
  static readonly DELETE_GROUPS_AND_ROLES = 'service/access/deleteGroup';
  static readonly SAVE_GROUPS_AND_ROLES = 'service/access/saveGroup';
  static readonly GET_ROLES = 'service/access/allRoles';

  static readonly ALL_USERS_LIST = 'service/access/allUsers';
  static readonly ASSIGN_USERS_ROLES =
    'service/access/allGroupsAndAssignedUsers';
  static readonly ASSIGN_USER_TO_GROUPs = '/service/access/assignUserToGroups';
  static readonly UNASSIGN_USER_TO_GROUPs =
    'service/access/unassignUserToGroups';
}
