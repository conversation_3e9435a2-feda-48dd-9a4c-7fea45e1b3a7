export interface AddPatient {
  address1: string;
  address2?: string;
  city?: string;
  firstname: string;
  lastname: string;
  phonenumber: string;
  persPhonenumber?: string;
  state?: string;
  zip: string;
  timezone?: string;
  country?: string;
  physicianId: number;
  primaryCaseManagerId: number;
  connectingDevice: string;
  dob: string;
  email?: string|null;
  gender?:any;
  chronicConditions?:any;
}

export interface AddPatientResp {
  responseCode?: number;
  responsecode?: number;
  status?: boolean;
  success?: boolean;
  messages?: any[];
}
