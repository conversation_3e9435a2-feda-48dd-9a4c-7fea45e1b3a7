.stat {
    ::ng-deep {
        .p-card .p-card-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background-color: #000;
            padding: 0.5rem 1.25rem;
            color: #F5F5F5;
            border-top-left-radius: 5px;
            border-top-right-radius: 5px;
        }

        .p-card .p-card-body {
            padding: 0rem;
            background: linear-gradient(0deg, rgba(247, 218, 53, 0.5), rgba(123, 245, 238, 0.5)),
                linear-gradient(92.54deg, #FFFFFF 47.88%, #F5F5F5 100.01%);
        }

        .p-card .p-card-content {
            padding: 1.25rem;
        }

        .p-dropdown {
            width: 100%;
        }

        .p-dropdown-filter {
            width: 100%;
        }
    }
}

.info {
    background-color: #d1ecf1;
}

.alarm {
    background-color: #f8d7da;
}
.heading
{
    padding: 15px 20px;
}
h6
		{
			margin: 0px !important;
		}
        hr{
            margin-top: 0px;
        }
        ::ng-deep .p-progressbar-value
        {
            background-color: #FAC032;
            .p-progressbar-label
            {
                color: #FAC032;
            }
        }
        
        .legend-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .color-box {
            width:15px;
            height: 15px;
            display: inline-block;
            border-radius: 3px;
        }
        .blue { background-color: #336CFB; }
        .yellow { background-color: #FAC032; }
        .brown { background-color: brown; }
        .green { background-color: #16D090; }
        .dark-brown { background-color: #BF6A02; }
        .red { background-color: #900B09; }
        .lightyellow{background-color: #FCDF98;}
        .pink{background-color: #EF5DA8;}
        .black{background-color: #000;}
        .orange{
            background-color: #FF9F40;
        }
        .rounded
        {
            border-radius: 50%;
        }
 .p-dialog-content
{
  //  height: 300px;
    overflow: auto;
}