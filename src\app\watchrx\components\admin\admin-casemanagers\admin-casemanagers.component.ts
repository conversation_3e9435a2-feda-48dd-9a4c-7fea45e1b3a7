import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { LoaderService } from '../../../loader/loader/loader.service';
import { MenuService } from '../../../service/menu.service';
import { AdminCasemanagersService } from './service/admin-casemanagers.service';
import { CreateCaseManagerComponent } from '../../shared/create-case-manager/create-case-manager.component';
import { debounceTime, Subject } from 'rxjs';
@Component({
  selector: 'app-admin-casemanagers',
  templateUrl: './admin-casemanagers.component.html',
  styleUrls: ['./admin-casemanagers.component.css'],
  providers: [MessageService, ConfirmationService],
})
export class AdminCasemanagersComponent implements OnInit {
  loader: boolean = false;
  caseManagersList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 15;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  @ViewChild(CreateCaseManagerComponent) createCaseManager!: CreateCaseManagerComponent;
  roleTypeList = [
    { type: 'Case Manager',value:'Case Manager' },
    { type: 'Care Giver',value:'Care Giver' },
    //{ type: 'Provider', value:'Provider' },
    {type:'MD',value:'Physician'},
    {type:'DO',value:'Physician'},
    {type:'NP',value:'Physician'},
    {type:'PA',value:'Physician'},
  ];
  rows: number = 15;
  private inputSubject: Subject<string> = new Subject();
  debouncedValue: any;
  searchText: string = '';
  constructor(
    public menuService: MenuService,
    private router: Router,
    private adminCaseManagerService: AdminCasemanagersService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef
  ) {
    this.inputSubject.pipe(debounceTime(500)).subscribe((value) => {
      this.debouncedValue = value;
      console.log('Search Value:', this.debouncedValue);
      this.onSearch(this.debouncedValue);
    });
  }

  ngOnInit() {
    this.cdr.detectChanges();
    this.menuService.changeMenu('Case Managers');
  }

  loadActivePatients($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.loader = true;
    let pageSize = $event?.rows || 15;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;

    let url = pageNo + '/' + pageSize;

    this.adminCaseManagerService.getManagersList(url).subscribe((res) => {
      if (res.success) {
        console.log(res);
        this.caseManagersList = res.clinicianList!;
        this.totalCaseManagers = res.count!;
        this.loader = false;
      }
    });
  }

  onDeleteCaseManager(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.adminCaseManagerService
          .deletePatient({ clinicianId: id })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Case Manager deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadActivePatients();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          });
      },
      reject: () => { },
    });
  }

  onToggleCaseManager(managerInfo: any, type: any) {
    console.log(managerInfo, 'managerInfo');
    this.loaderService.show();
    this.adminCaseManagerService
      .toggleCaseManagerStatus({
        clinicianId: managerInfo.clinicianId,
        type: 'caregiver',
      })
      .subscribe((res) => {
        this.loaderService.hide();
        managerInfo.status = type;
        this.loadActivePatients();
        this.table.first = 0;
      });
  }

  addCaseMaanger() {
    this.visible = true;
    this.createCaseManager.resetForm()
  }

  previousRowsPerPage = 15;
  first = 0;
  onPageChange(event: any) {
    if (event.rows !== this.previousRowsPerPage) {
      this.first = 0; // Reset to first page on rowsPerPage change
      this.previousRowsPerPage = event.rows;
      this.rows = event.rows
    } else {
      this.first = event.first;
    }
  }

  onInput(event: any): void {
    this.inputSubject.next(this.searchText);
  }
  onSearch(searchTerm: string): void {
    if (searchTerm && searchTerm !== undefined && searchTerm.length > 2) {
      this.loader = true;
      this.adminCaseManagerService.searchCasemanager('5/' + searchTerm).subscribe((res) => {
        if (res.success) {
          this.caseManagersList = res.clinicianList!;
          this.totalCaseManagers = res.count!;
          this.loader = false;
          this.rows = res.count! || 15;
        }
      },err=>{
        this.loader = false;
      });
    } else {
      if (searchTerm !== undefined && searchTerm.length === 0) {
        this.loadActivePatients();
        this.rows = 15;
      }
    }
  }
}
