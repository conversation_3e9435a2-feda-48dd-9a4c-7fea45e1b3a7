import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { StatisticsReport } from '../../../../../api/adminBillingReports';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { AdminBillingReportService } from '../../services/admin-billing-report.service';
import { Reports } from '../../../../../api/adminReports';
import { DateService } from '../../../../dashboard/service/date-service.service';
import { formatDate } from '@angular/common';
@Component({
  selector: 'app-statistics',
  templateUrl: './statistics.component.html',
  styleUrl: './statistics.component.scss',
})
export class StatisticsComponent implements OnInit {
  loader: boolean = false;
  statisticList: any[] = [];
  orgsList: any[] = [];
  clinicianList: any[] = [];
  pmclinicianList: any[] = [];
  filterList: any[] = [
    { value: 'all', label: 'All' },
    { value: 'rpm20Min', label: 'RPM(> 20Mins)' },
    { value: 'rpm40Min', label: 'RPM(> 40Mins)' },
    { value: 'rpm20Minless', label: 'RPM(< 20Mins)' },
    { value: 'rpm60Min', label: 'RPM(> 60Mins)' },
    { value: 'ccm20Minless', label: 'CCM(< 20Mins)' },
    { value: 'ccm20Min', label: 'CCM(> 20Mins)' },
    { value: 'ccm40Min', label: 'CCM(> 40Mins)' },
    { value: 'ccm60Min', label: 'CCM(> 60Mins)' },
    { value: 'pcm30Minless', label: 'PCM(< 30Mins)' },
    { value: 'pcm30Min', label: 'PCM(> 30Mins)' },
    { value: 'pcm60Min', label: 'PCM(> 60Mins)' },
    { value: 'dayMeasuredless', label: '< 16 Days' },
    { value: 'dayMeasured', label: '> 16 Days' },
  ];
  timeList: any[] = [
    { value: '6', label: 'Last 6 Months' },
    { value: '3', label: 'Last 3 Months' },
    { value: '1', label: 'Last Month' },
    { value: '0', label: 'This Month' },
    // { value: 'other', label: 'Set date Range' },
  ];
  totalRecords: number = 0;
  itemsPerPage: number = 10;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  date: any;
  search: any;
  selectedOrg: any;
  fromDate: any = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  toDate: any = new Date();
  filterObj = {
    clinic: 2,
    ca: 0,
    fromDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    toDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    filter: 'all',
  };
  statisticCount: any;
  reportData!: Reports;
  startDate: Date = this.dateService.getStartDateOfMonth();
  endDate: Date = this.dateService.getCurrentDate();
  startDate1: Date = this.dateService.getStartDateOfMonth();
  endDate1: Date = this.dateService.getCurrentDate();
  displayModal: boolean = false;
  pmSelectedClinician: any;
  fileDownloadLoading = false;
  totalPatients: any;
  totalCPT: any;
  totalReadings: any;
  colorsArray: any[] = [
    "#FF0000","#556B2F","#8B008B","#9932CC","#FF7F50","#FFA500","#FFD700","#ADFF2F","#00FF00","#32CD32",
    "#0000FF","#8A2BE2","#00FA9A","#4169E1","#BA55D3","#DA70D6","#FF00FF","#FF69B4","#FF1493","#DB7093",
    "#F08080","#FA8072","#FF8C00","#B22222","#DC143C","#EE82EE","#9370DB","#7B68EE","#48D1CC","#20B2AA",
    "#3CB371","#00BFFF","#5F9EA0","#66CDAA","#9ACD32","#C71585","#008080","#6A5ACD","#D2691E","#A0522D",
    "#228B22","#FF4500","#FF6347",
  ]
  
    userRole: any;
  user: any;
  userOrgId:any;
  constructor(
    private router: Router,
    private adminBillingReportService: AdminBillingReportService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef,
    private fb: FormBuilder,
    private dateService: DateService,
  ) { }

  userForm: FormGroup = this.fb.group({});

  ngOnInit(): void {
      if (localStorage.getItem('user')) {
      this.user = localStorage.getItem('user');
      this.userRole = this.user ? JSON.parse(this.user)?.roleType : null;
      this.userOrgId = this.user ? JSON.parse(this.user)?.orgId : null;
    }
    this.cdr.detectChanges();
      if (this.userRole !== 3) {
      this.getOrgsList();
    }
    else {
      this.getCaseManagerList();
    }

  }

  getOrgsList() {
    this.adminBillingReportService.getAllOrgsClinicList().subscribe(
      (res) => {
        if (res) {
          //console.log(res);
          res.orgList.unshift({ id: 0, orgId: null, label: "All" })
          this.orgsList = res.orgList!;
          //res.clinicianList.unshift({ id: 0, orgId: 0, label: "All" })
          this.clinicianList = res.clinicianList!;
          this.loader = false;
          this.filterObj = {
            clinic: this.orgsList[2].id,
            ca: 0,
            fromDate: this.fromDate,
            toDate: this.toDate,
            filter: 'all',
          };


          this.patientMonitoringList = [...this.orgsList];
          this.patientMonitoringList.splice(0, 1);
          this.selectedOrg = this.patientMonitoringList[0];

          this.loadBillingStatisticList(null);
          this.loadBillingStatisticCount(null);
          this.getGraph1(this.orgsList[0].id, this.timeList[0].value);
          this.getGraph2(this.orgsList[0].id, this.clinicianList[0].id, this.timeList[0].value);



          //console.log(this.clinicianList[0])
          //casemanagerslist
          this.selectedManagerList = this.pmclinicianList = this.clinicianList;
          this.selectedManager = this.selectedEncounterManager = this.pmSelectedClinician = this.clinicianList[0];
          //this.selectedReportManagerList = this.selectedEncounterManagerList = this.selectedManagerList = this.pmclinicianList = this.clinicianList.filter((x) => x.orgId == this.orgsList[0].id);

          this.selectedReportManagerList = this.clinicianList.filter((x) => x.orgId == this.orgsList[1].id);
          this.selectedReportManagerList.unshift({ id: 0, orgId: null, label: "All" })
          this.selectedReportManager = this.selectedReportManagerList[0];


          this.selectedEncounterManagerList = this.clinicianList.filter((x) => x.orgId == this.orgsList[1].id);
          this.selectedEncounterManagerList.unshift({ id: 0, orgId: null, label: "All" })
          this.selectedEncounterManager = this.selectedEncounterManagerList[0];
          //orgnization
          this.selectedManagerClinic = this.selectedClinic = this.selectedMetricClinic = this.orgsList[0];

          this.selectedReportClinicList = [...this.orgsList];
          this.selectedReportClinicList.splice(0, 1);
          this.selectedReportClinicList = [...this.selectedReportClinicList]
          this.selectedReportClinic = this.selectedReportClinicList[0];


          this.selectedEncounterClinicList = [...this.orgsList];
          this.selectedEncounterClinicList.splice(0, 1);
          this.selectedEncounterClinicList = [...this.selectedEncounterClinicList]
          this.selectedEncounterClinic = this.selectedEncounterClinicList[0];
          //time
          this.selectedManagerTime = this.selectedClinicTime = this.selectedMetricTime = this.selectedReportTime = this.selectedEncounterTime = this.timeList[0];

          this.getGraph3(this.selectedReportClinic.id, this.selectedReportManager.id, this.timeList[0].value);
          this.getGraph4(this.selectedEncounterClinic.id, this.selectedEncounterManager.id, this.timeList[0].value);
          //colors 
          // this.orgsList.forEach(a => {
          //   this.colorsArray.push(this.getRandomColor())
          // })
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  getCaseManagerList()
  {
      this.adminBillingReportService.getAllOrgsClinicList().subscribe(
      (res) => {
        if (res) {
          //console.log(res);
          //res.orgList.unshift({ id: 0, orgId: null, label: "All" })
          this.orgsList = res.orgList!;
          this.orgsList = this.orgsList.filter((org=>org.id == this.userOrgId))
          //res.clinicianList.unshift({ id: 0, orgId: 0, label: "All" })
          this.clinicianList = res.clinicianList!;
          this.clinicianList = this.clinicianList.filter((clinic)=>clinic.orgId==this.userOrgId)
          this.loader = false;
          this.filterObj = {
            clinic: this.orgsList[0].id,
            ca: this.clinicianList[0].id,
            fromDate: this.fromDate,
            toDate: this.toDate,
            filter: 'all',
          };


          this.patientMonitoringList = [...this.orgsList];
          //this.patientMonitoringList.splice(0, 1);
          this.selectedOrg = this.patientMonitoringList[0];

          this.loadBillingStatisticList(null);
          this.loadBillingStatisticCount(null);
          this.getGraph1(this.orgsList[0].id, this.timeList[0].value);
          this.getGraph2(this.orgsList[0].id, this.clinicianList[0].id, this.timeList[0].value);



          //console.log(this.clinicianList[0])
          //casemanagerslist
          this.selectedManagerList = this.pmclinicianList = this.clinicianList;
          this.selectedManager = this.selectedEncounterManager = this.pmSelectedClinician = this.clinicianList[0];
          //this.selectedReportManagerList = this.selectedEncounterManagerList = this.selectedManagerList = this.pmclinicianList = this.clinicianList.filter((x) => x.orgId == this.orgsList[0].id);

          this.selectedReportManagerList = this.clinicianList.filter((x) => x.orgId == this.orgsList[0].id);
          this.selectedReportManagerList.unshift({ id: 0, orgId: null, label: "All" })
          this.selectedReportManager = this.selectedReportManagerList[0];


          this.selectedEncounterManagerList = this.clinicianList.filter((x) => x.orgId == this.orgsList[0].id);
          this.selectedEncounterManagerList.unshift({ id: 0, orgId: null, label: "All" })
          this.selectedEncounterManager = this.selectedEncounterManagerList[0];
          //orgnization
          this.selectedManagerClinic = this.selectedClinic = this.selectedMetricClinic = this.orgsList[0];

          this.selectedReportClinicList = [...this.orgsList];
          this.selectedReportClinicList = [...this.selectedReportClinicList]
          this.selectedReportClinic = this.selectedReportClinicList[0];


          this.selectedEncounterClinicList = [...this.orgsList];
          this.selectedEncounterClinicList = [...this.selectedEncounterClinicList]
          this.selectedEncounterClinic = this.selectedEncounterClinicList[0];
          //time
          this.selectedManagerTime = this.selectedClinicTime = this.selectedMetricTime = this.selectedReportTime = this.selectedEncounterTime = this.timeList[0];

          this.getGraph3(this.selectedReportClinic.id, this.selectedReportManager.id, this.timeList[0].value);
          this.getGraph4(this.selectedEncounterClinic.id, this.selectedEncounterManager.id, this.timeList[0].value);
          //colors 
          // this.orgsList.forEach(a => {
          //   this.colorsArray.push(this.getRandomColor())
          // })
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  loadBillingStatisticList($event?: LazyLoadEvent | TableLazyLoadEvent | null) {
    this.loader = true;
    let pageSize = $event?.rows || 10;
    let first = $event?.first || 0;
    let pageNo = first / pageSize;
    let p = pageNo + '/' + pageSize;
    let url =
      this.filterObj.clinic +
      '/' +
      this.getFormatedDate(this.startDate1) +
      '/' +
      this.getFormatedDate(this.endDate1) +
      '/' +
      p +
      '/' +
      this.filterObj.ca +
      '/' +
      this.filterObj.filter;
    this.adminBillingReportService.getBilligStatisticsList(url).subscribe(
      (res: StatisticsReport) => {
        if (res) {
          //console.log(res);
          this.statisticList = res.data;
          this.totalRecords = res.totalCount;
          this.loader = false;
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  loadBillingStatisticCount(
    $event?: LazyLoadEvent | TableLazyLoadEvent | null
  ) {
    this.loader = true;
    let url =
      this.filterObj.clinic +
      '/' +
      this.getFormatedDate(this.startDate1) +
      '/' +
      this.getFormatedDate(this.endDate1) +
      '/' +
      this.filterObj.ca;
    this.adminBillingReportService.getBilligStatisticsCount(url).subscribe(
      (res) => {
        if (res) {
          //console.log(res);
          this.statisticCount = res;
          this.loader = false;
        }
      },
      (err) => {
        this.loader = false;
      }
    );
  }

  getFormatedDate(givenDate: any) {
    const date = new Date(givenDate);

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const day = String(date.getDate()).padStart(2, '0'); // Pad day with leading zero

    const formattedDate = `${year}-${month}-${day}`;
    return formattedDate;
  }

  filterData(type: any, value: any) {
    if (type == 'clinic') {
      this.pmclinicianList = this.clinicianList.filter((x) => x.orgId == value.id);
      this.pmSelectedClinician = this.pmclinicianList[0];
      this.filterObj.clinic = value.id;
      this.filterObj.ca = this.pmSelectedClinician.id;
    } else if (type == 'ca') {
      this.filterObj.ca = value.id;
    } else if (type == 'filter') {
      this.filterObj.filter = value.value;
    } else if (type == 'button') {
      this.filterObj.fromDate = this.fromDate;
      this.filterObj.toDate = this.toDate;
    } else if (type == 'link') {
      this.filterObj.filter = value;
    }

    this.loadBillingStatisticList(null);
    this.loadBillingStatisticCount(null);
  }

  getClass(x: any) {
    const rpm = parseInt(x?.rpmMins?.split('m')[0], 10);
    const ccm = parseInt(x?.ccmMins?.split('m')[0], 10);
    const pcm = parseInt(x?.pcmMins?.split('m')[0], 10);
    if (rpm >= 20 || ccm >= 20 || pcm >= 30 || x?.daysCounted >= 16) {
      return 'info';
    } else {
      return 'alarm';
    }
  }

  DownloadReport(id: any) { }
  // DownloadReport(id: any) {
  //   this.confirmService.confirm({
  //     message: `Are you sure you want to delete ?`,
  //     header: 'Confirmation',
  //     icon: 'pi pi-info-circle',
  //     acceptButtonStyleClass: 'p-button-danger p-button-text',
  //     rejectButtonStyleClass: 'p-button-text p-button-text',
  //     acceptIcon: 'none',
  //     rejectIcon: 'none',
  //     accept: () => {
  //       this.loaderService.show();
  //       this.adminBillingReportService
  //         .deleteFeature({ roleId: id })
  //         .subscribe((res) => {
  //           this.loaderService.hide();
  //           if (res) {
  //             this.messageService.add({
  //               severity: 'success',
  //               summary: 'Confirmed',
  //               detail: 'Case Manager deleted successfully.',
  //               life: 3000,
  //             });
  //             setTimeout(() => {
  //               this.loadBillingReportList();
  //             }, 1000);
  //           } else {
  //             this.messageService.add({
  //               severity: 'error',
  //               summary: 'Rejected',
  //               detail:
  //                 'Something went wrong',
  //               life: 3000,
  //             });
  //           }
  //         }, err => {
  //           this.loaderService.hide();
  //           this.messageService.add({
  //             severity: 'error',
  //             summary: 'Rejected',
  //             detail:
  //               'Something went wrong',
  //             life: 3000,
  //           });
  //         });
  //     },
  //     reject: () => { },
  //   });
  // }

  // addCaseMaanger() {
  //  this.visible=true
  //   }

  //   onSubmit() {
  //     this.loaderService.show();
  //     this.adminBillingReportService
  //         .saveFeature(this.userForm.value)
  //         .subscribe((res) => {
  //           this.loaderService.hide();
  //           this.userForm.reset();
  //           this.visible=false;
  //           if (res?.success) {
  //             this.messageService.add({
  //               severity: 'success',
  //               summary: 'Confirmed',
  //               detail: 'Feature Added successfully.',
  //               life: 3000,
  //             });
  //             setTimeout(() => {
  //               this.loadFeaturesList();
  //             }, 1000);
  //           } else {

  //             this.messageService.add({
  //               severity: 'error',
  //               summary: 'Rejected',
  //               detail:
  //                 'Something went wrong',
  //               life: 3000,
  //             });
  //           }
  //         },err=>{
  //           this.userForm.reset();
  //           this.visible=false;
  //           this.loaderService.hide();
  //           this.messageService.add({
  //             severity: 'error',
  //             summary: 'Rejected',
  //             detail:
  //               'Something went wrong',
  //             life: 3000,
  //           });
  //         });
  //     }

  // {"roleName":"sample Role","roleDescription":"sampleRole"};


  filterDate(type: string, event: any) {

  }

  downloadedManager: any;
  downloadedManagerClinic: any;

  selectedClinic: any;
  selectedClinicTime: any;
  data: any;
  options: any;

  selectedManager: any;
  selectedManagerClinic: any;
  selectedManagerTime: any;
  selectedManagerList: any;
  data1: any
  pieData1: any;
  pieoptions: any;
  pieData1Total: any;

  selectedMetricClinic: any;
  selectedMetricTime: any;
  lineData: any;
  lineOptions: any;

  selectedReportClinicList: any;
  selectedReportManager: any;
  selectedReportClinic: any;
  selectedReportTime: any;
  selectedReportManagerList: any;
  data2: any;
  options2: any;
  pieData2: any;

  selectedEncounterClinicList: any
  selectedEncounterClinic: any;
  selectedEncounterManagerList: any;
  selectedEncounterManager: any;
  selectedEncounterTime: any;
  data3: any;
  pieData3: any;

  patientMonitoringList: any;
  getGraph1(orgId?: any, duration?: any, type: string = '') {
    this.loaderService.show();
    this.adminBillingReportService.cptPatientOrgReports(orgId, duration).subscribe((res) => {

      this.reportData = res;
      //console.log(this.reportData);
      if (type == 'clinic') {
        let allLabels: any = [];
        if (orgId == 0) {
          const transformedData = this.transformClinicData(this.reportData.clinicCptmap, this.reportData.labels);
          // Prepare chart datasets
          const datasets = transformedData.map((arr, index) => ({
            label: Object.keys(this.reportData.clinicCptmap)[index],
            data: arr,
            fill: false,
            backgroundColor: this.colorsArray[index % this.colorsArray.length],
            borderColor: this.colorsArray[index % this.colorsArray.length],
            yAxisID: 'y',
            tension: 0.4,
            maxBarThickness: 30,
          }));

          this.data = {
            labels: this.reportData.labels,
            datasets: datasets
          };

        }
        else {
          this.data = {
            labels: this.reportData.labels,
            datasets: [
              {
                label: 'Billable CPT’s',
                fill: false,
                backgroundColor: '#336CFB',
                borderColor: '#336CFB',
                yAxisID: 'y',
                tension: 0.4,
                data: this.reportData.cptCodes,
                // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
                // categoryPercentage: 0.5,
                maxBarThickness: 30  // restrict maximum bar width
              },
              {
                label: 'Patients',
                fill: false,
                borderColor: '#FAC032',
                backgroundColor: '#FAC032',
                yAxisID: 'y1',
                tension: 0.4,
                data: this.reportData.patients,
                // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
                // categoryPercentage: 0.5,
                maxBarThickness: 30  // restrict maximum bar width
              }
            ]
          };
        }
      }
      else if (type == 'metric') {
        let dataset: any[] = [];
        let colorIndex = 0;
        for (var m in this.reportData.clinicCptmap) {
          //  for (var m in myMap){
          ////console.log(m)
          if (this.reportData.clinicCptmap.hasOwnProperty(m)) {
            var val = this.reportData.clinicCptmap[m as keyof typeof this.reportData.clinicCptmap];
            //console.log(val)
            // let color = this.getRandomColor();
            var a = {
              label: m,
              data: val,
              fill: false,
              borderColor: this.colorsArray[colorIndex % this.colorsArray.length],
              backgroundColor: this.colorsArray[colorIndex % this.colorsArray.length],
              tension: 0.4
            };
            dataset.push(a);
            colorIndex++;
          }
        }
        // var val = this.reportData.cptCodes;
        // var lineDatas = {
        //   label: 'Billable CPT’s',
        //   data: val,
        //   fill: false,
        //   borderColor: '#336CFB',
        //   backgroundColor: '#336CFB',
        //   tension: 0.4,
        //   pointRadius: 5,
        //   pointHoverRadius: 8
        // };
        //dataset.push(lineDatas);
        this.lineData = {
          labels: this.reportData.labels,
          datasets: dataset
        };
      }
      else {
        if (orgId == 0) {
          const transformedData = this.transformClinicData(this.reportData.clinicCptmap, this.reportData.labels);
          // Prepare chart datasets
          const datasets = transformedData.map((arr, index) => ({
            label: Object.keys(this.reportData.clinicCptmap)[index],
            data: arr,
            fill: false,
            backgroundColor: this.colorsArray[index % this.colorsArray.length],
            borderColor: this.colorsArray[index % this.colorsArray.length],
            yAxisID: 'y',
            tension: 0.4,
            maxBarThickness: 30,
          }));

          this.data = {
            labels: this.reportData.labels,
            datasets: datasets
          };

        }
        else {
          this.data = {
            labels: this.reportData.labels,
            datasets: [
              {
                label: 'Billable CPT’s',
                fill: false,
                backgroundColor: '#336CFB',
                borderColor: '#336CFB',
                yAxisID: 'y',
                tension: 0.4,
                data: this.reportData.cptCodes,
                // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
                // categoryPercentage: 0.5,
                maxBarThickness: 30  // restrict maximum bar width
              },
              {
                label: 'Patients',
                fill: false,
                borderColor: '#FAC032',
                backgroundColor: '#FAC032',
                yAxisID: 'y1',
                tension: 0.4,
                data: this.reportData.patients,
                // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
                // categoryPercentage: 0.5,
                maxBarThickness: 30  // restrict maximum bar width
              }
            ]
          };
        }
        let dataset: any[] = [];
        let colorIndex = 0;
        if (orgId == 0) {
          const transformedData = this.transformClinicData(this.reportData.clinicCptmap, this.reportData.labels);
          // Prepare chart datasets
          const datasets = transformedData.map((arr, index) => ({
            label: Object.keys(this.reportData.clinicCptmap)[index],
            data: arr,
            fill: false,
            backgroundColor: this.colorsArray[index % this.colorsArray.length],
            borderColor: this.colorsArray[index % this.colorsArray.length],
            yAxisID: 'y',
            tension: 0.4,
            maxBarThickness: 30,
          }));

          this.lineData = {
            labels: this.reportData.labels,
            datasets: datasets
          };
        }
        else {
          for (var m in this.reportData.clinicCptmap) {
            if (this.reportData.clinicCptmap.hasOwnProperty(m)) {
              var val = this.reportData.clinicCptmap[m as keyof typeof this.reportData.clinicCptmap];
              //console.log(val);
              let color = this.getRandomColor();
              var a = {
                label: m,
                data: val,
                fill: false,
                borderColor: this.colorsArray[colorIndex],
                backgroundColor: this.colorsArray[colorIndex],
                tension: 0.4
              };
              dataset.push(a);
              colorIndex++;
            }
          }
          this.lineData = {
            labels: this.reportData.labels,
            datasets: dataset
          };
        }
        //console.log(this.lineData, 'lineData');
        // var val = this.reportData.cptCodes;
        // //console.log(val)
        // var lineDatas = {
        //   label: 'Billable CPT’s',
        //   data: val,
        //   fill: false,
        //   borderColor: '#336CFB',
        //   backgroundColor: '#336CFB',
        //   tension: 0.4,
        //   pointRadius: 5,
        //   pointHoverRadius: 8
        // };
        // dataset.push(lineDatas);
      }

      this.loaderService.hide();
    }, (err) => {
      this.loaderService.hide();
    });

    this.graphOptions();
  }
  transformClinicData(clinicData: any, labels: any): number[][] {
    const clinicValues = Object.values(clinicData);
    const maxLength = labels.length;

    return clinicValues.map((arr: any) => {
      const padded = Array(maxLength).fill(0);
      for (let i = 0; i < arr.length && i < maxLength; i++) {
        padded[i] = arr[i];
      }
      return padded;
    });
  }
  getGraph2(orgId?: any, clinicianId?: any, duration?: any) {
    this.loaderService.show();
    this.adminBillingReportService.cptPatientOrgAndClinician(orgId, clinicianId, duration).subscribe((res) => {
      //console.log(res);
      this.reportData = res;
      //console.log(this.reportData);

      if (orgId == 0) {
        const transformedData = this.transformClinicData(this.reportData.clinicCptmap, this.reportData.labels);
        // Prepare chart datasets
        const datasets = transformedData.map((arr, index) => ({
          label: Object.keys(this.reportData.clinicCptmap)[index],
          data: arr,
          fill: false,
          backgroundColor: this.colorsArray[index % this.colorsArray.length],
          borderColor: this.colorsArray[index % this.colorsArray.length],
          yAxisID: 'y',
          tension: 0.4,
          maxBarThickness: 30,
        }));

        this.data1 = {
          labels: this.reportData.labels,
          datasets: datasets
        };

        const labels = Object.keys(this.reportData.clinicCptmap);
        const data = Object.values(this.reportData.clinicCptmap).map((arr: any) =>
          arr.reduce((sum: any, value: any) => sum + value, 0)
        );
        this.pieData1 = {
          labels: labels,
          datasets: [
            {
              data: data,
              backgroundColor: this.colorsArray,
              hoverBackgroundColor: this.colorsArray
            }
          ]
        }
        // this.pieData1Total = this.reportData.cptCodes.reduce((total, currentValue) => total + currentValue, 0);
      }
      else {
        this.data1 = {
          labels: this.reportData.labels,
          datasets: [
            {
              label: 'Billable CPT’s',
              fill: false,
              backgroundColor: '#336CFB',
              borderColor: '#336CFB',
              yAxisID: 'y',
              tension: 0.4,
              data: this.reportData.cptCodes,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              //minBarThickness:40
            },
            {
              label: 'Patients',
              fill: false,
              borderColor: '#FAC032',
              backgroundColor: '#FAC032',
              yAxisID: 'y1',
              tension: 0.4,
              data: this.reportData.patients,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              //minBarThickness:40
            }
          ]
        };

        this.pieData1 = {
          labels: this.reportData.labels,
          datasets: [
            {
              data: this.reportData.cptCodes,
              backgroundColor: ['#336CFB', '#BF6A02', '#16D090', '#FAC032', '#FCDF98', '#EF5DA8', '#F4BE37'],
              hoverBackgroundColor: ['#336CFB', '#BF6A02', '#16D090', '#FAC032', '#FCDF98', '#EF5DA8', '#F4BE37']
            }
          ]
        }
        // this.pieData1Total = this.reportData.cptCodes.reduce((total, currentValue) => total + currentValue, 0);
      }
      this.loaderService.hide();
    }, (err) => {
      this.loaderService.hide();
    });

    this.adminBillingReportService.cptTotalPatientOrgAndClinician(orgId, clinicianId, duration).subscribe((res) => {
      if (res) {
        //console.log(res);
        if (orgId == 0) {
          this.totalCPT = res?.cptCodes.reduce((total: any, currentValue: any) => total + currentValue, 0);
          this.totalPatients = res?.patients.reduce((total: any, currentValue: any) => total + currentValue, 0);
        }
        else {
          this.totalCPT = res?.cptCodes[0];
          this.totalPatients = res?.patients[0];
        }
      }
    });

    this.adminBillingReportService.vital16days(orgId, clinicianId, duration).subscribe((res) => {
      if (res) {
        if (orgId == 0) {
          this.totalReadings = res?.totalNoOfPatients.reduce((total: any, currentValue: any) => total + currentValue, 0);
        }
        else {
          this.totalReadings = res?.totalNoOfPatients[0];
        }
      }
    });
    this.graphOptions();
  }
  getGraph3(orgId?: any, clinicianId?: any, duration?: any) {
    this.loaderService.show();
    this.adminBillingReportService.vitalReport(orgId, clinicianId, duration).subscribe((res) => {
      this.reportData = res;
      //console.log(this.reportData);
      if (clinicianId == 0) {
        const transformedData = this.transformClinicData(this.reportData.caseMgrVtlMap, this.reportData.labels);
        // Prepare chart datasets
        const datasets = transformedData.map((arr, index) => ({
          label: Object.keys(this.reportData.caseMgrVtlMap)[index],
          data: arr,
          fill: false,
          backgroundColor: this.colorsArray[index % this.colorsArray.length],
          borderColor: this.colorsArray[index % this.colorsArray.length],
          yAxisID: 'y',
          tension: 0.4,
          maxBarThickness: 30,
        }));

        this.data2 = {
          labels: this.reportData.labels,
          datasets: datasets
        };

      }
      else {
        this.data2 = {
          labels: this.reportData.labels,
          datasets: [

            {
              type: 'bar',
              label: 'Patients Completed 16 Day Reading',
              backgroundColor: '#FAC032',
              data: this.reportData.data,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              //  minBarThickness:40
            },
            {
              type: 'bar',
              label: 'Total number of Patients',
              backgroundColor: '#FCDF98',
              data: this.reportData.totalNoOfPatients,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              // minBarThickness:40
            },
          ]
        }
        this.pieData2 = {
          labels: this.reportData.labels,
          datasets: [
            {
              data: this.reportData.data,
              backgroundColor: ['#F4BE37', '#FF9F40', '#0D2535', '#5388D8'],
              hoverBackgroundColor: ['#F4BE37', '#FF9F40', '#0D2535', '#5388D8']
            }
          ]
        }
      }
      this.loaderService.hide();
      // console.log(this.pieData2);
    }, (err) => {
      this.loaderService.hide();
    });
    this.graphOptions();
  }
  getGraph4(orgId?: any, clinicianId?: any, duration?: any) {
    this.loaderService.show();
    this.adminBillingReportService.encounterReport(orgId, clinicianId, duration).subscribe((res) => {
      // console.log(res);
      this.reportData = res;
      // //console.log(this.reportData);
      if (clinicianId == 0) {
        const transformedData = this.transformClinicData(this.reportData.caseMgrEncounterMap, this.reportData.labels);
        const datasets = transformedData.map((arr, index) => ({
          label: Object.keys(this.reportData.caseMgrEncounterMap)[index],
          data: arr,
          fill: false,
          backgroundColor: this.colorsArray[index % this.colorsArray.length],
          borderColor: this.colorsArray[index % this.colorsArray.length],
          yAxisID: 'y',
          tension: 0.4,
          maxBarThickness: 30,
        }));
        this.data3 = {
          labels: this.reportData.labels,
          datasets: datasets
        };
        const labels = Object.keys(this.reportData.caseMgrEncounterMap);
        const data = Object.values(this.reportData.caseMgrEncounterMap).map((arr: any) =>
          arr.reduce((sum: any, value: any) => sum + value, 0)
        );
        this.pieData3 = {
          labels: labels,
          datasets: [
            {
              data: data,
              backgroundColor: this.colorsArray,
              hoverBackgroundColor: this.colorsArray
            }
          ]
        }
      }
      else {
        this.data3 = {
          labels: this.reportData.labels,
          datasets: [
            {
              type: 'bar',
              label: 'Encounter Minutes',
              backgroundColor: '#EF5DA8',
              data: this.reportData.data,
              // barPercentage: 0.5, // Reduce bar width (0.0 to 1.0)
              // categoryPercentage: 0.5,
              maxBarThickness: 30,  // restrict maximum bar width
              //minBarThickness:40
            },
          ]
        }
        this.pieData3 = {
          labels: this.reportData.labels,
          datasets: [
            {
              data: this.reportData.data,
              backgroundColor: ['#336CFB', '#BF6A02', '#16D090', '#FAC032', '#FCDF98', '#EF5DA8', '#F4BE37'],
              hoverBackgroundColor: ['#336CFB', '#BF6A02', '#16D090', '#FAC032', '#FCDF98', '#EF5DA8', '#F4BE37']
            }
          ]
        }

        //console.log(this.pieData3);
      }
      this.loaderService.hide();
    }, (err) => {
      this.loaderService.hide();
    });
    this.graphOptions();
  }

  graphOptions() {
    const documentStyle = getComputedStyle(document.documentElement);
    const textColor = documentStyle.getPropertyValue('--text-color');
    const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
    const surfaceBorder = documentStyle.getPropertyValue('--surface-border');
    this.options = {
      stacked: false,
      maintainAspectRatio: false,
      aspectRatio: 0.6,
      plugins: {
        legend: {
          labels: {
            color: textColor
          },
          position: 'bottom',
          display: false
        }
      },
      scales: {

        x: {
          // barPercentage: 0.5, // default is 0.9
          // categoryPercentage: 0.5, // default is 0.8
          ticks: {
            color: textColorSecondary
          },
          grid: {
            color: surfaceBorder
          }
        },
        y: {

          type: 'linear',
          display: true,
          position: 'left',
          ticks: {
            color: textColorSecondary,
            stepSize: 1,
          },
          grid: {
            color: surfaceBorder
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          ticks: {
            color: textColorSecondary,
            stepSize: 1,
          },
          grid: {
            drawOnChartArea: false,
            color: surfaceBorder
          }
        }
      }
    };
    this.pieoptions = {
      cutout: '70%',
      plugins: {
        legend: {
          labels: {
            color: textColor
          },
          position: 'bottom',
          display: false
        },

      }
    };
    this.lineOptions = {
      maintainAspectRatio: false,
      aspectRatio: 0.6,
      plugins: {
        legend: {
          labels: {
            color: textColor
          },
          position: 'bottom',
          display: false
        }
      },
      scales: {
        y: {
          ticks: {
            stepSize: 1,
          },
        }
      },
    }
      this.options2 = {
        maintainAspectRatio: false,
        aspectRatio: 0.8,
        plugins: {
          tooltip: {
            mode: 'index',
            intersect: false
          },
          legend: {
            labels: {
              color: textColor
            },
            display: false
          }
        },
        scales: {
          x: {
            //barPercentage: 0.5, // default is 0.9
            // categoryPercentage: 0.5, // default is 0.8
            stacked: true,
            ticks: {
              color: textColorSecondary
            },
            grid: {
              color: surfaceBorder,
              drawBorder: false
            }
          },
          y: {
            stacked: true,
            ticks: {
              color: textColorSecondary,
              stepSize: 1,
            },
            grid: {
              color: surfaceBorder,
              drawBorder: false
            }
          }
        }
      };
    }

    filterData1(type: any, value: any) {
      if (type == 'clinic') {
        this.getGraph1(value.id, this.selectedClinicTime.value, type);
      }
      else if (type == 'caseManager') {
        if (value.id == 0) {
          this.selectedManagerList = this.clinicianList;
          this.selectedManager = this.selectedManagerList[0];
        }
        else {
          this.selectedManagerList = this.clinicianList.filter((x) => x.orgId == value.id);
          this.selectedManager = this.selectedManagerList[0];
        }
        this.getGraph2(value.id, this.selectedManager.id, this.selectedManagerTime.value);
      }
      else if (type == 'metric') {
        this.getGraph1(value.id, this.selectedMetricTime.value, type);
      }
      else if (type == 'report') {
        this.selectedReportManagerList = this.clinicianList.filter((x) => x.orgId == value.id);
        this.selectedReportManagerList.unshift({ id: 0, orgId: null, label: "All" })
        this.selectedReportManager = this.selectedReportManagerList[0];
        this.getGraph3(value.id, this.selectedReportManager.id, this.selectedReportTime.value);
      }
      else if (type == 'encounter') {
        this.selectedEncounterManagerList = this.clinicianList.filter((x) => x.orgId == value.id);
        this.selectedEncounterManagerList.unshift({ id: 0, orgId: null, label: "All" })
        this.selectedEncounterManager = this.selectedEncounterManagerList[0];
        this.getGraph4(value.id, this.selectedEncounterManager.id, this.selectedEncounterTime.value);
      }
    }
    filterDate1(type: string, value: any) {
      if (type == 'clinic') {
        this.getGraph1(this.selectedClinic.id, value.value, type);
      }
      else if (type == 'caseManager') {
        this.getGraph2(this.selectedManagerClinic.id, this.selectedManager.id, value.value);
      }
      else if (type == 'metric') {
        this.getGraph1(this.selectedMetricClinic.id, value.value, type);
      }
      else if (type == 'report') {
        this.getGraph3(this.selectedReportClinic.id, this.selectedReportManager.id, value.value);
      }
      else if (type == 'encounter') {
        this.getGraph4(this.selectedEncounterClinic.id, this.selectedEncounterManager.id, value.value);
      }
    }

    filterCaseManager(type: string, value: any) {
      if (type == 'caseManager') {
        this.getGraph2(this.selectedManagerClinic.id, value.id, this.selectedManagerTime.value);
      }
      else if (type == 'report') {
        this.getGraph3(this.selectedReportClinic.id, value.id, this.selectedReportTime.value);
      }
      else if (type == 'encounter') {
        this.getGraph4(this.selectedEncounterClinic.id, value.id, this.selectedEncounterTime.value);
      }
    }
    submit(type: string, value: any) {
      if (type == 'clinic') {
        this.getGraph1(this.selectedClinic.id, this.selectedClinicTime.value, type);
      }
      else if (type == 'caseManager') {

        this.getGraph2(this.selectedManagerClinic.id, this.selectedManager.id, this.selectedManagerTime.value);
      }
      else if (type == 'metric') {
        this.getGraph1(this.selectedMetricClinic.id, this.selectedMetricTime.value, type);
      }
      else if (type == 'report') {

        this.getGraph3(this.selectedReportClinic.id, this.selectedReportManager.id, this.selectedReportTime.value);
      }
      else if (type == 'encounter') {

        this.getGraph4(this.selectedEncounterClinic.id, this.selectedEncounterManager.id, this.selectedEncounterTime.value);
      }
    }

    generateStatistics() {
      if (this.downloadedManager && this.downloadedManagerClinic) {
        this.fileDownloadLoading = true;
        ////console.log( formatDate(this.startDate, 'yyyy-MM-dd', 'en-US'),this.endDate, this.downloadedManager.id, this.downloadedManagerClinic.id);
        const startDate = formatDate(this.startDate, 'yyyy-MM-dd', 'en-US');
        const endDate = formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
        const cmId = this.downloadedManager.id;
        const orgId = this.downloadedManagerClinic.id;
        const fileName =
          'Admin_Stats_' +
          formatDate(this.startDate, 'yyyyMMdd', 'en-US') +
          '_' +
          formatDate(this.endDate, 'yyyyMMdd', 'en-US') +
          '.xlsx';
        this.adminBillingReportService
          .downloadStatistics(startDate, endDate, cmId, orgId, fileName)
          .subscribe({
            next: (response) => {
              this.displayModal = false;
              this.fileDownloadLoading = false;
              if (response.success) {
                //console.log('File downloaded successfully');
                this.messageService.add({
                  severity: 'success',
                  summary: 'Success',
                  detail: 'File downloaded successfully',
                });
              } else {
                this.messageService.add({
                  severity: 'error',
                  summary: 'Error',
                  detail: 'Error downloading file',
                });
              }
            },
            error: (error) => {

              console.error('Error downloading file', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Error downloading file',
              });
            },
          });
      }
      else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Please select all fields',
        });
      }
    }
    getRandomColor() {

      return '#' + Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');

      // const baseHues: any = {
      //   blue: 220,
      //   orange: 30,
      //   red: 0,
      //   black: 0,
      //   brown: 30,
      //   green: 120,
      // };

      // const colorNames = Object.keys(baseHues);
      // const randomColorName = colorNames[Math.floor(Math.random() * colorNames.length)];
      // const hue: any = baseHues[randomColorName];

      // let saturation = 100;
      // let lightness = 50;

      // // Adjust lightness for variety based on color type
      // switch (randomColorName) {
      //   case 'black':
      //     saturation = 0;
      //     lightness = Math.floor(Math.random() * 20); // dark grays
      //     break;
      //   case 'brown':
      //     saturation = 70;
      //     lightness = 30 + Math.floor(Math.random() * 15); // darker browns
      //     break;
      //   default:
      //     lightness = 40 + Math.floor(Math.random() * 30); // mid to light shades
      // }

      // return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
    }
  }
