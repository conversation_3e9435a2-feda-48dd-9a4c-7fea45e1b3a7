import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableModule } from 'primeng/table';
import { encounterReasonList, deviceTypes } from '../../../api/globalConstants';
import { Program } from '../../../api/patientEncounter';
import { el } from '@fullcalendar/core/internal-common';
import { EncounterService } from '../../patients/edit-patient-tab-view/components/encounter-tab-view/encounter/service/encounter.service';

@Component({
  selector: 'app-encounter-form',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    DropdownModule,
    TableModule,
    CommonModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    AvatarModule,
    RadioButtonModule,
    InputNumberModule,
    ConfirmDialogModule,
  ],
  templateUrl: './encounter.component.html',
  styleUrl: './encounter.component.scss',
})
export class EncounterFormComponent implements OnInit {
  @Input() programList: Program[] = [];
  selectedProgram: any | undefined;
  selectedEReason: any = '';
  encounterDate: Date = new Date();
  encounterTime: Date = new Date();
  value4: any = '';
  description: any = '';
   safeDescription: SafeHtml = this.sanitizer.bypassSecurityTrustHtml('');
 //reasonList: any[] = encounterReasonList;
 @Input() reasonList: any[] =[]
  deviceTypes: any[] = deviceTypes;
  selectedDeviceType: any = '';
  @Input() editData: any = {};
  encounterId: number = 0;
  @Input() patientName: string | undefined = '';
  public safeHtml: SafeHtml | undefined;
  tempdescription: string = '';

   isListening: boolean = false;
  recognition: any;
  showSpeech:boolean=false;
@Input() isEditEncounter:boolean=false
  constructor(private sanitizer: DomSanitizer, public cdr: ChangeDetectorRef,
    public encounterService: EncounterService
  ) { }

  ngOnInit() {
    console.log('programList', this.programList)
   
    this.selectedProgram = undefined;
    this.selectedEReason = '';
    this.encounterDate = new Date();
    this.value4 = '';
    this.description = '';
    this.selectedDeviceType = "dfffff";
    this.setDefaultTime();
    if (this.editData !== undefined && this.editData !== '') {
      this.selectedProgram = this.editData?.selectedProgram;
      this.selectedEReason = { reason: this.editData?.reason, reasonCode: this.getReasonCode(this.editData?.reason) };
      this.description = this.editData?.description;
           this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description||'');
      this.value4 = this.editData?.duration;
      this.setEncounterDateTime(this.editData?.dateTime);
      this.encounterId = this.editData?.encounterId;
    }
  }

 
  getReasonCode(reasonType: string): string {
    const reason = this.reasonList.find((item) => item.reason === reasonType);  
    return reason ? reason.reasonCode : '';
  }
  setDefaultTime() {
    const now = new Date();
    const minutes = now.getMinutes();
    //const roundedMinutes = Math.floor(minutes / 15) * 15;
    //now.setMinutes(roundedMinutes, 0, 0);
    this.encounterTime = now;
  }

  setEncounterDateTime(encounterDateTime: string) {
    if (encounterDateTime) {
      const [datePart, timePart] = encounterDateTime?.split(' ');
      const [month, day, year] = datePart.split('-').map(Number);
      const [hours, minutes, seconds] = timePart.split(':').map(Number);
      this.encounterDate = new Date(year, month - 1, day);
      this.encounterTime = new Date(
        year,
        month - 1,
        day,
        hours,
        minutes,
        seconds
      );
    }
  }

  reasonChange() {
     this.stopSpeechToText()
    let desc = '';
    if (this.selectedEReason?.reason === 'Enrollment/Consent') {
      desc = "<p>Reviewed active diagnoses of **Mr./Ms. XXXXXXXX** chart. The patient has chronic medical conditions including XXXXXX which are being managed by our clinic.</p>" +
        "<p>I have conducted a comprehensive discussion with **Mr./Ms. XXXXXXXX** regarding the enrollment into our Chronic Care Management (CCM) and/or Remote Patient Monitoring (RPM) program in the clinic/ over the phone.</p>" +
        "<p>During this discussion, I clarified the cost - sharing responsibilities associated with the program and emphasized the patient's ability to voluntarily opt out of the program at any time. Furthermore, I ensured that **Mr./Ms. XXXXXXXX** fully understands all components of the consent for participation in the CCM and/or RPM program.</p>" +
        "<p>It was reiterated that these services do not constitute emergency care and are not intended to replace regular clinic visits.I also gave my information to the patient to contact me anytime. **Mr./Ms. XXXXXXXX** has expressed a clear understanding of the program's objectives and has indicated a desire to enroll in both CCM and/or RPM services.</p>";

    } else if (this.selectedEReason?.reason === 'Device Documentation/Training') {
      desc = "Mr/Ms **<span class='highlighted'>[Patient's Last Name]'s</span>** was enrolled in remote patient monitoring with Tenovi Glucometer. Patient was explained about each component: Glucometer, Hub, Test strips, lancets. Instructed how to insert a test strip into the meter. Recommended to wash hands thoroughly before testing to ensure accurate results. Shared contact information for any assistance with any issues. Patient was also informed that I will be contacting 2-3 times a month for close monitoring of blood sugars.";

    } else if (this.selectedEReason?.reason === 'BPM Device Documentation/Training') {
      desc = "Mr./Ms. **<span class='highlighted'>[Patient's Last Name]'s</span>** was enrolled in remote patient monitoring with Tenovi blood pressure (BP) monitor. Patient education was provided regarding each component of the monitoring system, including the BP monitor itself, the hub for data transmission, and the necessary accessories such as cuffs and batteries. The patient was instructed on how to properly apply the cuff to their upper arm and how to initiate a blood pressure reading. They were also educated on the importance of sitting in a relaxed position with feet flat on the floor and back supported during measurements to ensure accuracy. Additionally, the patient was advised to avoid talking or moving during the reading. It was emphasized that consistent positioning and timing of measurements are crucial for reliable results, and recommended to take at least one reading a day." +
        "The patient was assured that support would be available for any technical issues or questions they might encounter with the BP monitor. They were informed of the frequency of contact for monitoring purposes and encouraged to reach out for assistance or guidance as needed. This comprehensive education empowers patients to actively participate in managing their blood pressure and ensures the success of remote patient monitoring for hypertension.";

    } else if (this.selectedEReason?.reason === "Introduction to RPM") {
      desc =
        "<p>    During today's consultation, I discussed the enrollment of **<span class='highlighted'>Patient's Name's</span>** into the Remote Patient Monitoring (RPM) program. I explained that RPM involves the use of deviceType to monitor vitals in real time." +
        " This data is transmitted securely to the healthcare team, allowing continuous oversight of **<span class='highlighted'>Patient's Name's</span>** health status. I emphasized that this proactive approach enables early detection of potential issues, ensuring timely interventions and reducing the risk of complications and hospitalizations.</P>" +
        " Additionally, the care manager highlighted the convenience and peace of mind offered by the RPM program. **<span class='highlighted'>Patient's Name's</span>** was reassured that (patient) would receive regular feedback and support from the healthcare team without needing to frequently visit the clinic. I also explained that the program includes scheduled virtual check-ins and access to healthcare professionals who can answer questions and provide guidance based on the monitored data. The discussion underscored the RPM program's role in enhancing **<span class='highlighted'>Patient's Name's</span>** health management by offering a higher level of continuous care and immediate response to any health concerns.";

    } else if (this.selectedEReason?.reason === "Introduction to CCM") {
      desc =
        "<P>  During today's consultation, I spoke with **<span class='highlighted'>Patient's Name's</span>** about enrollment in the Chronic Care Management (CCM) program. I provided a thorough overview of the CCM program, emphasizing its purpose of offering continuous, comprehensive support for patients with chronic conditions. **<span class='highlighted'>Patient's Name's</span>** was informed that through CCM, patient would receive personalized care coordination, including monthly check-ins, medication management, and assistance with scheduling and keeping track of medical appointments. I explained that a dedicated care manager would be assigned to work closely with **<span class='highlighted'>Patient's Name's</span>**, ensuring that care plan is tailored to patientâ€™s specific needs and adjusted as necessary to optimize  health outcomes. </P>" +
        "I further elaborated on the various benefits of the CCM program, highlighting how it aims to improve **<span class='highlighted'>Patient's Name's</span>** overall quality of life by enhancing adherence to  treatment plan and providing proactive management of  chronic conditions. **<span class='highlighted'>Patient's Name's</span>** was reassured that he would have access to a consistent point of contact who could address any concerns, coordinate with  healthcare providers, and facilitate communication between different specialists involved in  care. The discussion underscored the value of the CCM program in providing structured support and fostering a collaborative approach to managing chronic illnesses, ultimately aiming to reduce hospitalizations and improve long-term health outcomes for **<span class='highlighted'>Patient's Name's</span>**.";

    } else if (this.selectedEReason?.reason === "Introduction to PCM") {
      desc =
        "<p>   During today's consultation, I discussed with **<span class='highlighted'>Patient's Name's</span>** regarding  enrollment in the Principal Care Management (PCM) program. I explained that PCM is specifically tailored for patients with single, high-risk chronic conditions like (Diabetes, CKD, CHF, COPD, Chronic respiratory failure) requiring focused and intensive management. **<span class='highlighted'>Patient's Name's</span>** was informed that the PCM program would provide him/her with a dedicated care manager who will coordinate  care and ensure all aspects of  condition are meticulously monitored. This includes regular check-ins, personalized care planning, medication management, and assistance with scheduling necessary appointments and tests. I emphasized that this program aims to optimize **<span class='highlighted'>Patient's Name's</span>** health outcomes by addressing  specific condition with targeted, ongoing support.</P>" +
        " I further explained the benefits of the PCM program, highlighting its role in improving **<span class='highlighted'>Patient's Name's</span>** quality of life by offering comprehensive and continuous care. Patient was reassured that patient would have a consistent point of contact for all healthcare needs, ensuring that any changes in  condition are promptly addressed and managed. I also emphasized the collaborative nature of the PCM program, where the dedicated care manager will work closely with **<span class='highlighted'>Patient's Name's</span>** healthcare providers to ensure seamless communication and coordination of care. The discussion underscored the PCM program's goal of providing a structured, proactive approach to managing Mr. Smith's chronic condition (Diabetes, CKD, CHF, COPD, Chronic respiratory failure), ultimately aiming to reduce hospitalizations and enhance long-term health outcomes.";

    } else if (this.selectedEReason?.reason === "Introduction to RTM") {
      desc =
        "<p> During today's consultation, I discussed the enrollment of **<span class='highlighted'>Patient's Name's</span>** into the Remote Therapy Monitoring (RTM) program. I explained that the RTM program is designed to monitor and support patients undergoing therapy for specific conditions (COPD, Chronic respiratory failure, Emphysema, Obstructive Sleep Apnea), ensuring that they adhere to their prescribed treatment plans and achieve the best possible outcomes. **<span class='highlighted'>Patient's Name's</span>** was informed that the program involves the use of remote monitoring devices and digital tools (Smart Inhalers) to track therapy-related metrics, such as medication adherence, and symptom progression. The data collected through these devices is transmitted securely to the healthcare team, enabling continuous oversight and timely adjustments to **<span class='highlighted'>Patient's Name's</span>** therapy plan as needed. </p>" +
        " Additionally, I highlighted the personalized support and convenience offered by the RTM program. **<span class='highlighted'>Patient's Name's</span>** was reassured that patient would receive regular feedback and guidance from the healthcare team based on the monitored data, without the need for frequent in-person visits. I emphasized that the program includes scheduled virtual check-ins and access to healthcare professionals who can address any questions or concerns related to  therapy. The discussion underscored the RTM program's role in enhancing **<span class='highlighted'>Patient's Name's</span>** therapy management by providing a higher level of continuous care, improving adherence to the treatment plan, and ultimately aiming to achieve better health outcomes.";

    } else if (this.selectedEReason?.reason === "Introduction to PCM & RPM") {
      desc =
        "<p> During today's consultation, I had an in-depth discussion with **<span class='highlighted'>Patient's Name's</span>** regarding  enrollment into the Principal Care Management (PCM) and Remote Patient Monitoring (RPM) programs. The care manager explained that the PCM program is specifically designed for patients with a single complex chronic condition like (drop down should be able to pick up Diabetes, CHF, CKD, CHF, COPD, Chronic respiratory failure) requiring intensive management. **<span class='highlighted'>Patient's Name's</span>** was informed that PCM would provide him/her with dedicated support focused on  specific health needs, including regular check-ins, personalized care plans, and close monitoring of  condition. I emphasized the importance of this focused approach in improving **<span class='highlighted'>Patient's Name's</span>** health outcomes by addressing  condition with targeted interventions and continuous oversight. </P" +
        " Additionally, I explained about the RPM program, which involves using at-home monitoring devices like deviceType to track **<span class='highlighted'>Patient's Name's</span>** deviceType in real time. This program will enable the care team to detect any potential health issues early and intervene promptly, thus preventing complications and reducing the likelihood of hospital visits. I highlighted that the integration of RPM with PCM would provide a comprehensive support system, ensuring that **<span class='highlighted'>Patient's Name's</span>** receives timely and effective care tailored to  specific condition. **<span class='highlighted'>Patient's Name's</span>** was reassured that this combined approach would enhance  health management and provide him with peace of mind, knowing  condition is being closely monitored and managed by  care team.";

    } else if (this.selectedEReason?.reason === "Introduction to PCM & RTM") {
      desc =
        "<p> During today's consultation, I discussed with **<span class='highlighted'>Patient's Name's</span>** regarding enrollment in both the Remote Therapy Monitoring (RTM) and Principal Care Management (PCM) programs. I explained that the RTM program is designed to support and monitor patients undergoing therapy for specific conditions. This involves the use of remote monitoring devices and digital tools (Smart inhaler) to track key therapy-related metrics such as physical activity levels, medication adherence, and symptom progression. The data collected is securely transmitted to the healthcare team, enabling continuous oversight and timely adjustments to **<span class='highlighted'>Patient's Name's</span>** therapy plan as needed. I emphasized that this proactive approach ensures **<span class='highlighted'>Patient's Name's</span>** remains on track with  therapy, improving adherence and outcomes without requiring frequent in-person visits. </p>" +
        " Simultaneously, I detailed the PCM program, which provides focused management for a single high-risk chronic condition. **<span class='highlighted'>Patient's Name's</span>** was informed that the PCM program would assign him a dedicated care manager to coordinate all aspects of  care, including regular check-ins, personalized care planning, medication management, and scheduling necessary appointments and tests. I highlighted that the PCM program aims to optimize **<span class='highlighted'>Patient's Name's</span>** health outcomes by offering comprehensive, ongoing support specifically tailored to  chronic condition (HTN, DM2, CHF, CKD, COPD). The discussion underscored the benefits of integrating both RTM and PCM programs, as the combination provides a robust support system. **<span class='highlighted'>Patient's Name's</span>** will have continuous monitoring and personalized care, ensuring a cohesive and proactive approach to managing  health and therapy, ultimately aiming to reduce hospitalizations and enhance  long-term health outcomes.";

    } else if (this.selectedEReason?.reason === "Introduction to CCM & RPM") {
      desc =
        "<p> During today's consultation, I discussed with **<span class='highlighted'>Patient's Name's</span>** the benefits and details of enrolling in the Chronic Care Management (CCM) and Remote Patient Monitoring (RPM) programs. I explained that the CCM program would provide **<span class='highlighted'>Patient's Name's</span>** with personalized care coordination, including monthly check-ins and continuous support from a dedicated care manager. This program aims to enhance adherence to  care plan, manage medications, and ensure regular follow-ups on  health status. I emphasized that the goal is to improve **<span class='highlighted'>Patient's Name's</span>** overall health outcomes and quality of life through structured and proactive management of  chronic conditions. </p>" +
        " Furthermore, I introduced the RPM program, detailing how it involves the use of at-home devices like deviceType to monitor **<span class='highlighted'>Patient's Name's</span>** deviceType in real-time. I highlighted that this program allows for early detection of potential health issues, enabling timely interventions and reducing the risk of hospitalizations. **<span class='highlighted'>Patient's Name's</span>** was reassured that the continuous monitoring would provide him/her with peace of mind and immediate access to support when needed. The integration of both CCM and RPM programs was presented as a comprehensive approach to managing **<span class='highlighted'>Patient's Name's</span>** health, offering a higher level of care and engagement to ensure better health outcomes.";

    } else if (this.selectedEReason?.reason === 'Data Review') {
      //getLatestVital();
    } else if (this.selectedEReason?.reason === 'Glucometer Device education Template') {
      desc = "<p>Mr./Ms. **<span class='highlighted'>Patient's Name's</span>** was enrolled in the remote patient monitoring (RPM) program with a glucometer. Comprehensive patient education was provided regarding each component of the monitoring system, including the glucometer device, the hub for data transmission, and the necessary accessories such as test strips, lancets, and batteries. </p>" +
        " The patient was instructed on how to properly insert a test strip into the glucometer and how to use the lancing device to obtain a blood sample from the fingertip. They were educated on the importance of washing hands with soap and warm water before testing to ensure accuracy, and drying them thoroughly to prevent dilution of the blood sample. The patient was also advised to use a fresh lancet for each test to maintain hygiene and reduce the risk of infection." +
        " Education included guidance on the optimal times for blood glucose testing, such as fasting in the morning, before meals, two hours after meals, and at bedtime, depending on their personalized care plan. The importance of consistent timing and proper documentation of readings was emphasized to ensure reliable data for effective blood glucose management." +
        " The patient was instructed on how to interpret their blood glucose readings and identify patterns or trends. They were also educated on recognizing signs and symptoms of hypo- and hyperglycemia and the appropriate actions to take in each scenario." +
        " Support resources were highlighted, ensuring the patient knows assistance is available for any technical issues or questions related to the glucometer. The frequency of monitoring contacts was discussed, and the patient was encouraged to reach out for guidance or support as needed.";

    } else if (this.selectedEReason?.reason === 'Weigh scale device education template for CHF') {
      desc = "<p>Mr./Ms. **<span class='highlighted'>Patient's Name's</span>** was enrolled in the remote patient monitoring (RPM) program with a weight scale. Comprehensive patient education was provided regarding each component of the monitoring system, including the digital weight scale, the hub for data transmission, and the necessary accessories such as batteries and charging cables." +
        " The patient was instructed on how to properly place the scale on a flat, hard surface to ensure accurate measurements. They were educated on the importance of weighing themselves at the same time each day, preferably in the morning after using the restroom and before eating or drinking, while wearing similar clothing to maintain consistency in readings." +
        " Education included guidance on the significance of daily weight monitoring in managing congestive heart failure (CHF), emphasizing that sudden changes in weight can indicate fluid retention and potential worsening of heart failure. The patient was instructed to stand still on the scale with feet evenly spaced and to wait until the measurement is complete and transmitted." +
        " The patient was also educated on recognizing symptoms of CHF exacerbation, such as shortness of breath, swelling in the legs or abdomen, and rapid weight gain (e.g., more than 2-3 pounds in a day or 5 pounds in a week). They were informed about the importance of reporting these symptoms or significant weight changes to their healthcare provider immediately." +
        " Support resources were highlighted, ensuring the patient knows assistance is available for any technical issues or questions related to the weight scale. The frequency of monitoring contacts was discussed, and the patient was encouraged to reach out for guidance or support as needed. CMS guidelines.";
    } else if (this.selectedEReason?.reason === 'Weigh scale device education template for ESRD, volume overload') {
      desc = "<p>Mr./Ms. **<span class='highlighted'>Patient's Name's</span>** was enrolled in the remote patient monitoring (RPM) program with a weight scale. Comprehensive patient education was provided regarding each component of the monitoring system, including the digital weight scale, the hub for data transmission, and the necessary accessories such as batteries and charging cables.</p>" +
        "The patient was instructed on how to properly place the scale on a flat, hard surface to ensure accurate measurements. They were educated on the importance of weighing themselves at the same time each day, preferably in the morning after using the restroom and before eating or drinking, while wearing similar clothing to maintain consistency in readings." +
        "Education included guidance on the significance of daily weight monitoring in managing end-stage renal disease (ESRD) and monitoring for volume overload. The patient was informed that sudden changes in weight can indicate fluid retention, which may require adjustments in dialysis or other treatment interventions. They were instructed to stand still on the scale with feet evenly spaced and to wait until the measurement is complete and transmitted." +
        "The patient was also educated on recognizing symptoms of volume overload, such as swelling in the legs, feet, or abdomen, shortness of breath, and rapid weight gain (e.g., more than 2-3 pounds in a day or 5 pounds in a week). They were informed about the importance of reporting these symptoms or significant weight changes to their healthcare provider immediately to prevent complications." +
        "Support resources were highlighted, ensuring the patient knows assistance is available for any technical issues or questions related to the weight scale. The frequency of monitoring contacts was discussed, and the patient was encouraged to reach out for guidance or support as needed";

    }
    else if (this.selectedEReason?.reason === 'Medication Review & Reconciliation') {
      desc = "Spoke with patient regarding current medications. Reviewed adherence, discussed side effects, and confirmed refill status. Patient reports taking [list key medications] as prescribed. Noted [any concerns, changes, or side effects]. ";
    }
    else if (this.selectedEReason?.reason === 'Vitals Review (BP, Weight, Glucose)') {
      desc = "Reviewed RPM data from [device name]. Vitals for review period: [summarize readings or trends]. Noted [abnormal/high/low] readings. Patient was [notified or called], and [next step taken]. ";
    }
    else if (this.selectedEReason?.reason === 'Chart Review Before Call') {
      desc = "Reviewed patient chart in EMR prior to outreach. Assessed prior office notes, medication list, and care plan progress. Identified [specific concerns, labs, or gaps]. ";
    }
    else if (this.selectedEReason?.reason === 'Call After Abnormal RPM Reading') {
      desc = "Called patient regarding abnormal [BP/glucose] reading of __ on __ date. Patient reports [e.g., skipped meds, stress, missed dose]. Discussed next steps including [med changes, hydration, follow-up]. ";
    }
    else if (this.selectedEReason?.reason === 'Coordination With Clinical Staff') {
      desc = "Spoke with [MA/nurse/pharmacist] regarding [refill request, appointment scheduling, referral]. Confirmed next steps and updated care plan as needed. ";
    }
    else if (this.selectedEReason?.reason === 'Discussion With Medical Director') {
      desc = "Discussed **<span class='highlighted'>Patient's Name's</span>** with Dr. [Meegada/Palabindala] regarding [e.g., persistent high BP despite meds]. Plan includes [adjust meds / schedule visit / escalate to provider]. ";
    }
    else if (this.selectedEReason?.reason === 'Care Coordination With PCP/Specialist') {
      desc = "Coordinated care with Dr. [Name], [specialty]. Discussed [e.g., uncontrolled diabetes, new symptoms, lab results]. Care plan updated to include [changes]. ";
    }
    else if (this.selectedEReason?.reason === 'Care Plan Review and Goal Setting') {
      desc = "Completed care plan review with patient. Discussed goals including [e.g., blood pressure control, diet adherence]. Identified barriers such as [e.g., poor motivation, access to healthy food]. Updated goals accordingly. ";
    }
    else if (this.selectedEReason?.reason === 'Support for SDOH') {
      desc = "Patient reported issue with [transportation/housing/food insecurity]. Referred to [social worker/community resource]. Provided patient with contact info and followed up on status. ";
    }
    else if (this.selectedEReason?.reason === 'Review of Asynchronous Messages') {
      desc = "Reviewed secure message from patient dated __ regarding [symptom/medication question]. Responded with clinical guidance including [recommendations or escalation]. ";
    }
    else if (this.selectedEReason?.reason === 'Device Setup or Tech Support') {
      desc = "Assisted patient via phone/video with device setup. Walked through pairing process and verified understanding. Patient was able to complete reading successfully. No billable time.";
    }
    else if (this.selectedEReason?.reason === 'Monthly CCM Check-In') {
      desc = "Completed monthly CCM call. Reviewed health status, medications, appointments, and care plan adherence. Patient reports [e.g., stable BP, med compliance]. Discussed lifestyle and follow-up care. ";
    } else if (this.selectedEReason?.reason === 'Lifestyle Coaching') {
      desc = "Provided coaching on [e.g., low-sodium diet, glucose monitoring, exercise]. Patient engaged in discussion and set goal to [e.g., walk 20 mins daily, reduce soda]. Encouraged adherence and will reassess next call. ";
    }
    else {
      desc = "";
    }
  
    // if (this.selectedEReason?.reason === 'Device Documentation/Training') {
    //   desc =
    //     "Mr/Ms **<span class='highlighted'>[Patient's Last Name]'s</span>** was enrolled in remote patient monitoring with Tenovi Glucometer. Patient was explained about each component: Glucometer, Hub, Test strips, lancets. Instructed how to insert a test strip into the meter. Recommended to wash hands thoroughly before testing to ensure accurate results. Shared contact information for any assistance with any issues. Patient was also informed that I will be contacting 2-3 times a month for close monitoring of blood sugars.";
    // } else if (
    //   this.selectedEReason?.reason === 'Device Documentation/Training'
    // ) {
    //   desc =
    //     "Mr/Ms **<span class='highlighted'>[Patient's Last Name]'s</span>** was enrolled in remote patient monitoring with Tenovi Glucometer. Patient was explained about each component: Glucometer, Hub, Test strips, lancets. Instructed how to insert a test strip into the meter. Recommended to wash hands thoroughly before testing to ensure accurate results. Shared contact information for any assistance with any issues. Patient was also informed that I will be contacting 2-3 times a month for close monitoring of blood sugars.";
    // } else if (
    //   this.selectedEReason?.reason === 'BPM Device Documentation/Training'
    // ) {
    //   desc =
    //     "Mr./Ms. **<span class='highlighted'>[Patient's Last Name]'s</span>** was enrolled in remote patient monitoring with Tenovi blood pressure (BP) monitor. Patient education was provided regarding each component of the monitoring system, including the BP monitor itthis.selectedEReason?.reasonreasonf, the hub for data transmission, and the necessary accessories such as cuffs and batteries. The patient was instructed on how to properly apply the cuff to their upper arm and how to initiate a blood pressure reading. They were also educated on the importance of sitting in a relaxed position with feet flat on the floor and back supported during measurements to ensure accuracy. Additionally, the patient was advised to avoid talking or moving during the reading. It was emphasized that consistent positioning and timing of measurements are crucial for reliable results, and recommended to take at least one reading a day." +
    //     'The patient was assured that support would be available for any technical issues or questions they might encounter with the BP monitor. They were informed of the frequency of contact for monitoring purposes and encouraged to reach out for assistance or guidance as needed. This comprehensive education empowers patients to actively participate in managing their blood pressure and ensures the success of remote patient monitoring for hypertension.';
    // } else if (this.selectedEReason?.reason === 'Introduction to RPM') {
    //   desc =
    //     "<p>    During today's consultation, I discussed the enrollment of **<span class='highlighted'>[Patient's Name]'s</span>** into the Remote Patient Monitoring (RPM) program. I explained that RPM involves the use of (BP monitor, Glucometer, Weigh Scale, Pulse Oximeter, Smart inhaler) to monitor (BP/Blood glucose/Body weight/Oxygen levels/compliance with inhalers) in real time." +
    //     " This data is transmitted securely to the healthcare team, allowing continuous oversight of **<span class='highlighted'>[Patient's Name]'s</span>** health status. I emphasized that this proactive approach enables early detection of potential issues, ensuring timely interventions and reducing the risk of complications and hospitalizations.</P>" +
    //     " Additionally, the care manager highlighted the convenience and peace of mind offered by the RPM program. **<span class='highlighted'>[Patient's Name]'s</span>** was reassured that (he/she) would receive regular feedback and support from the healthcare team without needing to frequently visit the clinic. I also explained that the program includes scheduled virtual check-ins and access to healthcare professionals who can answer questions and provide guidance based on the monitored data. The discussion underscored the RPM program's role in enhancing **<span class='highlighted'>[Patient's Name]'s</span>** health management by offering a higher level of continuous care and immediate response to any health concerns.";
    // } else if (this.selectedEReason?.reason === 'Introduction to CCM') {
    //   desc =
    //     "<P>  During today's consultation, I spoke with **<span class='highlighted'>[Patient's Name]'s</span>** about enrollment in the Chronic Care Management (CCM) program. I provided a thorough overview of the CCM program, emphasizing its purpose of offering continuous, comprehensive support for patients with chronic conditions. **<span class='highlighted'>[Patient's Name]'s</span>** was informed that through CCM, he/she would receive personalized care coordination, including monthly check-ins, medication management, and assistance with scheduling and keeping track of medical appointments. I explained that a dedicated care manager would be assigned to work clothis.selectedEReason?.reasonreasony with **<span class='highlighted'>[Patient's Name]'s</span>**, ensuring that care plan is tailored to patient’s specific needs and adjusted as necessary to optimize his/her health outcomes. </P>" +
    //     "I further elaborated on the various benefits of the CCM program, highlighting how it aims to improve **<span class='highlighted'>[Patient's Name]'s</span>** overall quality of life by enhancing adherence to his/her treatment plan and providing proactive management of his/her chronic conditions. **<span class='highlighted'>[Patient's Name]'s</span>** was reassured that he would have access to a consistent point of contact who could address any concerns, coordinate with his/her healthcare providers, and facilitate communication between different specialists involved in his/her care. The discussion underscored the value of the CCM program in providing structured support and fostering a collaborative approach to managing chronic illnesses, ultimately aiming to reduce hospitalizations and improve long-term health outcomes for **<span class='highlighted'>[Patient's Name]'s</span>**.";
    // } else if (this.selectedEReason?.reason === 'Introduction to PCM') {
    //   desc =
    //     "<p>   During today's consultation, I discussed with **<span class='highlighted'>[Patient's Name]'s</span>** regarding his/her enrollment in the Principal Care Management (PCM) program. I explained that PCM is specifically tailored for patients with single, high-risk chronic conditions like (Diabetes, CKD, CHF, COPD, Chronic respiratory failure) requiring focused and intensive management. **<span class='highlighted'>[Patient's Name]'s</span>** was informed that the PCM program would provide him/her with a dedicated care manager who will coordinate his/her care and ensure all aspects of his/her condition are meticulously monitored. This includes regular check-ins, personalized care planning, medication management, and assistance with scheduling necessary appointments and tests. I emphasized that this program aims to optimize **<span class='highlighted'>[Patient's Name]'s</span>** health outcomes by addressing his/her specific condition with targeted, ongoing support.</P>" +
    //     " I further explained the benefits of the PCM program, highlighting its role in improving **<span class='highlighted'>[Patient's Name]'s</span>** quality of life by offering comprehensive and continuous care. Patient was reassured that he/she would have a consistent point of contact for all healthcare needs, ensuring that any changes in his/her condition are promptly addressed and managed. I also emphasized the collaborative nature of the PCM program, where the dedicated care manager will work clothis.selectedEReason?.reasonreasony with **<span class='highlighted'>[Patient's Name]'s</span>** healthcare providers to ensure seamless communication and coordination of care. The discussion underscored the PCM program's goal of providing a structured, proactive approach to managing Mr. Smith's chronic condition (Diabetes, CKD, CHF, COPD, Chronic respiratory failure), ultimately aiming to reduce hospitalizations and enhance long-term health outcomes.";
    // } else if (this.selectedEReason?.reason === 'Introduction to RTM') {
    //   desc =
    //     "<p> During today's consultation, I discussed the enrollment of **<span class='highlighted'>[Patient's Name]'s</span>** into the Remote Therapy Monitoring (RTM) program. I explained that the RTM program is designed to monitor and support patients undergoing therapy for specific conditions (COPD, Chronic respiratory failure, Emphysema, Obstructive Sleep Apnea), ensuring that they adhere to their prescribed treatment plans and achieve the best possible outcomes. **<span class='highlighted'>[Patient's Name]'s</span>** was informed that the program involves the use of remote monitoring devices and digital tools (Smart Inhalers) to track therapy-related metrics, such as medication adherence, and symptom progression. The data collected through these devices is transmitted securely to the healthcare team, enabling continuous oversight and timely adjustments to **<span class='highlighted'>[Patient's Name]'s</span>** therapy plan as needed. </p>" +
    //     " Additionally, I highlighted the personalized support and convenience offered by the RTM program. **<span class='highlighted'>[Patient's Name]'s</span>** was reassured that he/she would receive regular feedback and guidance from the healthcare team based on the monitored data, without the need for frequent in-person visits. I emphasized that the program includes scheduled virtual check-ins and access to healthcare professionals who can address any questions or concerns related to his/her therapy. The discussion underscored the RTM program's role in enhancing **<span class='highlighted'>[Patient's Name]'s</span>** therapy management by providing a higher level of continuous care, improving adherence to the treatment plan, and ultimately aiming to achieve better health outcomes.";
    // } else if (
    //   this.selectedEReason?.reasonreason === 'Introduction to PCM & RPM'
    // ) {
    //   desc =
    //     "<p> During today's consultation, I had an in-depth discussion with **<span class='highlighted'>[Patient's Name]'s</span>** regarding his/her enrollment into the Principal Care Management (PCM) and Remote Patient Monitoring (RPM) programs. The care manager explained that the PCM program is specifically designed for patients with a single complex chronic condition like (drop down should be able to pick up Diabetes, CHF, CKD, CHF, COPD, Chronic respiratory failure) requiring intensive management. **<span class='highlighted'>[Patient's Name]'s</span>** was informed that PCM would provide him/her with dedicated support focused on his/her specific health needs, including regular check-ins, personalized care plans, and close monitoring of his/her condition. I emphasized the importance of this focused approach in improving **<span class='highlighted'>[Patient's Name]'s</span>** health outcomes by addressing his/her condition with targeted interventions and continuous oversight. </P" +
    //     " Additionally, I explained about the RPM program, which involves using at-home monitoring devices like (drop down to pick BP monitor, glucometer, weigh scale, pulse oximeter) to track **<span class='highlighted'>[Patient's Name]'s</span>** (drop down to pick BP, blood glucose, body weight, pulse oximeter) in real time. This program will enable the care team to detect any potential health issues early and intervene promptly, thus preventing complications and reducing the likelihood of hospital visits. I highlighted that the integration of RPM with PCM would provide a comprehensive support system, ensuring that **<span class='highlighted'>[Patient's Name]'s</span>** receives timely and effective care tailored to his/her specific condition. **<span class='highlighted'>[Patient's Name]'s</span>** was reassured that this combined approach would enhance his/her health management and provide him with peace of mind, knowing his/her condition is being clothis.selectedEReason?.reasonreasony monitored and managed by his/her care team.";
    // } else if (
    //   this.selectedEReason?.reasonreason === 'Introduction to PCM & RTM'
    // ) {
    //   desc =
    //     "<p> During today's consultation, I discussed with **<span class='highlighted'>[Patient's Name]'s</span>** regarding enrollment in both the Remote Therapy Monitoring (RTM) and Principal Care Management (PCM) programs. I explained that the RTM program is designed to support and monitor patients undergoing therapy for specific conditions. This involves the use of remote monitoring devices and digital tools (Smart inhaler) to track key therapy-related metrics such as physical activity levels, medication adherence, and symptom progression. The data collected is securely transmitted to the healthcare team, enabling continuous oversight and timely adjustments to **<span class='highlighted'>[Patient's Name]'s</span>** therapy plan as needed. I emphasized that this proactive approach ensures **<span class='highlighted'>[Patient's Name]'s</span>** remains on track with his/her therapy, improving adherence and outcomes without requiring frequent in-person visits. </p>" +
    //     " Simultaneously, I detailed the PCM program, which provides focused management for a single high-risk chronic condition. **<span class='highlighted'>[Patient's Name]'s</span>** was informed that the PCM program would assign him a dedicated care manager to coordinate all aspects of his/her care, including regular check-ins, personalized care planning, medication management, and scheduling necessary appointments and tests. I highlighted that the PCM program aims to optimize **<span class='highlighted'>[Patient's Name]'s</span>** health outcomes by offering comprehensive, ongoing support specifically tailored to his/her chronic condition (HTN, DM2, CHF, CKD, COPD). The discussion underscored the benefits of integrating both RTM and PCM programs, as the combination provides a robust support system. **<span class='highlighted'>[Patient's Name]'s</span>** will have continuous monitoring and personalized care, ensuring a cohesive and proactive approach to managing his/her health and therapy, ultimately aiming to reduce hospitalizations and enhance his/her long-term health outcomes.";
    // } else if (
    //   this.selectedEReason?.reasonreason === 'Introduction to CCM & RPM'
    // ) {
    //   desc =
    //     "<p> During today's consultation, I discussed with **<span class='highlighted'>[Patient's Name]'s</span>** the benefits and details of enrolling in the Chronic Care Management (CCM) and Remote Patient Monitoring (RPM) programs. I explained that the CCM program would provide **<span class='highlighted'>[Patient's Name]'s</span>** with personalized care coordination, including monthly check-ins and continuous support from a dedicated care manager. This program aims to enhance adherence to his/her care plan, manage medications, and ensure regular follow-ups on his/her health status. I emphasized that the goal is to improve **<span class='highlighted'>[Patient's Name]'s</span>** overall health outcomes and quality of life through structured and proactive management of his/her chronic conditions. </p>" +
    //     " Furthermore, I introduced the RPM program, detailing how it involves the use of at-home devices like (drop down to pick BP monitor, glucometer, weigh scale, pulse oximeter) to monitor **<span class='highlighted'>[Patient's Name]'s</span>** (drop down to pick BP, blood glucose, body weight, pulse oximeter) in real-time. I highlighted that this program allows for early detection of potential health issues, enabling timely interventions and reducing the risk of hospitalizations. **<span class='highlighted'>[Patient's Name]'s</span>** was reassured that the continuous monitoring would provide him/her with peace of mind and immediate access to support when needed. The integration of both CCM and RPM programs was presented as a comprehensive approach to managing **<span class='highlighted'>[Patient's Name]'s</span>** health, offering a higher level of care and engagement to ensure better health outcomes.";
    // } else {
    //   desc = '';
    // }
    desc = desc.replace(/\*\*(.*?)\*\*/g, '<span class="bold">$1</span>');
    desc = desc.replace(
      /<span class='highlighted'>(.*?)<\/span>/g,
      '<span class="highlighted">$1</span>'
    );
    //this.description = this.sanitizer.bypassSecurityTrustHtml(desc);
    this.description = desc;
        this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description||'');
    this.tempdescription = desc;
    if(this.description!="")
    this.formatPreDefinedText();
    if(desc=="")
    {
      this.showSpeech=true;
      this.description=undefined
    }
    else
    {
      this.showSpeech=false
    }
  }

  formatPreDefinedText() {
    this.description = this.tempdescription;
        this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description);

    var formattedText = this.description;
    formattedText = formattedText.replace(/\*\*(.*?)\*\*/g, '<span class="bold">$1</span>'); // Apply bold formatting
    formattedText = formattedText.replace(/<span class='highlighted'>(.*?)<\/span>/g, '<span class="highlighted">$1</span>'); // Preserve existing highlighted formatting
    formattedText = formattedText.replaceAll(/Patient's Name/g, this.patientName);
    formattedText = formattedText.replaceAll(/Patient's Last Name/g, this.patientName);
    formattedText = formattedText.replaceAll(/deviceType/g, this.selectedDeviceType.type || 'deviceType');
    this.description = this.sanitizer.bypassSecurityTrustHtml(formattedText);
  this.safeDescription =this.description
  };
  updateText(event: Event): void {
    const element = event.target as HTMLElement;
    this.description = element.innerHTML;
        this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description);

  }
  deviceChange() {
   // this.description = this.tempdescription;
     //   this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description);
   // this.formatPreDefinedText();
  }
  reset() {
    this.selectedProgram = undefined;
    this.selectedEReason = '';
    this.encounterDate = new Date();
    this.value4 = '';
    this.description = '';
    this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description);
    this.selectedDeviceType = "";
    this.cdr.detectChanges()
  }


  getData() {
    console.log(this.selectedDeviceType, this.selectedProgram)
  }



  startSpeechToText() {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech Recognition is not supported in this browser.');
      return;
    }
    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
    this.recognition = new SpeechRecognition();
    this.recognition.lang = 'en-US';
    this.recognition.interimResults = true;
    this.recognition.maxAlternatives = 1;
    this.isListening = true;
    this.recognition.start();

    let finalTranscript = this.description || '';
    this.recognition.onresult = (event: any) => {
      let interimTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; ++i) {
        if (event.results[i].isFinal) {
          finalTranscript += ' ' + event.results[i][0].transcript;
        } else {
          interimTranscript += event.results[i][0].transcript;
        }
      }
       
      this.description = (finalTranscript + ' ' + interimTranscript);
   this.description =  this.description.replace(/\(see https:\/\/g\.co\/ng\/security#xss\)/gi, '');
this.description = this.description.replace(/SafeValue must use \\[property\\]=binding:/gi, '');      this.safeDescription = this.sanitizer.bypassSecurityTrustHtml(this.description);
      this.cdr.detectChanges();
    };

    this.recognition.onerror = (event: any) => {
      this.isListening = true;
      //alert('Speech recognition error: ' + event.error);
    };

    this.recognition.onend = () => {
      if (this.isListening) {
        this.recognition.start(); // Restart if user hasn't pressed Stop
      }
    };
  }

  stopSpeechToText() {
    if (this.recognition) {
      this.recognition.stop();
      this.isListening = false;
    }
  }
  
  onContentChange(event: Event): void {
  const html = (event.target as HTMLElement).innerHTML;
  this.description = this.getCleanContent(html);
  // Now use cleanContent for your logic
}
getCleanContent(html: string): string {
  const text = html.replace(/<[^>]*>/g, '').trim();
  return text === '' ? '' : html.trim();
}

onDateChange(event: Date | null) {
  if (!event) {
    this.encounterDate = new Date(); 
  }
}
onTimeChange(event: Date | null) {
  if (!event) {
    this.encounterTime = new Date(); 
  }
}
}
