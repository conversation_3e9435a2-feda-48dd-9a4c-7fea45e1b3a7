import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AudioService {
  private static readonly UPLOAD_AUDIO='service/patient/uploadAudio'
  private static readonly MAX_SAMPLE_RATE = 48000
  constructor(private http: HttpClient) {}
  private audioContext!: AudioContext;
  private mediaRecorder!: MediaRecorder;
  private recordedChunks: Blob[] = [];
  private microphoneStream!: MediaStream;
  private systemAudioStream!: MediaStream;
  private combinedStream!: MediaStream;
  private gainNode!: GainNode;
  private micGainNode!: GainNode;
  private systemGainNode!: GainNode;
  private destination!: MediaStreamAudioDestinationNode;

  isSystemAudioSupported(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia);
  }

  async initRecorder(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.destination = this.audioContext.createMediaStreamDestination();
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.destination);

      let hasMicrophone = false;
      let hasSystemAudio = false;

      try {
        this.microphoneStream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            sampleRate: Math.min(44100, AudioService.MAX_SAMPLE_RATE)
          }
        });
        
        const micSource = this.audioContext.createMediaStreamSource(this.microphoneStream);
        this.micGainNode = this.audioContext.createGain();
        this.micGainNode.gain.value = 1.0;
        micSource.connect(this.micGainNode);
        this.micGainNode.connect(this.gainNode);
        
        hasMicrophone = true;
        console.log('Microphone audio captured and connected');
      } catch (error) {
        console.warn('Failed to get microphone access:', error);
        throw new Error('Microphone access denied or unavailable');
      }

      if (!this.isSystemAudioSupported()) {
        throw new Error('System audio recording is not supported in this browser. Please use Chrome or Edge.');
      }

      try {
        const displayStream = await navigator.mediaDevices.getDisplayMedia({
          video: { 
            width: 1,
            height: 1
          }, 
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            sampleRate: Math.min(44100, AudioService.MAX_SAMPLE_RATE),
            suppressLocalAudioPlayback: false
          } as any
        });
        
        const systemAudioTracks = displayStream.getAudioTracks();
        const videoTracks = displayStream.getVideoTracks();
        
        console.log(`Found ${systemAudioTracks.length} system audio tracks`);
        
        if (systemAudioTracks.length === 0) {
          displayStream.getTracks().forEach(track => track.stop());
          console.warn('No system audio tracks found');
          throw new Error('No system audio available. If you selected a window, please try "Entire Screen" or "Chrome Tab" instead. Window sharing does not support audio capture.');
        }
        
        this.systemAudioStream = new MediaStream(systemAudioTracks);
        
        const systemSource = this.audioContext.createMediaStreamSource(this.systemAudioStream);
        this.systemGainNode = this.audioContext.createGain();
        this.systemGainNode.gain.value = 1.0;
        systemSource.connect(this.systemGainNode);
        this.systemGainNode.connect(this.gainNode);
        
        hasSystemAudio = true;
        console.log('System audio captured and connected');
        
        videoTracks.forEach(track => {
          console.log(`Stopping video track: ${track.label}`);
          track.stop();
        });
        
        systemAudioTracks.forEach(track => {
          track.addEventListener('ended', () => {
            console.log('System audio track ended - user stopped screen sharing');
          });
        });
        
      } catch (error) {
        console.warn('Failed to get system audio access:', error);
        
        if (error instanceof Error && error.name === 'NotAllowedError') {
          throw new Error('Screen sharing was cancelled. Please select a tab and check "Share audio" to record.');
        }
        
        if (error instanceof Error && error.name === 'NotFoundError') {
          throw new Error('No audio source found. Please select a tab/window with audio or check "Share audio" option.');
        }
        
        if (error instanceof Error && error.message.includes('audio')) {
          throw new Error('System audio not available. Note: Window sharing does not support audio capture. Please select "Entire Screen" or a "Chrome Tab" to record system audio.');
        }
        
        throw new Error('System audio capture failed. Both microphone and system audio are required.');
      }

      if (!hasMicrophone && !hasSystemAudio) {
        throw new Error('No audio sources available');
      }

      if (!hasSystemAudio) {
        throw new Error('System audio is required but was not captured. Please try again and ensure you select "Share audio".');
      }

      this.combinedStream = this.destination.stream;
      
      this.combinedStream.getTracks().forEach((track, index) => {
        console.log(`   Track ${index + 1}: ${track.kind} - ${track.label} - enabled: ${track.enabled}`);
      });

      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(this.combinedStream, {
        mimeType: this.getSupportedMimeType()
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };
      
    } catch (error) {
      console.error('Failed to initialize audio recorder:', error);
      this.cleanup();
      throw error;
    }
  }

  private cleanup(): void {
    if (this.microphoneStream) {
      this.microphoneStream.getTracks().forEach(track => track.stop());
    }
    if (this.systemAudioStream) {
      this.systemAudioStream.getTracks().forEach(track => track.stop());
    }
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];
    
    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        console.log(`Using MIME type: ${type}`);
        return type;
      }
    }
    
    console.log('Using default MIME type');
    return '';
  }

  startRecording(): void {
    if (!this.combinedStream || !this.mediaRecorder) {
      throw new Error('Recorder not initialized. Call initRecorder() first.');
    }
    
    console.log('Starting recording with tracks:');
    this.combinedStream.getTracks().forEach((track, index) => {
      console.log(`Active track ${index + 1}: ${track.kind} - ${track.label} - enabled: ${track.enabled} - readyState: ${track.readyState}`);
    });
    
    this.recordedChunks = [];
    this.mediaRecorder.start(1000);
  }

  async stopRecording(): Promise<Blob> {
    return new Promise<Blob>((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
        reject(new Error('MediaRecorder is not active'));
        return;
      }

      this.mediaRecorder.onstop = async () => {
        try {
          const audioBlob = new Blob(this.recordedChunks, { type: 'audio/webm' });
          const wavBlob = await this.convertToWav(audioBlob);
          resolve(wavBlob);
        } catch (error) {
          reject(error);
        }
      };

      this.mediaRecorder.stop();
    });
  }

  private async convertToWav(audioBlob: Blob): Promise<Blob> {
    try {
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      return this.audioBufferToWav(audioBuffer);
    } catch (error) {
      console.warn('Failed to convert to WAV, returning original blob:', error);
      return audioBlob;
    }
  }

  private audioBufferToWav(audioBuffer: AudioBuffer): Blob {
    const numChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = audioBuffer.length * blockAlign;
    const bufferSize = 44 + dataSize;

    const buffer = new ArrayBuffer(bufferSize);
    const view = new DataView(buffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, bufferSize - 8, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    // Convert audio data
    let offset = 44;
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  }

  stopStream(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }
    
    if (this.combinedStream) {
      this.combinedStream.getTracks().forEach(track => track.stop());
    }
    
    this.cleanup();
    
    this.microphoneStream = null as any;
    this.systemAudioStream = null as any;
    this.combinedStream = null as any;
    this.mediaRecorder = null as any;
    this.recordedChunks = [];
    this.gainNode = null as any;
    this.micGainNode = null as any;
    this.systemGainNode = null as any;
    this.destination = null as any;
  }

  uploadAudio(details: any): Observable<any> {
    console.log("Uploading audio...");
    return this.http.post<any>(
      environment.BASE_URL + AudioService.UPLOAD_AUDIO,
      details
    );
  }
}
