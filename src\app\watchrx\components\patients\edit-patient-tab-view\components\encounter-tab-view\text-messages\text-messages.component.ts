import { CommonModule, formatDate } from '@angular/common';
import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { ToastModule } from 'primeng/toast';
import { Program } from '../../../../../../api/patientEncounter';
import { ScheduleMessage } from '../../../../../../api/scheduleTextMessageResp';
import { ViewMessage } from '../../../../../../api/textMessageResponse';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { convertToMins, convertToMins1 } from '../../../../../../utils/utils';
import { ScheduleTextMessageFormComponent } from '../../../../../shared/schedule-text-message-form/schedule-text-message-form.component';
import { TextMessageService } from './service/text-message.service';
import { SidebarModule } from 'primeng/sidebar';
import { CommonService } from '../../../../../../service/common.service';


interface ChatMessage {
  message: string;
  time: string;
  type: 'server' | 'mobile';
}

interface ChatResponse {
  time: string;
  data: ChatMessage[];
}

@Component({
  selector: 'app-text-messages',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TabViewModule,
    CalendarModule,
    DropdownModule,
    FormsModule,
    TableModule,
    DialogModule,
    RadioButtonModule,
    ReactiveFormsModule,
    CheckboxModule,
    ScheduleTextMessageFormComponent,
    ConfirmDialogModule,
    ToastModule,
    RadioButtonModule,
    SidebarModule
  ],
  templateUrl: './text-messages.component.html',
  styleUrl: './text-messages.component.scss',
  providers: [ConfirmationService, MessageService],
})
export class TextMessagesComponent implements OnInit {
  activeIndex: number = 0;
  patientId: number = -1;
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();

  @ViewChild(ScheduleTextMessageFormComponent)
  textMessageForm!: ScheduleTextMessageFormComponent;

  scheduleTextMessageList: ScheduleMessage[] = [];
  totalScheduleTextMessageCount: number = 0;

  viewTextMessageList: ViewMessage[] = [];
  totalMessageCount: number = 0;
  visible: boolean = false;
  programList: Program[] = [];
  loader: boolean = false;
  title: string = 'Add Scheduled Message'
  @ViewChild('chatContainer') chatContainer!: ElementRef;
  interval:any
  constructor(
    private confirmationService: ConfirmationService,
    private textMessageService: TextMessageService,
    private loaderService: LoaderService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private commonService:CommonService
  ) { }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.chatHistory = [];
    this.checkTab();
  
  }

  loadTextMessageList($event: LazyLoadEvent | TableLazyLoadEvent) {
    console.log('loadTextMessageList', $event);
    this.loaderService.show();
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;
    let payload = this.patientId + '/' + pageNo + '/' + pageSize;
    this.textMessageService.getTextMessages(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.status) {
        this.dataEmitter.emit(res?.minsDetails);
        this.totalMessageCount = res?.resultCount;
        this.viewTextMessageList = res?.viewMessages;
        this.programList = res?.programs;
      }
    });
  }

  loadScheduleTextMessageList($event: LazyLoadEvent | TableLazyLoadEvent) {
    this.loaderService.show();
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;
    let payload = this.patientId + '/' + pageNo + '/' + pageSize;
    this.textMessageService
      .getScheduleTextMessages(payload)
      .subscribe((res) => {
        this.loaderService.hide();
        if (res.success) {
          this.dataEmitter.emit(res?.minsDetails);
          this.totalScheduleTextMessageCount = res?.count;
          this.scheduleTextMessageList = res?.eList;
          this.scheduleTextMessageList.forEach(d => {
            d.timeSlotsArrary.forEach((e, i) => {
              const [hours, minutes] = e.split(':');
              const date = new Date();
              date.setHours(+hours, +minutes); // Convert to Date object
              d.timeSlotsArrary[i] = date.toLocaleString('en-US', {
                hour: 'numeric',
                minute: 'numeric',
                hour12: true
              });
            })
          })
          this.programList = res?.programs;
        }
      });
  }

  isShowSMS:boolean=true
  showDialog(messageType:string) {
    if(messageType=='schmessage')
    {
      this.title = "Add Scheduled Message"
      this.visible = true;
      this.textMessageForm?.resetFileds();
      this.isShowSMS=false;
    }
    else{
      this.title = "Add Text Message"
      this.visible = true;
      this.textMessageForm?.resetFileds();
      this.isShowSMS=true;
    }
 
  }

  onCancelModal() {
    this.visible = false;
  }

  editScheduleTextMessage(item: ScheduleMessage) {
    this.title = "Edit Scheduled Message"
    this.visible = true;
    console.log(item);
    this.textMessageForm?.editScheduleTextMessage(item);
  }

  onSubmitData() {
    let selectedProgram = this.textMessageForm.selectedProgram;
    if (!selectedProgram) {
      this.generateErrorMessage('One program should be selected');
      return;
    }
    let messageDescription = this.textMessageForm.messageDescription;
    if (!messageDescription) {
      this.generateErrorMessage('Please enter message');
      return;
    }
    let phoneNumber = this.textMessageForm.phoneNumber;
    if (!phoneNumber && this.textMessageForm?.messageType == "SMS") {
      this.generateErrorMessage('Please enter phonenumber');
      return;
    }
    let messageType = this.textMessageForm?.messageType;
    let ansSelected = this.textMessageForm?.isChecked;
    let frequencyType = this.textMessageForm?.frequencyType;

    let opt1 = this.textMessageForm.option1;
    let opt2 = this.textMessageForm.option2;
    let opt3 = this.textMessageForm.option3;



    let ans: any[] = [];

    if (ansSelected) {
      if (opt1 && opt1 !== '') {
        ans.push(opt1);
      } else {
        this.generateErrorMessage('Option 1 needed');
        return;
      }

      if (opt2 && opt2 !== '') {
        ans.push(opt2);
      } else {
        this.generateErrorMessage('Option 2 needed');
        return;
      }

      if (opt3 && opt3 !== '') {
        ans.push(opt3);
      }
    }
    let userName = '';
    let logUser = localStorage.getItem('user');
    if (logUser) {
      userName = JSON.parse(logUser)['firstName'];
    }
    let requestBody = {};
    this.loader = true;
    if ((messageType && messageType === 'Now') || messageType === 'SMS') {
    if(messageType === 'SMS')
    {
      requestBody = {
        patientIds: [this.patientId],
        deliveryTime: '',
        question: messageDescription,
        senderName: userName,
        timeZone: '',
        nowOrSchedule: 'Now',
        answers: ans,
        phoneNumber: phoneNumber,
        review: selectedProgram,
        sms:
          messageType === 'SMS'
            ? '**DO NOT REPLY To this message, For emergency please dial 911'
            : '',
      };
    }
    else
    {
      requestBody = {
        patientIds: [this.patientId],
        deliveryTime: '',
        question: messageDescription,
        senderName: userName,
        timeZone: '',
        nowOrSchedule: 'Now',
        answers: ans,
        phoneNumber: phoneNumber,
        review: selectedProgram,
      };
    }
     
      this.textMessageService.sendMessage(requestBody).subscribe((res) => {
        this.loader = false;
        this.visible = false;
        if (res.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: 'Message sent successfully.',
            life: 3000,
          });
          this.loadMessagesByActiveIndex();
          this.textMessageForm.resetFileds();
          this.commonService.updateData(true);
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail: 'Failed to send message',
            life: 3000,
          });
          return;
        }
      });
    } 
    else if (messageType === 'Schedule') {
      if (frequencyType === 'One Day') {
        let dayWeek = 'Su|Mo|Tu|We|Th|Fr|Sa';
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.oneDayStartDate) {
          startDate = formatDate(
            this.textMessageForm?.oneDayStartDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
          endDate = formatDate(
            this.textMessageForm?.oneDayStartDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.oneDayStartTime) {
          timeSlot = formatDate(
            this.textMessageForm?.oneDayStartTime,
            'HH:mm',
            'en-US'
          );
        }
        requestBody = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          patientId: this.patientId,
          question: messageDescription,
          senderName: userName,
          answer: ans,
          timeSlots: timeSlot,
          deliveryTime: startDate,
          timeZone: '',
          startDate: startDate,
          endDate: endDate,
          dayOfWeek: dayWeek,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(requestBody)
          .subscribe((res) => {
            this.loader = false;
            this.visible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
              this.loadMessagesByActiveIndex();
              this.textMessageForm.resetFileds();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      } else if (frequencyType === 'Daily') {
        let dayWeek = 'Su|Mo|Tu|We|Th|Fr|Sa';
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.startDate) {
          startDate = formatDate(
            this.textMessageForm?.startDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
        }
        if (this.textMessageForm?.endDate) {
          endDate = formatDate(
            this.textMessageForm?.endDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.time) {
          timeSlot = formatDate(this.textMessageForm?.time, 'HH:mm', 'en-US');
        }
        requestBody = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          patientId: this.patientId,
          question: messageDescription,
          senderName: userName,
          answer: ans,
          timeSlots: timeSlot,
          deliveryTime: startDate,
          timeZone: '',
          startDate: startDate,
          endDate: endDate,
          dayOfWeek: dayWeek,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(requestBody)
          .subscribe((res) => {
            this.loader = false;
            this.visible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
              this.loadMessagesByActiveIndex();
              this.textMessageForm.resetFileds();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      } else {
        let startDate = formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US');
        let endDate = formatDate(new Date(), 'yyyy-MM-dd 23:59:59', 'en-US');
        if (this.textMessageForm?.startDate) {
          startDate = formatDate(
            this.textMessageForm?.startDate,
            'yyyy-MM-dd HH:mm:ss',
            'en-US'
          );
        }
        if (this.textMessageForm?.endDate) {
          endDate = formatDate(
            this.textMessageForm?.endDate,
            'yyyy-MM-dd 23:59:59',
            'en-US'
          );
        }
        let timeSlot = formatDate(new Date(), 'HH:mm', 'en-US');
        if (this.textMessageForm?.time) {
          timeSlot = formatDate(this.textMessageForm?.time, 'HH:mm', 'en-US');
        }
        let dayofweek: any[] = [];
        let days = this.textMessageForm?.days;
        console.log(days, 'days......');
        if (days?.length === 0) {
          this.generateErrorMessage('Please select atleast one day');
        }

        let dayAbbreviations: { [key: string]: string } = {
          Sunday: 'Su',
          Monday: 'Mo',
          Tuesday: 'Tu',
          Wednesday: 'We',
          Thursday: 'Th',
          Friday: 'Fr',
          Saturday: 'Sa',
        };

        let daysString = days.map((day) => dayAbbreviations[day]).join('|');

        requestBody = {
          scheduledTextMessagesId:
            this.textMessageForm?.scheduledTextMessagesId,
          question: messageDescription,
          answer: ans,
          senderName: userName,
          createdDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          updatedDate: formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss', 'en-US'),
          dayOfWeek: daysString,
          timeSlots: timeSlot,
          patientId: this.patientId,
          startDate: startDate,
          endDate: endDate,
          review: selectedProgram,
        };
        this.textMessageService
          .createScheduleMessage(requestBody)
          .subscribe((res) => {
            this.loader = false;
            this.visible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
              this.loadMessagesByActiveIndex();
              this.textMessageForm.resetFileds();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to schedule message',
                life: 3000,
              });
              return;
            }
          });
      }
    }
  }

  loadMessagesByActiveIndex() {
    if (this.activeIndex === 0) {
      this.loadTextMessageList({ first: 0, rows: 25 });
    } else {
      this.loadScheduleTextMessageList({ first: 0, rows: 25 });
    }
  }

  generateErrorMessage(text: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: text,
      life: 3000,
    });
  }

  formattedTime(val: number): string {
    return convertToMins1(val);
  }

  deleteMessage(event: Event, id: number) {
    console.log('Delete clicked');
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.textMessageService
          .deletScheduleMessage({ scheduledTextMessagesId: id })
          .subscribe((res) => {
            this.loader = false;
            this.visible = false;
            if (res.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Schedule message created successfully.',
                life: 3000,
              });
              this.loadMessagesByActiveIndex();
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Failed to delete message',
                life: 3000,
              });
              return;
            }
          });
      },
      reject: () => { },
    });
  }

  getDays(data: any) {

    if (data.recurrence == "weekly") {
      let daysArray = data.dayOfWeek.split('|');
      let fullDaysArray = daysArray.map((day: any) => {
        switch (day.toLowerCase()) {
          case 'su':
            return 'Sun';
          case 'mo':
            return 'Mon';
          case 'tu':
            return 'Tue';
          case 'we':
            return 'Wed';
          case 'th':
            return 'Thu';
          case 'fr':
            return 'Fri';
          case 'sa':
            return 'Sat';
          default:
            return day;
        }
      });
      let days = fullDaysArray.join(',');
      return days;
    }
    else {
      return data.recurrence
    }
  }

  chatHistory: ChatResponse[] = [];
  newMessage: string = '';
  selectedChatProgram: string = '';

  sendMessage() {
    if (this.selectedChatProgram == '') {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Please select program',
        life: 3000,
      });
      return;
    }
    if (this.newMessage.trim()) {
      let obj = { "patientId": this.patientId, "chatText": this.newMessage.trim(), "isSenderServer": true, "isSenderApp": false, 'program':this.selectedChatProgram }
      this.textMessageService.saveChat(obj).subscribe(res => {
        this.getData();
        this.commonService.updateData(true)
      },err=>{
        this.getData()
      });
    }
    this.newMessage=''
  }
  getData() {
    let obj = { "patientId": this.patientId }
    this.textMessageService.getChatData(obj).subscribe(res => {
      this.chatHistory = res;
      this.chatHistory.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());
      setTimeout(() => this.scrollToBottom(), 100); // Wait a bit and scroll
    });
  }

  scrollToBottom() {
    setTimeout(() => {
      if (this.chatContainer) {
        this.chatContainer.nativeElement.scrollTop = this.chatContainer.nativeElement.scrollHeight;
      }
    }, 100);
  }
 
  checkTab()
  {
    
    if(this.activeIndex==2)
    {
      this.interval = setInterval(()=>{
        this.getData()
      },3000)
      this.getData();
    }
    else
    {
      clearInterval(this.interval)
      this.interval=null
    }
  }

  ngOnDestroy() {
    clearInterval(this.interval)// Stop timer when component is destroyed
  }

}
