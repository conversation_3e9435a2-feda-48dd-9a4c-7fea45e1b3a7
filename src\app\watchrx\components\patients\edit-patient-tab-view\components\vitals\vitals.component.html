<div class="p-fluid">
  <p-toast />
  <p-confirmDialog />
  <div class="flex flex-column md:flex-row align-items-center gap-3 topfilter">
    <div class="col-12 md:col-7 flex flex-row">
      <div class="flex flex-column col-5">
        <label class="mb-1 text-600 font-bold">Vital</label>
        <p-dropdown [options]="vitalList" placeholder="Select Vital" [(ngModel)]="selectedVitalType" optionLabel="name"
          (onChange)="vitalChanged()"></p-dropdown>
      </div>
      <div class="flex flex-row align-items-center col-7 mt-2">
        <div class="mt-2  flex">
          <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="vitalStatus"
              (onChange)="updateVitalStatus(vitalStatus)" pTooltip="Toggle to {{ vitalStatus ? 'Disable' : 'Enable' }}"
              tooltipPosition="top" />
          </span>
        </div>
        <div class="ml-1 text-600 font-bold">
          {{ vitalStatus ? "Vital Status [Enabled]" : "Vital Status [Disabled]" }}
        </div>
      </div>
    </div>
    <div class="col-12 md:col-5" style="display: flex; justify-content: flex-end; padding-right: 25px;">
      <div class="flex flex-column md:justify-content-start">
        <label class="mb-1 text-600 font-bold">Collect Vital</label>
        <div class="flex flex-row gap-3">
          <p-button label="Now" severity="secondary" [outlined]="true" class="p-button-sm" pTooltip="Collect now"
            tooltipPosition="top" (onClick)="collectNow($event)" [disabled]="!vitalStatus" />
          <p-button label="Schedule" severity="secondary" [outlined]="true" class="p-button-sm" pTooltip="Schedule"
            tooltipPosition="top" [disabled]="!vitalStatus" (onClick)="openSchedulePopup()" />
          <p-button label="Manual" severity="secondary" [outlined]="true" class="p-button-sm" pTooltip="Add Manual"
            tooltipPosition="top" (onClick)="openManulaDataPopup()"
            [disabled]="!manualDataRequiredVitals.includes(selectedVitalType?.name) || !vitalStatus" />
          <p-button icon="pi pi-download" severity="primary" [outlined]="true" label="Excel" iconPos="right"
            (onClick)="exportToXlsx()"></p-button>
        </div>
      </div>
    </div>
    <!-- <p-calendar
      [(ngModel)]="startDate"
      [showIcon]="true"
      inputId="buttondisplay"
      [showOnFocus]="false"
      placeholder="Start Date"
      class="col-3"
    />
    <p-calendar
      [(ngModel)]="endDate"
      [showIcon]="true"
      inputId="buttondisplay"
      [showOnFocus]="false"
      placeholder="End Date"
      class="col-3"
    /> -->

  </div>
  <div *ngIf="selectedVitalType != ''">

    <div class=" col-12 flex flex-row md:justify-content-between pb-3 pt-3" style="border-bottom:1px solid #E8E8E8">
      <div class="flex flex-row md:justify-content-start time">
        <button pButton (click)="prevClick()" class="center-button" tooltipPosition="top"
          pTooltip="Previous {{ weeklyEnabled ? 'Week' : 'Day' }}" style="width:45px">
          <i class="pi pi-angle-double-left"></i> </button>
        <strong class="flex flex-column md:flex-row md:align-items-center md:justify-content-between">
          <div *ngIf="weeklyEnabled">
            <button pButton type="button" class="center-button">
              <span><strong>{{ formatDate(startOfWeek) }} to
                  {{ formatDate(endOfWeek) }}</strong></span>
            </button>
          </div>
          <div *ngIf="dailyEnabled">
            <button pButton type="button" class="center-button">
              <span><strong> {{ currentDay }} </strong></span>
            </button>
          </div>
          <div *ngIf="monthlyEnabled">
            <button pButton type="button" class="center-button">
              <span><strong>{{ currentMonth }}</strong></span>
            </button>
          </div>
        </strong>
        <button pButton (click)="nextClick()" class="ml-2 p-button-sm" pTooltip="Next" tooltipPosition="top"
          pTooltip="Next {{ weeklyEnabled ? 'Week' : 'Day' }}" [disabled]="dailyEnabled ? isFutureDate() : false"
          class="center-button" style="width:45px"> <i class="pi pi-angle-double-right"></i> </button>
      </div>
      <div class="flex flex-row gap-2">
        <div class="flex flex-row md:justify-content-end">
          <p-dropdown [options]="filteredTypes" optionLabel="name" optionValue="value" [(ngModel)]="selectedType"
            placeholder="Select Type" class="mr-2" (onChange)="graphTypeClick($event.value)" />
          <!-- <p-button label="Daily" severity="secondary" [outlined]="!dailyEnabled" (onClick)="graphTypeClick('d')"
            class="p-button-sm daily" pTooltip="Daily Report" tooltipPosition="top" />
          <p-button label="Weekly" severity="secondary" [outlined]="!weeklyEnabled" (onClick)="graphTypeClick('w')"
            class="p-button-sm week" pTooltip="Weekly Report" tooltipPosition="top" />
          <p-button label="Monthly" severity="secondary" [outlined]="!monthlyEnabled" (onClick)="graphTypeClick('m')"
            class="p-button-sm month" pTooltip="Monthly Report" tooltipPosition="top" /> -->
        </div>

        <div class="flex flex-row md:justify-content-end">
          <p-button label="Grid View" severity="secondary" [outlined]="!gridView" (onClick)="viewFormat('g')"
            class="p-button-sm daily" pTooltip="Grid View" tooltipPosition="top" />
          <p-button label="Table View" severity="secondary" [outlined]="!tableView" (onClick)="viewFormat('t')"
            class="p-button-sm month" pTooltip="Table View" tooltipPosition="top" />
        </div>
      </div>
    </div>
    <div class="col-12 flex flex-column " style="border-bottom:1px solid #E8E8E8">
      <div class="mb-2">
        <!-- <div class="flex justify-content-between">
          <div class="mt-1 text-900 font-bold">
            <h4>{{ selectedVitalType?.name }}</h4>
          </div>
          <div class="mt-2 md:mt-2 flex">
            <button pButton pRipple icon="pi pi-pencil" class="p-button-rounded p-button-info"
              style="height: 25px; width: 25px" pTooltip="Edit Threshold" tooltipPosition="top"
              (click)="openEditThresholdModal()" [disabled]="
                  editThresholdNotReqList.includes(selectedVitalType?.name)
                "></button>
          </div>
        </div> -->
        <div class="body">
          <div *ngIf="selectedVitalType?.name == 'Blood Pressure'" class="flex flex-row w-full">
            <div class="w-50">
              <h6 style="border-bottom:1px solid #E8E8E8"
                class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
                <span class="flex"><img src="assets/watchrx/svg/blue-circle.svg" alt="Systolic" width="16"
                    height="16" /> Systolic
                  Blood Pressure(HG)</span>
                <button pButton icon="pi pi-pencil" class="mr-2 p-button-secondary p-button-outlined"
                  style="height: 25px; width: 25px;" pTooltip="Edit Threshold" tooltipPosition="top"
                  (click)="openEditThresholdModal()" [disabled]="
                    editThresholdNotReqList.includes(selectedVitalType?.name)
                  " *ngIf="userRole.roleType!==3"></button>
              </h6>
              <div class="custom-slider" *ngIf="!editThresholdNotReqList.includes(selectedVitalType?.name)">
                <ngx-slider [value]="sliderMinValueOne" [highValue]="sliderMaxValueOne" [options]="sbpOptions">
                </ngx-slider>
              </div>
              <!-- <div class="flex md:justify-content-between mt-2">
                <strong>
                  Critical Max:
                  <span class="text-red-500 font-medium">{{
                    criticalMaxValueOne
                    }}</span>
                </strong>
                <strong>
                  Critical Min:
                  <span class="text-red-500 font-medium">
                    {{ criticalMiniValueOne }}</span>
                </strong>
              </div> -->
              <!-- <div class="flex md:justify-content-between mt-2">
                <strong>
                  Maximum:
                  <span class="text-orange-500 font-medium">{{
                    maxValueOne
                    }}</span>
                </strong>
                <strong>
                  Minimum:
                  <span class="text-orange-500 font-medium">
                    {{ minValueOne }}</span>
                </strong>
              </div> -->
            </div>
            <hr />
            <div class="w-50">
              <h6 style="border-bottom:1px solid #E8E8E8"
                class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
                <span class="flex"><img src="assets/watchrx/svg/orange-circle.svg" alt="Diastolic" width="16"
                    height="16" />
                  Diastolic Blood Pressure(HG)</span>
                <button pButton icon="pi pi-pencil" class=" mr-2 p-button-secondary p-button-outlined"
                  style="height: 25px; width: 25px; float:right" pTooltip="Edit Threshold" tooltipPosition="top"
                  (click)="openEditThresholdModal()" [disabled]="
                    editThresholdNotReqList.includes(selectedVitalType?.name)
                  " *ngIf="userRole.roleType!==3"></button>
              </h6>

              <div class="custom-slider" *ngIf="!editThresholdNotReqList.includes(selectedVitalType?.name)">
                <ngx-slider [value]="sliderMinValueTwo" [highValue]="sliderMaxValueTwo" [options]="dbpOptions">

                </ngx-slider>
              </div>
              <!-- <div class="flex md:justify-content-between mt-2">
                <strong>
                  Critical Max:
                  <span class="text-red-500 font-medium">{{
                    criticalMaxValueTwo
                    }}</span>
                </strong>
                <strong>
                  Critical Min:
                  <span class="text-red-500 font-medium">
                    {{ criticalMiniValueTwo }}</span>
                </strong>
              </div>
              <div class="flex md:justify-content-between mt-2">
                <strong>
                  Maximum:
                  <span class="text-orange-500 font-medium">{{
                    maxValueTwo
                    }}</span>
                </strong>
                <strong>
                  Minimum:
                  <span class="text-orange-500 font-medium">
                    {{ minValueTwo }}</span>
                </strong>
              </div> -->
            </div>
          </div>
          <div *ngIf="
                selectedVitalType?.name !== 'Blood Pressure' &&
                selectedVitalType?.name !== 'Pedometer'
              ">
            <h6 style="border-bottom:1px solid #E8E8E8" *ngIf="selectedVitalType?.name == 'Blood Sugar'"
              class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
              <span class="flex"><img src="assets/watchrx/svg/orange-circle.svg" alt="Diastolic" width="16"
                  height="16" />
                Fasting Blood Sugar</span>
              <span class="flex"><img src="assets/watchrx/svg/blue-circle.svg" alt="Systolic" width="16" height="16" />
                Random Blood Sugar</span>
              <button pButton icon="pi pi-pencil" class=" mr-2 p-button-secondary p-button-outlined"
                style="height: 25px; width: 25px; float:right" pTooltip="Edit Threshold" tooltipPosition="top"
                (click)="openEditThresholdModal()" [disabled]="
                    editThresholdNotReqList.includes(selectedVitalType?.name)
                  " *ngIf="userRole.roleType!==3"></button>
            </h6>
            <h6 style="border-bottom:1px solid #E8E8E8" *ngIf="selectedVitalType?.name !== 'Blood Sugar'"
              class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
              <span class="flex"><img src="assets/watchrx/svg/blue-circle.svg" alt="Systolic" width="16" height="16" />
                {{selectedVitalType?.name}}</span>
              <button pButton icon="pi pi-pencil" class=" mr-2 p-button-secondary p-button-outlined"
                style="height: 25px; width: 25px; float:right" pTooltip="Edit Threshold" tooltipPosition="top"
                (click)="openEditThresholdModal()" [disabled]="
                    editThresholdNotReqList.includes(selectedVitalType?.name)
                  " *ngIf="userRole.roleType!==3"></button>
            </h6>
            <div class="custom-slider" *ngIf="!editThresholdNotReqList.includes(selectedVitalType?.name)">
              <ngx-slider [value]="sliderMinValueOne" [highValue]="sliderMaxValueOne" [options]="sbpOptions">
              </ngx-slider>
            </div>
            <!-- <div class="flex md:justify-content-between mt-2">
              <strong>
                Critical Max:
                <span class="text-red-500 font-medium">{{
                  criticalMaxValueOne
                  }}</span>
              </strong>
              <strong>
                Critical Min:
                <span class="text-red-500 font-medium">
                  {{ criticalMiniValueOne }}</span>
              </strong>
            </div>
            <div class="flex md:justify-content-between mt-2">
              <strong>
                Maximum:
                <span class="text-orange-500 font-medium">{{
                  maxValueOne
                  }}</span>
              </strong>
              <strong>
                Minimum:
                <span class="text-orange-500 font-medium">
                  {{ minValueOne }}</span>
              </strong>
            </div> -->
          </div>
          <div *ngIf="selectedVitalType?.name === 'Pedometer'">
            <h6 style="border-bottom:1px solid #E8E8E8" *ngIf="selectedVitalType?.name !== 'Blood Sugar'"
              class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
              <span class="flex"><img src="assets/watchrx/svg/blue-circle.svg" alt="Systolic" width="16" height="16" />
                {{selectedVitalType?.name}}</span>
              <button pButton icon="pi pi-pencil" class=" mr-2 p-button-secondary p-button-outlined"
                style="height: 25px; width: 25px; float:right" pTooltip="Edit Threshold" tooltipPosition="top"
                (click)="openEditThresholdModal()" [disabled]="
                  editThresholdNotReqList.includes(selectedVitalType?.name)
                " *ngIf="userRole.roleType!==3"></button>
            </h6>
            <div class="custom-slider" *ngIf="!editThresholdNotReqList.includes(selectedVitalType?.name)">
              <ngx-slider [value]="sliderMinValueOne" [highValue]="sliderMaxValueOne" [options]="sbpOptions">
              </ngx-slider>
            </div>
            <!-- <div class="flex md:justify-content-between mt-2">
              <strong>
                Critical Max:
                <span class="text-red-500 font-medium">{{
                  criticalMaxValueOne
                  }}</span>
              </strong>
              <strong>
                Critical Min:
                <span class="text-red-500 font-medium">
                  {{ criticalMiniValueOne }}</span>
              </strong>
            </div>
            <div class="flex md:justify-content-between mt-2">
              <strong>
                Maximum:
                <span class="text-orange-500 font-medium">{{
                  maxValueOne
                  }}</span>
              </strong>
              <strong>
                Minimum:
                <span class="text-orange-500 font-medium">
                  {{ minValueOne }}</span>
              </strong>
            </div> -->
          </div>
        </div>
      </div>
      <!-- <p-card header="Vital Status" [style]="{
            border: '1x solid black',
            borderRadius: '15px',
            width: 'auto',
            color: 'black',
            background:
              'linear-gradient(0deg, rgba(135, 206, 235, 0.5), rgba(135, 206, 235, 0.5)), linear-gradient(92.54deg, #87CEEB 47.88%, #FFFFFF 100.01%)'
          }">
          <div class="flex flex-row md:justify-content-between">
            <div class="mt-1 text-600 font-bold">
              {{ vitalStatus ? " [Enabled]" : "[Disabled]" }}
            </div>
            <div class="mt-2 md:mt-2 flex">
              <span class="text-black-500 ml-10 font-medium"><p-inputSwitch [(ngModel)]="vitalStatus"
                  (onChange)="updateVitalStatus(vitalStatus)"
                  pTooltip="Toggle to {{ vitalStatus ? 'Disable' : 'Enable' }}" tooltipPosition="top" />
              </span>
            </div>
          </div>
          <div class="flex flex-column md:justify-content-start">
            <div class="mt-1 text-600 font-bold">{{ "Collect Vital" }}</div>
            <div class="mt-2 md:mt-2 flex">
              <div class="flex flex-wrap gap-3">
                <div class="flex align-items-center">
                  <p-button label="Now" [raised]="true" severity="danger" [outlined]="true" class="p-button-sm"
                    [rounded]="true" pTooltip="Collect now" tooltipPosition="top" (onClick)="collectNow($event)"
                    [disabled]="!vitalStatus" />
                </div>

                <div class="flex align-items-center">
                  <p-button label="Schedule" [raised]="true" severity="danger" [outlined]="true" class="p-button-sm"
                    [rounded]="true" pTooltip="Schedule" tooltipPosition="top" [disabled]="!vitalStatus"
                    (onClick)="openSchedulePopup()" />
                </div>
                <div class="flex align-items-center">
                  <p-button label="Manual" [raised]="true" severity="danger" [outlined]="true" class="p-button-sm"
                    [rounded]="true" pTooltip="Add Manual" tooltipPosition="top" (onClick)="openManulaDataPopup()"
                    [disabled]="
                      !manualDataRequiredVitals.includes(
                        selectedVitalType?.name
                      ) || !vitalStatus
                    " />
                </div>
              </div>
            </div>
          </div>
        </p-card> -->
    </div>
    <div class="col-12" *ngIf="gridView">
      <div class="flex flex-row md:justify-content-start mb-2">
        <strong>No of days data collection: {{ noOfDaysCollected }}</strong>
      </div>
      <div class="p-3">
        <p-chart [type]="dailyEnabled?'bar':'line'" [data]="data" [options]="options" />
      </div>
    </div>

    <div class="flex col-12" *ngIf="tableView">
      <div class="col-12">
        <div class="card p-0" *ngIf="selectedVitalType?.name === 'Blood Pressure'">
          <p-table [value]="vitalReadingsData" [tableStyle]="{ 'min-width': '40rem' }" [paginator]="true" [rows]="25"
            [responsiveLayout]="'scroll'" [totalRecords]="totalReadings" [lazy]="true"
            [rowsPerPageOptions]="[15,25, 50,75, 100]" styleClass="p-datatable-gridlines p-datatable-striped">
            <ng-template pTemplate="header">
              <tr>
                <th>Systolic</th>
                <th>Diastolic</th>
                <th>Configuration</th>
                <th>Measurement Date</th>
                <th>Action</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-vital>
              <tr>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Systolic Blood Pressure'
                  ">
                  {{ vital.vitalTypeValueVOList[0]?.vitalValue }}
                  mmHg
                </td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Diastolic Blood Pressure'
                  ">
                  {{ vital.vitalTypeValueVOList[1]?.vitalValue }}
                  mmHg
                </td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[1]?.vitalTypeName ==
                    'Systolic Blood Pressure'
                  ">
                  {{ vital.vitalTypeValueVOList[0]?.vitalValue }}
                  mmHg
                </td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[1]?.vitalTypeName ==
                    'Diastolic Blood Pressure'
                  ">
                  {{ vital.vitalTypeValueVOList[1]?.vitalValue }}
                  mmHg
                </td>
                <td>{{ vital?.configuration }}</td>
                <td>{{ getConvertedDate(vital?.measuredDateTime) }}</td>
                <td>
                  <div class="flex flex-wrap gap-2">
                    <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary"
                      style="height: 30px; width: 30px" (click)="openEditVitalReadingPopup(vital)"
                      *ngIf="vital?.configuration === 'Manual'"></button>
                    <button pButton pRipple icon="pi pi-trash" class="p-button-outlined  p-button-secondary"
                      style="height: 30px; width: 30px" (click)="deleteVitalReadings($event, vital)"></button>
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
        <div class="card p-0" *ngIf="selectedVitalType?.name === 'Blood Sugar'">
          <p-table [value]="vitalReadingsData" [tableStyle]="{ 'min-width': '40rem' }" [paginator]="true" [rows]="25"
            [responsiveLayout]="'scroll'" [totalRecords]="totalReadings" [lazy]="true"
            [rowsPerPageOptions]="[15,25, 50,75, 100]" styleClass="p-datatable-gridlines p-datatable-striped">
            <ng-template pTemplate="header">
              <tr>
                <th>Fasting</th>
                <th>Random</th>
                <th>Configuration</th>
                <th>Measurement Date</th>
                <th>Action</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-vital>
              <tr>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Fasting Blood Sugar'
                  ">
                  {{ vital.vitalTypeValueVOList[0]?.vitalValue }}
                  mg/dl
                </td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Fasting Blood Sugar'
                  "></td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Random Blood Sugar'
                  "></td>
                <td *ngIf="
                    vital.vitalTypeValueVOList[0]?.vitalTypeName ==
                    'Random Blood Sugar'
                  ">
                  {{ vital.vitalTypeValueVOList[0]?.vitalValue }}
                  mg/dl
                </td>
                <td>{{ vital?.configuration }}</td>
                <td>{{ getConvertedDate(vital?.measuredDateTime) }}</td>
                <td>
                  <div class="flex flex-wrap gap-2">
                    <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary"
                      style="height: 30px; width: 30px" (click)="openEditVitalReadingPopup(vital)"
                      *ngIf="vital?.configuration === 'Manual'"></button>
                    <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                      style="height: 30px; width: 30px" (click)="deleteVitalReadings($event, vital)"></button>
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>

        <div class="card p-0" *ngIf="singleReadingsVital.includes(selectedVitalType?.name)">
          <p-table [value]="vitalReadingsData" [tableStyle]="{ 'min-width': '40rem' }" [paginator]="true" [rows]="15"
            [responsiveLayout]="'scroll'" [totalRecords]="totalReadings" [lazy]="true"
            (onLazyLoad)="getVitalReadings($event)" [rowsPerPageOptions]="[15,25, 50,75, 100]"
            styleClass="p-datatable-gridlines p-datatable-striped">
            <ng-template pTemplate="header">
              <tr>
                <th>{{ selectedVitalType?.name }}</th>
                <th>Configuration</th>
                <th>Measurement Date</th>
                <th>Action</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-vital>
              <tr>
                <td>
                  {{ vital.vitalTypeValueVOList[0]?.vitalValue }}
                  {{ getUnitName() }}
                </td>
                <td>{{ vital?.configuration }}</td>
                <td>{{ getConvertedDate(vital?.measuredDateTime) }}</td>
                <td>
                  <div class="flex flex-wrap gap-2">
                    <button pButton pRipple icon="pi pi-pencil" class="p-button-outlined p-button-secondary"
                      style="height: 30px; width: 30px" (click)="openEditVitalReadingPopup(vital)"
                      *ngIf="vital?.configuration === 'Manual'"></button>
                    <button pButton pRipple icon="pi pi-trash" class="p-button-outlined p-button-secondary"
                      style="height: 30px; width: 30px" (click)="deleteVitalReadings($event, vital)"></button>
                  </div>
                </td>
              </tr>
            </ng-template>
          </p-table>
        </div>
        <div class="card p-0" *ngIf="selectedVitalType?.name === 'Pedometer'">
          <p-table [value]="pedometerReadings" [tableStyle]="{ 'min-width': '40rem' }" [paginator]="true" [rows]="10"
            [responsiveLayout]="'scroll'" [totalRecords]="totalReadings" [rows]="10" [lazy]="true"
            styleClass="p-datatable-gridlines p-datatable-striped">
            <ng-template pTemplate="header">
              <tr>
                <th>Date</th>
                <th>Time</th>
                <th>Steps Count</th>
                <th>Distance (KM)</th>
                <th>Distance (Miles)</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-vital>
              <tr>
                <td>{{ vital.createdDate }}</td>
                <td>{{ vital.createdTime }}</td>
                <td>{{ vital.count }}</td>
                <td>{{ vital.distanceInKm }}</td>
                <td>{{ vital.distanceInMiles }}</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
        <div class="card p-0" *ngIf="selectedVitalType?.name === 'Sleep Monitor'">
          <p-table [value]="sleepReadings" [tableStyle]="{ 'min-width': '40rem' }" [paginator]="true" [rows]="10"
            [responsiveLayout]="'scroll'" [totalRecords]="totalReadings" [rows]="10" [lazy]="true"
            styleClass="p-datatable-gridlines p-datatable-striped">
            <ng-template pTemplate="header">
              <tr>
                <th>Date</th>
                <th>Deep Sleep</th>
                <th>Shallow Sleep</th>
                <th>Sleep Time</th>
              </tr>
            </ng-template>
            <ng-template pTemplate="body" let-vital>
              <tr>
                <td>{{ vital.createdDate }}</td>
                <td>{{ vital.deepSleep }}</td>
                <td>{{ vital.shallowSleep }}</td>
                <td>{{ vital.sleepTime }}</td>
              </tr>
            </ng-template>
          </p-table>
        </div>
      </div>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="manulaAddPopup" position="right" styleClass="w-40rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Add manual data for {{ selectedVitalType?.name }}
      </span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid">
    <div class="flex flex-row col-12 mt-2" *ngIf="selectedVitalType?.name === 'Blood Pressure'">
      <div class="col-12 md:col-6">
        <label for="vitalValueOne" class="font-semibold">Systolic Blood Pressure</label>
        <input pInputText id="vitalValueOne" class="flex-auto" [(ngModel)]="vitalValueOne" [required]="true"
          type="number" [min]="0" placeholder="{{ getUnitName() }}" />
      </div>
      <div class="col-12 md:col-6">
        <label for="vitalValueTwo" class="font-semibold">Diastolic Blood Pressure</label>
        <input pInputText id="vitalValueTwo" class="flex-auto" [(ngModel)]="vitalValueTwo" [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
    </div>
    <div class="flex flex-row col-12" *ngIf="selectedVitalType?.name === 'Blood Sugar'">
      <div class="field col-12 md:col-6">
        <p-checkbox [(ngModel)]="isfastingBsChecked" [binary]="true" label="Fasting Blood Sugar"
          (onChange)="onCheckboxChange('fasting')" class="m-1"></p-checkbox>
        <input pInputText id="vitalValueOne" class="flex-auto" [(ngModel)]="vitalValueOne" [required]="true"
          [disabled]="!isfastingBsChecked" placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
      <div class="field col-12 md:col-6">
        <p-checkbox [(ngModel)]="israndomBsChecked" [binary]="true" label="Random Blood Sugar"
          (onChange)="onCheckboxChange('random')" class="m-1"></p-checkbox>
        <input pInputText id="vitalValueTwo" class="flex-auto" [(ngModel)]="vitalValueTwo" [required]="true"
          [disabled]="!israndomBsChecked" placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
    </div>
    <div class="flex flex-row col-12" *ngIf="
        selectedVitalType?.name !== 'Blood Sugar' &&
        selectedVitalType?.name !== 'Blood Pressure'
      ">
      <div class="field col-12 md:col-6">
        <label for="vitalValueOne" class="font-semibold">{{
          selectedVitalType?.name
          }}</label>
        <input pInputText id="vitalValueOne" class="flex-auto" [(ngModel)]="vitalValueOne" [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
    </div>
    <div class="flex flex-row col-12">
      <div class="field col-12 md:col-6">
        <label htmlfor="description" class="font-bold block mb-2"> Date </label>
        <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay" appendTo="body"
          [(ngModel)]="vitalDate" />
      </div>
      <div class="field col-12 md:col-6">
        <label for="calendar-timeonly" class="font-bold block mb-2">
          Time
        </label>
        <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay"
          appendTo="body" [showTime]="true" hourFormat="12" [(ngModel)]="vitalTime">
          <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
            <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
          </ng-template>
        </p-calendar>
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="manulaAddPopup = false" />
      <p-button label="Save" (onClick)="saveManulaData()" />
    </div>
  </ng-template>
</p-sidebar>

<p-sidebar [(visible)]="editThresholdModalVisible" position="right" styleClass="w-40rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Edit Threshold Configs for {{ selectedVitalType?.name }}
      </span>
    </div>
  </ng-template>
  <div class="p-fluid p-formgrid grid mt-2">
    <div class="flex flex-row col-12" *ngIf="selectedVitalType?.name === 'Blood Pressure'">
      <h6 class="mb-0 mt-2">Systolic Blood Pressure(HG)</h6>
    </div>
    <div class="flex flex-row col-12 p-0">
      <div class="col-12 md:col-3">
        <label for="criticalMiniValueOne" class="font-semibold">Critical Minimum</label>
        <input pInputText id="criticalMiniValueOne" class="flex-auto" [(ngModel)]="criticalMiniValueOne"
          [required]="true" type="number" [min]="0" placeholder="{{ getUnitName() }}" />
      </div>
      <div class="col-12 md:col-3">
        <label for="minValueOne" class="font-semibold">Minimum</label>
        <input pInputText id="minValueOne" class="flex-auto" [(ngModel)]="minValueOne" [required]="true" type="number"
          [min]="0" placeholder="{{ getUnitName() }}" />
      </div>
      <div class="col-12 md:col-3">
        <label for="maxValueOne" class="font-semibold">Maximum</label>
        <input pInputText id="maxValueOne" class="flex-auto" [(ngModel)]="maxValueOne" [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>

      <div class="col-12 md:col-3">
        <label for="criticalMaxValueOne" class="font-semibold">Critical Maximum</label>
        <input pInputText id="criticalMaxValueOne" class="flex-auto" [(ngModel)]="criticalMaxValueOne" [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
    </div>
    <div class="flex flex-row col-12" *ngIf="selectedVitalType?.name === 'Blood Pressure'">
      <h6 class="mb-0 mt-2">Diastolic Blood Pressure(HG)</h6>
    </div>
    <div class="flex flex-row col-12 p-0" *ngIf="selectedVitalType?.name === 'Blood Pressure'">
      <div class="col-12 md:col-3">
        <label for="criticalMiniValueTwo" class="font-semibold">Critical Minimum</label>
        <input pInputText id="criticalMiniValueTwo" class="flex-auto" [(ngModel)]="criticalMiniValueTwo"
          [required]="true" type="number" [min]="0" placeholder="{{ getUnitName() }}" />
      </div>

      <div class="col-12 md:col-3">
        <label for="minValueTwo" class="font-semibold">Minimum</label>
        <input pInputText id="minValueTwo" class="flex-auto" [(ngModel)]="minValueTwo" 
        [required]="true" type="number"
          [min]="0" placeholder="{{ getUnitName() }}" />
      </div>
      <div class="col-12 md:col-3">
        <label for="maxValueTwo" class="font-semibold">Maximum</label>
        <input pInputText id="maxValueTwo" class="flex-auto" [(ngModel)]="maxValueTwo" 
        [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>

      <div class="col-12 md:col-3">
        <label for="criticalMaxValueTwo" class="font-semibold">Critical Maximum</label>
        <input pInputText id="criticalMaxValueTwo" class="flex-auto" [(ngModel)]="criticalMaxValueTwo" [required]="true"
          placeholder="{{ getUnitName() }}" [min]="0" type="number" />
      </div>
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="editThresholdModalVisible = false" />
      <p-button label="Save" (onClick)="
        selectedVitalType?.name === 'Pedometer'
          ? saveEditedPedometerThreshold()
          : saveEditedThresholdConfig()
      " />
    </div>
  </ng-template>
</p-sidebar>

<p-sidebar [(visible)]="pedometerSchedulePopup" position="right" styleClass="w-40rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Schedule for {{ selectedVitalType?.name }}
      </span>
    </div>
  </ng-template>
  <div class="flex flex-row col-12">
    <div class="field col-12 md:col-12">
      <label for="pedoMeterFrequencyValue" class="font-semibold">Frequency In minutes</label>
      <input pInputText id="pedoMeterFrequencyValue" class="flex-auto" [(ngModel)]="pedoMeterFrequencyValue"
        [required]="true" type="number" [min]="0" placeholder="Frequency In Minutes" />
    </div>
  </div>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="pedometerSchedulePopup = false" />
      <p-button label="Save" (onClick)="updatePedometerStatus(vitalStatus)" />
    </div>
  </ng-template>
</p-sidebar>
<p-sidebar [(visible)]="schedulePopup" position="right" styleClass="w-40rem">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        Schedule for {{ selectedVitalType?.name }}
      </span>
    </div>
  </ng-template>
  <p-tabView [(activeIndex)]="activeIndex" (onChange)="onTabChange($event)">
    <p-tabPanel header="Add">
      <div class="p-fluid p-formgrid grid">
        <div class="col-12 md:col-12 mt-2">
          <label htmlfor="description" class="font-bold block mb-2">
            Schedule Type
          </label>
          <div class="flex flex-wrap gap-3">
            <div class="flex align-items-center">
              <p-radioButton name="message" value="Daily" [(ngModel)]="scheduleType" inputId="daily" />
              <label for="daily" class="ml-2"> Daily </label>
            </div>

            <div class="flex align-items-center">
              <p-radioButton name="message" value="Weekly" [(ngModel)]="scheduleType" inputId="weekly" />
              <label for="weekly" class="ml-2"> Weekly </label>
            </div>
          </div>
        </div>
        <div class="flex flex-row col-12 md:col-12">
          <div class="col-12 md:col-5">
            <label htmlfor="description" class="font-bold block mb-2">
              Start Date
            </label>
            <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay" appendTo="body"
              [(ngModel)]="scheduleStartDate" />
          </div>
          <div class="col-12 md:col-5">
            <label htmlfor="description" class="font-bold block mb-2">
              End Date
            </label>
            <p-calendar [iconDisplay]="'input'" [showIcon]="true" inputId="icondisplay" appendTo="body"
              [(ngModel)]="scheduleEndDate" />
          </div>
        </div>
        <div class="col-12 md:col-12">
          <label htmlfor="description" class="font-bold block mb-2">
            Mode
          </label>
          <div class="flex flex-wrap gap-3">
            <div class="flex align-items-center">
              <p-radioButton name="mode" value="Frequency" [(ngModel)]="scheduleMode" inputId="Frequency" />
              <label for="Frequency" class="ml-2"> Frequency </label>
            </div>

            <div class="flex align-items-center">
              <p-radioButton name="mode" value="Time" [(ngModel)]="scheduleMode" inputId="Time" />
              <label for="Time" class="ml-2"> Time </label>
            </div>
          </div>
        </div>
        <div class="flex flex-row col-12">
          <div class="col-12 md:col-4" *ngIf="scheduleMode === 'Frequency'">
            <label for="frequency" class="font-semibold">Frequency In minutes</label>
            <input pInputText id="frequencyValue" class="flex-auto" [(ngModel)]="frequencyValue" [required]="true"
              type="number" [min]="0" placeholder="Frequency In Minutes" />
          </div>
        </div>
        <div class="flex flex-column col-12" *ngIf="scheduleMode === 'Time'">
          <div class="col-8">
            <div class="flex flex-row justify-content-between">
              <p-calendar [iconDisplay]="'input'" [showIcon]="true" [timeOnly]="true" inputId="templatedisplay"
                appendTo="body" [showTime]="true" hourFormat="12" [(ngModel)]="scheduleTime"
                class="flex-grow-1 gap-2 mr-1">
                <ng-template pTemplate="inputicon" let-clickCallBack="clickCallBack">
                  <i class="pi pi-clock pointer-events-none" (click)="clickCallBack($event)"></i>
                </ng-template>
              </p-calendar>

              <p-button label="Add Time" [raised]="true" severity="success" (click)="addTimeToList()" [outlined]="true"
                class="flex-grow-1 gap-2 mr-1"></p-button>
            </div>
          </div>
          <div class="col-10">
            <p-table class="w-full" responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped"
              [value]="scheduleTimeList">
              <ng-template pTemplate="header">
                <tr>
                  <th>Select Time</th>
                  <th>Action</th>
                </tr>
              </ng-template>

              <ng-template pTemplate="body" let-fixedMeds>
                <tr>
                  <td>
                    <p-calendar [(ngModel)]="fixedMeds.time" [timeOnly]="true" hourFormat="12" [showIcon]="true"
                      [iconDisplay]="'input'" [showButtonBar]="true" appendTo="body" class="w-full"></p-calendar>
                  </td>
                  <td>
                    <p-button icon="pi pi-times" [rounded]="true" [text]="true" severity="danger"
                      (click)="removeTime(fixedMeds)" class="w-full"></p-button>
                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>
        </div>

        <div class="field col-12 md:col-12" *ngIf="scheduleType === 'Weekly'">
          <label htmlfor="state" class="font-bold block mb-2">Choose Days</label>
          <div class="p-field-checkbox font-bold block mb-2">
            <p-checkbox name="allDays" [binary]="true" [(ngModel)]="allDaysSelected" (onChange)="toggleAllDays()"
              label="All Days"></p-checkbox>
          </div>
          <div class="flex justify-content-left gap-3">
            <div *ngFor="let day of daysOfWeek">
              <p-checkbox name="days" [value]="day.value" [(ngModel)]="selectedDays"
                (onChange)="updateAllDaysSelected()" label="{{ day.name }}"></p-checkbox>
            </div>
          </div>
        </div>

        <div class="col-12 md:col-4">
          <label for="configName" class="font-semibold">Configuration Name</label>
          <input pInputText id="configName" class="flex-auto" [(ngModel)]="configName" [required]="true" type="text"
            placeholder="Configuration Name" />
        </div>
      </div>
    </p-tabPanel>
    <p-tabPanel header="View">
      <strong *ngIf="vitalSchedules?.length == 0">No data found!!</strong>
      <p-table [value]="vitalSchedules" [tableStyle]="{ 'min-width': '40rem' }" [responsiveLayout]="'scroll'"
        [lazy]="true" styleClass="p-datatable-gridlines p-datatable-striped" *ngIf="vitalSchedules.length > 0">
        <ng-template pTemplate="header">
          <tr>
            <th>Configuration Name</th>
            <th>Start date</th>
            <th>End date</th>
            <th>Schedule</th>
            <th>Created Date</th>
            <th>Action</th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
          <tr>
            <td>{{ item.scheduleTitle }}</td>
            <td>{{ item.startDate }}</td>
            <td>{{ item.endDate }}</td>
            <td *ngIf="item.collectMode == 'frequency'">
              Frequency- {{ item.frequency }} Minutes
            </td>
            <td *ngIf="item.collectMode == 'time'">
              Timeslots- {{ item.timeSlots }}
            </td>
            <td>{{ item.createdDate }}</td>
            <td>
              <div class="flex flex-wrap gap-2">
                <button pButton pRipple icon="pi pi-trash" class="p-button-rounded p-button-warning"
                  style="height: 30px; width: 30px"
                  (click)="deleteVitalSchedule($event, item.vitalScheduleId)"></button>
              </div>
            </td>
          </tr>
        </ng-template>
      </p-table>
    </p-tabPanel>
  </p-tabView>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" severity="secondary" (click)="schedulePopup = false" class="mr-1" />
      <p-button label="Save" (onClick)="saveVitalSchedules()" class="mr-1" *ngIf="activeIndex === 0" />
    </div>
  </ng-template>
</p-sidebar>