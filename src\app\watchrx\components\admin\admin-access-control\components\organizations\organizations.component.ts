import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { ConfirmationService, LazyLoadEvent, MessageService } from 'primeng/api';
import { Table, TableLazyLoadEvent } from 'primeng/table';
import { debounceTime, Subject } from 'rxjs';
import { LoaderService } from '../../../../../loader/loader/loader.service';
import { AdminAccessControlService } from '../../service/admin-access-control.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  styleUrl: './organizations.component.scss'
})
export class OrganizationsComponent {
  loader: boolean = false;
  organizationList: any[] = [];
  rolesList: any[] = [];
  totalCaseManagers: number = 0;
  itemsPerPage: number = 10;
  @ViewChild('dt')
  table!: Table;
  visible: boolean = false;
  isUpdate: boolean = false;
  editingValue: any;
  OrganizationTitle: string = "Add New Organization"
  selectedTempOrgRoles: any[] = []
  showForm: boolean = true;
  selectedRole: any = null;
  tempOrganizationList: any[] = [];
  orgLoader:boolean=false;
  constructor(
    private router: Router,
    private adminAccessControlService: AdminAccessControlService,
    private loaderService: LoaderService,
    public confirmService: ConfirmationService,
    public messageService: MessageService,
    public cdr: ChangeDetectorRef,
    private fb: FormBuilder,
  ) { }

  organizationForm: FormGroup = this.fb.group({});

  ngOnInit(): void {
    this.organizationForm = this.fb.group({
      groupName: ['', Validators.required],
      groupDescription: ['', Validators.required],
      roleIds: ["", Validators.required],
      groupEmail: [''],
      groupFax: [''],
      groupPhoneNo: [''],
    });
    this.cdr.detectChanges();
    this.getRolesList();
    this.loadAllUsers();
  }


  getRolesList() {
    //this.loaderService.show();
    this.adminAccessControlService.getRoles().subscribe((res) => {
      if (res.status) {
        //console.log(res);
        this.rolesList = res.roles!;
        //this.loaderService.hide();
      }
    });
  }
  loadOrganizations($event?: LazyLoadEvent | TableLazyLoadEvent) {
    this.orgLoader = true;
    this.adminAccessControlService.getGroupsandRolesList().subscribe((res) => {
      if (res.status) {
        //console.log(res);
        this.organizationList = res.groupsAndAssignedRoles!;
        this.tempOrganizationList = JSON.parse(JSON.stringify(res.groupsAndAssignedRoles));
        this.orgLoader = false;
      }
    });
  }

  onDeleteFeature(id: any) {
    this.confirmService.confirm({
      message: `Are you sure you want to delete ?`,
      header: 'Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        this.loaderService.show();
        this.adminAccessControlService
          .deleteGroup({ groupId: id })
          .subscribe((res) => {
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'Oranization deleted successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadOrganizations();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail:
                  'Something went wrong',
                life: 3000,
              });
            }
          }, err => {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail:
                'Something went wrong',
              life: 3000,
            });
          });
      },
      reject: () => { },
    });
  }

  addOrganization() {
    this.OrganizationTitle = "Add New Organization"
    this.visible = true;
    this.organizationForm.reset();
    this.showForm = true;
    this.isUpdate = false;
  }

  onSubmit() {
    let arr = [];
    if (this.organizationForm.get('roleIds')?.value.length > 0) {
      arr = this.organizationForm.get('roleIds')?.value.map((d: any) => d.roleId);
    }
    let obj: any = {
      groupName: this.organizationForm.get('groupName')?.value,
      groupDescription: this.organizationForm.get('groupDescription')?.value,
      roleIds: arr,
      groupEmail: this.organizationForm.get('groupEmail')?.value,
      groupFax: this.organizationForm.get('groupFax')?.value,
      groupPhoneNo: this.organizationForm.get('groupPhoneNo')?.value
    }
    if (this.isUpdate) {
      obj.groupId = this.editingValue.groupId
    }
    this.loaderService.show();
    this.adminAccessControlService
      .saveGroup(obj)
      .subscribe((res) => {
        this.loaderService.hide();
        if (res?.success) {
          this.messageService.add({
            severity: 'success',
            summary: 'Confirmed',
            detail: !this.isUpdate ? 'Organization Added successfully.' : 'Organization Updated successfully.',
            life: 3000,
          });
          setTimeout(() => {
            this.loadOrganizations();
          }, 1000);
          this.organizationForm.reset();
          this.visible = false;
          this.isUpdate = false;
          this.editingValue = null;
        } else {

          this.messageService.add({
            severity: 'error',
            summary: 'Rejected',
            detail:
              'Something went wrong',
            life: 3000,
          });
        }
      }, err => {
        this.organizationForm.reset();
        this.visible = false;
        this.loaderService.hide();
        this.isUpdate = false;
        this.editingValue = null;
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail:
            'Something went wrong',
          life: 3000,
        });
      });
  }

  onUpdateFeature(form: any, showForm = true) {
    this.OrganizationTitle = "Update Organization"
    this.isUpdate = true;
    this.editingValue = form;
    this.organizationForm.patchValue(form);
    this.organizationForm.get('roleIds')?.setValue(form.roles)
    this.visible = true;
    this.selectedTempOrgRoles = JSON.parse(JSON.stringify(form.roles));
    this.showForm = showForm;
    this.selectedRole = null
  }

  onCheckboxChange(event: any, type: string, id?: any) {
    const selectedRoles = this.organizationForm.value.roleIds;
    if (type == 'checkbox') {
      if (event.target.checked) {
        selectedRoles.push(id);  // Add to the array
      } else {
        const index = selectedRoles.findIndex((val: any) => val.roleId == id.roleId)
        if (index > -1) {
          selectedRoles.splice(index, 1);  // Remove from the array
        }
      }
    }
    else {
      const index = this.selectedTempOrgRoles.filter((val: any) => val.roleId == event.value.roleId);
      if (index.length == 0) {
        selectedRoles.push(event.value);  // Remove from the array
        this.selectedTempOrgRoles.push(event.value)
      }
      else {
        // const index = this.selectedTempOrgRoles.findIndex((val: any) => val.roleId == event.value.roleId)
        // if (index > -1) {
        //   this.selectedTempOrgRoles.splice(index, 1);  // Remove from the array
        //   this.selectedTempOrgRoles.push(event.value)
        // }
      }
    }

    this.organizationForm.patchValue({ roleIds: selectedRoles });
  }
  onCancel() {
    this.visible = false
    this.organizationList = this.tempOrganizationList
  }

  //code for unassigning the users from organization
  visibleUsers: boolean = false;
  usersList: any[] = [];
  selectedUser: any;
  selectedGroups: any[] = [];
  selectedTempOrgUsers: any[] = [];
  dropdownUser:any;
  onShowUserSidebar(event: any) {
    console.log(event)
    this.selectedGroups=[];
    //this.selectedUser=event.
    this.visibleUsers = true;
    this.dropdownUser=null
    this.selectedGroups.push(event);
    if (event.users.length > 0) {
      event.users.forEach((user: any) => {
        user.checked = true;
      });
    }
    this.selectedTempOrgUsers = JSON.parse(JSON.stringify(event.users));
    this.selectedUser = []
  }
  loadAllUsers() {
    this.loader = true;
    this.adminAccessControlService.getUsersList().subscribe((res) => {
      if (res.status) {
        console.log(res);
        this.usersList = res.users!;
        this.loader = false;
      }
    });
  }
  userChange(event: any) {
    this.selectedUser = event.value;
  }
  onUserCheckboxChange(event: any, user: any, index: number) {
    console.log(event, user, index);
    this.selectedTempOrgUsers[index].checked = !this.selectedTempOrgUsers[index].checked;
  }
  assignOrg() {
    if (this.selectedUser && this.selectedGroups.length > 0) {
      let selectedUser = this.selectedUser.userId;
      let selectedGroupIds = this.selectedGroups.map((d) => d.groupId);
      this.loaderService.show();
      this.adminAccessControlService
        .assignUsersToGroups({
          userId: selectedUser,
          groupIds: selectedGroupIds,
        })
        .subscribe(
          (res) => {
            this.visibleUsers=false;
            this.loaderService.hide();
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'User Assigned to Group successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadOrganizations();
              }, 1000);
            } else {
              this.visibleUsers=false;
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          },
          (err) => {
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        );
    } else {
    }
  }
  unAssignOrg() {
    if (this.selectedTempOrgUsers.length > 0 && this.selectedGroups.length > 0) {
      let selectedUsers = this.selectedTempOrgUsers.filter(e => e.checked == false);
      let userList: any[] = [];
      if (selectedUsers.length > 0) {
        userList = this.usersList.filter(item1 => selectedUsers.some(item2 => item2.userName === item1.userName)).map(item => item.userId);
      }
      let selectedGroupIds = this.selectedGroups.map((d) => d.groupId);
      this.loaderService.show();
      this.adminAccessControlService
        .unassignUsersToGroups({
          userIds: userList,
          groupIds: selectedGroupIds,
        })
        .subscribe(
          (res) => {
            this.loaderService.hide();
            this.visibleUsers=false
            if (res?.success) {
              this.messageService.add({
                severity: 'success',
                summary: 'Confirmed',
                detail: 'User Unassigned to Group successfully.',
                life: 3000,
              });
              setTimeout(() => {
                this.loadOrganizations();
              }, 1000);
            } else {
              this.messageService.add({
                severity: 'error',
                summary: 'Rejected',
                detail: 'Something went wrong',
                life: 3000,
              });
            }
          },
          (err) => {
            this.visibleUsers=false
            this.loaderService.hide();
            this.messageService.add({
              severity: 'error',
              summary: 'Rejected',
              detail: 'Something went wrong',
              life: 3000,
            });
          }
        );
    } else {
    }
  }
  onSidebarOpen() {
    document.body.style.overflow = 'hidden';
  }

  onSidebarClose() {
    document.body.style.overflow = '';
  }

  groupPhoneNoValid(): boolean {
    const value = this.organizationForm.get('groupPhoneNo')?.value;
    if (!value) return true;
    return /^\+?[0-9]{10,15}$/.test(value);
  }
}
