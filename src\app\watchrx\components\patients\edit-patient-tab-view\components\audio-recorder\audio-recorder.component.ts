import { Component, Injectable, OnInit } from '@angular/core';
import { AudioService } from './service/audio.service';
import { ElectronAudioService } from './service/electron-audio.service';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { LoaderService } from '../../../../../loader/loader/loader.service';
@Component({
  selector: 'audio-recorder',
  standalone: true,
  imports: [CommonModule, ButtonModule, ToastModule],
  templateUrl: './audio-recorder.component.html',
  styleUrl: './audio-recorder.component.scss',
  providers: [MessageService, ConfirmationService]
})
@Injectable({
  providedIn: 'root',
})
export class AudioRecorderComponent implements OnInit {
  isRecording = false;
  audioUrl: string | null = null;
  patientId: number = -1;
  errorMessage: string | null = null;
  isInitializing = false;
  duration = 0.0;
  isElectronApp = false;
  private activeRecorder: AudioService | ElectronAudioService;

  constructor(
    private webRecorder: AudioService, 
    private electronRecorder: ElectronAudioService,
    private route: ActivatedRoute,  
    private messageService: MessageService,
    private loaderService: LoaderService
  ) {
    // Detect if running in Electron and choose appropriate recorder
    this.isElectronApp = this.electronRecorder.isElectronApp();
    this.activeRecorder = this.isElectronApp ? this.electronRecorder : this.webRecorder;
    
    console.log(`🎯 Using ${this.isElectronApp ? 'Electron' : 'Web'} audio recorder`);
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
  }

  async toggleRecording() {
    if (!this.isRecording) {
      await this.startRecording();
    } else {
      await this.stopRecording();
    }
  }

  private async startRecording() {
    try {
      this.isInitializing = true;
      this.errorMessage = null;
      
      await this.activeRecorder.initRecorder();
      this.activeRecorder.startRecording();
      this.isRecording = true;
      
      this.messageService.add({
        severity: 'success',
        summary: 'Recording Started',
        detail: `${this.isElectronApp ? 'Enhanced desktop' : 'Browser'} audio recording in progress`
      });
      
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
      this.errorMessage = error instanceof Error ? error.message : 'Failed to start recording';
      this.isRecording = false;
      
      this.messageService.add({
        severity: 'error',
        summary: 'Recording Failed',
        detail: this.errorMessage
      });
    } finally {
      this.isInitializing = false;
    }
  }

  private async stopRecording() {
    try {
      console.log(`⏹️ Stopping ${this.isElectronApp ? 'Electron' : 'Web'} recording...`);
      const blob = await this.activeRecorder.stopRecording();
      const tempAudio = new Audio();
      this.audioUrl = URL.createObjectURL(blob);
      tempAudio.src = this.audioUrl;
        tempAudio.addEventListener('loadedmetadata', () => {
         this.duration = tempAudio.duration/ 60; // This is your audio length in seconds!
        console.log('Recorded audio duration:', this.duration, 'mins');
        const filename = `${this.patientId}-recording.wav`;
      const formData = new FormData();
      formData.append('file', blob, filename);
      formData.append('patientId', this.patientId.toString());
      formData.append('audioSources', this.isElectronApp ? 'microphone,system,electron' : 'microphone,system');
      formData.append('duration',this.duration+"");
      console.log(`📤 Uploading ${this.isElectronApp ? 'Electron-enhanced' : 'browser'} audio with both microphone and system audio`);
      this.loaderService.show();
      this.activeRecorder.uploadAudio(formData).subscribe((res: any) => {
      this.loaderService.hide();
      //if (res?.success) {
         this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: res.messages[0],
        });
     // }
    }, (err: any) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to upload audio.',
        });
      this.loaderService.hide();
    });
    }, { once: true });   
      this.activeRecorder.stopStream();
      this.isRecording = false;
      this.errorMessage = null;
      
      console.log('Recording stopped and uploaded successfully');
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.errorMessage = error instanceof Error ? error.message : 'Failed to stop recording';
      this.isRecording = false;
    }
  }

  clearError() {
    this.errorMessage = null;
  }

  getButtonText(): string {
    if (this.isInitializing) return 'Initializing...';
    if (this.isRecording) return 'Stop Recording';
    return 'Record Audio';
  }

  getButtonIcon(): string {
    if (this.isInitializing) return 'pi pi-spin pi-spinner';
    if (this.isRecording) return 'pi pi-stop';
    return 'pi pi-microphone';
  }

  isButtonDisabled(): boolean {
    return this.isInitializing;
  }
}
