import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { CareManager } from '../../../api/careManagers';
import { environment } from '../../../../../environments/environment';
import { DatePipe, formatDate } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class CareManagersService {
  static readonly CAREGIVER_URL = 'service/clinician/getCasemanagersByOrgId';
  static readonly UPDATED_USER_URL='service/patient/setUpdatedUser'
  datePipe: DatePipe = new DatePipe('en-US');
  constructor(private http: HttpClient) {}

  getUsers(skip: number, rows: number): Observable<CareManager[]> {
    let fromToDetails =
    formatDate(new Date(), 'yyyy-MM-dd', 'en-US') +
    '/' +
    formatDate(new Date(), 'yyyy-MM-dd', 'en-US');
    return this.http.get<CareManager[]>(
      environment.BASE_URL + CareManagersService.CAREGIVER_URL + '/' + fromToDetails
    );
  } 

  updatedUser(userId:any)
  {
    return this.http.get<any>(
      environment.BASE_URL + CareManagersService.UPDATED_USER_URL + '/' + userId
    );
  }

  searchUser(userName:string)
  {
    return this.http.get<any>(
      environment.BASE_URL + CareManagersService.CAREGIVER_URL + '/' + userName
    );
  }
  private fromToDate() {
    var enddate = new Date();
    var strtdate = new Date().setDate(1);
    var transToDate = this.datePipe.transform(enddate, 'yyyy-MM-dd');
    var transformDate = this.datePipe.transform(strtdate, 'yyyy-MM-dd');
    return transformDate + '/' + transToDate;
  }
}
