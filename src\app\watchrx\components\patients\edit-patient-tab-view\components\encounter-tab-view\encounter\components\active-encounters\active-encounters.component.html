<div class="encounterscreen position-relative ">
  <p-confirmDialog />
  <p-toast />
  <div class="flex flex-column md:flex-row md:justify-content-between filters">
    <div class="flex gap-3 align-items-center ">
      <div class="flex flex-column">
        <label htmlfor="state" class="font-bold font-12 block mb-2">From Date</label>
        <p-calendar [(ngModel)]="startDate" [showIcon]="true" inputId="buttondisplay" [showOnFocus]="false"
          placeholder="Start Date" />
      </div>
      <div class="flex flex-column">
        <label htmlfor="state" class="font-bold font-12 block mb-2">To Date</label>
        <p-calendar [(ngModel)]="endDate" [showIcon]="true" inputId="buttondisplay" [showOnFocus]="false"
          placeholder="End Date" />
      </div>
      <div class="flex flex-column">
        <label htmlfor="state" class="font-bold font-12 block mb-2">Encounter Reason</label>
        <p-dropdown [options]="reasonList" [(ngModel)]="filterReason" checkmark="true" optionLabel="reason"
          [showClear]="filterReason?true:false" placeholder="Filter By" (onChange)="reasonChange()" />
      </div>
      <p-button severity="primary" label="Submit" (click)="filterEnounter()" class="mt-4"></p-button>
    </div>

    <div class="flex gap-3 align-items-center mt-4">
      <p-button icon="pi pi-plus" label="Add Encounter" severity="secondary" (click)="showDialog()"
        [outlined]="true"></p-button>
      <p-button icon="pi pi-download" severity="primary" [outlined]="true" label="Download"
        (click)="downloadEncounter()"></p-button>
    </div>
  </div>

  <p-table [value]="encounterList" [paginator]="true" [totalRecords]="totalEncounterCount" [rows]="25" [lazy]="true"
    [loading]="loader" (onLazyLoad)="loadEncounterList($event)" responsiveLayout="scroll" 
    styleClass="p-datatable-gridlines p-datatable-striped">
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 200px;">Date, Time and Duration</th>
        <th>Encounter Details</th>
        <th>Actions</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-enc>
      <tr>

        <td style="width: 200px;">
          <div>{{ enc.encounterDateTime | date : "MM-dd-yyyy" }}</div>
          <div>{{ enc.encounterDateTime | date : "h:mm a" }}-{{enc.encounterEndDateTime | date : "h:mm a"}}</div>
          <div class="font-bold">{{ convert(enc.duration) }}</div>
        </td>

        <td style="word-break: break-all;">
          <div class="desc1">
            Category: <span class="message mr-2"> {{ enc.review|| '-' | uppercase }}</span> |
            <span class="ml-2">Added by: </span><span class="message mr-2"> {{ enc.addedByUser }}</span> | 
            <span class="ml-2">Ecounter Reason: </span> <span class="message"> {{ enc.encounterReason }}</span></div>
          <div class="desc2">
            <p class="p-0 m-0">Encounter Description:</p><span class="message" [innerHtml]="enc.encounterDescription"></span>
          </div>
        </td>
        <td>
          <div class="flex">
            <button pButton icon="pi pi-pencil" class="p-button-secondary p-button-outlined mr-2"
              (click)="editEncounter(enc)"></button>
            <button pButton icon="pi pi-trash" class="p-button-secondary p-button-outlined"
              (click)="deleteEncounter($event, enc.encounterId)" severity="danger"></button>
          </div>
        </td>
      </tr>
    </ng-template>
  
  </p-table>
  <div class="footer1" > Total {{totalEncounterCount}} Encounters </div>
</div>

<p-sidebar [(visible)]="visible" position="right" [style]="{ width: '40rem' }" class="addencountersidebar" (onShow)="updateScrollBlocking()" (onHide)="updateScrollBlocking()"
 >
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-pencil mr-3" *ngIf="isEditEncounter"></i>
        <i class="pi pi-plus mr-3" *ngIf="!isEditEncounter"></i>
        {{title}}
      </span>
    </div>
  </ng-template>
  <app-encounter-form  *ngIf="visible" [isEditEncounter]="isEditEncounter" #encounterForm [programList]="programList" [reasonList]="reasonList" [editData]="editData" [patientName]="patientData?.firstName + ' ' + patientData?.lastName"></app-encounter-form>

  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="onCancel()" />
      <p-button label="Save" severity="primary" (click)="onSubmit()" [loading]="addEncounterLoader" />
    </div>
  </ng-template>
</p-sidebar>