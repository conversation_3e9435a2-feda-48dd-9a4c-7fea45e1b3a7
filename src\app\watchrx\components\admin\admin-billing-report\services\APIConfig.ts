export abstract class APIConfig {
  static readonly ADMIN_BILLING_REPORT_LIST =
    'service/clinician/adminBillingReport/';
  static readonly ALL_ORGS_LIST = 'service/patient/getAllOrgs';
  static readonly GENERATE_BILLING =
    'service/clinician/generateBillingByClinic/';

  static readonly ALL_ORGS_CLINIC_LIST =
    'service/clinician/clinicianOrgDetails';
  static readonly ADMIN_BILLING_STATISTICS =
    'service/patient/patientsStatsByClinic/';
  static readonly ADMIN_BILLING_STATISTICS_COUNT =
    'service/patient/patientsStatsCountByClinic/';
  static readonly GET_ROLES = 'service/access/allRoles';

  static readonly ALL_USERS_LIST = 'service/access/allUsers';
  static readonly ASSIGN_USERS_ROLES =
    'service/access/allGroupsAndAssignedUsers';
  static readonly ASSIGN_USER_TO_GROUPs = '/service/access/assignUserToGroups';
  static readonly UNASSIGN_USER_TO_GROUPs =
    'service/access/unassignUserToGroups';
    static readonly ENCOUNTER_REPORT =
    'service/admin/report/encounter';
    static readonly VITAL_REPORT =
    'service/admin/report/vital';
    static readonly CPT_BY_PATIENT_ORG_REPORT =
    'service/admin/report/cptPatientOrg';
    static readonly CPT_BY_PATIENT_ORG_CLINICIAN_REPORT =
    'service/admin/report/cptPatientOrgAndClinician';
    static readonly TOTAL_CPT_BY_PATIENT_ORG_CLINICIAN_REPORT =
    'service/admin/report/totalCptPatientOrgAndClinician';
    static readonly VITAL_16_REPORT =
    'service/admin/report/vital16Days';
    static readonly DOWNLOAD_STATISTICS_REPORT ='service/patient/patientsStatsByClinicReport'
}
