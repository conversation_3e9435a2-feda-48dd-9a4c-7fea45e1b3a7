import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';

declare global {
  interface Window {
    electronAPI?: {
      getDesktopSources: () => Promise<any[]>;
      getSystemAudioStream: (sourceId: string) => Promise<{success: boolean, streamId?: string, error?: string}>;
      getEnhancedMediaStream: (constraints: any) => Promise<MediaStream>;
      platform: string;
      versions: {
        node: string;
        chrome: string;
        electron: string;
      };
    };
  }
}

@Injectable({
  providedIn: 'root'
})
export class ElectronAudioService {
  private static readonly UPLOAD_AUDIO = 'service/patient/uploadAudio';
  private static readonly MAX_SAMPLE_RATE = 48000;

  private audioContext!: AudioContext;
  private mediaRecorder!: MediaRecorder;
  private recordedChunks: Blob[] = [];
  private microphoneStream!: MediaStream;
  private systemAudioStream!: MediaStream;
  private combinedStream!: MediaStream;
  private gainNode!: GainNode;
  private micGainNode!: GainNode;
  private systemGainNode!: GainNode;
  private destination!: MediaStreamAudioDestinationNode;

  constructor(private http: HttpClient) {}

  isElectronApp(): boolean {
    return !!(window && window.electronAPI);
  }

  async initRecorder(): Promise<void> {
    if (!this.isElectronApp()) {
      throw new Error('This service requires Electron app environment');
    }

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume();
      }

      this.destination = this.audioContext.createMediaStreamDestination();
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.destination);

      await this.getMicrophoneStream();
      await this.getElectronSystemAudio();
      this.createCombinedStream();
      this.setupMediaRecorder();
      
    } catch (error) {
      this.cleanup();
      throw error;
    }
  }

  private async getMicrophoneStream(): Promise<void> {
    try {
      this.microphoneStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          sampleRate: Math.min(44100, ElectronAudioService.MAX_SAMPLE_RATE),
          autoGainControl: false
        }
      });
    } catch (error) {
      throw new Error('Microphone access denied or unavailable');
    }
  }

  private async getElectronSystemAudio(): Promise<void> {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: { width: 1, height: 1 },
        audio: {
          echoCancellation: false,
          noiseSuppression: false,
          sampleRate: Math.min(44100, ElectronAudioService.MAX_SAMPLE_RATE)
        } as any
      });
      
      const audioTracks = stream.getAudioTracks();
      const videoTracks = stream.getVideoTracks();

      if (audioTracks.length === 0) {
        throw new Error('No system audio tracks available. Please select "Entire Screen" or "Chrome Tab" and ensure "Share audio" is checked.');
      }

      this.systemAudioStream = new MediaStream(audioTracks);
      videoTracks.forEach(track => track.stop());

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Electron system audio capture failed: ${errorMessage}`);
    }
  }

  private createCombinedStream(): void {
    try {
      const micSource = this.audioContext.createMediaStreamSource(this.microphoneStream);
      this.micGainNode = this.audioContext.createGain();
      this.micGainNode.gain.value = 1.0;
      micSource.connect(this.micGainNode);
      this.micGainNode.connect(this.gainNode);

      const systemSource = this.audioContext.createMediaStreamSource(this.systemAudioStream);
      this.systemGainNode = this.audioContext.createGain();
      this.systemGainNode.gain.value = 1.0;
      systemSource.connect(this.systemGainNode);
      this.systemGainNode.connect(this.gainNode);

      this.combinedStream = this.destination.stream;
    } catch (error) {
      throw error;
    }
  }

  private setupMediaRecorder(): void {
    try {
      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(this.combinedStream, {
        mimeType: this.getSupportedMimeType()
      });

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data);
        }
      };
    } catch (error) {
      throw error;
    }
  }

  startRecording(): void {
    if (!this.mediaRecorder || !this.combinedStream) {
      throw new Error('Recorder not initialized. Call initRecorder() first.');
    }
    
    this.recordedChunks = [];
    this.mediaRecorder.start(1000);
  }

  async stopRecording(): Promise<Blob> {
    return new Promise<Blob>((resolve, reject) => {
      if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
        reject(new Error('MediaRecorder is not active'));
        return;
      }

      this.mediaRecorder.onstop = async () => {
        try {
          const audioBlob = new Blob(this.recordedChunks, { type: 'audio/webm' });
          const wavBlob = await this.convertToWav(audioBlob);
          resolve(wavBlob);
        } catch (error) {
          reject(error);
        }
      };

      this.mediaRecorder.stop();
    });
  }

  private async convertToWav(audioBlob: Blob): Promise<Blob> {
    try {
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
      return this.audioBufferToWav(audioBuffer);
    } catch (error) {
      return audioBlob;
    }
  }

  private audioBufferToWav(audioBuffer: AudioBuffer): Blob {
    const numChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const format = 1;
    const bitDepth = 16;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;
    const byteRate = sampleRate * blockAlign;
    const dataSize = audioBuffer.length * blockAlign;
    const bufferSize = 44 + dataSize;

    const buffer = new ArrayBuffer(bufferSize);
    const view = new DataView(buffer);

    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, bufferSize - 8, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(36, 'data');
    view.setUint32(40, dataSize, true);

    let offset = 44;
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
    }

    return new Blob([buffer], { type: 'audio/wav' });
  }

  private getSupportedMimeType(): string {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg;codecs=opus'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return '';
  }

  stopStream(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.combinedStream) {
      this.combinedStream.getTracks().forEach(track => track.stop());
    }

    this.cleanup();

    this.microphoneStream = null as any;
    this.systemAudioStream = null as any;
    this.combinedStream = null as any;
    this.mediaRecorder = null as any;
    this.recordedChunks = [];
    this.gainNode = null as any;
    this.micGainNode = null as any;
    this.systemGainNode = null as any;
    this.destination = null as any;
  }

  private cleanup(): void {
    if (this.microphoneStream) {
      this.microphoneStream.getTracks().forEach(track => track.stop());
    }

    if (this.systemAudioStream) {
      this.systemAudioStream.getTracks().forEach(track => track.stop());
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
  }

  uploadAudio(details: any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + ElectronAudioService.UPLOAD_AUDIO,
      details
    );
  }
}