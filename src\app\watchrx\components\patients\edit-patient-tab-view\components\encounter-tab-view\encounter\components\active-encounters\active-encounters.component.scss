.filters
{
    padding: 1.25rem !important;
    border-bottom: 1px solid #E8E8E8;
}
.desc1
{
    border-bottom: 1px solid #E8E8E8;
    padding-bottom:10px;
}
.desc2
{
    padding-top:10px;
    word-break: break-all;
}

::ng-deep .addencountersidebar
{
    .p-sidebar-content
    {
        padding: 1.25rem;
    }
    .bold-textarea {
        min-height: 140px;
        max-height: 140px;
    }
}
.font-12 {
    font-size: 12px;
}
::ng-deep .footer1
{
    position: absolute;
    bottom: 23px;
    padding-left: 13px;
}
.message
{
    color:#000000;
}
::ng-deep body.blocked-scroll {
    overflow: hidden !important;
  }
  ::ng-deep .p-sidebar-footer {
    border-top: 1px solid #000000;
}
::ng-deep .p-sidebar-header {
    border-bottom: 1px solid #000000;
}