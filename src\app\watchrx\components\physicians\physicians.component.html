<div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb">
  <span class="font-bold font-16 flex align-items-center">
    <img src="assets/watchrx/svg/doctor.svg" alt="patients" width="16" height="16" class="mr-2" />
    Physicians </span>
</div>
<div class="card" style="margin-bottom: 23px">
  <h6 class="font-bold pl-3 pr-3 pt-3"> Physicians List</h6>
  <hr />
  <div class="flex flex-row p-3 md:justify-content-start ">
    <div class="flex-1 mr-3">
      <span class="p-input-icon-left p-input-icon-right w-full">
        <i class="pi pi-search" style="width: 20px;"></i>
        <input type="text" pInputText (input)="onInput($event)" placeholder="Search" class="w-full"
          style="border-radius: 20px;" [(ngModel)]="searchText"/>
      </span>
    </div>
    <div class="flex">
      <p-button label="Search" severity="primary" class="mr-1" (onClick)="onSearch(debouncedValue)" />
    </div>
  </div>
  <div class="col-12 lg:col-12 xl:col-12 position-relative">
    <p-table
      [value]="physicians"
      [paginator]="true"
      [rows]="rows"
      [lazy]="true"
      (onLazyLoad)="loadPhysicians($event)"
      [totalRecords]="totalRecords"
      [loading]="loading"
      [rowsPerPageOptions]="searchText.length==0?[15,25, 50,75, 100]:[rows]"
      responsiveLayout="scroll"
      styleClass="p-datatable-gridlines p-datatable-striped"
      [first]="first"
      (onPage)="onPageChange($event)"
      #dt
    >
      <ng-template pTemplate="header">
        <tr>
          <!-- <th>Id</th> -->
          <th>Name</th>
          <th>Phone</th>
          <th>Email</th>
          <th>Patient Count</th>
          <!-- <th>Actions</th> -->
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-cm let-i="rowIndex">
        <tr>
          <!-- <td>{{dt.first + i+1 }}</td> -->
          <td>{{ cm.firstName }} {{ cm.lastName }}</td>
          <td>{{ cm.phone }}</td>
          <td>{{ cm.email }}</td>
          <td>{{ cm.patientCount }}</td>
          <!-- <td> <p-button label="View" severity="secondary" class="mr-3" [outlined]="true"
            (onClick)="onViewDetails(cm)"></p-button></td> -->
        </tr>
      </ng-template>
    </p-table>
    <div class="footer1" > Total {{totalRecords}} Physicians </div>
  </div>
</div>
