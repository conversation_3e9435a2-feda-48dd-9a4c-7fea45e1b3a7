 <p-toast />
<div class="audio-recorder">
  <div class="error-message" *ngIf="errorMessage">
    <i class="fas fa-exclamation-triangle"></i>
    <span>{{ errorMessage }}</span>
    <button class="clear-error-btn" (click)="clearError()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <p-button
    [label]="getButtonText()"
    [outlined]="true"
    severity="secondary"
    [disabled]="isButtonDisabled()"
    [icon]="getButtonIcon()"
    class="mr-3"
    [class.recording-active]="isRecording"
    (onClick)="toggleRecording()"
  />

  <div class="recording-status" *ngIf="isRecording">
    <div class="status-indicator">
      <div class="pulse-dot"></div>
      <span>Recording...</span>
    </div>
  </div>

  <div class="audio-playback" *ngIf="audioUrl">
    <audio [src]="audioUrl" controls></audio>
  </div>
</div>

