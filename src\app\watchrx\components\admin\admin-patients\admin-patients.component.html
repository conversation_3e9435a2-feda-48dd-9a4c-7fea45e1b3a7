<p-toast />
<p-confirmDialog />
<div
  class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb"
>
  <span class="font-bold font-16 flex align-items-center">
    <img
      src="assets/watchrx/svg/nurse.svg"
      alt="patients"
      width="16"
      height="16"
      class="mr-2"
    />
    Patients</span
  >
</div>
<div class="grid">
  <div class="col-12">
    <div class="card pt-3">
      <div class="flex flex-row md:justify-content-start mb-2">
        <div class="col-8 flex searchdiv pl-0">
          <div class="flex flex-column">
            <label class="font-bold mb-1">Select Clinic/Orgnaization</label>
            <p-dropdown
              [options]="orgsList"
              checkmark="true"
              optionLabel="label"
              [filter]="true"
              [(ngModel)]="selectedOrg"
              filterBy="label"
              [showClear]="true"
              placeholder="Select Organization"
              class="mr-2"
              (onChange)="onClinicSelect()"
            />
          </div>
          <div class="flex flex-column">
            <label class="font-bold mb-1">Select CaseManager</label>
            <p-dropdown
              [options]="filteredClinicians"
              checkmark="true"
              optionLabel="label"
              [filter]="true"
              filterBy="label"
              [showClear]="true"
              placeholder="Select CaseManager"
              class="mr-2"
              (onChange)="onClinicianSelect($event.value)"
              [(ngModel)]="fromClinican"
            />
          </div>
           <p-button
            label="Submit"
            severity="primary"
            class="mt-4 ml-2"
            [outlined]="false"
            (onClick)="loadActivePatients()"
          />
         
        </div>
        <div class="col-4 flex justify-content-end ">
           <div class="flex flex-column mt-4 mr-3">
          <span class="p-input-icon-left p-input-icon-right">
            <i class="pi pi-user"></i>
            <input
              type="text"
              pInputText
              (input)="onInput($event)"
              placeholder="Search By Patient Name"
              [(ngModel)]="searchText"
            />
            <i class="pi pi-search"></i>
          </span>
          </div>
          <p-button
            label="Fax"
            icon="pi pi-send"
            severity="primary"
            class="mt-4 mr-3"
            [outlined]="true"
            (onClick)="onFax()"
          />
          <p-button
            label="Transfer"
            severity="primary"
            class="mt-4"
            [outlined]="true"
            (onClick)="op.toggle($event);onOpen()"
          />
          
          <p-overlayPanel #op class="">
          <div class="flex flex-column gap-3 w-35rem panellist">
            <div class="flex flex-column p-3 pb-0">
              <label class="font-bold">Transferring FROM</label>
              <p-dropdown
                [options]="filteredClinicians"
                checkmark="true"
                optionLabel="label"
                [filter]="true"
                filterBy="label"
                [showClear]="true"
                placeholder="Select CaseManager"
                class="mr-2"
                [(ngModel)]="fromClinicans"
              (onChange)="onOpen()"
              />
            </div>
            <div class="flex flex-column p-3 pt-0 pb-1 ">
              <label class="font-bold">Transferring TO</label>
              <p-dropdown
                [options]="filteredToClinicians"
                checkmark="true"
                optionLabel="label"
                [filter]="true"
                filterBy="label"
                [showClear]="true"
                placeholder="Select CaseManager"
                class="mr-2"
                [(ngModel)]="toClinican"
              />
            </div>
            <p-divider layout="horizontal" />
            <div class="flex justify-content-between p-3 pt-1 align-items-center ">
              <div ><span *ngIf="this.selectedRows.length>0||globalSelectAll">Transfering {{globalSelectAll?totalActivePatients:selectedRows.length}} patients</span></div>
              <div>
                <p-button
                label="Cancel"
                severity="primary"
                class="mr-2"
                [outlined]="true"
                (onClick)="op.toggle($event);clearSelection()"
              />
                <p-button
                [loading]="loader"
                label="Transfer"
                severity="primary"
                class="mr-2"
                [outlined]="false"
                (onClick)="op.toggle($event);transeferPatients()"
                [disabled]="
                  fromClinicans === undefined ||
                  fromClinicans === null ||
                  toClinican === undefined ||
                  toClinican === null || this.selectedRows.length==0
                "
              />
              </div>
           
        </div>
          </div>
      </p-overlayPanel>
  
        </div>
      
      </div>
      <hr />
      <!-- <div class="flex flex-row md:justify-content-start mb-2 gap-3">
        <div class="flex flex-column">
          <label class="font-bold">All Patients?</label>
          <p-checkbox
            [(ngModel)]="globalSelectAll"
            [binary]="true"
            label="Select"
          ></p-checkbox>
        </div>
        <p-divider layout="vertical" />
        <div class="flex flex-column">
          <label class="font-bold">From CaseManager</label>
          <p-dropdown
            [options]="filteredClinicians"
            checkmark="true"
            optionLabel="label"
            [filter]="true"
            filterBy="label"
            [showClear]="true"
            placeholder="Select CaseManager"
            class="mr-2"
            [(ngModel)]="fromClinican"
          />
        </div>
        <p-divider layout="vertical" />
        <div class="flex flex-column">
          <label class="font-bold">To CaseManager</label>
          <p-dropdown
            [options]="filteredClinicians"
            checkmark="true"
            optionLabel="label"
            [filter]="true"
            filterBy="label"
            [showClear]="true"
            placeholder="Select CaseManager"
            class="mr-2"
            [(ngModel)]="toClinican"
          />
        </div>
        <p-divider layout="vertical" />
        <div class="flex flex-column">
          <label class="font-bold">{{ " Action " }}</label>
          <p-button
            label="Transfer"
            severity="primary"
            class="mr-2"
            [outlined]="false"
            icon="pi pi-plus"
            (onClick)="transeferPatients()"
            [disabled]="
              fromClinican === undefined ||
              fromClinican === null ||
              toClinican === undefined ||
              toClinican === null
            "
          />
        </div>
        <div class="flex flex-column">
          <label class="font-bold">{{ " Reset " }}</label>
          <p-button
            label="Clear"
            severity="help"
            class="mr-2"
            [outlined]="false"
            icon="pi pi-refresh"
            (onClick)="clearSelection()"
          />
        </div>
      </div> -->
      <div class="grid p-fluid mt-3">
        <div class="field col-12 position-relative">
          <p-table
            #dt
            [value]="patientList"
            [paginator]="true"
            [totalRecords]="totalActivePatients"
            [rows]="itemsPerPage"
            [lazy]="true"
            [loading]="loader"
            (onLazyLoad)="loadActivePatients($event)"
            responsiveLayout="scroll"
            styleClass="p-datatable-gridlines p-datatable-striped"
            [(selection)]="selectedRows"
            dataKey="patientId"
            [rowHover]="true"
            [selectionPageOnly]="true"
            [selectionMode]="'multiple'"
            (onRowSelect)="onRowSelect($event)"
            (onRowUnselect)="onRowUnselect($event)"
          [rowsPerPageOptions]="searchText.length==0?[15,25, 50,75, 100]:[itemsPerPage]"
            (onPage)="onPageChange($event)" [first]="first"
          >
            <ng-template pTemplate="header">
              <tr>
                <th>
                  <p-checkbox
                    [(ngModel)]="selectAll"
                    [binary]="true"
                    (onChange)="selectAllRows($event)"
                  ></p-checkbox>
                </th>
                <th>Patient Name</th>
                <th>Phone Number</th>
                <th>Provider</th>
                <th>Case Manager</th>
                <th>Clinic/Org</th>
              </tr>
            </ng-template>

            <ng-template let-patientInfo pTemplate="body">
              <tr>
                <td>
                  <!-- Use (change) instead of (click) to rely on ngModel binding -->
                  <p-checkbox
                    [(ngModel)]="patientInfo.selected"
                    [binary]="true"
                    (onChange)="onCheckboxSelect(patientInfo)"
                  ></p-checkbox>
                </td>
                <td>{{ patientInfo.patientName }}</td>
                <td>{{ patientInfo.patientPhoneNumber }}</td>
                <td>{{ patientInfo.physicianName }}</td>
                <td>{{ patientInfo.caseManagerName }} <i class="pi pi-pencil ml-1 cursor-pointer"  (click)="op.toggle($event);onEditCaseManager(patientInfo)"></i> </td>
                <td>{{ patientInfo.groupName }}</td>
              </tr>
            </ng-template>

            <ng-template pTemplate="footer">
              <tr>
                <td colspan="6">Selected Rows: {{globalSelectAll?totalActivePatients:selectedRows.length }}</td>
              </tr>
            </ng-template>
          </p-table>
           <div class="text-900 font-bold footer1">Total {{totalActivePatients}} Patients </div>
        </div>
      </div>
    </div>
  </div>
</div>

<p-dialog header="Fax Encounters" [(visible)]="faxDialogVisible" [modal]="true" [closable]="true" [style]="{width: '400px'}">
          <div class="p-fluid">
            <div class="field">
              <label for="faxFromDate">From Date</label>
              <p-calendar id="faxFromDate" [(ngModel)]="faxFromDate" dateFormat="mm/dd/yy" [showIcon]="true" [appendTo]="'body'"></p-calendar>
            </div>
            <div class="field">
              <label for="faxToDate">To Date</label>
              <p-calendar id="faxToDate" [(ngModel)]="faxToDate" dateFormat="mm/dd/yy" [showIcon]="true" [appendTo]="'body'"></p-calendar>
            </div>
            <div class="field flex justify-content-end mt-5">
              <p-button label="Submit"  [loading]="isLoading" (onClick)="onFaxDialogSubmit()" [disabled]="!faxFromDate || !faxToDate"></p-button>
            </div>
          </div>
        </p-dialog>
