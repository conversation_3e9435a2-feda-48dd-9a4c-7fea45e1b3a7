import { Program } from './patientEncounter';

export interface CareplanChronic {
  data: Chronic[];
  status?: boolean;
}

export interface Chronic {
  id: number;
  label: string;
}

export interface CarePlanTab {
  id: number;
  isEnabled: boolean;
  tabName: string;
}

export interface LatestCarePlan {
  carePlanData: any[];
  latestVital: any[];
  programs: Program[];
}

export interface CarePlanDetails {
  date: string;
  data: string;
  id: number;
}

export interface TemplateDetails {
  questions: string;
  questionId: number;
  answer: string;
  createdDate: string;
  status: boolean;
  history: string;
  historyV1: string;
  defaultAnswers: any[];
}


export interface dialog {
  date?: string;
  comm?: string;
  id?: string;
  dialogName?: string;
}