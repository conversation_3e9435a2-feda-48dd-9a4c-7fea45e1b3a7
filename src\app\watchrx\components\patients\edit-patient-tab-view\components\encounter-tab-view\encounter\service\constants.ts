export abstract class Constants {
  static readonly GET_ENCOUNTERS = 'service/patient/getEncounters/';
  static readonly ADD_ENCOUNTER = 'service/patient/saveEncounter';
  static readonly DELETE_ENCOUNTER = 'service/patient/deleteEncounter';
  static readonly GET_ECOUNTERS_BY_REASON =
    'service/patient/getPatientEncountersByReason/';
  static readonly DOWNLOAD_REPORT = 'service/patient/downloadXlsForEncounters';
  static readonly GET_REASON_LIST = 'service/patient/getDisplayableEncounterReasonsAndCodes';

  static readonly PATIENT_ENROLLED_PROGRAM = 'service/patient/patientEnrolledPrograms/'
  static readonly GET_PENDING_ENCOUNTERS = 'service/patient/getDrafts/';
  static readonly ADD_PENDING_ENCOUNTER = 'service/patient/saveDraft';
  static readonly SAVE_PENDING_ENCOUNTER = 'service/patient/submitDraft';
  static readonly DELETE_PENDING_ENCOUNTER='service/patient/deleteDraft';

}
