import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { FormsModule } from '@angular/forms';
import { BadgeModule } from 'primeng/badge';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { PhysiciansRoutingModule } from './physicians-routing.module';
import { PhysiciansComponent } from './physicians.component';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

@NgModule({
  declarations: [PhysiciansComponent],
  imports: [
    CommonModule,
    PhysiciansRoutingModule,
    FormsModule,
    TableModule,
    StyleClassModule,
    BadgeModule,
    ButtonModule,
    InputTextModule
  ],
})
export class PhysiciansModule {}
