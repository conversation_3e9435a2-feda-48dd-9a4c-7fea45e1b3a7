import { CommonModule } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { DocumentationComponent } from './documentation/documentation.component';
import { EncounterComponent } from './encounter/encounter.component';
import { PhoneCommunicationComponent } from './phone-communication/phone-communication.component';
import { TasksComponent } from './tasks/tasks.component';
import { TextMessagesComponent } from './text-messages/text-messages.component';

@Component({
  selector: 'app-encounter-tab-view',
  standalone: true,
  templateUrl: './encounter-tab-view.component.html',
  styleUrl: './encounter-tab-view.component.scss',
  imports: [
    CommonModule,
    TabViewModule,
    ButtonModule,
    EncounterComponent,
    TextMessagesComponent,
    PhoneCommunicationComponent,
    DocumentationComponent,
    TasksComponent,
  ],
})
export class EncounterTabViewComponent implements OnInit {
  activeIndex: any = 0;
  patientId: number = -1;
  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();
@Input() patientData:any;
  handleDataFromChild(data: any) {
    if (data && data?.length > 0) {
      this.dataEmitter.emit(data);
      this.cdr.detectChanges();
    } else {
      this.cdr.detectChanges();
    }
  }

  constructor(private route: ActivatedRoute, private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.activeIndex = localStorage.getItem('subTab')
      ? localStorage.getItem('subTab')
      : 0;

    this.route.queryParams.subscribe((params) => {
      console.log(params['id'], 'ID;;;;;');
      this.patientId = params['id'];
    });
  }

  onEncounterTabClick(e: any) {
    localStorage.setItem('subTab', this.activeIndex);
  }
}
