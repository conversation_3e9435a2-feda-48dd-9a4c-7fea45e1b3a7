import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import {
  TemplateDetailsReponse,
  TemplateResponse,
} from '../../../../api/adminTemplates';
import { GenericResponse } from '../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class TemplateService {
  constructor(private http: HttpClient) {}

  getAllChroniConditions(): Observable<TemplateResponse> {
    return this.http.get<TemplateResponse>(
      environment.BASE_URL + Constants.CHRONIC_CONDITIONS_LLIST
    );
  }

  getTemplateDetailsById(id: number): Observable<TemplateDetailsReponse> {
    return this.http.get<TemplateDetailsReponse>(
      environment.BASE_URL + Constants.DETAILS_BY_ID + id
    );
  }
  addTemplateManual(payload: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SAVE_TEMPLATE,
      payload
    );
  }

  deleteQuestion(payload: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.DELETE_QUESTION,
      payload
    );
  }

  updateQuestion(payload: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPDATE_QUESTION,
      payload
    );
  }

  updateTemplateFile(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.UPLOAD_SHEET,
      details
    );
  }

  toggleStatus(id: number): Observable<{
    messages: string[];
    status: boolean;
    responseCode: any;
    success: boolean;
  }> {
    return this.http
      .get<void>(`${environment.BASE_URL}${Constants.TOGGLE_STATUS}${id}`, {
        observe: 'response',
        responseType: 'text' as 'json',
      })
      .pipe(
        map((response) => {
          return {
            messages: ['Status toggled successfully'],
            status: true,
            responseCode: response.status,
            success: response.status === 200,
          };
        }),
        catchError((error) => {
          return of({
            messages: ['Error toggling status'],
            status: false,
            responseCode: error.status,
            success: false,
          });
        })
      );
  }

  getChronicConditions(): Observable<TemplateResponse> {
    return this.http.get<TemplateResponse>(
      environment.BASE_URL + Constants.CHRONIC_CONDITIONS_LIST_PATIENT
    );
  }
}
