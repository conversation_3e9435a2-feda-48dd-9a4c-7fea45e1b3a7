import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import saveAs from 'file-saver';
import { catchError, map, Observable, throwError } from 'rxjs';
import { environment } from '../../../../../../../../environments/environment';
import {
  CareplanChronic,
  CarePlanDetails,
  CarePlanTab,
  dialog,
  LatestCarePlan,
  TemplateDetails,
} from '../../../../../../api/carePlan';
import { GenericResponse } from '../../../../../../api/editPatientProfile';
import { Constants } from './constants';

@Injectable({
  providedIn: 'root',
})
export class CarePlanService {
  constructor(private http: HttpClient) {}

  getSelectedChronic(patientId: number): Observable<CareplanChronic> {
    return this.http.get<CareplanChronic>(
      environment.BASE_URL + Constants.GET_CHRONIC + patientId
    );
  }

  getTabs(): Observable<CarePlanTab[]> {
    return this.http.get<CarePlanTab[]>(
      environment.BASE_URL + Constants.GET_TABS_CONFIG
    );
  }

  getTabsQuestions(details:any): Observable<any> {
    return this.http.post<any>(
      environment.BASE_URL + Constants.CAREPLAN_QUESTIONS,details
    );
  }

  getLatestCarePlan(patientId: number): Observable<LatestCarePlan> {
    return this.http.get<LatestCarePlan>(
      environment.BASE_URL + Constants.LATEST_CAREPLAN + patientId
    );
  }

  submitCarePlanNotes(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SUBMIT_NOTES,
      details
    );
  }

  getCarePlanDetails(details: string): Observable<CarePlanDetails[]> {
    return this.http.get<CarePlanDetails[]>(
      environment.BASE_URL + Constants.CAREPLAN_DETAILS + details
    );
  }

  templateDetailsByChronicId(details: any): Observable<TemplateDetails[]> {
    return this.http.post<TemplateDetails[]>(
      environment.BASE_URL + Constants.TEMPLATE_DETAILS,
      details
    );
  }

  submitTemplateQAnsNotes(details: any): Observable<GenericResponse> {
    return this.http.post<GenericResponse>(
      environment.BASE_URL + Constants.SUBMIT_TEMPLATE,
      details
    );
  }

  getTemplateHistoryData(details: string): Observable<CarePlanDetails[]> {
    return this.http.get<CarePlanDetails[]>(
      environment.BASE_URL + Constants.TEMPLATE_HISTORY + details
    );
  }

  downloadTempHistory(details: string, fileName: string): Observable<any> {
    const url =
      environment.BASE_URL + Constants.DOWNLAOD_TEMPLATE_HISTORY + details;
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.get(url, { headers, responseType: 'arraybuffer' }).pipe(
      map((response: BlobPart) => {
        const blob = new Blob([response], {
          type: 'application/pdf',
        });
        saveAs(blob, fileName);
        return { success: true };
      }),
      catchError((error) => {
        return throwError({ success: false });
      })
    );
  }

  getDialog(patientId:any):Observable<dialog[]>
  {
    return this.http.get<dialog[]>(
      environment.BASE_URL + Constants.SHOW_DIALOG + patientId
    );
  }
  getSDOH(details:any):Observable<dialog[]>
  {
    return this.http.post<dialog[]>(
      environment.BASE_URL + Constants.SHOW_SDOH,details
    );
  }
  getTriage(details:any):Observable<dialog[]>
  {
    return this.http.post<dialog[]>(
      environment.BASE_URL + Constants.SHOW_TRIAGE,details
    );
  }

  sendDialog(patientId:any,dialogId:any):Observable<string>
  {
    return this.http.get<string>(
      environment.BASE_URL + Constants.SEND_DIALOG + patientId+'/'+dialogId, { responseType: 'text' as 'json' }
    );
  }

  deleteDialog(dialogId:any):Observable<string>
  {
    return this.http.get<string>(
      environment.BASE_URL + Constants.DELETE_DIALOG +dialogId,{ responseType: 'text' as 'json' }
    );
  }
}
