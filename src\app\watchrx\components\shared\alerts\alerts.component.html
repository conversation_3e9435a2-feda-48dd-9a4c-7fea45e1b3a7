<p-confirmDialog />
<div [ngClass]="patientId == 0?'pad-btm':''">
  <div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb" *ngIf="patientId == 0">
    <span class="font-bold font-16 flex align-items-center">
      <img src="assets/watchrx/svg/triangled.svg" alt="patients" width="16" height="16" class="mr-2" />
      Alerts</span>
  </div>
  <div class="flex flex-column md:flex-row md:justify-content-between filters">
    <div class="flex gap-2">
      <p-dropdown [(ngModel)]="patient" [options]="response?.patientWatchCaregiver" placeholder="Select Patient"
      optionLabel="patientName" [showClear]="true" (onChange)="alertByDate()" *ngIf="patientId == 0" />
      <p-dropdown [(ngModel)]="alertType" [options]="alertTypeList" [showClear]="true" placeholder="Select Alert Type"
      (onChange)="alertByDate()"  class="alertdropdown"/>
      <p-calendar [(ngModel)]="startDate" [showIcon]="true" inputId="buttondisplay" [showOnFocus]="false"
        placeholder="Start Date" [showButtonBar]="true" />
      <p-calendar [(ngModel)]="endDate" [showIcon]="true" inputId="buttondisplay" [showOnFocus]="false"
        placeholder="End Date" [showButtonBar]="true" />
      <p-button severity="primary" label="Submit" (onClick)="alertByDate()"></p-button>
    </div>
    <div class="flex gap-2">
      <p-button class="ml-2" icon="pi pi-download" label="Excel" severity="primary" [outlined]="true"
        (onClick)="exportToExcel('xlsx')"></p-button>
      <p-button icon="pi pi-download" label="CSV" severity="primary" [outlined]="true"
        (onClick)="exportToExcel('csv')"></p-button>
    </div>
  </div>
  <p-table [value]="response?.patientAlert" responsiveLayout="scroll"
    styleClass="p-datatable-gridlines p-datatable-striped" [paginator]="true" [loading]="loadingAlerts"
    [totalRecords]="totalAlertCount" [rows]="15" [lazy]="true" (onLazyLoad)="onAlertDataByPage($event)"
    [rowsPerPageOptions]="[15,25, 50,75, 100]" [responsiveLayout]="'scroll'" [first]="firstPage"
    (onPage)="onPageChange($event)">
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 15%" *ngIf="patientId == 0">
          Patient Name
          <!-- <div *ngIf="patientId == 0">
          <p-columnFilter [showMenu]="false"><ng-template pTemplate="filter" let-value let-filter="filterCallback">
              <p-dropdown [(ngModel)]="patient" [options]="response?.patientWatchCaregiver" placeholder="Select Patient"
                optionLabel="patientName" [showClear]="true" (onChange)="alertByDate()" />
            </ng-template>
          </p-columnFilter>
        </div> -->
          <!-- <div *ngIf="patientId > 0">Patient Name</div> -->
        </th>
        <th style="width: 10%">
          Alert Type
          <!-- <p-columnFilter [showMenu]="false">
          <ng-template pTemplate="filter" let-value let-filter="filterCallback">
            <p-dropdown [(ngModel)]="alertType" [options]="alertTypeList" [showClear]="true"
              placeholder="Select Alert Type" (onChange)="alertByDate()" />
          </ng-template>
        </p-columnFilter> -->
        </th>
        <th style="width: 20%">Date and Time</th>
        <th style="width: 40%">Alert Description</th>
        <th style="width: 15%">Alert Status</th>

      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-alert>
      <tr> <!--[ngClass]="getRowClass(alert.severity)"-->
        <td *ngIf="patientId == 0">
          <span class="cursor-pointer font-bold p-text-primary link" title="{{alert.patientInfo }}"
            (click)="editPatient( alert['patientId'])">{{alert.patientInfo }}</span>
          <!-- {{ alert.patientInfo }} -->
        </td>
        <td>
          <p-tag [value]="alert.severity" [severity]="getSeverity(alert.severity)" />
        </td>
        <td>
          <div>{{ alert.dateTime | date:'MM-dd-yyyy h:mm a' }}</div>
          <!-- <div class="font-12">{{ alert.dateTime | date:'h:mm a' }}</div> -->
        </td>
        <td>{{ alert.description }}</td>
        <td>{{alert.acknowledgementStatus}}</td>
        <!-- <td>
          <div class="flex">
            <button pButton icon="pi pi-trash" class="p-button-outlined p-button-secondary"
              (click)="deleteAlert(alert.alertId)"></button>
          </div>
        </td> -->
      </tr>
    </ng-template>
  </p-table>
</div>