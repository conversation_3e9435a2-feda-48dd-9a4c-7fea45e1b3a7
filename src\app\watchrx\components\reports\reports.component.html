<div class="flex gap-2 flex-row justify-content-between w-full align-items-center breadcrumb">
  <span class="font-bold font-16 flex align-items-center">
    <img src="assets/watchrx/svg/papers.svg" alt="patients" width="16" height="16" class="mr-2" />
    Reports</span>
</div>
<div class="card m-0 p-0" style="margin-bottom: 23px">
  <div class="col-12 flex flex-column p-3 pl-4 border">
    <label class="font-13 font-bold ">Patients</label>
    <p-autoComplete [suggestions]="filteredPatients" (completeMethod)="filterPatient($event)" field="patientName"
      [(ngModel)]="selectedPatient" placeholder="Select a Patient" appendTo="body" [dropdown]="true"
      emptyMessage="No patient found" [showEmptyMessage]="true" class="mr-2"
      (onSelect)="onPatientSelect($event.value)"></p-autoComplete>
  </div>
  <div class="flex flex-row align-items-between justify-content-between p-2 pl-3 border">
    <div class="flex">
      <div class="button-group">
        <button pButton type="button" class="left-button" icon="pi pi-angle-double-left"
          (click)="onPreviousClick()"></button>
        <div class="center-buttons">
          <div *ngIf="weeklyEnabled">
            <button pButton type="button" class="center-button">
              <span>{{ formatDate(startOfWeek) }} to
                {{ formatDate(endOfWeek) }}</span>
            </button>
          </div>
          <div *ngIf="monthlyEnabled">
            <button pButton type="button" label="{{ currentMonth }}" class="center-button"></button>
          </div>
        </div>
        <button pButton type="button" class="right-button" icon="pi pi-angle-double-right"
          (click)="onNextClick()"></button>

      </div>
      <div class="button-group">
        <p-button label="Weekly" severity="secondary" [outlined]="!weeklyEnabled" (onClick)="graphTypeClick('w')"
          class=" ml-3 p-button-sm daily p-0" pTooltip="Weekly Report" tooltipPosition="top" />
        <p-button label="Montly" severity="secondary" [outlined]="!monthlyEnabled" (onClick)="graphTypeClick('m')"
          class="mr-2 p-button-sm month p-0" pTooltip="Monthly Report" tooltipPosition="top" />
      </div>
      <!-- <p-autoComplete [suggestions]="filteredReportTypes" (completeMethod)="filterReportType($event)" field="name"
        [(ngModel)]="selectedReportType" placeholder="Report Type" appendTo="body" [dropdown]="true" class="mr-2"
        (onSelect)="onPatientSelect()"></p-autoComplete> -->
    </div>
    <div class="mt-3">
      <p-button icon="pi pi-download" label="Download" [outlined]="true" severity="primary" class="ml-2"
        (click)="exportToXlsx('All')" />
    </div>
  </div>
  <div class="flex flex-column">
    <!-- <div class="col-3">
      <div class="card gradient-card mb-0">
        <div class="flex justify-content-between">
          <div>
            <span class="block text-500 font-medium mb-3"
              ><h5>Maximum Threshold</h5></span
            >
            <div class="text-900 font-medium text-xl">192/110</div>
          </div>
        </div>
        <span class="text-500"> 16 Jun, 2024</span>
      </div>
      <div class="card gradient-card mb-0 mt-3">
        <div class="flex justify-content-between">
          <div>
            <span class="block text-500 font-medium mb-3"
              ><h5>Minimum Threshold</h5></span
            >
            <div class="text-900 font-medium text-xl">89/60</div>
          </div>
        </div>
        <span class="text-500"> 07 Jun, 2024</span>
      </div>
      <div class="card gradient-card mb-0 mt-3">
        <div class="flex justify-content-between mb-3">
          <div>
            <span class="block text-500 font-medium mb-3"
              ><h5>Average</h5></span
            >
            <div class="text-900 font-medium text-xl">112/89</div>
          </div>
        </div>
      </div>
      <div class="card gradient-card mb-0 mt-3">
        <div class="flex justify-content-between mb-3">
          <div>
            <span class="block text-500 font-medium mb-3"
              ><h5>No. of days</h5></span
            >
            <div class="text-900 font-medium text-xl">16</div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-9">
      <p-card
        class="mb-2"
        [style]="{
          border: '1x solid black',
          borderRadius: '15px',
          width: 'auto',
          color: 'black',
          background:
            'linear-gradient(0deg, rgba(255, 245, 238, 0.5), rgba(255, 245, 238, 0.5)), linear-gradient(92.54deg, #FFFFFF 47.88%, #F5F5F5 100.01%)'
        }"
      >
        <ng-template pTemplate="header">
          <div class="flex justify-content-between align-items-center mb-1">
            <div class="custom-header">
              <h4>{{ selectedReportType.name }}</h4>
            </div>
            <div class="mt-3">
              <p-button
                label="W"
                [raised]="true"
                severity="info"
                [outlined]="!weeklyEnabled"
                (onClick)="graphTypeClick('w')"
                class="p-button-sm"
                [rounded]="true"
                pTooltip="Weekly Report"
                tooltipPosition="top"
              />
              <p-button
                label="M"
                [raised]="true"
                severity="info"
                [outlined]="!monthlyEnabled"
                (onClick)="graphTypeClick('m')"
                class="ml-2 mr-2 p-button-sm"
                [rounded]="true"
                pTooltip="Monthly Report"
                tooltipPosition="top"
              />
            </div>
          </div>
        </ng-template>
        <p-chart type="bar" [data]="data" [options]="options" />
      </p-card>
    </div> -->

    <div class="col-12 lg:col-12 config1 p-0" style="height:380px;">
      <div class="flex p-card-header justify-content-between p-2 pl-3 align-items-center">
        <h6 class="m-0 font-bold">Alerts</h6>
        <p-button icon="pi pi-download" label="Download" [outlined]="true" severity="primary" class="ml-2"
          (click)="exportToXlsx('Alerts')" />
      </div>
      <hr />
      <div class="flex p-card-header justify-content-start p-2">
        <div class="flex mr-3"><img src="assets/watchrx/svg/red-circle.svg" alt="Critical Stats" width="16"
            height="16" />
          Critical</div>
        <!-- <div class="flex mr-3"><img src="assets/watchrx/svg/orange1-circle.svg" alt="Alarm Stats" width="16"
            height="16" />
          Alarm</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/yellow-circle.svg" alt="Warning Stats" width="16"
            height="16" />
          Warning</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/blue1-circle.svg" alt="Information Stats" width="16"
            height="16" />
          Information</div> -->
      </div>
      <hr />
      <p-chart type="bar" [data]="alertsData" [options]="graphOptions"  />
    </div>
    <div class="col-12 lg:col-12 config1 p-0" style="height:380px;">
      <hr />
      <div class="flex p-card-header justify-content-between p-2 pl-3 align-items-center">
        <h6 class="m-0 font-bold">Medication</h6>
        <p-button icon="pi pi-download" label="Download" [outlined]="true" severity="primary" class="ml-2"
          (click)="exportToXlsx('Medication')" />
      </div>
      <hr />
      <div class="flex p-card-header justify-content-around p-2">
        <div class="flex mr-3"><img src="assets/watchrx/svg/blue-circle.svg" alt="Critical Stats" width="16"
            height="16" />
          Medication to Take</div>
        <div class="flex mr-3"><img src="assets/watchrx/svg/orange1-circle.svg" alt="Alarm Stats" width="16"
            height="16" />
          Missed Medication</div>
      </div>
      <hr />
      <p-chart type="line" [data]="medicationData" [options]="medicationGraphOptions" />
    </div>
    <ng-container *ngFor="let datas of data1; let i=index">
      <div class=" mt-3 lg:col-12 sm:col-12" style="height:450px;" [ngClass]="{'lg:col-12': data.length === 1}">
        <hr />
        <div class="flex p-card-header justify-content-between p-2 pl-3 align-items-center">
          <h6 class="font-bold m-0">{{datas.name}}</h6>
          <p-button icon="pi pi-download" label="Download" [outlined]="true" severity="primary" class="ml-2"
            (click)="exportToXlsx(datas.name)" />
        </div>
        <hr />
        <ng-container *ngIf="datas.name=='SpO2'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minSP" [highValue]="maxSP" [options]="spOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.spo2CriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.spo2CriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions3" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Heart Rate'">
          <div class="flex flex-row w-full" style="min-height:90px" >
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minHR" [(highValue)]="maxHR" [options]="hrOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.heartRateCriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.heartRateCriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Temperature'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minTemp" [(highValue)]="maxTemp" [options]="tempOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.temperatureCriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.temperatureCriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions5" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Blood Pressure'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-50">
              <h6 style="border-bottom:1px solid #E8E8E8"
                class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
                <span class="flex"><img src="assets/watchrx/svg/blue-circle.svg" alt="Systolic" width="16"
                    height="16" /> Systolic
                  Blood Pressure(HG)</span>
              </h6>
              <div class="custom-slider"style="min-height:90px">
                <ngx-slider [(value)]="minSBP" [(highValue)]="maxSBP" [options]="sbpOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
            <div class="w-50">
              <h6 style="border-bottom:1px solid #E8E8E8"
                class="m-0 p-3 flex align-items-center justify-content-between align-items-center ">
                <span class="flex"><img src="assets/watchrx/svg/orange-circle.svg" alt="Diastolic" width="16"
                    height="16" />
                  Diastolic Blood Pressure(HG)</span>
              </h6>

              <div class="custom-slider" style="min-height:90px">
                <ngx-slider [(value)]="minDBP" [(highValue)]="maxDBP" [options]="dbpOptions">
                </ngx-slider>
              </div>
            </div>
          </div>

          <!-- <div class="flex p-card-header justify-content-around">
            <div class="flex justify-content-between align-items-center">
              <div class="flex mr-3"><img src="assets/watchrx/svg/blue-circle.svg" alt="Download Stats" width="16"
                  height="16" />
                Systolic</div>
              <div>
                <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.systolicBloodPressureCriticalMax
                  ||0}}</p>
                <p>Critical Min - {{patientData?.thresholdMinMax?.systolicBloodPressureCriticalMin||0}} </p>
              </div>
            </div>
            <div class="flex justify-content-between align-items-center">
              <div class="flex mr-3"> <img src="assets/watchrx/svg/orange-circle.svg" alt="Download Stats" width="16"
                  height="16" />
                Diastolic</div>
              <div>
                <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMax
                  ||0}}</p>
                <p>Critical Min - {{patientData?.thresholdMinMax?.diastolicBloodPressureCriticalMin||0}} </p>
              </div>
            </div>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions1" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Blood Sugar'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minBS" [(highValue)]="maxBS" [options]="bsOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.bloodSugarCriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.bloodSugarCriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions6" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Steps Count'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minSC" [(highValue)]="maxSC" [options]="scOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.pedometerStepCountCriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.pedometerStepCountCriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions2" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Weight'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minW" [(highValue)]="maxW" [options]="wOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.weightCriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.weightCriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions4" />
          </div>
        </ng-container>
        <ng-container *ngIf="datas.name=='Sleep Monitor'">
          <div class="flex flex-row w-full" style="min-height:90px">
            <div class="w-full">
              <div class="custom-slider">
                <ngx-slider [(value)]="minSLP" [(highValue)]="maxSLP" [options]="slpOptions">
                </ngx-slider>
              </div>
            </div>
            <hr />
          </div>
          <!-- <div class="flex p-card-header justify-content-center align-items-center flex-column">
            <p class="mb-1">Critical Max - {{patientData?.thresholdMinMax?.spo2CriticalMax ||0}}</p>
            <p>Critical Min - {{patientData?.thresholdMinMax?.spo2CriticalMin||0}} </p>
          </div> -->
          <hr />
          <div class="p-3">
            <p-chart type="line" [data]="datas.obj" [options]="chartOptions7" />
          </div>
        </ng-container>

        <!-- <div class="p-3">
                  <p-chart type="line" [data]="datas.obj" [options]="chartOptions" />
                </div> -->
      </div>
    </ng-container>



  </div>
</div>
<p-toast key="tst"></p-toast>