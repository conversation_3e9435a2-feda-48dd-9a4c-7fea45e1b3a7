export interface ApiResponse {
  success: boolean;
  responseCode: number | null;
  responsecode: number | null;
  messages: string[];
  cumulativeTime: number;
  count: number;
  rpmTime: number;
  ccmTime: number;
  pcmTime: number;
  programs: Program[];
  minsDetails: string;
  patientDocumentationVOList: PatientDocument[];
}

export interface Program {
  id: number;
  label: string;
  value: string;
}

export interface PatientDocument {
  documentationId: number;
  note: string;
  documentedDate: string;
  duration: number;
  createdDate: string;
  patientId: number;
  review: string | null;
}
