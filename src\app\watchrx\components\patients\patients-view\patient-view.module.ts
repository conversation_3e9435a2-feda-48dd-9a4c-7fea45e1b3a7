import { CommonModule } from '@angular/common';
import { NgModule,CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { FileUploadModule } from 'primeng/fileupload';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputTextModule } from 'primeng/inputtext';
import { PanelModule } from 'primeng/panel';
import { PanelMenuModule } from 'primeng/panelmenu';
import { RippleModule } from 'primeng/ripple';
import { StyleClassModule } from 'primeng/styleclass';
import { TableModule } from 'primeng/table';
import { TabViewModule } from 'primeng/tabview';
import { PatientsViewComponent } from './patients-view.component';
import { PatientsViewRoutingModule } from './patients-view.routing.module';
import { DialogModule } from 'primeng/dialog';
import { RadioButtonModule } from 'primeng/radiobutton';
import { CallsComponent } from "../../shared/calls/calls.component";
import { CardModule } from 'primeng/card';
import { SidebarModule } from 'primeng/sidebar';
import { SharedModule } from '../../shared/shared.module';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { FormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';

@NgModule({
  imports: [
    CommonModule,
    TableModule,
    ButtonModule,
    StyleClassModule,
    PanelMenuModule,
    InputGroupAddonModule,
    InputGroupModule,
    PanelModule,
    RippleModule,
    TabViewModule,
    PatientsViewRoutingModule,
    FileUploadModule,
    InputTextModule,
    DialogModule,
    RadioButtonModule,
    CallsComponent,
    CardModule,
    SidebarModule,
    SharedModule,
    ToastModule,
    ConfirmDialogModule,
    FormsModule,
    DropdownModule
],
  declarations: [PatientsViewComponent],
})
export class PatientViewModule {}
