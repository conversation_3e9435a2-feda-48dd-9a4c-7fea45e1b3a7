import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import moment from 'moment';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { ToastModule } from 'primeng/toast';
import { PatientData } from '../../../../../../api/editPatientProfile';
import { LoaderService } from '../../../../../../loader/loader/loader.service';
import { DailyScheduleService } from './service/dailySchedule.service';

@Component({
  selector: 'app-daily-schedule',
  standalone: true,
  imports: [CommonModule, FormsModule, CalendarModule, ToastModule],
  templateUrl: './daily-schedule.component.html',
  styleUrl: './daily-schedule.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class DailyScheduleComponent {
  patientId: number = 0;
  earlyMorningTime: any = new Date(1970, 1, 1, 6, 0, 0);
  bfTime: any = new Date(1970, 1, 1, 8, 0, 0);
  luncTime: any = new Date(1970, 1, 1, 11, 30, 0);
  afnsTime: any = new Date(1970, 1, 1, 15, 0, 0);
  dinnerTime: any = new Date(1970, 1, 1, 18, 0, 0);
  bedTime: any = new Date(1970, 1, 1, 20, 0, 0);

  response: PatientData | undefined;

  constructor(
    private dailyScheduleService: DailyScheduleService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getDailySchedule();
  }

  getDailySchedule() {
    this.loaderService.show();
    let req = {
      patientId: this.patientId,
    };
    this.dailyScheduleService.getDailySchedule(req).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        this.response = res;
        this.earlyMorningTime = this.getDate(
          res?.earlyMorningHour + ':' + res?.earlyMorningMin
        );
        this.bfTime = this.getDate(
          res?.breakFastHour + ':' + res?.breakFastMin
        );
        this.luncTime = this.getDate(res?.lunchHour + ':' + res?.lunchMin);
        this.afnsTime = this.getDate(
          res?.noonSnackHour + ':' + res?.noonSnackMin
        );
        this.dinnerTime = this.getDate(res?.dinnerHour + ':' + res?.dinnerMin);
        this.bedTime = this.getDate(res?.bedHour + ':' + res?.bedMin);
      }
    });
  }

  updateDailySchedule() {
    let payload = {
      patientId: this.patientId,
      firstName: this.response?.firstName,
      lastName: this.response?.lastName,
      isConsentTaken: this.response?.isConsentTaken,
      address: {
        addressId: this.response?.address?.addressId,
        address1: this.response?.address?.address1,
        address2: this.response?.address?.address2,
        city: this.response?.address?.city,
        state: this.response?.address?.state,
        zip: this.response?.address?.zip,
        country: this.response?.address?.country,
      },
      dob: this.formatDateToISO(),
      phoneNumber: this.response?.phoneNumber,
      earlyMorningHour: this.earlyMorningTime
        .getHours()
        .toString()
        .padStart(2, '0'),
      earlyMorningMin: this.earlyMorningTime
        .getMinutes()
        .toString()
        .padStart(2, '0'),
      breakFastHour: this.bfTime.getHours().toString().padStart(2, '0'),
      breakFastMin: this.bfTime.getMinutes().toString().padStart(2, '0'),
      lunchHour: this.luncTime.getHours().toString().padStart(2, '0'),
      lunchMin: this.luncTime.getMinutes().toString().padStart(2, '0'),
      noonSnackHour: this.afnsTime.getHours().toString().padStart(2, '0'),
      noonSnackMin: this.afnsTime.getMinutes().toString().padStart(2, '0'),
      dinnerHour: this.dinnerTime.getHours().toString().padStart(2, '0'),
      dinnerMin: this.dinnerTime.getMinutes().toString().padStart(2, '0'),
      bedHour: this.bedTime.getHours().toString().padStart(2, '0'),
      bedMin: this.bedTime.getMinutes().toString().padStart(2, '0'),
    };
    this.loaderService.show();
    this.dailyScheduleService.updateDailySchedule(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.messageService.add({
          severity: 'success',
          summary: 'Success',
          detail: 'Schedule updated successfully',
        });
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to update schedule',
        });
      }
    });
  }

  getDate(dateTime: string) {
    return moment(
      `1970-02-01 ${dateTime}`,
      'YYYY-MM-DD HH:mm Z'
    ).toDate();
  }

  formatDateToISO() {
    if (this.response?.dateOfBirth) {
      let date = new Date(this.response?.dateOfBirth);
      return new Date(
        date.getTime() - date.getTimezoneOffset() * 60000
      ).toISOString();
    }
    return null;
  }

  resetSchedule()
  {
  this.earlyMorningTime = new Date(1970, 1, 1, 6, 0, 0);
  this.bfTime = new Date(1970, 1, 1, 8, 0, 0);
  this.luncTime = new Date(1970, 1, 1, 11, 30, 0);
  this.afnsTime = new Date(1970, 1, 1, 15, 0, 0);
  this.dinnerTime = new Date(1970, 1, 1, 18, 0, 0);
  this.bedTime = new Date(1970, 1, 1, 20, 0, 0);
  }
}
