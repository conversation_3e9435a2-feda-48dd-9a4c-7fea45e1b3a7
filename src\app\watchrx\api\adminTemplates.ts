export interface TemplateResponse {
  success: boolean;
  responseCode: string;
  messages: string[];
  eList: Template[];
}

export interface Template {
  chonicConditionId: number;
  chonicConditionName: string;
  createdByUserId: number;
  createdByUserName: string;
  createdDate: string;
  updatedDate: string;
  icdCode: string;
}

export interface TemplateDetailsReponse {
  chronicConditionId: number;
  templates: TemplateDetails[];
}

export interface TemplateDetails {
  chronicConditionId: any;
  questions: string;
  questionId: number;
  answer: string;
  createdDate: string;
  status: boolean;
  category: string;
  programName: string;
}
