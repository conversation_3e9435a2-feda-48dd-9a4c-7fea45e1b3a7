<p-toast />
<p-confirmDialog [focusTrap]="false" [blockScroll]="false" />
<div class="grid">
  <div class="col-12">
    <div class="flex flex-row md:justify-content-end mb-2">
      <p-button label="Add New Feature" severity="secondary" class="mr-1" [outlined]="true" icon="pi pi-plus"
        (click)="addCaseMaanger()" />
    </div>

    <div class="grid p-fluid mt-3">
      <div class="field col-12">
        <p-table #dt [value]="caseManagersList" [paginator]="false" [totalRecords]="totalCaseManagers"
          [rows]="itemsPerPage" [lazy]="true" [loading]="loader" (onLazyLoad)="loadFeaturesList($event)"
          responsiveLayout="scroll" styleClass="p-datatable-gridlines p-datatable-striped table-min-height">
          <ng-template pTemplate="header">
            <tr>
              <th>Feature Id</th>
              <th>Feature Name</th>
              <th>Feature Description</th>
              <th>Action</th>
            </tr>
          </ng-template>
          <ng-template let-featureInfo pTemplate="body">
            <tr>
              <td>
                {{ featureInfo.roleId }}
              </td>
              <td>{{ featureInfo.roleName }}</td>
              <td>{{ featureInfo.roleDescription }}</td>
              <td>
                <div class="flex">
                  <p-button [rounded]="false" icon="pi pi-trash" severity="secondary" size="small" class="mr-1"
                    title="delete" [outlined]="true" (click)="onDeleteFeature(featureInfo['roleId'])" />
                </div>
              </td>
            </tr>
          </ng-template>
        </p-table>
      </div>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="visible" position="right" [style]="{ width: '40rem' }">
  <ng-template pTemplate="header">
    <div class="flex align-items-center gap-2">
      <span class="font-bold">
        <i class="pi pi-plus mr-3"></i>
        Add Feature
      </span>
    </div>
  </ng-template>
  <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
    <div class="p-fluid p-formgrid grid">
      <div class="field col-12 md:col-12">
        <label for="rolename" class="font-bold block mb-2">Role Name <span class="p-text-danger">*</span></label>
        <input id="rolename" formControlName="roleName" pInputText />
        <div *ngIf="
            userForm.get('roleName')?.invalid &&
            userForm.get('roleName')?.touched
          ">
          <small class="ng-dirty ng-invalid p-text-danger"> Role Name is required.</small>
        </div>
      </div>

      <div class="field col-12 md:col-12">
        <label for="roledescription" class="font-bold block mb-2">Role Description <span
            class="p-text-danger">*</span></label>
        <textarea id="roledescription" formControlName="roleDescription" pInputText></textarea>
        <div *ngIf="
            userForm.get('roleDescription')?.invalid &&
            userForm.get('roleDescription')?.touched
          ">
          <small class="ng-dirty ng-invalid p-text-danger"> Role Description is required.</small>
        </div>
      </div>
    </div>
  </form>
  <ng-template pTemplate="footer">
    <div class="flex justify-content-end gap-2">
      <p-button label="Cancel" [outlined]="true" severity="secondary" (click)="visible = false" />
      <p-button label="Save" severity="primary" (click)="onSubmit()" />
    </div>
  </ng-template>
</p-sidebar>