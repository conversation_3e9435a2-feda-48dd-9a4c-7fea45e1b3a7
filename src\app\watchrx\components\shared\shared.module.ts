import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { AlertsComponent } from './alerts/alerts.component';
import { EncounterFormComponent } from './encounter/encounter.component';
import { AllocatedMedicalDevicesComponent } from './medical-devices-tab/components/allocated-medical-devices/allocated-medical-devices.component';
import { BulkAddDevicesComponent } from './medical-devices-tab/components/bulk-add-devices/bulk-add-devices.component';
import { UnallocatedMedicalDevicesComponent } from './medical-devices-tab/components/unallocated-medical-devices/unallocated-medical-devices.component';
import { MedicalDevicesTabComponent } from './medical-devices-tab/medical-devices-tab.component';
import { ScheduleTextMessageFormComponent } from './schedule-text-message-form/schedule-text-message-form.component';
import { WatchrxCalendarComponent } from './watchrx-calendar/watchrx-calendar.component';
import { CallsComponent } from './calls/calls.component';
import { VideoCallsComponent } from './video-calls/video-calls.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    EncounterFormComponent,
    ScheduleTextMessageFormComponent,
    AlertsComponent,
    WatchrxCalendarComponent,
    MedicalDevicesTabComponent,
    AllocatedMedicalDevicesComponent,
    UnallocatedMedicalDevicesComponent,
    BulkAddDevicesComponent,
    CallsComponent,
    VideoCallsComponent
  ],
  exports: [
    EncounterFormComponent,
    ScheduleTextMessageFormComponent,
    AlertsComponent,
    WatchrxCalendarComponent,
    MedicalDevicesTabComponent,
    AllocatedMedicalDevicesComponent,
    UnallocatedMedicalDevicesComponent,
    BulkAddDevicesComponent,
    CallsComponent,
    VideoCallsComponent
  ],
})
export class SharedModule {}
