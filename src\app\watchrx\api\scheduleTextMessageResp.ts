import { Program } from "./patientEncounter";

export interface ScheduleMessage {
  scheduledTextMessagesId: number;
  question: string;
  answer: string[];
  question_time: string | null;
  senderName: string;
  createdDate: string;
  updatedDate: string;
  dayOfWeek: string;
  timeSlots: string;
  patientId: number;
  startDate: string;
  endDate: string;
  recurrence: string;
  timeSlotsArrary: string[];
  dayOfWeekArrary: boolean[];
  duration: number;
  review: string | null;
}

export interface ScheduleTextMessageResp {
  success: boolean;
  responseCode: string | null;
  responsecode: string | null;
  messages: string[];
  eList: ScheduleMessage[];
  count: number;
  phoneNumber: string | null;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  minsDetails: string;
  programs: Program[];
}
