<div class="card" style="margin-bottom: 23px">
  <div class="flex md:flex-row mb-5">
    <div class="col-3">
      <p-dropdown
        [options]="orgsList"
        checkmark="true"
        optionLabel="groupName"
        optionValue="groupId"
        [filter]="true"
        [(ngModel)]="selectedOrg"
        filterBy="groupName"
        [showClear]="true"
        placeholder="Select Organization"
        class="mr-2"
      />
    </div>
    <div class="col-2">
      <p-calendar
        [(ngModel)]="startDate"
        [showIcon]="true"
        inputId="buttondisplay"
        [showOnFocus]="false"
        placeholder="Start Date"
        [showButtonBar]="true"
      />
    </div>
    <div class="col-2">
      <p-calendar
        [(ngModel)]="endDate"
        [showIcon]="true"
        inputId="buttondisplay"
        [showOnFocus]="false"
        placeholder="End Date"
        [showButtonBar]="true"
      />
    </div>
    <div class="col-2 flex flex-row justify-content-start gap-2">
      <p-button
        icon="pi pi-filter"
        [raised]="true"
        severity="info"
        label="Filter"
        [outlined]="true"
        (onClick)="submitFilter()"
        pTooltip="Submit Filter"
        tooltipPosition="top"
        class="w-100"
      ></p-button
      ><p-button
        icon="pi pi-refresh"
        [raised]="true"
        severity="danger"
        [outlined]="false"
        [rounded]="false"
        (onClick)="clearFilter()"
        pTooltip="Clear Filter"
        tooltipPosition="top"
      ></p-button>
    </div>
    <div class="col-2 flex flex-row justify-content-between gap-2">
      <p-button
        icon="pi pi-download"
        [raised]="true"
        label="XLSX"
        [outlined]="true"
        pTooltip="Download in XLSX"
        tooltipPosition="top"
        (onClick)="downlaodXLSX()"
      ></p-button>
    </div>
  </div>
  <p-table
    [value]="alertsList"
    responsiveLayout="scroll"
    styleClass="p-datatable-gridlines p-datatable-striped"
    [paginator]="true"
    [totalRecords]="totalCount"
    [rows]="10"
    [lazy]="true"
    (onLazyLoad)="loadAlerts($event)"
    [responsiveLayout]="'scroll'"
  >
    <ng-template pTemplate="header">
      <tr>
        <th style="width: 10%">
          <p-columnFilter [showMenu]="false">
            <ng-template
              pTemplate="filter"
              let-value
              let-filter="filterCallback"
            >
              <p-dropdown
                [(ngModel)]="alertType"
                [options]="alertTypeList"
                [showClear]="true"
                placeholder="Alert Type"
                (onChange)="alertByType()"
                appendTo="body"
                [filter]="true"
                filterBy="value"
                checkmark="true"
                optionLabel="value"
                optionValue="value"
                class="mr-2"
              />
            </ng-template>
          </p-columnFilter>
        </th>
        <th style="width: 20%">Date & Time</th>
        <th style="width: 15%">
          <p-columnFilter [showMenu]="false"
            ><ng-template
              pTemplate="filter"
              let-value
              let-filter="filterCallback"
            >
              <p-dropdown
                [(ngModel)]="selectedPatient"
                [options]="patientsList"
                [showClear]="true"
                placeholder="Patient Name"
                (onChange)="alertByPatient()"
                appendTo="body"
                [filter]="true"
                filterBy="value"
                checkmark="true"
                optionLabel="patientName"
                optionValue="patientName"
                class="mr-2"
              />
            </ng-template>
          </p-columnFilter>
        </th>
        <th style="width: 55%">Description</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-alert>
      <tr>
        <td class="full-width">
          <p-tag
            [value]="alert.severity"
            [severity]="getSeverity(alert.severity)"
            class="full-width-tag"
          />
        </td>
        <td>{{ alert.dateTime }}</td>
        <td>{{ alert.patientInfo }}</td>
        <td>{{ alert.description }}</td>
      </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="4" style="text-align: center">
          <h4>No records found</h4>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>
