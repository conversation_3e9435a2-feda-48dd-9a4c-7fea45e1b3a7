* {
    box-sizing: border-box;
}

html {
    height: 100%;
    font-size: $scale;
}

body {
    font-family: var(--font-family);
    color: var(--text-color);
    background-color: var(--surface-ground);
    margin: 0;
    padding: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    // color: var(--primary-color);
}

.layout-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}


.button-group {
    display: flex;
    align-items: center;
}

.left-button {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    background-color: green;
}

.right-button {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: green;
}

.center-button {
    border-radius: 0px;
    background-color: gray;
}