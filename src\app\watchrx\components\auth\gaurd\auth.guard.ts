import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  createUrlTreeFromSnapshot,
} from '@angular/router';
import { map } from 'rxjs';
import { AuthenticatorService } from '../service/authenticator.service';

export const authGuard = (next: ActivatedRouteSnapshot) => {
  return inject(AuthenticatorService)
    .isAuthenticated ? true : createUrlTreeFromSnapshot(next, ['/', 'login']);
 // return true;
};