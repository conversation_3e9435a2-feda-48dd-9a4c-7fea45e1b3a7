import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { ProgressBarModule } from 'primeng/progressbar';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { LoaderService } from './loader.service';

@Component({
  selector: 'app-loader',
  templateUrl: './loader.component.html',
  styleUrls: ['./loader.component.css'],
  imports: [ProgressSpinnerModule, CommonModule, ProgressBarModule],
  standalone: true,
})
export class LoaderComponent implements OnInit {
  showLoader = false;

  constructor(public loaderService: LoaderService) {}

  ngOnInit(): void {
    this.loaderService.loaderState.subscribe((state) => {
      this.showLoader = state;
    });
  }
}
