import { Program } from "./patientEncounter";

export interface TextMessageRep {
  responseCode: string | null;
  responseMessage: string | null;
  responseType: string | null;
  nurseId: string | null;
  status: boolean;
  viewMessages: ViewMessage[];
  resultCount: number;
  mins: number;
  phoneNo: string;
  rpmMins: number;
  ccmMins: number;
  pcmMins: number;
  minsDetails: string;
  programs: Program[];
}

export interface ViewMessage {
  question: string;
  answer: string;
  timeStamp: string;
  phoneNo: string;
  mins: number | null;
  duration: number;
}
