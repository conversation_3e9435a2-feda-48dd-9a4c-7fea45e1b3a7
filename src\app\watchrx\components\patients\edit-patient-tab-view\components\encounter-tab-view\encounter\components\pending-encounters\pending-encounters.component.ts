import { CommonModule, DatePipe, formatDate } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import {
  ConfirmationService,
  LazyLoadEvent,
  MessageService,
} from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { RadioButtonModule } from 'primeng/radiobutton';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { encounterReasonList } from '../../../../../../../../api/globalConstants';
import {
  AddEncounter,
  Encounter,
  Program,
} from '../../../../../../../../api/patientEncounter';
import { LoaderService } from '../../../../../../../../loader/loader/loader.service';
import { GenericDateService } from '../../../../../../../../service/generic-date.service';
import { EncounterFormComponent } from '../../../../../../../shared/encounter/encounter.component';
import { EncounterService } from '../../service/encounter.service';
import { SidebarModule } from 'primeng/sidebar';
import { CommonService } from '../../../../../../../../service/common.service';
import { TabViewModule } from 'primeng/tabview';

@Component({
  selector: 'app-pending-encounters',
  standalone: true,
  imports: [
    CalendarModule,
    FormsModule,
    DropdownModule,
    TableModule,
    CommonModule,
    DialogModule,
    ButtonModule,
    InputTextModule,
    AvatarModule,
    RadioButtonModule,
    InputNumberModule,
    ConfirmDialogModule,
    EncounterFormComponent,
    ToastModule,
    SidebarModule,
    TabViewModule,
  ],
  templateUrl: './pending-encounters.component.html',
  styleUrl: './pending-encounters.component.scss',
  providers: [ConfirmationService, MessageService,]
})
export class PendingEncountersComponent implements OnInit {
  activeIndex: any = 0;
  datePipe: DatePipe = new DatePipe('en-US');
  sortOptions: any[] = [];
  startDate: Date = this.dateService.getStartDateOfMonth();
  endDate: Date = this.dateService.getCurrentDate();
  patientId: number = -1;
  encounterList: Encounter[] = [];
  totalEncounterCount: number = 0;
  loader: boolean = false;
  programList: Program[] = [];
  addEncounterLoader: boolean = false;
  isEditEncounter: boolean = false;
  editData: any = {};
  visible: boolean = false;
  reasonList: any[] = encounterReasonList;
  filterReason: any = '';

  @Output() dataEmitter: EventEmitter<any> = new EventEmitter<any>();
  @ViewChild(EncounterFormComponent) encounterForm!: EncounterFormComponent;
  @Input() patientData: any;
  title: string = "Add Encounter";
  reasonCodeMap = {
    "Critical Alert": "EC_001",
    "Data Review": "EC_002",
    "Phone Call": "EC_003",
    "RPM Review": "EC_004",
    "CCM Review": "EC_005",
    "PCM Review": "EC_006",
    "EMR/Chart review": "EC_007",
    "BPM Device Documentation/Training": "EC_008",
    "Glucometer Device education Template": "EC_009",
    "Weigh scale device education template for CHF": "EC_010",
    "Weigh scale device education template for ESRD, volume overload": "EC_011",
    "Introduction to RPM": "EC_012",
    "Introduction to CCM": "EC_013",
    "Introduction to PCM": "EC_014",
    "Introduction to RTM": "EC_015",
    "Introduction to PCM & RPM": "EC_016",
    "Introduction to PCM & RTM": "EC_017",
    "Introduction to CCM & RPM": "EC_018",
    "Other": "EC_019",
    "Text Message": "EC_020",
    "Appointment": "EC_021",
    "Home Visits": "EC_022",


  };

  addPendingEncounterLoader = false;
  constructor(
    private encounterService: EncounterService,
    private route: ActivatedRoute,
    private messageService: MessageService,
    private loaderService: LoaderService,
    private confirmationService: ConfirmationService,
    private dateService: GenericDateService,
    private commonService: CommonService
  ) {
    this.sortOptions = [];
  }

  loadEncounterList($event: LazyLoadEvent | TableLazyLoadEvent) {
    this.loaderService.show();
    let pageSize = $event.rows || 25;
    let first = $event.first || 0;
    let pageNo = first / pageSize;

    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let payload =
      this.patientId + '/' + pageNo + '/' + pageSize + '/' + fromToDetails;
    this.encounterService.getPendingEncounters(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res.success) {
        this.dataEmitter.emit(res?.minsDetails);
        this.encounterList = res?.draftList;
        this.totalEncounterCount = res?.resultCount;
        this.programList = res?.programs;
        this.encounterList.forEach(d => {
          d.encounterDescription = d.encounterDescription.replace(/\n/g, '<br>');
        })
        console.log(this.encounterList)
      }
    });
  }

  filterEnounter() {
    this.loader = true;
    let pageNo = 0;
    let pageSize = 25;
    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let payload =
      this.patientId + '/' + pageNo + '/' + pageSize + '/' + fromToDetails;
    this.encounterService.getPendingEncounters(payload).subscribe((res) => {
      this.loader = false;
      if (res.success) {
        this.dataEmitter.emit(res?.minsDetails);
        this.encounterList = res?.draftList;
        this.totalEncounterCount = res?.resultCount;
        this.encounterList.forEach(d => {
          d.encounterDescription = d.encounterDescription.replace(/\n/g, '<br>');
        })
        console.log(this.encounterList)
      }
    });
  }

  reasonChange() {
    if (
      this.filterReason?.reason === undefined ||
      this.filterReason?.reason === ''
    ) {
      this.filterEnounter();
      return;
    }
    let fromToDetails =
      formatDate(this.startDate, 'yyyy-MM-dd', 'en-US') +
      '/' +
      formatDate(this.endDate, 'yyyy-MM-dd', 'en-US');
    let payload =
      this.patientId + '/' + this.filterReason?.reason + '/' + fromToDetails;
    this.encounterService.getEncountersByReason(payload).subscribe((res) => {
      this.loader = false;
      if (res.success) {
        this.dataEmitter.emit(res?.minsDetails);
        this.encounterList = res?.encounterNewList;
        this.totalEncounterCount = res?.resultCount;
        this.encounterList.forEach(d => {
          d.encounterDescription = d.encounterDescription.replace(/\n/g, '<br>');
        })
        console.log(this.encounterList)
      }
    });
  }

  stripHtmlTags(html: any) {
    if (html && html.changingThisBreaksApplicationSecurity) {
      var xx = html?.changingThisBreaksApplicationSecurity.replace(/<p>/g, '\n');
      return xx.replace(/<\/?[^>]+(>|$)|&nbsp;/g, '');
    }
    else {
      var xx = html?.replace(/<p>/g, '\n');
      return xx.replace(/<\/?[^>]+(>|$)|&nbsp;/g, '');
    }

  }

  onSubmit() {
    this.encounterForm.stopSpeechToText()
    if (
      this.encounterForm.selectedProgram === undefined ||
      this.encounterForm.selectedProgram === ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select program',
        life: 3000,
      });
      return;
    }

    if (
      this.encounterForm.selectedEReason?.reason === undefined ||
      this.encounterForm.selectedEReason?.reason === ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please select encounter reason',
        life: 3000,
      });
      return;
    }
    if (
      this.encounterForm?.description === undefined ||
      this.encounterForm?.description === '' ||
      this.stripHtmlTags(this.encounterForm.description) == ''
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Rejected',
        detail: 'Please enter a description',
        life: 3000,
      });
      return;
    }

    let req: AddEncounter = {
      patientId: this.patientId,
      encounterReason: this.encounterForm.selectedEReason?.reason || '',
      encounterDescription:
        this.stripHtmlTags(this.encounterForm.description) || '',
      encounterDateTime:
        formatDate(this.encounterForm.encounterDate, 'yyyy-MM-dd', 'en-US') +
        ' ' +
        formatDate(this.encounterForm.encounterTime, 'HH:mm:ss', 'en-US'),
      review: this.encounterForm.selectedProgram || 'rpm',
      duration: this.encounterForm.value4 || '0',
      //reasonCode:this.reasonCodeMap[this.encounterForm.selectedEReason.reason as keyof typeof this.reasonCodeMap]||'EC_019',
      reasonCode: this.encounterForm.selectedEReason?.reasonCode || 'EC_019',
       "isDraft":false
    };

    if (this.isEditEncounter) {
      req.encounterId = this.encounterForm?.encounterId;
    }

    this.addEncounterLoader = true;
    this.encounterService.savePendingEncounter(req).subscribe((res) => {
      this.addEncounterLoader = false;
      if (res.success) {
        this.filterEnounter();
        this.visible = false;
        this.isEditEncounter = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Encounter createad successfully.',
          life: 3000,
        });
        this.commonService.updateData(true);
      } else {

        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: res.messages && res.messages.length > 0 ? res.messages[0] : 'Failed to create encounter',
          life: 3000,
        });
      }
    });
  }

  onSubmitDraft() {
    let req: AddEncounter = {
      patientId: this.patientId,
      encounterReason: this.encounterForm.selectedEReason?.reason || '',
      encounterDescription: (this.encounterForm.description != "" && this.encounterForm.description != undefined) ?
        this.stripHtmlTags(this.encounterForm.description) || '' : '',
      encounterDateTime:
        formatDate(this.encounterForm.encounterDate, 'yyyy-MM-dd', 'en-US') +
        ' ' +
        formatDate(this.encounterForm.encounterTime, 'HH:mm:ss', 'en-US'),
      review: this.encounterForm.selectedProgram || 'rpm',
      duration: this.encounterForm.value4 || '0',
      //reasonCode:this.reasonCodeMap[this.encounterForm.selectedEReason.reason as keyof typeof this.reasonCodeMap]||'EC_019',
      reasonCode: this.encounterForm.selectedEReason?.reasonCode || 'EC_019',
      "isDraft":true
    };
    if (this.isEditEncounter) {
      req.encounterId = this.encounterForm?.encounterId;
    }
    this.addPendingEncounterLoader = true;
    this.encounterService.addPendingEncounter(req).subscribe((res) => {
      this.addPendingEncounterLoader = false;
      if (res.success) {
        this.filterEnounter();
        this.visible = false;
        this.isEditEncounter = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Confirmed',
          detail: 'Encounter createad successfully.',
          life: 3000,
        });
        this.commonService.updateData(true);
      } else {

        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: res.messages && res.messages.length > 0 ? res.messages[0] : 'Failed to create encounter',
          life: 3000,
        });
      }
    });
  }
  onCancel() {
    this.visible = false;
    this.encounterForm.stopSpeechToText()
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.patientId = params['id'];
    });
    this.getReasonList();
    this.getProgramsList();
  }

  getProgramsList() {
    this.encounterService.getPatientPrograms(this.patientId).subscribe((token) => {
      this.programList = token?.program;
    });
  }

  convert(val: number): string {
    const minutes: number = Math.floor(val);
    const seconds: number = Math.round((val - minutes) * 60);
    return `${minutes}m:${seconds}s`;
  }

  showDialog() {
    this.title = "Add Encounter"
    this.editData = '';
    this.visible = true;
    this.isEditEncounter = false;
  }

  deleteEncounter(event: Event, encounterId: number) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Do you want to delete this record?',
      header: 'Delete Confirmation',
      icon: 'pi pi-info-circle',
      acceptButtonStyleClass: 'p-button-danger p-button-text',
      rejectButtonStyleClass: 'p-button-text p-button-text',
      acceptIcon: 'none',
      rejectIcon: 'none',
      accept: () => {
        let payload = {
          encounterId: encounterId,
        };
        this.encounterService.deleteEncounter(payload).subscribe((res) => {
          if (res.success) {
            this.filterEnounter();
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: 'Encounter deleted successfully',
            });
          } else {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: 'Failed to delete Encounter.',
            });
          }
        });
      },
      reject: () => { },
    });
  }

  editEncounter(item: Encounter) {
    this.title = "Edit Encounter"
    this.visible = true;
    this.isEditEncounter = true;
    this.editData = {
      reason: item?.encounterReason,
      description: item?.encounterDescription,
      selectedProgram: item?.review,
      dateTime: item?.encounterDateTime,
      duration: item?.duration,
      encounterId: item?.encounterId,
    };
  }

  downloadEncounter() {
    this.loaderService.show();
    let payload = {
      startDate: formatDate(
        this.startDate,
        'yyyy-MM-ddTHH:mm:ss.SSSZ',
        'en-US'
      ),
      endDate: formatDate(this.endDate, 'yyyy-MM-ddTHH:mm:ss.SSSZ', 'en-US'),
      userId: this.patientId,
      filterType:
        this.filterReason?.reason === undefined ||
          this.filterReason?.reason === ''
          ? 'all'
          : this.filterReason?.reason,
    };

    this.encounterService.downloadEncounterReport(payload).subscribe((res) => {
      this.loaderService.hide();
      if (res) {
        console.log(res, 'ddddddddd');
        const link = document.createElement('a');
        link.setAttribute('download', '');
        link.href = res?.url;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        this.messageService.add({
          severity: 'error',
          summary: 'Rejected',
          detail: 'Failed to download encounter',
          life: 3000,
        });
      }
    });
  }

  updateScrollBlocking() {
    const anySidebarOpen = this.visible
    if (anySidebarOpen) {
      document.body.classList.add('blocked-scroll');
    } else {
      document.body.classList.remove('blocked-scroll');
    }
  }

  getReasonList() {
    this.encounterService.getReasonList().subscribe((res: any) => {
      if (res) {
        console.log('reasonList', res);
        this.reasonList = this.convertObjectToArray(res);
      }
    }, err => {
      this.reasonList = [];
    });
  }

  convertObjectToArray(obj: any): { reason: string, reasonCode: string }[] {
    return Object.keys(obj).map(key => ({
      reason: key,
      reasonCode: obj[key]
    }));
  }
}
